#!/usr/bin/env node

const { spawn } = require('child_process');
const pinoElastic = require('pino-elasticsearch');

const streamToElastic = pinoElastic({
  index: 'api_development_%{DATE}',
  type: 'log',
  consistency: 'one',
  node: 'http://***********:9200',
  'es-version': 7,
  'bulk-size': 20,
  ecs: true,
});
const child = spawn('pm2', ['logs', 's1-api', '--raw']);
child.stdout.pipe(streamToElastic);
child.on('close', (code) => {
  console.log(`child process exited with code ${code}`);
});
