#!/usr/bin/env node

const { spawn } = require('child_process');
const pinoElastic = require('pino-elasticsearch');
const config = require('../config');

const child = spawn('pm2', ['logs', '--raw']);

if (config.elasticUrl) {
  const streamToElastic = pinoElastic({
    index: 'api_production_%{DATE}',
    type: 'log',
    consistency: 'one',
    node: config.elasticUrl,
    'es-version': 7,
    'bulk-size': 2000,
  });
  child.stdout.pipe(streamToElastic);
}

child.on('close', (code) => {
  console.log(`child process exited with code ${code}`);
});
