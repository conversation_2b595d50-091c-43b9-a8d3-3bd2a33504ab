const { Server } = require('socket.io');
const config = require('../config');

global.io = new Server({
    cors: {
        origin: true,
        methods: ['GET', 'POST'],
    },
});

global.io.on('connection', (socket) => {
    if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'development') console.log('Socket client connected:', socket.id);

    // disabled re-translate all messages between clients (api servers and panels, etc)
    // instead use send to groups: admin-panel, support-panel, tracker-panel, s1-api
    // socket.onAny((eventName, ...args) => {
    //     global.io.emit(eventName, ...args);
    // });
    socket.on('disconnect', () => {
        if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'development') console.log('Socket client disconnected', socket.id);

        socket.leaveAll();
    });
    // resend message to the specific client ID from another client
    socket.on('private', (request) => {
        const senderClientId = socket.id;
        const { data, targetClientId, eventName } = request;
        io.to(targetClientId).emit(eventName, { data, senderClientId });
    });
    // connect clients to the specific group, for example admin panel for update lists
    socket.on('joinGroup', (groupName) => {
        if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'development') console.log(`Client ${socket.id} joined to the group "${groupName}"`);

        socket.join(groupName);
    });
    socket.on('group', (request) => {
        const senderClientId = socket.id;
        const { data, groupName, eventName } = request;
        io.to(groupName).emit(eventName, { data, senderClientId });
    });
});

global.io.listen(config.socket.port);
console.log(`Listening socket io http on port: ${config.socket.port}`);
