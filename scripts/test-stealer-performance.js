#!/usr/bin/env node

/**
 * Performance comparison script for stealer detection
 * Tests both original and optimized versions
 */

const mongoose = require('mongoose');
const config = require('../config');

async function testStealerPerformance() {
  console.log('🔬 Testing stealer detection performance...\n');

  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(config.mongodb.audb, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB\n');

    const User = require('../rest/model/audb/User');
    
    // Get sample of trial users for testing
    const sampleSize = 10; // Test with small sample first
    const trialUsers = await User.find({
      package: 1,
      expires: { $gt: Math.floor(Date.now() / 1000) },
    })
    .limit(sampleSize)
    .exec();

    console.log(`📊 Testing with ${trialUsers.length} trial users\n`);

    if (trialUsers.length === 0) {
      console.log('❌ No trial users found for testing');
      return;
    }

    // Test 1: Original method (single user)
    console.log('🐌 Testing ORIGINAL method (single user):');
    const originalStart = Date.now();
    
    let originalResults = 0;
    for (const user of trialUsers.slice(0, 3)) { // Test only 3 users
      try {
        const logs = [
          require('../rest/model/audb/UserLogLogin'),
          require('../rest/model/audb/UserLogLive'),
          require('../rest/model/audb/UserLogRecord'),
          require('../rest/model/audb/UserLogVod'),
        ];
        
        const logsResults = await Promise.all(logs.map(model => user.reduceLogsByComparing({
          depth: 1,
          compareFields: { fingerprint: 1, ip: 1 },
          model,
          query: { playtime: { $gt: Math.floor(Date.now() / 1000) - (30 * 3600 * 24) } },
        })));
        
        const uids = new Set();
        logsResults.forEach(results => results.forEach(uids.add, uids));
        originalResults += uids.size;
        
        console.log(`  User ${user.id}: found ${uids.size} duplicates`);
      } catch (err) {
        console.log(`  User ${user.id}: error - ${err.message}`);
      }
    }
    
    const originalDuration = Date.now() - originalStart;
    console.log(`  ⏱️  Original method: ${originalDuration}ms for 3 users`);
    console.log(`  📊 Total duplicates found: ${originalResults}\n`);

    // Test 2: Optimized method
    console.log('🚀 Testing OPTIMIZED method:');
    const optimizedStart = Date.now();
    
    // Import optimized function
    const findDuplicateUsersForUser = require('../scheduler/jobs/resetStealersOptimized.js');
    
    // We need to extract the function from the module
    // Let's create a simple version for testing
    async function testOptimizedMethod(users) {
      const logs = [
        require('../rest/model/audb/UserLogLogin'),
        require('../rest/model/audb/UserLogLive'),
        require('../rest/model/audb/UserLogRecord'),
        require('../rest/model/audb/UserLogVod'),
      ];
      
      let totalResults = 0;
      const processedUsers = new Set();
      
      for (const user of users) {
        if (processedUsers.has(user.id)) continue;
        
        try {
          const queryCopy = { playtime: { $gt: Math.floor(Date.now() / 1000) - (30 * 3600 * 24) } };
          let filterIds = [user.id];
          const ids = new Set();
          const compareFields = { fingerprint: 1, ip: 1 };
          const fields = Object.keys(compareFields);
          
          let currentDepth = 1;
          while (filterIds.length && currentDepth > 0) {
            const logPromises = logs.map(async (model) => {
              try {
                const records = await model.find(
                  { ...queryCopy, uid: { $in: filterIds } }, 
                  compareFields
                ).lean().exec();
                
                if (records.length === 0) return [];
                
                const searchPromises = [];
                const perSetIds = [];
                
                fields.forEach((field) => {
                  const values = new Set();
                  records.forEach((record) => {
                    if (!record[field] || record[field] === 'undefined' || record[field] === '') return;
                    values.add(record[field]);
                  });
                  
                  if (values.size > 0) {
                    searchPromises.push(
                      model.find(
                        {
                          ...queryCopy,
                          [field]: { $in: [...values] },
                          uid: { $nin: [...filterIds, ...ids, ...processedUsers] },
                        },
                        { uid: 1 }
                      ).lean().exec()
                    );
                  } else {
                    searchPromises.push(Promise.resolve([]));
                  }
                });
                
                const resultSet = await Promise.all(searchPromises);
                resultSet.forEach((results) => {
                  const currentSetIds = new Set();
                  perSetIds.push(currentSetIds);
                  results.forEach(({ uid }) => {
                    if (!uid || ids.has(uid)) return;
                    currentSetIds.add(uid);
                  });
                });
                
                if (perSetIds.length > 0) {
                  return [...perSetIds[0]].filter((id) => 
                    perSetIds.reduce((counter, set) => counter + (set.has(id) ? 1 : 0), 0) === perSetIds.length
                  );
                }
                
                return [];
              } catch (err) {
                return [];
              }
            });
            
            const allResults = await Promise.all(logPromises);
            const newFilterIds = new Set();
            
            allResults.flat().forEach(uid => {
              if (uid && !ids.has(uid)) {
                newFilterIds.add(uid);
              }
            });
            
            filterIds = [...newFilterIds];
            filterIds.forEach(id => ids.add(id));
            currentDepth--;
          }
          
          totalResults += ids.size;
          processedUsers.add(user.id);
          ids.forEach(id => processedUsers.add(id));
          
          console.log(`  User ${user.id}: found ${ids.size} duplicates`);
        } catch (err) {
          console.log(`  User ${user.id}: error - ${err.message}`);
        }
      }
      
      return totalResults;
    }
    
    const optimizedResults = await testOptimizedMethod(trialUsers.slice(0, 3));
    const optimizedDuration = Date.now() - optimizedStart;
    
    console.log(`  ⏱️  Optimized method: ${optimizedDuration}ms for 3 users`);
    console.log(`  📊 Total duplicates found: ${optimizedResults}\n`);

    // Performance comparison
    console.log('📈 Performance Comparison:');
    console.log(`  Original:  ${originalDuration}ms`);
    console.log(`  Optimized: ${optimizedDuration}ms`);
    
    if (optimizedDuration < originalDuration) {
      const improvement = ((originalDuration - optimizedDuration) / originalDuration * 100).toFixed(1);
      console.log(`  🚀 Improvement: ${improvement}% faster`);
    } else {
      const slower = ((optimizedDuration - originalDuration) / originalDuration * 100).toFixed(1);
      console.log(`  🐌 Slower by: ${slower}%`);
    }
    
    console.log(`  Results match: ${originalResults === optimizedResults ? '✅' : '❌'}`);

    // Memory usage estimation
    console.log('\n💾 Memory Usage Estimation:');
    console.log('  Original: High (loads all user objects)');
    console.log('  Optimized: Medium (uses lean queries + batching)');
    
    // Scalability projection
    console.log('\n📊 Scalability Projection (for 10,000 users):');
    const originalProjection = (originalDuration / 3) * 10000;
    const optimizedProjection = (optimizedDuration / 3) * 10000;
    
    console.log(`  Original: ~${Math.round(originalProjection / 1000 / 60)} minutes`);
    console.log(`  Optimized: ~${Math.round(optimizedProjection / 1000 / 60)} minutes`);
    
    console.log('\n💡 Recommendations:');
    if (optimizedDuration < originalDuration) {
      console.log('  ✅ Use optimized version for production');
      console.log('  ✅ Consider running during low-traffic hours');
      console.log('  ✅ Monitor database performance during execution');
    } else {
      console.log('  ⚠️  Optimized version needs further tuning');
      console.log('  ⚠️  Consider different batch sizes or parallelism');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n📡 Disconnected from MongoDB');
  }
}

// Run the test
testStealerPerformance().then(() => {
  console.log('\n✅ Performance test completed!');
  process.exit(0);
}).catch(err => {
  console.error('❌ Test failed:', err);
  process.exit(1);
});
