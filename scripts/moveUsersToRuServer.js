const User = require('../rest/model/audb/User');
const PaymentLog = require('../rest/model/audb/PaymentLog');
const getPaymentBlacklistGeneralConfig = require('../rest/commands/paymentBlacklist/getGeneralConfig');
const UserConfig = require('../rest/model/audb/UserConfig');
const UserPermissionGroup = require('../rest/model/audb/UserPermissionGroup');
// const { paymentTypes } = require('../rest/constants/paymentType');

const newUsers = [];

async function checkPayments(userId, speedServer) {
  const countLogs = await PaymentLog.find({ uid: userId, upback: true, amount: { $gt: 0 } }).count();
  // const countLogs = await PaymentLog.find({ uid: userId, upback: true, amount: { $gt: 0 }, pptype: { $in: [paymentTypes.braintree, paymentTypes.nftgateEpik, paymentTypes.nftgatePaypal, paymentTypes.transakNft, paymentTypes.mercuryo, paymentTypes.moongate, paymentTypes.nftpay, paymentTypes.paper, paymentTypes.ramper, paymentTypes.winter, paymentTypes.wynpay, paymentTypes.cometh, paymentTypes.bitstore] } }).count();

  if (countLogs === 0 && speedServer) {
    await UserConfig.findOneAndUpdate(
      { uid: userId },
      { $set: { speed: speedServer } },
      { upsert: true, new: false },
    ).exec();
  }

  // return countLogs > 1; // paid
  return countLogs === 0; // paid
  // return countLogs < 2; // less 2
  // return true;
}

const runCheckUser = async (userId, speedServer) => {
  try {
    const result = await checkPayments(userId, speedServer);

    if (result) newUsers.push(userId);
  } catch (e) {
    console.log(e);
  }
};

const run = async () => {
  const permissionGroup = await UserPermissionGroup.findOne({ name: 'Unactive users' }).lean().exec();
  const userIds = await User.find({ permissionGroups: [permissionGroup._id.toString()] }, { id: 1 }).lean();
  let promises = [];

  const paymentBlacklistedGeneralConfig = await getPaymentBlacklistGeneralConfig();
  const blacklistedStreamingServersIds = paymentBlacklistedGeneralConfig.generalConfig.streamingServers || [];
  let speedServer;

  if (blacklistedStreamingServersIds && blacklistedStreamingServersIds.length) {
    speedServer = blacklistedStreamingServersIds[0];
  }

  for (let i = 0; i < userIds.length - 1; ++i) {
    promises.push(runCheckUser(userIds[i].id, speedServer));

    if (promises.length % 50 === 0) {
      await Promise.all(promises);
      promises = [];
    }
  }

  await Promise.all(promises);

  console.log('new users:', newUsers);

  return true;
};

run().then((result) => {
  console.log(`DONE: ${result}`);
  process.exit(0);
}).catch((err) => {
  console.log(err);
  process.exit(1);
});
