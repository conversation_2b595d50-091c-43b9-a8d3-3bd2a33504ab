const moment = require('moment');
const User = require('../rest/model/audb/User');
const PaymentLog = require('../rest/model/audb/PaymentLog');
const getPaymentBlacklistGeneralConfig = require('../rest/commands/paymentBlacklist/getGeneralConfig');
const UserConfig = require('../rest/model/audb/UserConfig');

const newUsers = [];

async function checkPayments(userId, speedServer) {
  const countLogs = await PaymentLog.find({ uid: userId, upback: true, amount: { $gt: 0 } }).count();

  if (countLogs === 0 && speedServer) {
    await UserConfig.findOneAndUpdate(
      { uid: userId },
      { $set: { speed: speedServer } },
      { upsert: true, new: false },
    ).exec();
  }

  // return countLogs > 1; // paid
  return countLogs === 0; // paid
  // return countLogs < 2; // less 2
}

const runCheckUser = async (userId, speedServer) => {
  try {
    const result = await checkPayments(userId, speedServer);

    if (result) newUsers.push(userId);
  } catch (e) {
    console.log(e);
  }
};

const run = async () => {
  const dateFrom = moment().subtract(1, 'month').unix();
  const userIds = await User.find({ regtime: { $gte: dateFrom } }, { id: 1 }).lean();
  let promises = [];

  const paymentBlacklistedGeneralConfig = await getPaymentBlacklistGeneralConfig();
  const blacklistedStreamingServersIds = paymentBlacklistedGeneralConfig.generalConfig.streamingServers || [];
  let speedServer;

  if (blacklistedStreamingServersIds && blacklistedStreamingServersIds.length) {
    speedServer = blacklistedStreamingServersIds[0];
  }

  for (let i = 0; i < userIds.length - 1; ++i) {
    promises.push(runCheckUser(userIds[i].id, speedServer));

    if (promises.length % 50 === 0) {
      await Promise.all(promises);
      promises = [];
    }
  }

  await Promise.all(promises);

  console.log('new users:', newUsers);

  return true;
};

run().then((result) => {
  console.log(`DONE: ${result}`);
  process.exit(0);
}).catch((err) => {
  console.log(err);
  process.exit(1);
});
