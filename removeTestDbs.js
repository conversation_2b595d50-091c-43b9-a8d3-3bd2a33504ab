// eslint-disable-next-line import/no-extraneous-dependencies
const MongoClient = require('mongodb').MongoClient;
const config = require('./config');

const checkTestDB = connection => connection && connection.name && connection.name.includes(config.testDBPrefix);

const dropDatabases = async () => {
  const startTime = Date.now();
  // Connect to MongoDB
  const conn = await MongoClient.connect(config.testDBUrl, { useNewUrlParser: true });
  const db = await conn.db('mydb');
  const response = await db.executeDbAdminCommand({ listDatabases: 1 });
  // filter tests databases
  const testDbs = response.databases.filter(checkTestDB);
  // remove tests databases
  for (let i = 0; i < testDbs.length; ++i) {
    await conn.db(testDbs[i].name).dropDatabase();
  }
  console.log('Dropped DBs:', testDbs.length);
  console.log('Dropped DBs Time:', (Date.now() - startTime) / 1000, 's');

  return conn.close();
};

const run = async () => {
  await dropDatabases();
};
run().then(() => {
  console.log('Drop DBs Done');
  process.exit(0);
}).catch(console.log);
