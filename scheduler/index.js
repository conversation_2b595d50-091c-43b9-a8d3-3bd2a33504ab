const io = require('socket.io-client');

require('@s1/log').level('debug');
const log = require('@s1/log').create(__filename);

require('@s1/log').level(require('../config').loglevel);

const schedule = require('node-schedule');
const config = require('../config');
const maxmindConf = require('../config').maxmind;
const checkLogsAboutProxies = require('./jobs/checkLogsAboutProxies');
const resetStealersJob = require('./jobs/resetStealers');
const collectPopularData = require('./jobs/collectPopularData');
const copySchedulesToDev = require('./jobs/copySchedulesToDev');
const removeOldPopularData = require('./jobs/removeOldPopularData');
const computeSchedulesYear = require('./jobs/computeSchedulesYear');
const computeStatisticOfCategories = require('./jobs/computeStatisticOfCategories');
const updateProxyDB = require('./jobs/updateProxyDB');
// const removeSchedule = require('./jobs/removeSchedule');
const removeOldUsersLogs = require('./jobs/removeOldUsersLogs');
const reindexOldUsersLogs = require('./jobs/reindexOldUsersLogs');
const removeDeletedVODsFromUsersLogsFav = require('./jobs/removeDeletedVODsFromUsersLogsFav');
const sendPaymentReminders = require('./jobs/sendPaymentReminders');
const checkStreamingServersTrafficAndLoadBalance = require('./jobs/checkStreamingServersTrafficAndLoadBalance');
const populateUsersStat = require('./jobs/populateUsersStat');
const checkAccountsProviderBalance = require('./jobs/checkAccountsProviderBalance');
const checkPaymentTypesApiState = require('./jobs/checkPaymentTypesApiState');
const reindexDbsDaily = require('./jobs/reindexDbsDaily');
const reindexDbsWeekly = require('./jobs/reindexDbsWeekly');
const restartEmployeesDaytimeWorkActivities = require('./jobs/restartEmployeesDaytimeWorkActivities');
const resetPaymentTypesDailyPaidAmounts = require('./jobs/resetPaymentTypesDailyPaidAmounts');
const checkEmployeesDailyWorkActivitiesLimits = require('./jobs/checkEmployeesDailyWorkActivitiesLimits');
const resetMaxmindCache = require('../rest/commands/maxmind/reset');
const removeOldCaptcha = require('../rest/commands/captcha/removeOld');
// const collectDailyPopularStats = require('../rest/commands/popular/collectDailyStats');
const { SocketClientNames } = require('../rest/constants/socket');
const updateModelViews = require('./jobs/updateModelViews');
const checkUsersSuspectedWatchActivities = require('./jobs/checkUsersSuspectedWatchActivities');
const collectDailyUsersWatchActivitiesStats = require('./jobs/collectDailyUsersWatchActivitiesStats');
const removeRuStreamingServerFromUsersConfigs = require('./jobs/removeRuStreamingServerFromUsersConfigs');

global.io = io(config.socket.host);
global.io.on('connect', () => {
  global.io.emit('joinGroup', SocketClientNames.s1Api);

  if (process.env.NODE_ENV !== 'production') console.log('Socket connected:', global.io.id); // x8WIv7-mJelg7on_ALbx
});
global.io.on('reconnect', () => {
  global.io.emit('joinGroup', SocketClientNames.s1Api);

  if (process.env.NODE_ENV !== 'production') console.log('Socket re-connected:', global.io.id); // x8WIv7-mJelg7on_ALbx
});
global.io.on('disconnect', () => {
  if (process.env.NODE_ENV !== 'production') console.log('Socket disconnected:', global.io.id); // undefined
});

(async () => {
  // format second(OPTIONAL)|minute|hour|day of month|month|day of week
  log.warn('Started S1-API scheduler');
  schedule.scheduleJob('0 2,3 * * *', resetPaymentTypesDailyPaidAmounts); // At 02h by GTM+2 and 03h by GTM+3 or UTC+0
  schedule.scheduleJob('0 2,3 * * *', restartEmployeesDaytimeWorkActivities); // At 02h by GTM+2 and 03h by GTM+3 or UTC+0
  schedule.scheduleJob('*/15 * ? * *', computeStatisticOfCategories);
  schedule.scheduleJob('0 4 * * *', collectDailyUsersWatchActivitiesStats);
  schedule.scheduleJob('0 6 * * *', reindexDbsDaily);
  schedule.scheduleJob('5 6 * * 1', reindexDbsWeekly);
  schedule.scheduleJob('40 6-12 * * *', copySchedulesToDev); // At minute 40 past every hour from 6 through 12
  schedule.scheduleJob('44 */4 * * *', resetStealersJob);
  schedule.scheduleJob('22 * * * *', computeSchedulesYear);
  schedule.scheduleJob('0 7 * * *', removeRuStreamingServerFromUsersConfigs);
  schedule.scheduleJob('40 7 * * *', checkLogsAboutProxies);
  schedule.scheduleJob('0 8 * * 1', updateProxyDB);
  schedule.scheduleJob('20 8 * /3 *', reindexOldUsersLogs);
  // schedule.scheduleJob('0 10 * * *', removeSchedule);
  schedule.scheduleJob('55 11 * * *', collectPopularData);
  schedule.scheduleJob('50 12 * * *', removeDeletedVODsFromUsersLogsFav);
  schedule.scheduleJob('55 12 * * *', removeOldPopularData);
  schedule.scheduleJob('0 13 * * *', removeOldUsersLogs);
  schedule.scheduleJob('0 0 * * *', sendPaymentReminders);
  schedule.scheduleJob('10 */8 * * *', populateUsersStat);
  schedule.scheduleJob(maxmindConf.resetCronInterval, resetMaxmindCache);
  // schedule.scheduleJob('0 0 * * *', collectDailyPopularStats);
  schedule.scheduleJob('*/10 * * * *', removeOldCaptcha);
  schedule.scheduleJob('*/30 * * * *', checkStreamingServersTrafficAndLoadBalance);
  schedule.scheduleJob('*/30 * * * *', checkAccountsProviderBalance);
  schedule.scheduleJob('*/3 * * * *', checkPaymentTypesApiState);
  schedule.scheduleJob('*/3 * * * *', checkEmployeesDailyWorkActivitiesLimits);
  schedule.scheduleJob('*/10 * * * *', updateModelViews);
  // Schedule for each 3d hour to check users watch activities
  schedule.scheduleJob('0 */3 * * *', checkUsersSuspectedWatchActivities);
})();
