const config = require('../../config');
const Suspended = require('../../rest/model/audb/Suspended');
const User = require('../../rest/model/audb/User');
const isBlacklistedUser = require('../../rest/commands/payment/helpers/rules/isBlacklistedUser');

const defaultOptions = {
  trialPackage: 1,
  batchSize: 10,        // Process users in batches
  parallelBatches: 1,    // Process multiple batches in parallel
  days: 30,
  minDuplicates: 1,      // Minimum duplicates to consider blocking
  maxUsersPerRun: 10000, // Limit total users processed per run
};

// eslint-disable-next-line import/order
const log = require('@s1/log').create(__filename);

// Consolidated log models for better performance
const logModels = [
  { model: require('../../rest/model/audb/UserLogLogin'), collection: 'userloglogin' },
  { model: require('../../rest/model/audb/UserLogLive'), collection: 'userloglive' },
  { model: require('../../rest/model/audb/UserLogRecord'), collection: 'userlogrecord' },
  { model: require('../../rest/model/audb/UserLogVod'), collection: 'userlogvod' },
];

/**
 * Optimized method to find duplicate users using aggregation
 */
async function findDuplicateUsers(userIds, days) {
  const cutoffTime = Math.floor(Date.now() / 1000) - (days * 3600 * 24);
  const duplicateGroups = new Map();

  // Process all log collections in parallel
  const logPromises = logModels.map(async ({ model, collection }) => {
    try {
      // Use aggregation to find duplicates efficiently
      const duplicates = await model.aggregate([
        {
          $match: {
            uid: { $in: userIds },
            playtime: { $gt: cutoffTime },
            // eslint-disable-next-line no-dupe-keys
            fingerprint: { $exists: true, $ne: null, $ne: '' },
            // eslint-disable-next-line no-dupe-keys
            ip: { $exists: true, $ne: null, $ne: '' }
          }
        },
        {
          $group: {
            _id: {
              fingerprint: "$fingerprint",
              ip: "$ip"
            },
            uids: { $addToSet: "$uid" },
            count: { $sum: 1 }
          }
        },
        {
          $match: {
            "uids.1": { $exists: true } // At least 2 different UIDs
          }
        },
        {
          $project: {
            _id: 0,
            fingerprint: "$_id.fingerprint",
            ip: "$_id.ip",
            uids: 1,
            count: 1
          }
        }
      ]).allowDiskUse(true);

      return duplicates;
    } catch (err) {
      log.error(`Error processing ${collection}:`, err);

      return [];
    }
  });

  const allDuplicates = await Promise.all(logPromises);

  // Merge results from all log collections
  [].concat(...allDuplicates).forEach(group => {
    const key = `${group.fingerprint}_${group.ip}`;

    if (duplicateGroups.has(key)) {
      // Merge UIDs from different log types
      const existing = duplicateGroups.get(key);
      group.uids.forEach(uid => existing.add(uid));
    } else {
      duplicateGroups.set(key, new Set(group.uids));
    }
  });

  return duplicateGroups;
}

/**
 * Check if any UID in the group is a paid user
 */
async function hasAnyPaidUser(uids) {
  const paidCount = await User.countDocuments({
    id: { $in: uids },
    $or: [
      { package: { $ne: defaultOptions.trialPackage } },
      { 'config.skipTrialCheck': true }
    ]
  });

  return paidCount > 0;
}

/**
 * Process a batch of users for stealer detection
 */
async function processBatch(users, suspendedUserIds) {
  const userIds = users.map(u => u.id);
  let blockedCount = 0;

  try {
    // Find all duplicate groups for this batch
    const duplicateGroups = await findDuplicateUsers(userIds, defaultOptions.days);

    if (duplicateGroups.size === 0) {
      return blockedCount;
    }

    // Process each duplicate group
    for (const [key, uidsSet] of duplicateGroups) {
      const uids = Array.from(uidsSet);

      // Skip if not enough duplicates
      if (uids.length <= defaultOptions.minDuplicates) continue;
      // Skip if any user is suspended (already handled)
      if (uids.some(uid => suspendedUserIds.has(uid))) continue;
      // Skip if any user is paid
      if (await hasAnyPaidUser(uids)) {
        log.info(`Skipping group ${key}: contains paid user(s) among ${JSON.stringify(uids)}`);
        continue;
      }

      // Block all users in this group
      log.warn(`Blocking group ${key}: ${uids.length} accounts ${JSON.stringify(uids)}`);

      const usersToBlock = await User.find({ id: { $in: uids } }).exec();
      const blockPromises = usersToBlock.map(async (user) => {
        try {
          const previousEmails = usersToBlock.map(u => u.email).join(', ');

          // Set expiry to 0 (block user)
          user.expires = 0;

          // Add admin comment
          user.addAdminComment(`user has registered ${uids.length} accounts ${JSON.stringify(uids)} during ${defaultOptions.days} days, he haven't paid account and is reset to be expired at 1970`);

          // Send email if not blacklisted
          const isUserBlacklisted = await isBlacklistedUser({ user });

          if (!isUserBlacklisted) {
            user.mail('already_registered', config.email.noReply, {}, {
              previous_email: previousEmails
            });
          }

          await user.save();

          return true;
        } catch (err) {
          log.error(`Error blocking user ${user.id}:`, err);

          return false;
        }
      });

      const results = await Promise.all(blockPromises);
      blockedCount += results.filter(Boolean).length;
    }

  } catch (err) {
    log.error('Error processing batch:', err);
  }

  return blockedCount;
}

/**
 * Main optimized stealer reset function
 */
module.exports = async () => {
  const startTime = Date.now();
  log.info('Starting optimized stealer detection...');

  try {
    // Get suspended users once
    const suspendedUsers = await Suspended.find({}, { uid: 1, _id: 0 }).lean();
    const suspendedUserIds = new Set(suspendedUsers.map(s => s.uid));

    // Get trial users in batches
    const totalUsers = await User.countDocuments({
      package: defaultOptions.trialPackage,
      expires: { $gt: Math.floor(Date.now() / 1000) },
    });

    log.info(`Found ${totalUsers} active trial users to check`);

    if (totalUsers === 0) {
      log.info('No trial users found');

      return;
    }

    // Limit processing if too many users
    const usersToProcess = Math.min(totalUsers, defaultOptions.maxUsersPerRun);
    const totalBatches = Math.ceil(usersToProcess / defaultOptions.batchSize);

    log.info(`Processing ${usersToProcess} users in ${totalBatches} batches`);

    let totalBlocked = 0;
    let processedUsers = 0;

    // Process batches in parallel groups
    for (let batchStart = 0; batchStart < usersToProcess; batchStart += defaultOptions.batchSize * defaultOptions.parallelBatches) {
      const batchPromises = [];

      // Create parallel batch promises
      for (let i = 0; i < defaultOptions.parallelBatches; i++) {
        const currentBatchStart = batchStart + (i * defaultOptions.batchSize);

        if (currentBatchStart >= usersToProcess) break;

        const batchPromise = User.find({
          package: defaultOptions.trialPackage,
          expires: { $gt: Math.floor(Date.now() / 1000) },
        })
          .select('id email expires config')
          .skip(currentBatchStart)
          .limit(defaultOptions.batchSize)
          .lean()
          .exec()
          .then(users => {
            if (users.length === 0) return 0;

            return processBatch(users, suspendedUserIds);
          });

        batchPromises.push(batchPromise);
      }

      // Wait for all parallel batches to complete
      const batchResults = await Promise.all(batchPromises);
      const batchBlocked = batchResults.reduce((sum, count) => sum + count, 0);

      totalBlocked += batchBlocked;
      processedUsers += batchPromises.length * defaultOptions.batchSize;

      const progress = Math.min(100, (processedUsers / usersToProcess * 100).toFixed(1));
      log.info(`Progress: ${progress}% (${processedUsers}/${usersToProcess}) - Blocked: ${batchBlocked} in this batch`);
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);

    if (totalBlocked > 0) {
      log.warn(`✅ Completed in ${duration}s. Checked ${totalUsers} users. Blocked ${totalBlocked} stealers.`);
    } else {
      log.info(`✅ Completed in ${duration}s. Checked ${totalUsers} users. No stealers found.`);
    }

  } catch (err) {
    log.error('Fatal error in stealer detection:', err);

    throw err;
  }
};
