const config = require('../../config');
const Suspended = require('../../rest/model/audb/Suspended');
const User = require('../../rest/model/audb/User');
const isBlacklistedUser = require('../../rest/commands/payment/helpers/rules/isBlacklistedUser');

const defaultOptions = {
  trialPackage: 1,
  batchSize: 10,         // Process users in smaller batches
  parallelBatches: 1,    // Process multiple batches in parallel
  depth: 1,              // Same as original
  days: 30,
  uidsMoreThan: 0,       // Same as original - minimum duplicates to consider blocking
  maxUsersPerRun: 5000,  // Limit total users processed per run
};

const log = require('@s1/log').create(__filename);

// Log models - same as original
const logs = [
  require('../../rest/model/audb/UserLogLogin'),
  require('../../rest/model/audb/UserLogLive'),
  require('../../rest/model/audb/UserLogRecord'),
  require('../../rest/model/audb/UserLogVod'),
];

/**
 * Optimized version of reduceLogsByComparing for a single user
 * This replicates the original logic but with better performance
 */
async function findDuplicateUsersForUser(user, days, depth, excludeUserIds) {
  const queryCopy = { playtime: { $gt: Math.floor(Date.now() / 1000) - (days * 3600 * 24) } };
  let filterIds = [user.id];
  const ids = new Set();
  const compareFields = { fingerprint: 1, ip: 1 };
  const fields = Object.keys(compareFields);

  let currentDepth = depth;
  while (filterIds.length && currentDepth > 0) {
    // Process all log models in parallel for current filterIds
    const logPromises = logs.map(async (model) => {
      try {
        // Get records for current filterIds
        const records = await model.find(
          { ...queryCopy, uid: { $in: filterIds } },
          compareFields
        ).lean().exec();

        if (records.length === 0) return [];

        // Search for other users with same fingerprint OR ip values
        const searchPromises = [];
        const perSetIds = [];

        fields.forEach((field) => {
          const values = new Set();
          records.forEach((record) => {
            if (!record[field] || record[field] === 'undefined' || record[field] === '') return;

            values.add(record[field]);
          });

          if (values.size > 0) {
            searchPromises.push(
              model.find(
                {
                  ...queryCopy,
                  [field]: { $in: [...values] },
                  uid: { $nin: [...filterIds, ...ids, ...excludeUserIds] },
                },
                { uid: 1 }
              ).lean().exec()
            );
          } else {
            searchPromises.push(Promise.resolve([]));
          }
        });

        const resultSet = await Promise.all(searchPromises);
        resultSet.forEach((results) => {
          const currentSetIds = new Set();
          perSetIds.push(currentSetIds);
          results.forEach(({ uid }) => {
            if (!uid || ids.has(uid)) return;

            currentSetIds.add(uid);
          });
        });

        // Find intersection - users that match ALL fields (fingerprint AND ip)
        if (perSetIds.length > 0) {
          return [...perSetIds[0]].filter((id) =>
            perSetIds.reduce((counter, set) => counter + (set.has(id) ? 1 : 0), 0) === perSetIds.length
          );
        }

        return [];
      } catch (err) {
        log.error(`Error processing model ${model.collection.collectionName}:`, err);
        
        return [];
      }
    });

    const allResults = await Promise.all(logPromises);
    const newFilterIds = new Set();

    // Merge results from all log models
    allResults.flat().forEach(uid => {
      if (uid && !ids.has(uid)) {
        newFilterIds.add(uid);
      }
    });

    filterIds = [...newFilterIds];
    filterIds.forEach(id => ids.add(id));
    currentDepth--;
  }

  return [...ids];
}

/**
 * Process a batch of users for stealer detection using the original logic
 */
async function processBatch(users, paidUserIds, suspendedUserIds) {
  let blockedCount = 0;
  const processedUserIds = new Set();

  try {
    // Process each user individually (like original)
    const userPromises = users.map(async (user) => {
      try {
        // Skip if already processed or suspended
        if (processedUserIds.has(user.id) || suspendedUserIds.has(user.id)) {
          return 0;
        }

        // Find duplicate users for this specific user
        const uids = await findDuplicateUsersForUser(
          user,
          defaultOptions.days,
          defaultOptions.depth,
          [...processedUserIds, ...suspendedUserIds]
        );

        if (uids.length <= defaultOptions.uidsMoreThan) {
          return 0;
        }
        // Check if any of the found users is paid
        if (uids.some(uid => paidUserIds.has(uid))) {
          log.warn(`one of ${user.id} accounts ${JSON.stringify(uids)} is paid user, skipped`);

          return 0;
        }

        // Block the original user and all duplicates
        const allUsersToBlock = [user.id, ...uids];
        log.warn(`blocking ${user.id} for having ${uids.length} not-paid accounts: ${JSON.stringify(uids)}`);

        const usersToBlock = await User.find({ id: { $in: allUsersToBlock } }).exec();
        const previousEmails = usersToBlock.map(u => u.email).join(', ');

        // Block all users in parallel
        const blockPromises = usersToBlock.map(async (userToBlock) => {
          try {
            userToBlock.expires = 0;
            const isUserBlacklisted = await isBlacklistedUser({ user: userToBlock });

            if (!isUserBlacklisted) {
              userToBlock.mail('already_registered', config.email.noReply, {}, { previous_email: previousEmails });
            }

            userToBlock.addAdminComment(
              `user has registered ${allUsersToBlock.length} accounts ${JSON.stringify(allUsersToBlock)} during ${defaultOptions.days} days, he haven't paid account and is reset to be expired at 1970`
            );

            await userToBlock.save();

            // Mark as processed to avoid duplicate processing
            processedUserIds.add(userToBlock.id);

            return true;
          } catch (err) {
            log.error(`Error blocking user ${userToBlock.id}:`, err);

            return false;
          }
        });

        const results = await Promise.all(blockPromises);

        return results.filter(Boolean).length;
      } catch (err) {
        log.error(`Error processing user ${user.id}:`, err);

        return 0;
      }
    });

    // Process users with limited concurrency to avoid overwhelming the database
    const batchSize = 5; // Process 5 users concurrently
    for (let i = 0; i < userPromises.length; i += batchSize) {
      const batch = userPromises.slice(i, i + batchSize);
      const results = await Promise.all(batch);
      blockedCount += results.reduce((sum, count) => sum + count, 0);
    }

  } catch (err) {
    log.error('Error processing batch:', err);
  }

  return blockedCount;
}

/**
 * Main optimized stealer reset function
 */
module.exports = async () => {
  const startTime = Date.now();
  log.warn('Starting stealer detection...');

  try {
    // Get paid users (same as original)
    log.warn('Getting paid users (without users with config.skipTrialCheck === true)');
    const paidUsers = await User.getPaidUserIds(true);
    const suspendedUsers = await Suspended.find({}, { uid: 1, _id: 0 }).lean();
    const suspendedUsersIds = suspendedUsers.map(suspend => suspend.uid);
    const paidUserIds = new Set(paidUsers.filter(uid => !suspendedUsersIds.includes(uid)));
    const suspendedUserIds = new Set(suspendedUsersIds);

    log.warn(`Checking trial users for accounts which used same fingerprint AND ip during last ${defaultOptions.days} days`);

    // Get trial users count
    const totalUsers = await User.countDocuments({
      package: defaultOptions.trialPackage,
      expires: { $gt: Math.floor(Date.now() / 1000) },
    });

    log.info(`Found ${totalUsers} active trial users to check`);

    if (totalUsers === 0) {
      log.warn('no trial users found');

      return;
    }

    // Limit processing if too many users
    const usersToProcess = Math.min(totalUsers, defaultOptions.maxUsersPerRun);
    const totalBatches = Math.ceil(usersToProcess / defaultOptions.batchSize);

    log.info(`Processing ${usersToProcess} users in ${totalBatches} batches`);

    let totalBlocked = 0;
    let usersChecked = 0;

    // Process batches with limited parallelism
    for (let batchStart = 0; batchStart < usersToProcess; batchStart += defaultOptions.batchSize * defaultOptions.parallelBatches) {
      const batchPromises = [];

      // Create parallel batch promises
      for (let i = 0; i < defaultOptions.parallelBatches; i++) {
        const currentBatchStart = batchStart + (i * defaultOptions.batchSize);

        if (currentBatchStart >= usersToProcess) break;

        const batchPromise = User.find({
          package: defaultOptions.trialPackage,
          expires: { $gt: Math.floor(Date.now() / 1000) },
        })
          .select('id email expires config')
          .skip(currentBatchStart)
          .limit(defaultOptions.batchSize)
          .exec() // Don't use .lean() because we need full User objects for methods
          .then(users => {
            if (users.length === 0) return { blocked: 0, checked: 0 };

            return processBatch(users, paidUserIds, suspendedUserIds).then(blocked => ({
              blocked,
              checked: users.length
            }));
          });

        batchPromises.push(batchPromise);
      }

      // Wait for all parallel batches to complete
      const batchResults = await Promise.all(batchPromises);
      const batchBlocked = batchResults.reduce((sum, result) => sum + result.blocked, 0);
      const batchChecked = batchResults.reduce((sum, result) => sum + result.checked, 0);

      totalBlocked += batchBlocked;
      usersChecked += batchChecked;

      const progress = Math.min(100, (usersChecked / usersToProcess * 100).toFixed(1));
      log.info(`Progress: ${progress}% (${usersChecked}/${usersToProcess}) - Blocked: ${batchBlocked} in this batch`);
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);

    if (totalBlocked > 0) {
      log.warn(`✅ Completed in ${duration}s. Checked ${usersChecked} trial users. Blocked ${totalBlocked} stealers.`);
    } else {
      log.warn(`✅ Completed in ${duration}s. Checked ${usersChecked} trial users. No new stealers found.`);
    }

  } catch (err) {
    log.error('Fatal error in stealer detection:', err);

    throw err;
  }
};
