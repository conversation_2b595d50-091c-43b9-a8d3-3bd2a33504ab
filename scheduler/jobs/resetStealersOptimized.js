const config = require('../../config');
const Suspended = require('../../rest/model/audb/Suspended');
const User = require('../../rest/model/audb/User');
const PaymentLog = require('../../rest/model/audb/PaymentLog');
const isBlacklistedUser = require('../../rest/commands/payment/helpers/rules/isBlacklistedUser');

const defaultOptions = {
  trialPackage: 1,
  batchSize: 10,         // Process users in smaller batches
  parallelBatches: 1,    // Process multiple batches in parallel
  depth: 1,              // Same as original
  days: 30,
  uidsMoreThan: 0,       // Same as original - minimum duplicates to consider blocking
  maxUsersPerRun: 5000,  // Limit total users processed per run
};

// eslint-disable-next-line import/order
const log = require('@s1/log').create(__filename);

// Log models - same as original
const logs = [
  require('../../rest/model/audb/UserLogLogin'),
  require('../../rest/model/audb/UserLogLive'),
  require('../../rest/model/audb/UserLogRecord'),
  require('../../rest/model/audb/UserLogVod'),
];

/**
 * Optimized version of reduceLogsByComparing for a single user
 * This replicates the original logic but with better performance
 */
async function findDuplicateUsersForUser(user, days, depth, excludeUserIds = [], compareFields = ['fingerprint', 'ip']) {
  const queryCopy = {
    playtime: {
      $gt: Math.floor(Date.now() / 1000) - (days * 24 * 3600)
    }
  };

  let filterIds = [user.id];
  const ids = new Set();
  const currentDepth = depth;

  const requiredFieldFilters = compareFields.map(field => ({
    [field]: {
      $exists: true,
      $nin: [null, '', 'undefined']
    }
  }));
  const fieldFilterObject = requiredFieldFilters.reduce((acc, cond) => Object.assign(acc, cond), {});
  const groupId = compareFields.reduce((acc, field) => {
    acc[field] = `$${field}`;

    return acc;
  }, {});
  const projectFields = compareFields.reduce((acc, field) => {
    acc[field] = `$_id.${field}`;

    return acc;
  }, { _id: 0 });

  for (let i = 0; i < currentDepth && filterIds.length > 0; i++) {
    // eslint-disable-next-line no-loop-func
    const logPromises = logs.map(async (model) => {
      try {
        const records = await model.aggregate([
          {
            $match: {
              ...queryCopy,
              uid: { $in: filterIds },
              ...fieldFilterObject,
            }
          },
          {
            $group: {
              _id: groupId
            }
          },
          {
            $project: projectFields
          }
        ]).exec();

        if (records.length === 0) return [];

        const matchConditions = records.filter((record) =>
          compareFields.every((field) =>
            record[field] && record[field] !== 'undefined' && record[field] !== ''
          )
        );

        if (matchConditions.length === 0) return [];

        const results = await model.aggregate([
          {
            $match: {
              ...queryCopy,
              $or: matchConditions,
              uid: { $nin: [...filterIds, ...ids, ...excludeUserIds] }
            }
          },
          {
            $group: {
              _id: '$uid'
            }
          },
          {
            $project: {
              _id: 0,
              uid: '$_id'
            }
          }
        ]).exec();

        return results.map(r => r.uid);
      } catch (err) {
        console.error(`Error processing model ${model.collection.collectionName}:`, err);

        return [];
      }
    });

    const allResults = await Promise.all(logPromises);
    const newFilterIds = new Set();

    allResults.forEach(results => {
      results.forEach(uid => {
        if (uid && !ids.has(uid)) {
          newFilterIds.add(uid);
          ids.add(uid);
        }
      });
    });

    filterIds = [...newFilterIds];
  }

  return [...ids];
}

/**
 * Check if user is suspended (on-the-fly check)
 */
async function isUserSuspended(uid) {
  try {
    const suspended = await Suspended.findOne({ uid }, { uid: 1 }).lean();

    return !!suspended;
  } catch (err) {
    log.error(`Error checking suspended status for user ${uid}:`, err);

    return false;
  }
}

const getPaidAggregationForUsers = async (userIdsToCheck = [], useSkipTrial = false) => {
  if (!userIdsToCheck.length) return false;

  const currentTime = Math.floor(Date.now() / 1000);

  const initialMatch = {
    $match: {
      upback: true,
      created: { $gte: currentTime - 3600 * 24 * 180 }, // max package 180 днів
      ...(userIdsToCheck.length > 0 ? { uid: { $in: userIdsToCheck } } : {}),
    },
  };

  const finalMatch = {
    till: { $gte: currentTime },
    suspended: { $ne: true },
  };

  if (useSkipTrial) finalMatch.skipTrialCheck = { $ne: true };

  const plan = [
    initialMatch,
    {
      $lookup: {
        from: 'package',
        localField: 'package',
        foreignField: 'id',
        as: 'Package',
      },
    },
    {
      $unwind: '$Package',
    },
    {
      $lookup: {
        from: 'suspended',
        localField: 'uid',
        foreignField: 'uid',
        as: 'Suspended',
      },
    },
    {
      $unwind: {
        path: '$Suspended',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        till: { $add: ['$created', '$Package.length'] },
        tkey: 1,
        amount: 1,
        uid: 1,
        suspended: { $ifNull: ['$Suspended.suspended', false] },
        skipTrialCheck: { $ifNull: ['$Suspended.skipTrialCheck', false] },
      },
    },
    {
      $match: finalMatch,
    },
    {
      $group: {
        _id: null,
        count: { $sum: 1 },
      },
    }
  ];

  const result = await PaymentLog.aggregate(plan);

  return !!(result.length && result[0].count && result[0].count > 0);
}

/**
 * Process a batch of users for stealer detection using the original logic
 */
async function processBatch(users) {
  let blockedCount = 0;
  const processedUserIds = new Set();

  try {
    // Process each user individually (like original)
    const userPromises = users.map(async (user) => {
      try {
        // Skip if already processed
        if (processedUserIds.has(user.id)) {
          return 0;
        }
        // Check if current user is suspended (on-the-fly)
        if (await isUserSuspended(user.id)) {
          processedUserIds.add(user.id);

          return 0;
        }

        // Find duplicate users for this specific user
        const uids = await findDuplicateUsersForUser(
          user,
          defaultOptions.days,
          defaultOptions.depth,
          [...processedUserIds]
        );

        if (uids.length <= defaultOptions.uidsMoreThan) {
          return 0;
        }

        // Check if any of the found users is paid or suspended (on-the-fly)
        const allUsersToCheck = [user.id, ...uids];
        const hasPaidUser = await getPaidAggregationForUsers(allUsersToCheck);

        if (hasPaidUser) {
          log.warn(`one of ${user.id} accounts ${JSON.stringify(uids)} is paid user, skipped`);

          return 0;
        }

        // Block the original user and all duplicates
        const allUsersToBlock = [user.id, ...uids];
        log.warn(`blocking ${user.id} for having ${uids.length} not-paid accounts: ${JSON.stringify(uids)}`);

        const usersToBlock = await User.find({ id: { $in: allUsersToBlock } }).exec();
        const previousEmails = usersToBlock.map(u => u.email).join(', ');

        // Block all users in parallel
        const blockPromises = usersToBlock.map(async (userToBlock) => {
          try {
            userToBlock.expires = 0;

            const isUserBlacklisted = await isBlacklistedUser({ user: userToBlock });

            if (!isUserBlacklisted) {
              userToBlock.mail('already_registered', config.email.noReply, {}, { previous_email: previousEmails });
            }

            userToBlock.addAdminComment(
              `user has registered ${allUsersToBlock.length} accounts ${JSON.stringify(allUsersToBlock)} during ${defaultOptions.days} days, he haven't paid account and is reset to be expired at 1970`
            );

            await userToBlock.save();

            // Mark as processed to avoid duplicate processing
            processedUserIds.add(userToBlock.id);

            return true;
          } catch (err) {
            log.error(`Error blocking user ${userToBlock.id}:`, err);

            return false;
          }
        });

        const results = await Promise.all(blockPromises);

        return results.filter(Boolean).length;

      } catch (err) {
        log.error(`Error processing user ${user.id}:`, err);

        return 0;
      }
    });

    // Process users with limited concurrency to avoid overwhelming the database
    const concurrencyLimit = 3; // Process 3 users concurrently
    for (let i = 0; i < userPromises.length; i += concurrencyLimit) {
      const batch = userPromises.slice(i, i + concurrencyLimit);
      const results = await Promise.all(batch);
      blockedCount += results.reduce((sum, count) => sum + count, 0);
    }

  } catch (err) {
    log.error('Error processing batch:', err);
  }

  return blockedCount;
}

/**
 * Main optimized stealer reset function
 */
module.exports = async () => {
  const startTime = Date.now();
  log.warn('Starting stealer detection...');

  try {
    log.warn(`Checking trial users for accounts which used same fingerprint AND ip during last ${defaultOptions.days} days`);
    log.info('Using on-the-fly checks for paid/suspended users to save memory');

    // Get trial users count
    const totalUsers = await User.countDocuments({
      package: defaultOptions.trialPackage,
      expires: { $gt: Math.floor(Date.now() / 1000) },
    });

    log.info(`Found ${totalUsers} active trial users to check`);

    if (totalUsers === 0) {
      log.warn('no trial users found');

      return;
    }

    // Limit processing if too many users
    const usersToProcess = Math.min(totalUsers, defaultOptions.maxUsersPerRun);
    const totalBatches = Math.ceil(usersToProcess / defaultOptions.batchSize);

    log.info(`Processing ${usersToProcess} users in ${totalBatches} batches`);

    let totalBlocked = 0;
    let usersChecked = 0;

    // Process batches with limited parallelism
    for (let batchStart = 0; batchStart < usersToProcess; batchStart += defaultOptions.batchSize * defaultOptions.parallelBatches) {
      const batchPromises = [];

      // Create parallel batch promises
      for (let i = 0; i < defaultOptions.parallelBatches; i++) {
        const currentBatchStart = batchStart + (i * defaultOptions.batchSize);

        if (currentBatchStart >= usersToProcess) break;

        const batchPromise = User.find({
          package: defaultOptions.trialPackage,
          expires: { $gt: Math.floor(Date.now() / 1000) },
        })
          .select('id email expires config')
          .skip(currentBatchStart)
          .limit(defaultOptions.batchSize)
          .exec() // Don't use .lean() because we need full User objects for methods
          .then(users => {
            if (users.length === 0) return { blocked: 0, checked: 0 };

            return processBatch(users).then(blocked => ({
              blocked,
              checked: users.length
            }));
          });

        batchPromises.push(batchPromise);
      }

      // Wait for all parallel batches to complete
      const batchResults = await Promise.all(batchPromises);
      const batchBlocked = batchResults.reduce((sum, result) => sum + result.blocked, 0);
      const batchChecked = batchResults.reduce((sum, result) => sum + result.checked, 0);

      totalBlocked += batchBlocked;
      usersChecked += batchChecked;

      const progress = Math.min(100, (usersChecked / usersToProcess * 100).toFixed(1));
      log.info(`Progress: ${progress}% (${usersChecked}/${usersToProcess}) - Blocked: ${batchBlocked} in this batch`);
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);

    if (totalBlocked > 0) {
      log.warn(`✅ Completed in ${duration}s. Checked ${usersChecked} trial users. Blocked ${totalBlocked} stealers.`);
    } else {
      log.warn(`✅ Completed in ${duration}s. Checked ${usersChecked} trial users. No new stealers found.`);
    }

  } catch (err) {
    log.error('Fatal error in stealer detection:', err);

    throw err;
  }
};
