require('../../config');
const log = require('@s1/log').create(__filename);
const moment = require('moment');
const User = require('../../rest/model/audb/User');
const getPaymentGeneralConfig = require('../../rest/commands/payment/getPaymentGeneralConfig');
const getPaymentBlacklistGeneralConfig = require('../../rest/commands/paymentBlacklist/getGeneralConfig');
const getCanPayWithBasicRulesIds = require('../../rest/commands/payment/helpers/getCanPayWithBasicRulesIds');
const canPayWithGroupRule = require('../../rest/commands/payment/helpers/rules/canPayWithGroupRule');
const UserConfig = require('../../rest/model/audb/UserConfig');
const PaymentBlacklist = require('../../rest/model/audb/PaymentBlacklist');
const UserPermissionGroup = require('../../rest/model/audb/UserPermissionGroup');
const PaymentGroupRule = require('../../rest/model/audb/PaymentGroupRule');
const UserDailyStatistics = require('../../rest/model/audb/UserDailyStatistics');
const Invoice = require('../../rest/model/audb/Invoice');
const { Package } = require('../../rest/model/audb/Package');

const CHUNK_SIZE = 50;

const checkUser = async (userId, rule, pack) => {
  const invoices = await Invoice.find({ id: userId, packageId: pack.id }).exec();
  const invoice = invoices.find(invoice => invoice.packageId === pack.id);
  const { tkey = null } = invoice || {};

  try {
    const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user: { id: userId }, paymentInfo: pack, tkey });
    const canRemoveUserRuStreamingServer = canPayWithGroupRule(rule, canPayWithBasicRules);

    if (canRemoveUserRuStreamingServer) {
      const userConfig = await UserConfig.findOne({ uid: userId }).exec();
      userConfig.speed = null;
      await userConfig.save();
    }
  } catch (e) {
    console.error(e);
  }
};

const checkUsers = async (usersIds, rule, pack) => {
  const promises = [];
  usersIds.forEach(userId => promises.push(checkUser(userId, rule, pack)))

  await Promise.all(promises);
};

const processRemoveRuServerUsers = async (rule, ruServerId) => {
  const blacklistedUsers = await PaymentBlacklist.find({ uid: { $exists: true } }, { uid: 1 }).lean();
  const blacklistedUsersIds = blacklistedUsers.map(log => log.uid);

  const startOfYesterday = moment().subtract(1, 'day').startOf('day').unix();
  const stats = await UserDailyStatistics.find(
    { userId: { $nin: blacklistedUsersIds }, date: { $gte: startOfYesterday } },
    { _id: 0, userId: 1 },
  ).lean();
  const userIdsFromStats = stats.map(stat => stat.userId);

  const permissionGroup = await UserPermissionGroup.findOne({ name: 'General users' }).lean().exec();
  const activeUsers = await User.find(
    { isactive: 1, expires: { $gte: startOfYesterday }, id: { $in: userIdsFromStats }, permissionGroups: permissionGroup._id.toString() },
    { _id: 0, id: 1},
  ).lean();
  const activeUsersIds = activeUsers.map(user => user.id);

  const ruServerUsersConfigs = await UserConfig.find(
    // eslint-disable-next-line no-dupe-keys
    { uid: { $in: activeUsersIds }, speed: { $exists: true }, speed: ruServerId },
    { _id: 0, uid: 1 },
  ).lean().exec();
  const ruServerUsersIds = ruServerUsersConfigs.map(config => config.uid);

  if (ruServerUsersIds.length > 0) {
    const chunks = [];
    while (ruServerUsersIds.length) chunks.push(
      ruServerUsersIds.splice(0, CHUNK_SIZE),
    );

    const pack = await Package.findOne({ price: 30 }).exec();
    for (let c = 0; c < chunks.length; ++c) {
      await checkUsers(chunks[c], rule, pack);
    }
  }
};

const run = async () => {
  const paymentGeneralConfigResponse = await getPaymentGeneralConfig();

  if (paymentGeneralConfigResponse && paymentGeneralConfigResponse.generalConfig
    && paymentGeneralConfigResponse.generalConfig.canRemoveUserRuStreamingServerRuleId) {
    const paymentGeneralConfig = paymentGeneralConfigResponse.generalConfig;

    const paymentBlacklistedGeneralConfig = await getPaymentBlacklistGeneralConfig();
    const blacklistedStreamingServersIds = paymentBlacklistedGeneralConfig.generalConfig.streamingServers || [];

    if (blacklistedStreamingServersIds && blacklistedStreamingServersIds.length) {
      const rule = await PaymentGroupRule.findOne({ _id: paymentGeneralConfig.canRemoveUserRuStreamingServerRuleId.toString() }).lean();

      if (rule && rule.enabled) {
        await processRemoveRuServerUsers(rule, blacklistedStreamingServersIds[0].toString());
      } else {
        console.error(`canRemoveUserRuStreamingServerRuleId rule not found or disabled`);
      }
    } else {
      console.error(`PaymentBlacklistedGeneralConfig streamingServers are not configured`);
    }
  } else {
    console.error(`PaymentGeneralConfig canRemoveUserRuStreamingServerRuleId in not configured`);
  }
}

module.exports = () => {
  run()
    .then(() => log.warn('Remove RU streaming server from users configs: DONE!'))
    .catch(log.error);
};
