const config = require('../../config');
const Suspended = require('../../rest/model/audb/Suspended');

const defaultOptions = {
  trialPackage: 1,
  parallel: 1,
  depth: 1,
  days: 30,
  uidsMoreThan: 0,
};

// eslint-disable-next-line import/order
const log = require('@s1/log').create(__filename);

const logs = [
  require('../../rest/model/audb/UserLogLogin'),
  require('../../rest/model/audb/UserLogLive'),
  require('../../rest/model/audb/UserLogRecord'),
  require('../../rest/model/audb/UserLogVod'),
];
const User = require('../../rest/model/audb/User');
const isBlacklistedUser = require('../../rest/commands/payment/helpers/rules/isBlacklistedUser');

module.exports = async () => {
  const {
    days, depth, uidsMoreThan, parallel, trialPackage,
  } = defaultOptions;
  log.warn('Getting paid users (without users with config.skipTrialCheck === true)');
  const paidUsers = await User.getPaidUserIds(true);
  const suspendedUsers = await Suspended.find({}, { uid: 1, _id: 0 }).lean();
  const suspendedUsersIds = suspendedUsers.map(suspend => suspend.uid);
  const paidUserIds = new Set(paidUsers.filter(uid => !suspendedUsersIds.includes(uid)));
  log.warn(`Checking trial users for accounts which used same fingerprint AND ip during last ${days} days`);
  let blocksDone = 0;
  let usersChecked = 0;
  await User.find({
    package: trialPackage,
    expires: { $gt: Math.floor(Date.now() / 1000) },
  }).cursor().eachAsync(async (user) => {
    usersChecked++;
    const uids = new Set();
    const logsResults = await Promise.all(logs.map(model => user.reduceLogsByComparing({
      depth,
      compareFields: { fingerprint: 1, ip: 1 },
      model,
      query: { playtime: { $gt: Math.floor(Date.now() / 1000) - (days * 3600 * 24) } },
    })));
    logsResults.forEach(results => results.forEach(uids.add, uids));
    const uidsArray = [...uids];

    if (uidsArray.length <= uidsMoreThan) return;
    if (uidsArray.some(paidUserIds.has, paidUserIds)) {
      log.warn(`one of ${user.id} accounts ${JSON.stringify(uidsArray)} is paid user, skipped`);

      return;
    }

    log.warn(`blocking ${user.id} for having ${uidsArray.length} not-paid accounts: ${JSON.stringify(uidsArray)}`);
    blocksDone++;
    const users = await User.find({ id: { $in: uidsArray } }).exec();
    // eslint-disable-next-line camelcase
    const previous_email = users.map(user => user.email).join(', ');
    user.expires = 0;

    const isUserBlacklisted = await isBlacklistedUser({ user });

    if (!isUserBlacklisted) user.mail('already_registered', config.email.noReply, {}, { previous_email });

    user.addAdminComment(`user has registered ${uidsArray.length} accounts ${JSON.stringify(uidsArray)} during ${days} days, he haven't paid account and is reset to be expired at 1970`);

    return user.save();
  }, { parallel });

  if (blocksDone > 0) log.warn(`Checked ${usersChecked} trial users. Blocked ${blocksDone} stealers.`);
  else log.warn('no new stealers found');
};
