const _getPopularRecords = require('../commands/popular/getRecords');
const _getPopularVods = require('../commands/popular/getVods');
const extractPaginationData = require('../helpers/paginationExtractor');
const PopularEpisodes = require('../model/audb/PopularEpisodes');
const PopularMovies = require('../model/audb/PopularMovies');

const popularController = {
  async getPopularRecords(req, res) {
    const days = +req.params.days;
    const { page, pageSize } = extractPaginationData(req);
    const result = await _getPopularRecords({ days, page, pageSize });
    res.send({ error: 0, result });
  },
  async getPopularTvShow(req, res) {
    const days = +req.params.days;
    const { page, pageSize } = extractPaginationData(req);
    const result = await _getPopularVods({ days, page, pageSize, Model: PopularEpisodes });
    res.send({ error: 0, result });
  },
  async getPopularMovies(req, res) {
    const days = +req.params.days;
    const { page, pageSize } = extractPaginationData(req);
    const result = await _getPopularVods({ days, page, pageSize, Model: PopularMovies });
    res.send({ error: 0, result });
  },
};

module.exports = popularController;
