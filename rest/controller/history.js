const UserLogLive = require('../model/audb/UserLogLive');
const UserLogRecord = require('../model/audb/UserLogRecord');
const UserLogVod = require('../model/audb/UserLogVod');
const _getLogs = require('../commands/history/getLogs');
const _deleteLog = require('../commands/history/deleteLog');
const errorMapper = require('../errors/maps/express');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const formApiResponse = require('../helpers/formApiResponse');

const HistoryController = {
  async getVodLogs(req, res) {
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const locale = getParamFromRequest(req, 'locale');
    const { history, fullPath } = await _getLogs({
      LogModel: UserLogVod, uid: user.id, ISP, countryCode, stateCode, locale,
    });
    res.send(formApiResponse(history, true, { fullcategoryofshow: fullPath }));
  },
  async deleteVodLog(req, res) {
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const removeOne = getParamFromRequest(req, 'removeone');
    const locale = getParamFromRequest(req, 'locale');
    const result = await errorMapper(_deleteLog({
      LogModel: UserLogVod,
      removeOne,
      uid: user.id,
      ISP,
      countryCode,
      stateCode,
      locale,
    }));

    if (result instanceof Error) throw result;

    res.send(formApiResponse(result.history, true, { fullcategoryofshow: result.fullPath }));
  },
  async getLiveLogs(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const results = await _getLogs({ LogModel: UserLogLive, uid: user.id, locale });
    res.send(formApiResponse(results, true));
  },
  async deleteLiveLog(req, res) {
    const { user } = req;
    const removeOne = getParamFromRequest(req, 'removeone');
    const locale = getParamFromRequest(req, 'locale');
    const results = await errorMapper(_deleteLog({ LogModel: UserLogLive, removeOne, uid: user.id, locale }));

    if (results instanceof Error) throw results;

    res.send(formApiResponse(results, true));
  },
  async getRecordLogs(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const results = await _getLogs({ LogModel: UserLogRecord, uid: user.id, locale });
    res.send(formApiResponse(results, true));
  },
  async deleteRecordLog(req, res) {
    const { user } = req;
    const removeOne = getParamFromRequest(req, 'removeone');
    const locale = getParamFromRequest(req, 'locale');
    const results = await _deleteLog({ LogModel: UserLogRecord, uid: user.id, removeOne, locale });

    if (results instanceof Error) throw results;

    res.send(formApiResponse(results, true));
  },
};

module.exports = HistoryController;
