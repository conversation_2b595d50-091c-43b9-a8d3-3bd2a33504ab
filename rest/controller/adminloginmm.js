const { LoginNeededError, ApiError } = require('@s1/api-errors');
const Session = require('../model/audb/Session');
const User = require('../model/audb/User');
const formatResponseIDs = require('../helpers/formatResponseIDs');

const getUserInfo = (req) => {
  const { user: userBody, pass: passBody, password: passwordBody } = req.body;
  const { user: userQuery, pass: passQuery, password: passwordQuery } = req.query;
  const email = User.encryptEmail(userBody || userQuery || '');

  return {
    email,
    password: passBody || passQuery || passwordBody || passwordQuery || '',
  };
};

const logIn = async (req, res) => {
  const { email, password } = getUserInfo(req);

  if (!email || !password) throw new LoginNeededError('please login');

  try {
    const user = await User.findOne({ em: email }).exec();

    if (!user || !password) {
      return res.status(403).json({ errorcode: 99, result: 'please login' });
    }

    const isCorrectPass = await user.checkPassword(password);

    if ((user.isadmin || user.issuperadmin || user.issupport) && isCorrectPass) {
      req.session.user_id = user.id;
      req.session.admin = user;
      user._doc.sid = req.sessionID;
      user._doc.email = User.decryptEmail(email);

      return res.json({ errorcode: 0, result: formatResponseIDs(user) });
    }

    throw new LoginNeededError('please login');
  } catch (error) {
    if (error instanceof ApiError) throw error;

    res.status(400).json([]);
  }
};

const logOut = async (req, res) => {
  const { act } = req.query;

  if (act === 'logout') {
    try {
      await Session.drop(req.sessionID);
      res.clearCookie('connect.sid');

      return res.json(0);
    } catch (error) {
      console.error(error);
    }
  } else {
    return logIn(req, res);
  }
};

module.exports = {
  logIn,
  logOut,
};
