const multer = require('multer');
const { HttpError } = require('@s1/api-errors');
const _getAwaitingShows = require('../commands/poster/show/getAwaiting');
const _getAwaitingSeasons = require('../commands/poster/season/getAwaiting');
const _getCombinedPosters = require('../commands/poster/getCombined');
const _savePoster = require('../commands/poster/save');
const extractPaginationData = require('../helpers/paginationExtractor');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const { clean } = require('../service/prepare/clean');

const upload = multer().single('poster');

module.exports = {
  async getCombinedPosters(req, res) {
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { page, pageSize } = extractPaginationData(req);
    const extended = getParamFromRequest(req, 'extended', false);
    const result = await _getCombinedPosters({
      page,
      pageSize,
      liteQuery: !extended,
      user,
      ISP,
      countryCode,
      stateCode,
    });
    res.json({ error: 0, result });
  },
  async getAwaitingSeasons(req, res) {
    const { page, pageSize } = extractPaginationData(req);
    const result = await _getAwaitingSeasons({ page, pageSize });
    const responseData = { result };
    clean(responseData, { category: { path: 'result', appended: ['path'] } });
    res.json(responseData.result);
  },
  async getAwaitingShows(req, res) {
    const { page, pageSize } = extractPaginationData(req);
    const result = await _getAwaitingShows({ page, pageSize });
    const responseData = { result };
    clean(responseData, {
      vod: { path: 'result', appended: ['category'] },
      category: 'result.category',
    });
    res.json(responseData.result);
  },
  savePoster: type => async (req, res, next) => {
    upload(req, res, async (error) => {
      const { file, resolved } = req;
      const entity = resolved[type];

      if (!file) return next(new HttpError('Poster file is required', 400));
      if (!entity) return next(new HttpError(`No such ${type}`, 404));
      if (error) return next(error);

      try {
        const { buffer, originalname: filename } = file;
        const result = await _savePoster({ entity, filename, buffer });
        const responseData = clean(result.format({}), { [type]: { path: '', appended: ['poster'] } });
        res.json(responseData);
      } catch (err) {
        next(err);
      }
    });
  },
};
