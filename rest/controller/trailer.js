const { HttpError } = require('@s1/api-errors');
const _saveTrailer = require('../commands/trailer/save');
const errorMapper = require('../errors/maps/express');
const getParamFromRequest = require('../helpers/getParamFromRequest');

module.exports = {
  saveTrailer: type => async (req, res) => {
    const { body, resolved } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { src } = body;
    const entity = resolved[type];

    if (!entity) throw new HttpError(req.__('No such %s', type), 404);

    const result = await errorMapper(_saveTrailer({ entity, src, locale }));

    if (result instanceof Error) throw result;

    res.send({ error: 0, result });
  },
};
