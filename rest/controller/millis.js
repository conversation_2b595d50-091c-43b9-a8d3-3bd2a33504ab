const {
  MissingRequestArgumentError,
} = require('@s1/api-errors');
const { WrongAct } = require('../errors/index');
const Country = require('../model/audb/Country');
const Player = require('../model/audb/Player');
const StreamingServerGroup = require('../model/audb/StreamingServerGroup');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const { loadAllPackages } = require('../helpers/getPackagesOldApi');

const actConstants = {
  UCLOAD: 'ucload',
  COUNTRIES: 'countries',
};
const MillisController = {
  async getMillis(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let responseData;

      switch (act) {
        case actConstants.COUNTRIES: {
          responseData = await Country.find({}).limit(300).exec();
          break;
        }
        case actConstants.UCLOAD: {
          const packages = await loadAllPackages();
          const [countries, players, sss] = await Promise.all([
            Country.find({}).limit(300).exec(),
            Player.find({}).exec(),
            StreamingServerGroup.find().exec()]);
          responseData = {};
          responseData.countries = countries;
          responseData.packages = packages;
          responseData.players = players;
          responseData.sss = sss;
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(formatResponseIDs(responseData));
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = MillisController;
