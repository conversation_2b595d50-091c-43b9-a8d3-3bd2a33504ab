const isLocal = require('is-local-ip');
const _getEuropeCountry = require('../commands/country/getEuropeCountry');
const errorMapper = require('../errors/maps/express');
const formApiResponse = require('../helpers/formApiResponse');
const { getIspLookup, getCityLookup } = require('../helpers/maxmind');

const countryController = {
  async isEuropeCountry(req, res) {
    const { query } = req;
    const { code } = query;
    const { country } = await errorMapper(_getEuropeCountry({ code }));
    res.send(formApiResponse(country, true));
  },

  /**
   * example:
   * "GET":“/country/ipcapi.php?ipad=**************"
   * used on payment gateway
   */
  async ipcapi(req, res) {
    let ipAddress = req.query.ipad;

    if (!ipAddress) if (req.headers['x-forwarded-for']) {
      ipAddress = req.headers['x-forwarded-for'];
    }
    if (isLocal(ipAddress)) res.send({
      countryCode: 'US',
      countryName: 'USA',
      cityName: 'Sacramento',
      stateCode: 'CA',
      isp: 'Vodafone bla bla',
      connectionType: 'Cable/DSL',
    });

    const isp = getIspLookup().get(ipAddress);
    const city = getCityLookup().get(ipAddress);
    res.send({
      country: city && city.country && city.country.names ? city.country.names.en : '', // old API
      countrycode: city && city.country && city.country.iso_code ? city.country.iso_code : '',
      region: city && city.subdivisions && city.subdivisions[0] && city.subdivisions[0].iso_code ? city.subdivisions[0].iso_code : '',
      state: city && city.subdivisions && city.subdivisions[0] && city.subdivisions[0].names && city.subdivisions[0].names ? city.subdivisions[0].names.en : '',
      postcode: city && city.postal && city.postal.code ? city.postal.code : '',
      city: city && city.city && city.city.names ? city.city.names.en : '',
      latitude: city && city.location ? city.location.latitude : 0,
      longitude: city && city.location ? city.location.longitude : 0,
      isp: isp && isp.isp ? isp.isp : '',
    });
  },
};

module.exports = countryController;
