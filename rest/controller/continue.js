const getMoviesCommand = require('../commands/continue/getMovies');
const getEpisodesCommand = require('../commands/continue/getEpisodes');
const getRecordsCommand = require('../commands/continue/getRecords');
const { clean } = require('../service/prepare/clean');
const formApiResponse = require('../helpers/formApiResponse');

const continueController = {
  async getMovies(req, res) {
    const { user, query } = req;
    const { locale, key, page = 1, pageSize = 10 } = query;
    const results = await getMoviesCommand({ uid: user.id, key, locale, page, pageSize });
    const responseData = formApiResponse(
      results.vods,
      true,
      { pagination: { page: Number(page), pageSize: Number(pageSize), count: results.count } },
    );
    clean(responseData, {
      vod: 'results',
    });
    res.json(responseData);
  },
  async getEpisodes(req, res) {
    const { user, query } = req;
    const { locale, key, page = 1, pageSize = 10 } = query;
    const results = await getEpisodesCommand({ uid: user.id, key, locale, page, pageSize });
    const responseData = formApiResponse(
      results.vods,
      true,
      { pagination: { page: Number(page), pageSize: Number(pageSize), count: results.count } },
    );
    clean(responseData, {
      vod: 'results',
    });
    res.json(responseData);
  },
  async getRecords(req, res) {
    const { user, query, userLocation } = req;
    const { locale, key } = query;
    const { ISP, countryCode, stateCode } = userLocation;
    const results = await getRecordsCommand({ user, key, locale, ISP, countryCode, stateCode });
    const responseData = formApiResponse(results, true);
    clean(responseData, {
      schedule: 'results',
    });
    res.json(responseData);
  },
};

module.exports = continueController;
