const { user: { APPLE_INVITE_ACTIONS } } = require('@s1/api-constants');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const validateEmail = require('../../helpers/validators/emailValidator');
const createInviteCommand = require('../../commands/user/invite/create');
const invitesListCommand = require('../../commands/user/invite/list');
const sendInviteCommand = require('../../commands/user/invite/send');
const createAppleInviteCommand = require('../../commands/user/invite/apple/create');
const appleInvitesListCommand = require('../../commands/user/invite/apple/list');
const deleteAppleInviteCommand = require('../../commands/user/invite/apple/delete');

const UserInvitesController = {
  async getNewReferralCode(req, res) {
    const locale = getParamFromRequest(req, 'locale');
    const referralCode = await createInviteCommand({ userId: req.user.id, locale });
    res.send({
      errorcode: 0,
      results: {
        referral: String(referralCode.uniquekey),
      },
    });
  },
  async getAllReferralCodes(req, res) {
    const results = await invitesListCommand({ user: req.user });
    res.send({ errorcode: 0, results });
  },
  async sendInvite(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const emailto = getParamFromRequest(req, 'emailto');
    const name = getParamFromRequest(req, 'name');
    const uniquekey = getParamFromRequest(req, 'uniquekey');
    const fromname = getParamFromRequest(req, 'fromname');
    await sendInviteCommand({ user, emailto, name, uniquekey, fromname, locale });
    res.send({ errorcode: 0, results: req.__('Invite email successfully sent') });
  },
  async processAppleInvite(req, res) {
    const { user, query } = req;
    const { act: action } = query;

    if (action === APPLE_INVITE_ACTIONS.GET_ALL) {
      const invites = await appleInvitesListCommand({ uid: user.id });

      return res.send(invites);
    }
    if (action === APPLE_INVITE_ACTIONS.CREATE) {
      if (!validateEmail(query.email)) return res.send({ errorcode: 58118 });
      // list of allowed types
      if ((['iphone', 'ipad', 'apple'].filter(item => item === query.type)).length === 0) return res.send({ errorcode: 58118 });

      await createAppleInviteCommand({
        uid: user.id,
        type: query.type,
        email: query.email,
      });

      return res.send({ errorcode: 0 });
    }
    if (action === APPLE_INVITE_ACTIONS.REMOVE) await deleteAppleInviteCommand({
      uid: user.id,
      type: query.type,
      email: query.email,
    });

    res.send();
  },
};

module.exports = UserInvitesController;
