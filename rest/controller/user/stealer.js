const log = require('@s1/log').create(__filename);
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _getStealersList = require('../../commands/stealer/getAll');
const _saveStealer = require('../../commands/stealer/save');
const _deleteStealer = require('../../commands/stealer/delete');
const _deleteStealerUsers = require('../../commands/stealer/deleteUsers');
const _getStealerIpReviewList = require('../../commands/stealer/getAllIpReview');
const _updateStealerIpReview = require('../../commands/stealer/updateIpReview');
const _deleteStealerFingerprint = require('../../commands/stealer/deleteFingerprint');
const _getStealerGeneralConfig = require('../../commands/stealer/getGeneralConfig');
const _updateStealerGeneralConfig = require('../../commands/stealer/updateGeneralConfig');
const _getStealerFingerprint = require('../../commands/stealer/getFingerprint');
const _getStealerCardNumbers = require('../../commands/stealer/getCardNumbers');
const _deleteStealerCardNumbers = require('../../commands/stealer/deleteCardNumbers');
const _getStealerUserRelationsList = require('../../commands/stealer/getStealerUserRelationsList');
const _getStealerRelatedUsers = require('../../commands/stealer/getStealerRelatedUsers');
const _getStealerUserRelationsGraph = require('../../commands/stealer/getStealerUserRelationsGraph');
const _getStealerUserRelationsBillingStealerUsers = require('../../commands/stealer/getStealerUserRelationsBillingStealerUsers');
const _moveStealerFingerprintToWhitelist = require('../../commands/stealer/moveStealerFingerprintToWhitelist');
const _moveStealerFingerprintUseragentToIgnore = require('../../commands/stealer/moveStealerFingerprintUseragentToIgnore');
const { SocketClientNames } = require('../../constants/socket');
const _getWhitelistCards = require('../../commands/stealer/getWhitelistCards');
const _moveStealerCardToWhitelist = require('../../commands/stealer/moveStealerCardToWhitelist');
const _saveWhitelistCard = require('../../commands/stealer/saveWhitelistCard');
const _deleteWhitelistCard = require('../../commands/stealer/deleteWhitelistCard');

const stealerController = {
  async getStealersList(req, res) {
    const type = getParamFromRequest(req, 'type');
    const result = await _getStealersList(type);

    res.json(result);
  },
  async updateStealer(req, res) {
    const { body, user } = req;
    const result = await _saveStealer(body, user);

    res.json(result);
  },
  async deleteStealer(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const { user } = req;
    const result = await _deleteStealer({ _id, adminUser: user });

    res.json(result);
  },
  async deleteStealerUsers(req, res) {
    const uids = getParamFromRequest(req, 'uids');
    const { user, session } = req;
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const result = await _deleteStealerUsers({ uids, adminUser: user });

    const socketData = { data: { ...result, sid }, groupName: SocketClientNames.adminPanel, eventName: 'user/stealer/users' };
    global.io.emit('group', socketData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API user/stealer/users, sid: ${sid}`);
      }
    });

    res.json(result);
  },
  async getStealerIpReviewList(req, res) {
    const result = await _getStealerIpReviewList();

    res.json(result);
  },
  async updateStealerIpReview(req, res) {
    const { body, user } = req;
    const result = await _updateStealerIpReview(body, user);

    res.json(result);
  },
  async getStealerGeneralConfig(req, res) {
    const result = await _getStealerGeneralConfig();

    res.json(result);
  },
  async updateStealerGeneralConfig(req, res) {
    const { body, user } = req;
    const result = await _updateStealerGeneralConfig(body, user);

    res.json(result);
  },
  async getStealerFingerprint(req, res) {
    const result = await _getStealerFingerprint();

    res.json(result);
  },
  async deleteStealerFingerprint(req, res) {
    const fingerprint = getParamFromRequest(req, 'fingerprint');
    const result = await _deleteStealerFingerprint({ fingerprint });

    res.json(result);
  },
  async getStealerCardNumbers(req, res) {
    const result = await _getStealerCardNumbers();

    res.json(result);
  },
  async deleteStealerCardNumbers(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteStealerCardNumbers({ number });

    res.json(result);
  },
  async getStealerUserRelationsList(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getStealerUserRelationsList(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'user/stealer/user-relations/id';
    const requestData = {
      eventName,
      data: { ...result, sid },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });

    res.json(result);
  },
  async getStealerRelatedUsers(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getStealerRelatedUsers(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'user/stealer/user-relations/id/users';
    const requestData = {
      eventName,
      data: { ...result, sid },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async getStealerUserRelationsGraph(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getStealerUserRelationsGraph(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'user/stealer/user-relations/id/graph';
    const requestData = {
      eventName,
      data: { ...result, sid, userId: parseInt(userId) },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async getStealerUserRelationsBillingStealerUsers(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getStealerUserRelationsBillingStealerUsers(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'user/stealer/user-relations/id/billing-stealer-users';
    const requestData = {
      eventName,
      data: { ...result, sid, userId },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async moveStealerFingerprintToWhitelist(req, res) {
    const { body, user } = req;
    const result = await _moveStealerFingerprintToWhitelist(body, user);

    res.json(result);
  },
  async moveStealerFingerprintUseragentToIgnore(req, res) {
    const { body, user } = req;
    const result = await _moveStealerFingerprintUseragentToIgnore(body, user);

    res.json(result);
  },
  async getWhitelistCards(req, res) {
    const result = await _getWhitelistCards();

    res.json(result);
  },
  async moveStealerCardToWhitelist(req, res) {
    const { body, user } = req;
    const result = await _moveStealerCardToWhitelist(body, user);

    res.json(result);
  },
  async saveWhitelistCard(req, res) {
    const { body, user } = req;
    const result = await _saveWhitelistCard(body, user);

    res.json(result);
  },
  async deleteWhitelistCard(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteWhitelistCard(number);

    res.json(result);
  },
};

module.exports = stealerController;
