const { ObjectId } = require('mongoose').Types;
const { MissingRequestArgumentError } = require('@s1/api-errors');
const UserConfig = require('../../model/audb/UserConfig');
const getParamsFromRequest = require('../../helpers/getParamFromRequest');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const isMongooseObjectID = require('../../helpers/isMongooseObjectID');
const generateMongoId = require('../../helpers/generateMongoId');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const setUaPlayer = require('../../commands/uaplayer/set');
const { WrongAct, WrongKeyId } = require('../../errors/');

const actConstants = {
  SET: 'set',
  GET: 'get',
  GET_ALL: 'getall',
};
const forConstants = {
  USER_SUSPEND: 'usersuspend',
  SPEED: 'speed',
  PLAYER_ID: 'playerid',
  SSS: 'sss',
};

const isArray = item => Array.isArray(item);

const getConfigs = async (forQuery, uid) => {
  forQuery = isArray(forQuery) ? forQuery[forQuery.length - 1] : forQuery;
  const configs = await UserConfig.find({ uid }, { [forQuery]: 1 }).lean().exec();
  const result = formatResponseIDs(configs);

  return result.length === 0 ? null : result.length === 1 ? result[0] : result;
};

const UserConfigController = {
  /**
   * Requests params description
   * @param req.query.keyid Secret key
   * @param req.query.uid User id. Only admin and support can see and change other users config.
   * If you want to see your own config - you should be authorized
   * @param req.query.act Action with user config. Can be: set, get (or getall, it's the same, like get)
   * @param req.query.for In php you can't see all configs... In this param you should set the requested config field
   * can be: usersuspend, speed, playerid, sss, maxconn. Seems that this param was created only for changing configs
   * ToDo: change this logic to REST...
   * @param req.query.toval New value of query.for param
   * @param res
   * @returns {Promise<*>}
   */
  async getOrModifyData(req, res) {
    try {
      let uid;
      const { user } = req;

      if (isAdminOrSupportUser(user)) {
        uid = getParamsFromRequest(req, 'uid');
      } else {
        uid = req.session && req.session.user_id ? req.session.user_id : '';
      }
      if (uid) uid = parseInt(uid);
      else throw new WrongKeyId('Please set uid (userid) or login and uid will be taken from current session');

      const act = getParamsFromRequest(req, 'act');
      const forQuery = getParamsFromRequest(req, 'for');
      let toval = getParamsFromRequest(req, 'toval');
      let result;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      switch (act) {
        case actConstants.SET: {
          if (toval !== '' && toval !== undefined) {
            if (toval === 'true' || toval === 'false' || toval === 'null') toval = JSON.parse(toval);
            else if (typeof toval === 'string' && toval.match(/^[0-9]+$/)) toval = parseInt(toval);
            else if (toval.$id) toval = toval.$id;
          }
          if (forQuery === forConstants.SSS) {
            const generatedMongoIDs = [];
            for (const item of toval) {
              generatedMongoIDs.push(generateMongoId(item));
            }
            toval = generatedMongoIDs;
          } else if (forQuery === forConstants.USER_SUSPEND) {
            if (toval) {
              // block device
            } else {
              // unblock device
            }
          } else if (forQuery === forConstants.SPEED) {
            toval = (typeof toval === 'string' || toval instanceof ObjectId) && isMongooseObjectID(toval) ? toval : null;
          } else if (forQuery === forConstants.PLAYER_ID) {
            await setUaPlayer({ forWho: 'tempuser', uid, playerid: toval });
          }

          const updateOperation = await UserConfig.findOneAndUpdate(
            { uid },
            { $set: { [forQuery]: toval } },
            { new: true, upsert: true },
          ).select([forQuery]).lean().exec();
          result = formatResponseIDs(updateOperation);
          break;
        }
        case actConstants.GET: {
          result = await getConfigs(forQuery, uid);
          break;
        }
        case actConstants.GET_ALL: {
          result = await getConfigs(forQuery, uid);
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      throw error;
    }
  },
};

module.exports = UserConfigController;
