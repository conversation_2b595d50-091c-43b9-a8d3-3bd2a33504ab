module.exports = {
  /**
   * Returns complete information of your IP using GeoIP2.
   *
   * <AUTHOR> <<EMAIL>>
   *
   * Query
   * @param {string} sid - Session ID;
   * @param {string} ip - Your custom IP address;
   * @returns {object} - Your complete IP information;
   */
  getIPInformation: (req, res) => {
    const { IPInfo, session } = req;
    const result = {
      ...IPInfo,
      uid: session.user_id,
    };
    res.send(result);
  },
};
