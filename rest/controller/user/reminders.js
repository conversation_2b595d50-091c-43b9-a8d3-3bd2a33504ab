const addReminderCommand = require('../../commands/user/reminder/add');
const remindersListCommand = require('../../commands/user/reminder/list');
const deleteReminderCommand = require('../../commands/user/reminder/delete');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

module.exports = {
  async addReminder(req, res) {
    const { body: { channel, rdatetime }, user: { id: uid } } = req;
    const locale = getParamFromRequest(req, 'locale');
    const result = await addReminderCommand({ uid, channel, rdatetime, locale });
    res.json({ error: 0, result });
  },

  async getReminders(req, res) {
    const { user, userLocation, query } = req;
    const { countryCode, ISP, stateCode } = userLocation;
    const withChannel = !!(+query.withChannel);
    const result = await remindersListCommand({ user, withChannel, ISP, countryCode, stateCode });
    res.json({ error: 0, result });
  },

  async deleteReminders(req, res) {
    const { user: { id: uid }, params: { channel, rdatetime } } = req;
    await deleteReminderCommand({ uid, channel, rdatetime });
    res.json({ error: 0, result: true });
  },
};
