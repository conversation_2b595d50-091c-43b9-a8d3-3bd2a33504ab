/* eslint-disable no-case-declarations */
const { WrongParamsError } = require('@s1/api-errors');
const { HttpError } = require('@s1/api-errors');
const { favorites: { TYPE_BY_ALIAS, DEFAULT_TYPE, TYPES } } = require('@s1/api-constants');
const i18n = require('../../helpers/geti18n');
const UserFavoriteLive = require('../../model/audb/UserFavoriteLive');
const UserFavoriteTvShow = require('../../model/audb/UserFavoriteTvShow');
const UserFavoriteVod = require('../../model/audb/UserFavoriteVod');
const favoritesListCommand = require('../../commands/user/favorites/list');
const addFavoritesCommand = require('../../commands/user/favorites/add');
const deleteFavoritesCommand = require('../../commands/user/favorites/delete');
const setPositionCommand = require('../../commands/user/favorites/setPosition');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

const sortOption = { order: 1, created: -1 };

function parseRequestParam(req, param, isInteger = false) {
  const result = req.query[param] ? req.query[param] : (req.body[param] ? req.body[param] : false);

  return isInteger && result ? parseInt(result) : result;
}

async function getFavorites(req, res) {
  const { user, userLocation } = req;
  const locale = getParamFromRequest(req, 'locale');
  const { ISP, countryCode, stateCode } = userLocation;
  const _type = req.params.type;
  const type = (_type && TYPE_BY_ALIAS[_type]) || DEFAULT_TYPE;
  const results = await favoritesListCommand({ type, user, ISP, countryCode, stateCode, locale });
  res.json({ error: 0, results });
}

async function sortFavorites(type, to, id, userId) {
  let model;
  switch (type) {
    case TYPES.LIVE:
      model = UserFavoriteLive;
      break;
    case TYPES.VOD:
      model = UserFavoriteVod;
      break;
    case TYPES.TVSHOW:
      model = UserFavoriteTvShow;
      break;
    default:

      return false;
  }

  const promises = [];
  const favorites = await model.find({ uid: userId }).sort(sortOption).exec();
  const from = favorites.findIndex(fav => fav._id.toString() === id);

  if (from === -1) throw new HttpError(i18n.__('Favorite not found #%s', id), 400);

  const favorite = favorites[from];
  favorites.splice(from, 1);
  favorites.splice(to, 0, favorite);
  favorites.forEach((fav, index) => {
    if (fav.order !== index) {
      fav.order = index;
      promises.push(fav.save());
    }
  });
  await Promise.all(promises);

  return favorites;
}

const UserFavoritesController = {
  async postFavorites(req, res) {
    const { user } = req;
    const { id, type } = req.params;
    await addFavoritesCommand({ type, user, id });

    return getFavorites(req, res);
  },

  async deleteFavorites(req, res) {
    const { user } = req;
    const { id, type } = req.params;
    await deleteFavoritesCommand({ type, user, id });

    return getFavorites(req, res);
  },

  async setPosition(req, res) {
    const { user } = req;
    const { id, type, to } = req.params;
    await setPositionCommand({ type, user, id, to });

    return getFavorites(req, res);
  },

  /**
   * "stfor": query param for type of favorites list
   * "ch": query param for favorite channel ID
   * "vodid": query param for favorite vod ID
   * "act" (action) - post query param: 1 = add, 11 = delete
   */

  async process(req, res) {
    const types = {
      LIVE: 0,
      RECORD: 1,
      VOD: 2,
      TVSHOW: 3,
      RADIO: 4,
      ALL: 99,
      LIVE_ID_ONLY: 1000,
    };
    const modelMapping = {
      [types.LIVE]: UserFavoriteLive,
      [types.VOD]: UserFavoriteVod,
      [types.TVSHOW]: UserFavoriteTvShow,
    };
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const to = parseRequestParam(req, 'to', true);
    const id = parseRequestParam(req, 'id');
    const action = parseRequestParam(req, 'act', true);
    const channel = parseRequestParam(req, 'ch', true);
    const vodId = parseRequestParam(req, 'vodid', true);
    const type = parseRequestParam(req, 'stfor', true);
    const locale = getParamFromRequest(req, 'locale');

    if (action) {
      if (!channel && !vodId) throw new WrongParamsError(locale);
      if (action === 1) {
        const movieTypeMapping = {
          [types.LIVE]: 1,
          [types.VOD]: 3,
          [types.TVSHOW]: 4,
        };
        const movieType = movieTypeMapping[type];

        await addFavoritesCommand({
          favoritesModel: modelMapping[type],
          id: channel || vodId,
          user,
          type,
          movieType,
        });
      }
      if (action === 11) await deleteFavoritesCommand({
        favoritesModel: modelMapping[type],
        id: channel || vodId,
        user,
      });
      if (action === 6) await sortFavorites(type, to, id, user.id);
    }
    if (type === 1000) return res.json([]);

    const results = await favoritesListCommand({ type, user, types, ISP, countryCode, stateCode, locale });
    res.json({ error: 0, results });
  },

  getFavorites,
  sortFavorites,
};

module.exports = UserFavoritesController;
