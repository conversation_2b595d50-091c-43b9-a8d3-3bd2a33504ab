const getInvoiceUrlCommand = require('../../commands/user/invoice/getUrl');
const errorMapper = require('../../errors/maps/express');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

module.exports = {
  async getInvoiceUrl(req, res) {
    const { params: { tkey }, sessionID } = req;
    const locale = getParamFromRequest(req, 'locale');
    const result = await errorMapper(getInvoiceUrlCommand({ sessionID, tkey, locale }));
    res.send({ error: 0, result });
  },
};
