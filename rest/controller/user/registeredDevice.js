const devicesListCommand = require('../../commands/user/registeredDevices/list');
const removeDeviceCommand = require('../../commands/user/registeredDevices/delete');
const removeManyDevicesCommand = require('../../commands/user/registeredDevices/deleteMany');
const getAdminUserFromReq = require('../../helpers/getAdminUserFromReq');

const RegisteredDeviceController = {
  async getDevicesList(req, res) {
    const user = await getAdminUserFromReq(req);

    const devices = await devicesListCommand({ user });
    const result = {
      error: 0,
      devices,
    };

    res.json(result);
  },

  async removeDevice(req, res) {
    const { id } = req.params;
    const { user } = req;

    if (id) {
      await removeDeviceCommand(id);
    } else {
      await removeManyDevicesCommand(user.id);
    }

    const devices = await devicesListCommand({ user });
    const result = {
      error: 0,
      devices,
    };

    res.json(result);
  },
};

module.exports = RegisteredDeviceController;
