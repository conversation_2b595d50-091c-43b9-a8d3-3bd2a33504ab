const { EmailSendingError } = require('@s1/api-errors');
const sendMailCommand = require('../commands/mail/send');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const config = require('../../config');

const MailController = {
  async send(req, res) {
    const tag = getParamFromRequest(req, 'tag', 'contactus');
    const locale = getParamFromRequest(req, 'locale');

    const content = {
      name: getParamFromRequest(req, 'name'),
      from: getParamFromRequest(req, 'from'),
      subject: getParamFromRequest(req, 'subject'),
      message: getParamFromRequest(req, 'message'),
      email: getParamFromRequest(req, 'email'),
      source: `domain: ${req.headers['x-site']}, api: ${req.headers.referral}, ua: ${req.headers['user-agent']}`,
    };

    const data = await sendMailCommand({
      tag,
      from: getParamFromRequest(req, 'emailfrom', config.email.noReply),
      to: getParamFromRequest(req, 'emailto', config.email.info),
      data: content,
      locale,
      replyTo: content.email || content.from,
    });

    if (!data) throw new EmailSendingError(locale);

    res.send({
      errorcode: 0,
      result: `email ${tag} success`,
    });
  },
};

module.exports = MailController;
