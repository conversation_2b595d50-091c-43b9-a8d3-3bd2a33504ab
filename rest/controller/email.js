const { WrongAct } = require('../errors');
const formApiResponse = require('../helpers/formApiResponse');
const loadTemplates = require('../commands/mail/loadTemplates');
const saveTemplate = require('../commands/mail/saveTemplate');
const deleteTemplate = require('../commands/mail/deleteTemplate');
const createPostmarkId = require('../commands/mail/createPostmarkId');
const sendTestTemplate = require('../commands/mail/sendTestTemplate');

const ACT_CONSTANTS = {
  LOAD: 'load',
  SAVE: 'save',
  DELETE: 'delete',
  POSTMARK_ADD_TEMPLATE: 'postmarkaddtemplate',
  SEND_EMAIL: 'sendemail',
  SEND_EMAIL_TEMPLATE: 'sendemailtemplate',
};

const EmailController = {
  async postData(req, res) {
    const { act, provider, type } = req.query;
    const { body } = req;
    let result = null;

    switch (act) {
      case ACT_CONSTANTS.LOAD:
        result = await loadTemplates({ provider, type });
        break;
      case ACT_CONSTANTS.SAVE:
        result = await saveTemplate({ data: body, provider });
        break;
      case ACT_CONSTANTS.DELETE:
        result = await deleteTemplate({ data: body });
        break;
      case ACT_CONSTANTS.POSTMARK_ADD_TEMPLATE:
        result = await createPostmarkId({ data: body });
        break;
      case ACT_CONSTANTS.SEND_EMAIL:
        break;
      case ACT_CONSTANTS.SEND_EMAIL_TEMPLATE:
        result = await sendTestTemplate({ data: body, provider });
        break;
      default:
        throw new WrongAct('Act is wrong');
    }

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result);
    res.json(responseData);
  },
};

module.exports = EmailController;
