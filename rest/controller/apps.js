const multer = require('multer');
const { AppVersionDuplicationError } = require('@s1/api-errors');
const { AppVersion } = require('../model/audb/AppVersion');
const uploadAssets = require('../service/uploadAssets');

const upload = multer().single('package');
const config = require('../../config');
const getAppsHomepage = require('../commands/apps/getHomepage');
const getAllApps = require('../commands/apps/getAll');
const saveApp = require('../commands/apps/save');
const deleteApp = require('../commands/apps/delete');
const _getAllAppsAccesses = require('../commands/apps/getAllAppsAccesses');
const _saveAppAccess = require('../commands/apps/saveAppAccess');
const _deleteAppAccess = require('../commands/apps/deleteAppAccess');
const _getUserAllowedApps = require('../commands/apps/getUserAllowedApps');
const getParamsFromRequest = require('../helpers/getParamFromRequest');
const _getAllAppsImages = require('../commands/apps/getAllAppsImages');
const _saveAppImage = require('../commands/apps/saveAppImage');
const _getAppImageByType = require('../commands/apps/getAppImageByType');
const _getSuggestApp = require('../commands/apps/getSuggestApp');

const appsController = {
  async release(req, res, next) {
    // TODO we need new logic to upload app file or link to the file
    upload(req, res, async (err) => {
      if (err) return next(err);
      if (!req.file) return next(new Error('There is no any files!'));

      const {
        version, type, packageName, appName, versionName,
      } = req.body;
      const exists = await AppVersion.findOne({ version, type }).exec();

      if (exists) return next(new AppVersionDuplicationError());

      try {
        const path = await uploadAssets('apps', req.file.originalname, {
          value: req.file.buffer,
          options: {
            filename: req.file.originalname,
          },
        });
        const fresh = new AppVersion({
          type,
          version,
          packageName,
          appName,
          packagePath: path,
          versionName,
        });
        await fresh.save();
        res.json({
          success: true,
          url: `https://${config.baseImagesPath}/assets${path}`,
        });
      } catch (e) {
        next(e);
      }
    });
  },

  async latest(req, res) {
    const { type } = req.params;
    const packageName = getParamsFromRequest(req, 'package', null);

    if (!type) throw new Error('type is required');
    if (!packageName) {
      res.json({ result: null });

      return;
    }

    const query = { type, packageName };

    const result = await AppVersion.findOne(query, {
      version: 1,
      versionName: 1,
      packagePath: 1,
      packageName: 1,
      appName: 1,
      appLink: 1,
      buildDate: 1,
      force: 1,
      forceVersion: 1,
    }).sort({ version: -1 }).lean().exec();

    if (result) {
      if (result.appLink) {
        result.url = result.appLink;
        delete result.appLink;
      } else if (type === 'android_tv') {
        result.url = 'https://afs.su/israeltv';
      } else {
        result.url = `https://${config.baseImagesPath}/assets${result.packagePath}`;
      }

      delete result.packagePath;
    }

    res.json({ result });
  },

  async homePage(req, res) {
    const { user, userLocation, query } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { sections, sid, locale = 'he' } = query;

    if (!sections) return res.json({ error: 0, result: {} });

    const items = sections.split(',').filter(item => !!item);
    const result = await getAppsHomepage({ items, user, ISP, countryCode, stateCode, locale, sid });
    res.json({ error: 0, result });
  },

  async getAll(req, res) {
    const result = await getAllApps();

    res.json(result);
  },

  async save(req, res) {
    const { user, body } = req;
    const result = await saveApp({ user, data: body });

    res.json(result);
  },

  async delete(req, res) {
    const _id = getParamsFromRequest(req, 'id');
    const result = await deleteApp({ _id });

    res.json(result);
  },

  async getAllAppsAccesses(req, res) {
    const result = await _getAllAppsAccesses();

    res.json(result);
  },

  async saveAppAccess(req, res) {
    const { user, body } = req;
    const result = await _saveAppAccess(body, user);

    res.json(result);
  },

  async deleteAppAccess(req, res) {
    const _id = getParamsFromRequest(req, 'id');
    const result = await _deleteAppAccess(_id);

    res.json(result);
  },
  async getUserAllowedApps(req, res) {
    const userId = getParamsFromRequest(req, 'userId');
    const result = await _getUserAllowedApps(userId);

    res.json(result);
  },

  async getAllAppsImages(req, res) {
    const result = await _getAllAppsImages();

    res.json(result);
  },

  async saveAppImage(req, res) {
    const { user, body } = req;
    const result = await _saveAppImage(body, user);

    res.json(result);
  },

  async getAppImageByType(req, res) {
    const { type } = req.params;
    const result = await _getAppImageByType(type);

    res.json(result);
  },

  async getSuggestApp(req, res) {
    const { user, query } = req;
    const result = await _getSuggestApp(user, query);

    res.json(result);
  },
};

module.exports = appsController;
