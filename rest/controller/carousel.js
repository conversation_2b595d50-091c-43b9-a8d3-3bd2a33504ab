const getCommand = require('../commands/carousel/get');
const addCommand = require('../commands/carousel/add');
const updateCommand = require('../commands/carousel/update');
const updateOrdersCommand = require('../commands/carousel/updateOrders');
const deleteCommand = require('../commands/carousel/delete');
const formApiResponse = require('../helpers/formApiResponse');
const errorMapper = require('../errors/maps/express');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const { clean } = require('../service/prepare/clean');

const CarouselController = {
  /**
   * Load carousel items by type
   *
   * @param {number} categoryId: id of main category
   * @param {boolean} allLocales: [ true | false | empty ] (Required only to get all localised fields for admin panel)
   * @returns {Array<CarouselItem>}
   */
  async getCarousel(req, res) {
    const { categoryId } = req.query;
    const useAllLocales = getParamFromRequest(req, 'allLocales');
    const { user } = req;

    // To return all fields - hebrew should be default locale
    const locale = useAllLocales ? 'he' : getParamFromRequest(req, 'locale');
    const result = await errorMapper(getCommand({ categoryId, useAllLocales, user, locale }));

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result, true);

    // For admin panel, should return raw model with all locales from database
    if (!useAllLocales) clean(responseData, { carouselItem: 'results' });

    res.json(responseData);
  },

  /**
   * Create carousel item
   *
   * @param {number} categoryId: id of main category
   * @param {boolean} isVod
   * @param {number} linkTo: vod or series id
   * @returns {{result: true}}
   */
  async createCarousel(req, res) {
    const { isVod, linkTo, categoryId } = req.body;
    const result = await errorMapper(addCommand(isVod, linkTo, categoryId));

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result);
    res.json(responseData);
  },

  /**
   * Edit carousel item
   *
   * @param {string} _id
   * @param {CarouselItem} data
   * @returns {{result: true}}
   */
  async editCarousel(req, res) {
    const { _id, ...data } = req.body;
    const result = await errorMapper(updateCommand(_id, data));

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result);
    res.json(responseData);
  },

  /**
   * Remove carousel item
   *
   * @param {string} _id
   * @returns {{result: true}}
   */
  async deleteCarousel(req, res) {
    const { _id } = req.query;
    const result = await errorMapper(deleteCommand(_id));

    const responseData = formApiResponse(result);
    res.json(responseData);
  },

  /**
   * Update carousel orders
   *
   * @param {object} newItems: list of {_id, sorder, index}
   * @returns {{result: true}}
   */
  async updateCarouselOrders(req, res) {
    const { newItems } = req.body;

    const result = await errorMapper(updateOrdersCommand(newItems));

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result);
    res.json(responseData);
  },
};

module.exports = CarouselController;
