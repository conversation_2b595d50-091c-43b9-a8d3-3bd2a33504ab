const log = require('@s1/log').create(__filename);
const { HttpError } = require('@s1/api-errors');
const _getChannel = require('../commands/channel/getChannel');
const _getAll = require('../commands/channel/getAll');
const _getInfo = require('../commands/channel/getInfo');
const _getGroups = require('../commands/channel/getGroups');
const _rateChannel = require('../commands/channel/rateChannel');
const _updateChannelGroups = require('../commands/channel/updateChannelGroups');
const _replaceChannelsInGroups = require('../commands/channel/replaceChannelsInGroups');
const { clean } = require('../service/prepare/clean');
const formApiResponse = require('../helpers/formApiResponse');
const errorMapper = require('../errors/maps/express');
const getParamFromRequest = require('../helpers/getParamFromRequest');

module.exports = {
  async getChannel(req, res) {
    const { user, userIP, userLocation, userAllIPs, resolved, query: { sid, cid = 0, epg } } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const locale = getParamFromRequest(req, 'locale');
    log.info({
      play: {
        live: {
          user_id: user.id.toString(),
          channel_id: cid || '',
        },
      },
    });

    const result = await errorMapper(_getChannel({
      user,
      userIP,
      userLocation,
      userAllIPs,
      resolved,
      sid,
      cid,
      epg,
      ISP,
      countryCode,
      stateCode,
      req,
      locale,
    }));

    if (result instanceof Error) throw result;

    const responseData = formApiResponse(result, true);

    if (locale) clean(responseData, { streamingServerISP: 'results.othersip' });

    res.json(responseData);
  },

  async getAll(req, res) {
    const { user, query: { hideShows, page = 1, pageSize = 10 } } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { channels, count } = await _getAll({ user, hideShows, locale, pagination: { page, pageSize } });
    const responseData = { result: channels, pagination: { page, pageSize, count } };
    const prepareConfig = {
      channel: {
        path: 'result',
        appended: hideShows ? [] : ['show'],
      },
    };

    if (!hideShows) prepareConfig.schedule = 'result.show';
    if (locale) clean(responseData, prepareConfig);

    res.json(responseData);
  },

  async getInfo(req, res) {
    const { params: { channel }, user } = req;
    const result = await _getInfo({ channelId: channel, user });

    if (!result) throw new HttpError(req.__('Cannot find such channel'), 404);

    const responseData = { error: 0, result };
    clean(responseData, { channel: 'result' });
    res.json(responseData);
  },

  async getGroups(req, res) {
    const { query: { allchannel: allchannelRaw, isradio, hideShows, sort, sortGroup, groupsOnly, noCache }, user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const allchannel = !!parseInt(allchannelRaw || '0', 10);
    const results = await _getGroups({ allchannel, isradio, hideShows, sort, sortGroup, user, locale, groupsOnly, noCache });
    const responseData = allchannel ? results : { error: 0, results };
    const prepareConfig = {
      channel: {
        path: allchannel ? 'channels' : 'results',
        appended: hideShows ? [] : ['show'],
      },
    };

    if (!hideShows) prepareConfig.schedule = allchannel ? 'channels.show' : 'results.show';
    if (locale) clean(responseData, prepareConfig);

    res.json(responseData);
  },

  async rateChannel(req, res) {
    const { user, query, resolved } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { rdatetime = 0, tostar: rating } = query;
    const { channel } = resolved;
    const result = await _rateChannel({ user, channel, rdatetime, rating, locale });
    res.send({ ...result });
  },

  async updateChannelGroups(req, res) {
    const groups = req.body.groups;
    await _updateChannelGroups(groups);
    res.send({ error: 0, result: [] });
  },

  async replaceChannelsInGroups(req, res) {
    const { newItems, oldItems, gid } = req.body.payload;
    await _replaceChannelsInGroups(newItems, oldItems, gid);
    res.json({ error: 0, result: [] });
  },
};
