const { ApiError } = require('@s1/api-errors');
const _set = require('../commands/uaplayer/set');
const _remove = require('../commands/uaplayer/remove');
const _get = require('../commands/uaplayer/get');
const _getAll = require('../commands/uaplayer/getAll');
const md5 = require('../helpers/md5');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const { forWhoConstants } = require('../commands/uaplayer/index');

const getCorrectParam = ({ body, query }, name, defaultParam = '') => {
  const param = body[name] || query[name] || defaultParam;

  return param;
};

module.exports = {
  /**
   * This method sets/gets/removes the UAPlayers.
   *
   * Query
   * @param {string} act - Action name.
   * @param {string} keyid - Secret key to use this endpoint.
   * @param {number} uid - User ID.
   * @param {string} for - Collection name - uaplayer.*for*
   * @param {string} ua - User Agent name.
   *
   * Request Body
   * @param {number} uid - User ID.
   * @param {string} for - Collection name - uaplayer.*for*
   * @param {string} ua - User Agent name.
   * @param {number} playerid - UAPlayer ID.
   * @param {number} flash - version of flash player.
   * @param {string} name - UAPlayer name.
   * @param {number} live -
   * @param {number} record -
   * @param {number} player -
   * @param {string} memo -
   *
   * Actions and Constants 'For':
   * - GET for GLOBAL - returns Array by {ua} in query
   * - GET for DEFAULT - returns Array by {ua ('mdkey' value in collection)} in query
   * - GET for USER - returns Array by {uid, ua} in query
   * - GET_ALL for GLOBAL - returns Array
   * - GET_ALL for DEFAULT - returns first created item in collection
   * - GET_ALL for USER - returns Array by {uid} in query
   * - GET_ALL for GLOBAL_AND_DEFAULT - returns GLOBAL Array & first DEFAULT item
   * - GET_ALL for UA - returns USER Array & GLOBAL Array by {ua} in query
   * - GET_ALL for USER_UA - returns USER Array & User's last log created (live | vod | record) by {uid} in query
   * - SET for GLOBAL - sets UAPlayer with predefined {uid: 0}
   * - SET for DEFAUT - sets UAPlayer with predefined {uid: 0, ua: 'default'}
   * - SET for TEMP_USER - sets UAPlayer with predefined {ua: 'temp', mdkey: md5('temp')}
   * - SET for USER - sets UAPlayer without predefined fields
   * - REMOVE for GLOBAL - deletes UAPlayer with certain {flash, ua} in request body
   * - REMOVE for DEFAULT - deletes UAPlayer with certain {flash, ua ('mdkey' value in collection)} in request body
   * - REMOVE for USER - deletes UAPlayer with certain {uid, flash, ua} in request body
   *
   * SET UAPlayer takes {ua.value} from request body, converts into {md5(ua)} & save into {mdkey} (ex. DEFAULT)
   * SET for DEFAULT:
   * - ua.value is saving into mdkey.value
   * - ua.value become 'default'
   * - SAVING item with: {ua: 'default', mdkey: ua.value}
   *
   * GET UAPlayer takes {ua.value} from query, converts into {md5(ua)} & finds items by {mdkey: md5(ua)} (ex. DEFAULT)
   * GET for DEFAULT by "ua: ua.value"
   * - ua.value become mdkey.value
   * - find in collection by {mdkey: ua.value}
   * - RESPONSE: {ua: 'default', mdkey: ua.value}
   *
   * Minimum data set for UPDATE existing UAPlayer:
   * - GLOBAL: {for, flash, ua, playerid}
   * - DEFAULT: {for, flash, ua, playerid}
   * - TEMP_USER: {for, uid, flash, playerid}
   * - USER: {for, uid, flash, ua, playerid}
  */
  common: async (req, res) => {
    const { query, body, headers, user = {} } = req;
    const { act } = query;

    const forWho = getCorrectParam(req, 'for', 'user');
    const uid = parseInt(getCorrectParam(req, 'uid', user.id || 0));
    const ua = getCorrectParam(req, 'ua', headers['user-agent']).toLowerCase();
    const mdkey = forWhoConstants.DEFAULT === forWho ? ua : md5(ua);
    const requestBody = { ...body, act, uid, ua, forWho, mdkey };
    const actTypes = {
      set: _set,
      remove: _remove,
      get: _get,
      getall: _getAll,
    };

    if (actTypes[act]) {
      try {
        const result = await actTypes[act](requestBody);

        return res.json(formatResponseIDs(result));
      } catch (error) {
        if (error instanceof ApiError) throw error;
      }
    }

    return res.json(null);
  },
};
