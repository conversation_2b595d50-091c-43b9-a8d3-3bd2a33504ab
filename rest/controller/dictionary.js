const getParamFromRequest = require('../helpers/getParamFromRequest');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const Dictionary = require('../model/audb/Dictionary');
const { LastIdModel } = require('../model/audb/LastId');

const DictionaryController = {
  async getData(req, res) {
    const act = getParamFromRequest(req, 'act');
    switch (act) {
      case 'load':
        await DictionaryController.load(req, res);
        break;
      case 'save':
        await DictionaryController.save(req, res);
        break;
      case 'delete':
        await DictionaryController.delete(req, res);
        break;
      default:
        break;
    }
  },

  async getAllDictionary(req, res) {
    const { page = 1, searchString } = req.query;
    const pageSize = req.query.pagesize || 10;
    const query = {};

    if (searchString !== 'undefined') query.he = { $regex: searchString };

    const total = await Dictionary.countDocuments(query);
    const pages = Math.ceil(total / pageSize);
    const response = await Dictionary.find(query).sort({ tword: 1 })
      .limit(pageSize).skip((page - 1) * pageSize)
      .lean();
    res.send({ result: response, pages });
  },

  async load(req, res) {
    const response = await Dictionary
      .find({ id: { $exists: true } })
      .limit(100)
      .exec();

    res.send({ dictionary: formatResponseIDs(response) });
  },

  async save(req, res) {
    const { id } = req.body;
    let newDictionary = { ...req.body };

    const duplicateDictionary = await Dictionary.findOne({ tword: newDictionary.tword }).exec();
    const isNewDictionaryAlreadyExist = duplicateDictionary && duplicateDictionary.id
      && duplicateDictionary.id !== +newDictionary.id;

    if (isNewDictionaryAlreadyExist) throw new Error('Duplicated dictionary');

    let condition = { id };

    if (!id) {
      const nextId = await LastIdModel.getNextId(Dictionary, 'id');
      condition = { id: nextId };
      newDictionary = { ...newDictionary, id: nextId };
    }

    await Dictionary
      .updateOne(condition, { $set: newDictionary }, { upsert: true })
      .exec();
    const response = await Dictionary
      .findOne({ id: newDictionary.id })
      .exec();
    res.send({ dic: response });
  },

  async delete(req, res) {
    const { id } = req.body;

    if (!id) throw new Error('id param is required');

    const response = await Dictionary
      .deleteOne({ id })
      .exec();

    res.send({ id: response.deletedCount ? id : 0 });
  },

  async updateDictionary(req, res) {
    const { _id, ...data } = req.body;

    if (!_id) throw new Error('_id param is required');

    await Dictionary.updateOne({ _id }, { $set: data });
    res.send({ result: true });
  },

  async deleteDictionary(req, res) {
    const { _id } = req.query;

    if (!_id) throw new Error('_id param is required');

    await Dictionary.deleteOne({ _id });
    res.send({ result: true });
  },
};

module.exports = DictionaryController;
