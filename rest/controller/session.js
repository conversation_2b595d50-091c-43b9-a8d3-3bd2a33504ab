const Session = require('../model/audb/Session');
const SessionIdError = require('../errors/index').SessionId;

const SessionController = {
  async loadSession(req, res) {
    try {
      const sessionId = req.sessionID;

      if (sessionId) {
        const response = Session.load(sessionId);
        res.json(response);
      } else res.send(new SessionIdError('No sessionId'));
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = SessionController;
