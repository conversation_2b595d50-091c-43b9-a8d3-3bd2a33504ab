const getParamFromRequest = require('../helpers/getParamFromRequest');
const _getAllUserPermissionGroups = require('../commands/permission/getAllUserPermissionGroups');
const _saveUserPermissionGroup = require('../commands/permission/saveUserPermissionGroup');
const _deleteUserPermissionGroup = require('../commands/permission/deleteUserPermissionGroup');

const permissionController = {
  async getAllUserPermissionGroups(req, res) {
    const result = await _getAllUserPermissionGroups();

    res.send(result);
  },
  async saveUserPermissionGroup(req, res) {
    const { user, body } = req;
    const result = await _saveUserPermissionGroup({ user, data: body });

    res.send(result);
  },
  async deleteUserPermissionGroup(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deleteUserPermissionGroup({ _id });

    res.send(result);
  },
};

module.exports = permissionController;
