const moment = require('moment');
const getCountByChannelID = require('../commands/schedule/getCountByChannelID');
const { getChannelsListWithCountSchedules } = require('../commands/schedule/getCountForAllChannels');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const formatResponseIDs = require('../helpers/formatResponseIDs');

const getDate = (req) => {
  const nowDate = moment().format('YYYY-MM-DD');

  return getParamFromRequest(req, 'dateof', nowDate);
};
const getCurrentIP = (req) => {
  const queryIP = getParamFromRequest(req, 'settoip');

  return queryIP || req.userIP || '';
};
const checkTrustedIP = (currentIP) => {
  const TRUSTED_IPS = ['**************', '**************'];

  return TRUSTED_IPS.includes(currentIP);
};
const getSimulateText = currentIP => `${currentIP} simulate ip<br>`;

/**
 * Returns a list with channels information with the number of schedules or looks for schedules by channel ID.
 *
 * @param {string} keyid - Secret key to manage this controller.
 * @param {string} dateof - The date by which the start and end time of the schedule will be determined - '2021-04-13'
 * @param {string} channel - Channel ID or type for getting all channels - '573' | 'all'
 * @param {string} settoip - IP address to manage this controller. If your IP isn't trusted,
 * you will get an error. The trusted IP addresses listed above. - **************
 * @param {string} detail - The flag that divides information between the total number of schedules and schedules
 * with genre. Response: { total: 10, genre: 7 }. The channel has 10 schedules in total, but only 7 schedules have
 * a genre. If this flag is undefined, you will get the folded value - 17 (10 + 7).
 *
 * channel === 'all'
 * @returns {Promise<[{
 *   id: 573, - Channel ID
 *   total: 10, - Total number schedules for this channel
 *   genre: 7, - Total number schedules with genre field for this channel
 * }, {...}]>}
 *
 * channel === '573'
 * detail === undefined
 * @returns {Promise<'17'>}
 *
 * channel === '573'
 * detail === '1'
 * @returns {Promise<{
 *   total: 10, - Total number schedules for this channel
 *   genre: 7, - Total number schedules with genre field for this channel (the genre field isn't empty)
 * }>}
 */
const QScheduleController = {
  async receiveHandler(req, res) {
    const channelID = getParamFromRequest(req, 'channel', 0);

    try {
      if (channelID === 'all') {
        await QScheduleController.getAll(req, res);
      } else {
        await QScheduleController.getByChannelID(req, res);
      }
    } catch (e) {
      console.error(e);
    }
  },
  async getByChannelID(req, res) {
    const channelID = getParamFromRequest(req, 'channel', 0);
    const isResponseWithDetail = getParamFromRequest(req, 'detail');
    const currentIP = getCurrentIP(req);
    const isTrustedIP = checkTrustedIP(currentIP);
    const simulateText = getSimulateText(currentIP);
    const date = getDate(req);

    if (!isTrustedIP) {
      return res.send(`${currentIP}<br>worm`);
    }

    const {
      totalNumberGroupedSchedule,
      totalNumberGroupedScheduleWithGenre,
      sumBothScheduleGroups,
    } = await getCountByChannelID({ channelID, date });

    // Split the total count and find out the count with genre.
    if (isResponseWithDetail) {
      const detailResponse = JSON.stringify({
        total: totalNumberGroupedSchedule,
        genre: totalNumberGroupedScheduleWithGenre,
      });

      return res.send(`${simulateText}${detailResponse}`);
    }

    res.send(`${simulateText}${sumBothScheduleGroups}`);
  },
  async getAll(req, res) {
    const date = getDate(req);
    const currentIP = getCurrentIP(req);
    const simulateText = getSimulateText(currentIP);
    const isTrustedIP = checkTrustedIP(currentIP);

    if (!isTrustedIP) {
      return res.send(`${currentIP}<br>worm`);
    }

    const channelsListWithCountSchedules = await getChannelsListWithCountSchedules(date);
    const formattedChannels = formatResponseIDs(channelsListWithCountSchedules);
    const parsedChannelsInfo = JSON.stringify(formattedChannels);

    return res.send(`${simulateText}${parsedChannelsInfo}`);
  },
};

module.exports = QScheduleController;
