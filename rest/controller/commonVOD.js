const _fixVODListSource = require('../commands/common/vod/fixVODListSource');
const _saveTime = require('../commands/common/vod/saveTime');
const _loadSSS = require('../commands/common/loadsss');
const _load = require('../commands/common/load');
const _loadSome = require('../commands/common/loadSome');
const _delete = require('../commands/common/delete');
const _save = require('../commands/common/save');
const _saveAll = require('../commands/common/saveAll');
const _saveSSS = require('../commands/common/saveSSS');
const _runIt = require('../commands/common/runIt');

const commonVodController = {
  async commonVOD(req, res) {
    const { act, table: queryTable } = req.query;
    const { table: bodyTable } = req.body;

    const currentTableName = queryTable || bodyTable;
    const requestBody = { ...req.body, table: currentTableName };

    const actTypes = {
      fixvodlistsource: _fixVODListSource,
      savetime: _saveTime,
      loadsss: _loadSSS,
      loadsome: _loadSome,
      runit: _runIt,
      savesss: _saveSSS,
      load: _load,
      save: _save,
      saveall: _saveAll,
      delete: _delete,
    };

    if (actTypes[act]) {
      const result = await actTypes[act](requestBody, 'vdf');
      res.send(result);
    }

    res.send();
  },
};

module.exports = commonVodController;
