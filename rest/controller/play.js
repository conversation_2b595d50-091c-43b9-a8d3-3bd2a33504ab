const _play = require('../commands/play');

async function getItemWithStreams(req, modelKey) {
  const { user, userLocation, userIP, userAllIPs, sid, resolved, flags, isOldApp = false } = req;
  const { ISP, countryCode, stateCode } = userLocation;
  const { item, streams, streamingServers } = await _play({
    resolved,
    flags,
    modelKey,
    user,
    ISP,
    countryCode,
    stateCode,
    userIP,
    userAllIPs,
    sid,
    clientHeaders: req.clIpHeaders,
    isOldApp,
  });
  req.streamingServers = streamingServers;

  return { item, streams };
}

module.exports = resolvedModelKey => ({
  async play(req, res) {
    const { item, streams } = await getItemWithStreams(req, resolvedModelKey);
    const { flags: { withItem } } = req;

    return res.json({
      error: 0,
      result: withItem ? { item, streams } : streams,
    });
  },

  async redirect(req, res) {
    const { params: { index } } = req;
    const { streams } = await getItemWithStreams(req, resolvedModelKey);

    return res.redirect(301, streams[index]);
  },

  async stream(req, res) {
    const { params: { index: indexRaw }, flags: { withItem } } = req;
    const { item, streams } = await getItemWithStreams(req, resolvedModelKey);
    const index = indexRaw % streams.length;

    return res.json({
      error: 0,
      result: withItem ? { item: item.$data, stream: streams[index] } : streams[index],
      total: streams.length,
      current: +index,
    });
  },
});
