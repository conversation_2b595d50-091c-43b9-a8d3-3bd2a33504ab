const { proxy: { proxyCheckStatuses } } = require('@s1/api-constants');
const _detectProxy = require('../commands/proxy/detect');
const _checkProxy = require('../commands/proxy/check');
const { getUserLocation } = require('../service/maxmind');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const _getProxiesList = require('../commands/proxy/getProxiesList');
const _saveProxy = require('../commands/proxy/saveProxy');
const _deleteProxy = require('../commands/proxy/deleteProxy');
const _validateProxy = require('../commands/proxy/validateProxy');
const _getCountryProxyMapList = require('../commands/proxy/getCountryProxyMapList');
const _saveCountryProxyMap = require('../commands/proxy/saveCountryProxyMap');
const _deleteCountryProxyMap = require('../commands/proxy/deleteCountryProxyMap');
const _getProxiesByCountryIp = require('../commands/proxy/getProxiesByCountryIp');

const ProxyController = {
  async detect(req, res) {
    const ip = req.params.ip || req.query.ip;
    const appendLocation = +req.query.location || 0;
    const force = +req.query.force || false;
    const data = await _detectProxy({ ip, appendLocation, force });
    res.send(data);
  },

  // ToDo delete if no-one use this method
  async check(req, res) {
    const { userIP, userAllIPs, userLocation, user } = req;
    const { countryCode, ISP, stateCode, countryName } = userLocation;
    const status = await _checkProxy({ user, userIP, countryName, ISP });
    res.send({
      error: status,
      results: proxyCheckStatuses[status],
      isp: ISP,
      country: countryName,
      ccst: `${countryCode}-${stateCode}`,
      ip: userIP,
      allips: userAllIPs,
    });
  },
  async checkByIP(req, res) {
    const ip = getParamFromRequest(req, 'ip') || req.ip;
    const { countryName, stateCode, countryCode, ISP } = getUserLocation(ip);
    const userStub = { UserConfig: { skipbidproxy: false } };
    const status = await _checkProxy({ user: userStub, userIP: ip, countryName, ISP });
    /**
     * Deletes the User IP because we only need IP info from query.
     * userAllIPs can have IP from headers and connection.
     */
    const allIPs = req.userAllIPs.filter(ip => ip !== req.userIP);

    res.send({
      error: status,
      results: proxyCheckStatuses[status],
      isp: ISP,
      country: countryName,
      ccst: `${countryCode}-${stateCode}`,
      ip,
      allips: allIPs,
    });
  },
  /**
   * Method validate user IP from white and black lists
   *
   * @property {String} ip - user IP from params
   * @returns {Object} { error: 0, results: 'all good', isp: '', country: Australia', 'ccst': 'AU-', 'ip': '' }
   */
  async checkUserIP(req, res) {
    const { params: { ip }, user } = req;
    const userLocation = getUserLocation(ip);
    const { countryCode, ISP, stateCode, countryName } = userLocation;
    const status = await _checkProxy({ user, userIP: ip, countryName, ISP });
    res.send({
      error: status,
      results: proxyCheckStatuses[status],
      isp: ISP,
      country: countryName,
      ccst: `${countryCode}-${stateCode}`,
      ip,
    });
  },
  async getProxiesList(req, res) {
    const result = await _getProxiesList();

    res.json(result);
  },
  async saveProxy(req, res) {
    const { body } = req;
    const result = await _saveProxy(body);

    res.json(result);
  },
  async deleteProxy(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deleteProxy(_id);

    res.json(result);
  },
  async validateProxy(req, res) {
    const { body, userIP } = req;
    const result = await _validateProxy(body, userIP);

    res.json(result);
  },
  async getCountryProxyMapList(req, res) {
    const result = await _getCountryProxyMapList();

    res.json(result);
  },
  async saveCountryProxyMap(req, res) {
    const { body } = req;
    const result = await _saveCountryProxyMap(body);

    res.json(result);
  },
  async deleteCountryProxyMap(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deleteCountryProxyMap(_id);

    res.json(result);
  },
  async getProxiesByCountryIp(req, res) {
    const ip = getParamFromRequest(req, 'ip');
    const result = await _getProxiesByCountryIp(ip);

    res.json(result);
  },
};

module.exports = ProxyController;
