const getParamFromRequest = require('../helpers/getParamFromRequest');
const enablePackage = require('../commands/package/enable');

const paymentController = {
  async enable(req, res) {
    const { params, body } = req;
    const { id } = params;
    const locale = getParamFromRequest(req, 'locale');
    const { enable } = body;
    const result = await enablePackage({ id, enable, locale });
    res.send(result);
  },
};

module.exports = paymentController;
