const _getDeclined = require('../commands/transaction/getDeclined');
const formatResponseIDs = require('../helpers/formatResponseIDs');

const TransactionController = {
  async getDeclined(req, res) {
    const daysLeft = Number(req.body.daysLeft) || Number(req.query.daysLeft) || 10;
    const data = await _getDeclined({ daysLeft });
    res.send(formatResponseIDs(data));
  },
};

module.exports = TransactionController;
