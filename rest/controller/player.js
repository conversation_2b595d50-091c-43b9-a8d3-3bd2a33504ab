const {
  MissingRequestArgumentError,
  MissingPlayerIDLoadError,
  MissingPlayerIDRemoveError,
  ApiError,
} = require('@s1/api-errors');
const _getPlayer = require('../commands/player/get');
const _getAll = require('../commands/player/getAll');
const _getById = require('../commands/player/getById');
const _set = require('../commands/player/set');
const _remove = require('../commands/player/remove');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const { WrongAct } = require('../errors');

const actConstants = {
  GET: 'get',
  GET_ALL: 'getall',
  SET: 'set',
  REMOVE: 'remove',
};
const playerController = {
  /**
   * Return player info for current user
   * @param req
   * @param req.query.flash {number} - Max version of flash player rule.
   * @param req.query.useragent {string} - Useragent of player rule.
   * @param res
   * @returns {Promise<{
   *   errorcode: 0,
   *   results: {object}
   * }>}
   */
  async loadPlayer(req, res) {
    const { user, query, headers } = req;
    const { useragent: userAgentQuery, flash } = query;
    const useragent = userAgentQuery || headers['user-agent'] || '';
    const { player, playerRule } = await _getPlayer({ user, useragent, flash });

    res.send({
      errorcode: 0,
      results: {
        ...playerRule.format({ player, hideIds: true }),
        getuseragent: useragent.toLowerCase(),
      },
    });
  },
  async process(req, res) {
    try {
      const { act } = req.query;
      const playerID = getParamFromRequest(req, 'playerid');

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.GET_ALL:
          result = await _getAll();
          break;
        case actConstants.GET: {
          if (!playerID) throw new MissingPlayerIDLoadError();
          else result = await _getById(playerID);

          break;
        }
        case actConstants.SET: {
          const name = getParamFromRequest(req, 'name');
          const live = getParamFromRequest(req, 'live');
          const record = getParamFromRequest(req, 'record');
          const flash = getParamFromRequest(req, 'flash');
          const playerstyle = getParamFromRequest(req, 'playerstyle');
          const memo = getParamFromRequest('memo');
          const data = {
            name,
            live: live || 0,
            record: record || 0,
            flash: flash || 0,
            playerstyle: playerstyle || 0,
            memo,
          };
          result = await _set(playerID, data);
          break;
        }
        case actConstants.REMOVE: {
          if (!playerID) throw new MissingPlayerIDRemoveError();
          else result = await _remove(playerID);

          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      if (error instanceof ApiError) throw error;

      res.send(error.message);
    }
  },
};

module.exports = playerController;
