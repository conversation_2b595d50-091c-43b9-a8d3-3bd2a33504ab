const { ApiError } = require('@s1/api-errors');
const { WrongAct } = require('../errors');
const VodSettings = require('../model/audb/VodSettings');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const getParamFromRequest = require('../helpers/getParamFromRequest');

/**
 * Returns and changes VodSettings. There is three actions: load, save and getvodinfo (doesn't work).
 *
 * @param {string} act - Action
 * @returns {Promise<object as VodSettings schema>} Total info about vod settings.
 */
module.exports = {
  load: async (req, res, next) => {
    const vodSetting = await VodSettings.findOne({ data: 'vodurl' }).exec();

    if (!vodSetting) {
      return next(new ApiError(99, 'vodurl data doesn`t exist in the `VodSettings` Model'));
    }

    res.send(formatResponseIDs(vodSetting));
  },
  common: async (req, res, next) => {
    const act = getParamFromRequest(req, 'act');

    if (act === 'getvodinfo') {
      /**
       * Early we was sending a request for getting response from http://198.16.100.26/vod-temp/,
       * but it's not available now. This action is a stub.
       */
      return next(new ApiError(99, 'http://198.16.100.26/vod-temp/ are not available.'));
    }
    if (act === 'save') {
      const vodSetting = await VodSettings.findOneAndUpdate({ data: 'vodurl' }, req.body, { upsert: true, new: true }).exec();

      return res.send(formatResponseIDs(vodSetting));
    }

    throw new WrongAct('Act is wrong');
  },
};
