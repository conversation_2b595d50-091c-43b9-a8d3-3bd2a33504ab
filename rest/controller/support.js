const SupporterIP = require('../model/audb/SupporterIP');

const SupportController = {
  async getAllIPs(req, res) {
    const ips = await SupporterIP.find({}).select('-_id -__v').exec();
    res.send(ips);
  },
  async addIP(req, res) {
    const { user } = req;
    const supporter = new SupporterIP({
      ip: req.body.ip,
      created: Math.floor(Date.now() / 1000),
      user_id: user.id,
    });
    supporter.save();
    res.send(supporter);
  },
  async removeIP(req, res) {
    const ip = req.params.ip;
    const result = await SupporterIP.deleteOne({ ip }).exec();
    res.send({
      message: result.n === 0 ? 'Record was not found!' : `${result.n} record was deleted!`,
    });
  },
};

module.exports = SupportController;
