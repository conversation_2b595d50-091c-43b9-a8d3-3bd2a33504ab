const getMainCategoriesCommand = require('../commands/vod/getMainCategories');
const getSubCategoriesCommand = require('../commands/vod/getSubCategories');
const getVodsByCategoryCommand = require('../commands/vod/getByCategory');
const { clean } = require('../service/prepare/clean');
const formApiResponse = require('../helpers/formApiResponse');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const getRelativeVods = require('../commands/vod/getVodRelative');
const settingsService = require('../service/settings');
const Settings = require('../model/audb/Settings');

const CategoriesController = {
  // used for admin panel, do not need localizations
  async getVodRelative(req, res) {
    const category = getParamFromRequest(req, 'vodcate');
    const response = await getRelativeVods(category);
    const formattedResponse = formatResponseIDs(response);

    res.json(formattedResponse);
  },
  async getMainCategories(req, res) {
    const locale = getParamFromRequest(req, 'locale');
    const { ISP, countryCode, stateCode } = req.userLocation;
    const results = await getMainCategoriesCommand({ locale, ISP, countryCode, stateCode });
    const responseData = formApiResponse(results, true);

    if (locale) clean(responseData, { category: { path: 'results', appended: ['cat_id'] } });

    res.json(responseData);
  },
  async getSubCategories(req, res) {
    const { user, query, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const settings = await settingsService.get();
    const defaultPageSize = settings.ui.seriesPageSize || Settings.schema.obj.ui.seriesPageSize.default;
    const { id, hf, page, pagesize: pageSize = defaultPageSize, mo } = query;
    const locale = getParamFromRequest(req, 'locale');
    const results = await getSubCategoriesCommand({
      user,
      id,
      hf,
      locale,
      page,
      pageSize,
      ISP,
      countryCode,
      stateCode,
      mo,
    });
    const responseData = { error: 0, results };
    const cleanConfig = {
      category: {
        path: ['results.cate', 'results.upper', 'results.subetcs.sons'],
        appended: ['cat_id', 'path'],
      },
    };

    if (results.show) cleanConfig.category.path.push('results.show');
    if (locale) clean(responseData, cleanConfig);

    res.json(responseData);
  },
  async getMoviesByCategory(req, res) {
    const { user, query, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { hf } = query;
    const locale = getParamFromRequest(req, 'locale');
    const search = getParamFromRequest(req, 'search', null);
    const page = parseInt(query.page, 10);
    const sortNeeded = parseInt(query.sort, 10) > 0;
    const gs = parseInt(query.gs, 10);
    const pageSize = parseInt(query.pagesize || query.pageSize, 10) || 30;
    const rawId = query.id.toString();
    const mo = parseInt(query.mo, 10);
    const isVdfPage = parseInt(query.vdf, 10);
    const { result, vodmsPagination } = await getVodsByCategoryCommand({
      rawId,
      user,
      hf,
      locale,
      page,
      sortNeeded,
      gs,
      pageSize,
      mo,
      ISP,
      countryCode,
      stateCode,
      search,
      isVdfPage,
    });
    const responseData = formApiResponse(result, true, { pagination: vodmsPagination });

    if (locale)
      clean(responseData, {
        category: {
          path: ['results.cate', 'results.upper'],
          appended: ['cat_id'],
        },
        vod: 'results.subshows.vodms',
      });

    res.json(responseData);
  },
};

module.exports = CategoriesController;
