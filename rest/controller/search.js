const searchCommand = require('../commands/search');
const searchPrepareConfig = require('../helpers/searchPrepareConfig');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const { clean } = require('../service/prepare/clean');

module.exports = {
  async search(req, res) {
    const { user, query, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { act, page = query.page || 1 } = query;
    const locale = getParamFromRequest(req, 'locale');
    const searchString = query.key || '';
    const key = searchString.replace(/([.\\^$()*+:!?<{}[\]])/g, '\\$1');
    const types = act === 'all'
      ? ['record', 'vod', 'channel', 'radio']
      : (act.search(',') !== -1 ? act.split(',') : [act]);
    const searchPromises = types
      .map(type => searchCommand({ key, type, locale, user, page, ISP, countryCode, stateCode }));
    const searchResults = await Promise.all(searchPromises);
    const results = types
      .reduce((results, type, index) => Object.assign(results, { [type]: searchResults[index] }), { page });

    if (locale) clean(results, searchPrepareConfig);

    return res.send({ errorcode: 0, results });
  },
};
