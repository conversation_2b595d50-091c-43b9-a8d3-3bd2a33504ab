const { MissingRequestArgumentError } = require('@s1/api-errors');
const _getActiveNotices = require('../commands/notice/getActive');
const _load = require('../commands/notice/load');
const _save = require('../commands/notice/save');
const _delete = require('../commands/notice/delete');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const { WrongAct } = require('../errors');

const actConstants = {
  SAVE: 'save',
  DELETE: 'delete',
};

const NoticeController = {
  async getActive(req, res) {
    const locale = getParamFromRequest(req, 'locale');
    const notices = await _getActiveNotices(locale);
    res.json(formatResponseIDs(notices));
  },

  async getData(req, res) {
    try {
      const notices = await _load();
      res.send(formatResponseIDs(notices));
    } catch (error) {
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.SAVE:
          result = await _save(req.body);
          break;
        case actConstants.DELETE: {
          const id = getParamFromRequest(req, 'id');

          if (id) result = await _delete(id);
          else result = new MissingRequestArgumentError('id');

          break;
        }
        default:
          throw new WrongAct('act is wrong');
      }

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = NoticeController;
