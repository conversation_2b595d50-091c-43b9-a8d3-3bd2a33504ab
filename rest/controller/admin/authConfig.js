const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../../errors');
const AuthConfig = require('../../model/audb/AuthConfig');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

const actConstants = {
  LOAD: 'load',
  SAVE: 'save',
  REMOVE: 'remove',
};
const AuthConfigController = {
  async loadData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.LOAD: {
          const allConfigs = await AuthConfig.loadAll();
          result = formatResponseIDs(allConfigs);
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
  async modifyData(req, res) {
    try {
      const { act } = req.query;
      const data = req.body;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.SAVE: {
          const savedConfig = await AuthConfig.saveData(data);
          result = formatResponseIDs(savedConfig);
          break;
        }
        case actConstants.REMOVE: {
          result = await AuthConfig.removeData(data._id);
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
};

module.exports = AuthConfigController;
