const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../../errors');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const Portal = require('../../model/audb/Portal');

const actConstants = {
  LOAD: 'load',
  SAVE: 'save',
  DELETE: 'delete',
};
const PortalsController = {
  async loadData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;
      switch (act) {
        case actConstants.LOAD: {
          const data = await Portal.getAll();
          result = { portals: formatResponseIDs(data) };
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
  async modifyData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result = {};
      switch (act) {
        case actConstants.LOAD: {
          const data = await Portal.getAll();
          result = { portals: formatResponseIDs(data) };
          break;
        }
        case actConstants.SAVE: {
          const data = req.body;
          const agent = getParamFromRequest(req, 'agent');

          const existingPortal = await Portal.checkExisting(agent);

          if (existingPortal && existingPortal.id !== data.id) {
            result = { errorcode: 'ERROR_CODE_DUPLICATED' };
            break;
          }

          const savedPortal = await Portal.saveOne(data);

          result = { portal: savedPortal };

          break;
        }
        case actConstants.DELETE: {
          const id = getParamFromRequest(req, 'id');

          if (id !== '') result = await Portal.deleteOne(id);
          else result = new MissingRequestArgumentError('id');

          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
};

module.exports = PortalsController;
