const _getAllEmployees = require('../../commands/employee/getAllEmployees');
const _employeeStartTracker = require('../../commands/employee/startTracker');
const _employeeStopTracker = require('../../commands/employee/stopTracker');
const _employeeTrackerConfirmInteraction = require('../../commands/employee/trackerConfirmInteraction');
const getOneEmployeeActivities = require('../../commands/employee/getOneEmployeeActivities');
const getAllEmployeesActivities = require('../../commands/employee/getAllEmployeesActivities');
const _employeeTrackerInfo = require('../../commands/employee/getTrackerInfo');
const _getLastComments = require('../../commands/employee/getLastComments');
const getOneEmployeeConfig = require('../../commands/employee/getOneEmployeeConfig');
const updateOneEmployeeConfig = require('../../commands/employee/updateOneEmployeeConfig');
const _saveEmployeeActivity = require('../../commands/employee/saveEmployeeActivity');
const _deleteEmployeeActivity = require('../../commands/employee/deleteEmployeeActivity');
const _getAwaitingApprovalActivities = require('../../commands/employee/getAwaitingApprovalActivities');
const _activityAction = require('../../commands/employee/activityAction');
const _getAllEmployeesPaymentHistory = require('../../commands/employee/getAllEmployeesPaymentHistory');
const _getAllEmployeesPaymentHistoryReport = require('../../commands/employee/getAllEmployeesPaymentHistoryReport');
const _saveEmployeePaymentHistory = require('../../commands/employee/saveEmployeePaymentHistory');
const _getEmployeePaymentInfo = require('../../commands/employee/getEmployeePaymentInfo');
const _getEmployeeGeneralConfig = require('../../commands/employee/getEmployeeGeneralConfig');
const _saveEmployeeGeneralConfig = require('../../commands/employee/saveEmployeeGeneralConfig');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

const AdminEmployeeController = {
  async getAllEmployees(req, res) {
    const result = await _getAllEmployees();

    res.json(result);
  },
  async employeeStartTracker(req, res) {
    const { user, body } = req;
    const { comment } = body;
    const result = await _employeeStartTracker(user, comment);

    res.json(result);
  },
  async employeeStopTracker(req, res) {
    const { id } = req.params;
    const { user } = req;
    const userId = id ? parseInt(id) : user.id;
    const result = await _employeeStopTracker(user, userId);

    res.json(result);
  },
  async employeeTrackerInfo(req, res) {
    const { user } = req;
    const result = await _employeeTrackerInfo(user);

    res.json(result);
  },
  async getLastComments(req, res) {
    const { user } = req;
    const result = await _getLastComments(user);

    res.json(result);
  },
  async employeeTrackerConfirmInteraction(req, res) {
    const { user } = req;
    const result = await _employeeTrackerConfirmInteraction(user);

    res.json(result);
  },
  async allEmployeesActivities(req, res) {
    const { body } = req;
    const result = await getAllEmployeesActivities(body);

    res.json(result);
  },
  async employeeActivities(req, res) {
    const { id } = req.params;
    const { user, body } = req;
    const userId = id ? parseInt(id) : user.id;
    const result = await getOneEmployeeActivities(body, userId);

    res.json(result);
  },
  async saveEmployeeActivity(req, res) {
    const { id } = req.params;
    const { user, body } = req;
    const userId = parseInt(id);
    const result = await _saveEmployeeActivity(body, userId, user);

    res.json(result);
  },
  async deleteEmployeeActivity(req, res) {
    const { user } = req;
    const _id = getParamFromRequest(req, '_id');
    const result = await _deleteEmployeeActivity(_id, user);

    res.json(result);
  },
  async getAwaitingApprovalActivities(req, res) {
    const result = await _getAwaitingApprovalActivities();

    res.json(result);
  },
  async activityAction(req, res) {
    const { body, user } = req;
    const result = await _activityAction(body, user);

    res.json(result);
  },
  async getAllEmployeesPaymentHistory(req, res) {
    const { body, user } = req;
    const result = await _getAllEmployeesPaymentHistory(body, user);

    res.json(result);
  },
  async getAllEmployeesPaymentHistoryReport(req, res) {
    const { body, user } = req;
    const result = await _getAllEmployeesPaymentHistoryReport(body, user);

    res.json(result);
  },
  async getEmployeesPaymentHistory(req, res) {
    const { body, user } = req;
    const { id } = req.params;

    // eslint-disable-next-line no-mixed-operators
    if (!id || !user.isadmin && !user.issuperadmin && parseInt(id) !== user.id) res.json({
      error: 0,
      paymentHistories: [],
    });

    const result = await _getAllEmployeesPaymentHistory(body, user);

    res.json(result);
  },
  async getEmployeesPaymentHistoryReport(req, res) {
    const { body, user } = req;
    const { id } = req.params;

    // eslint-disable-next-line no-mixed-operators
    if (!id || !user.isadmin && !user.issuperadmin && parseInt(id) !== user.id) res.json({
      error: 0,
      paymentHistories: [],
      headers: [],
    });

    const result = await _getAllEmployeesPaymentHistoryReport(body, user);

    res.json(result);
  },
  async saveEmployeePaymentHistory(req, res) {
    const { body, user } = req;
    const result = await _saveEmployeePaymentHistory(body, user);

    res.json(result);
  },
  async getEmployeePaymentInfo(req, res) {
    const { body } = req;
    const result = await _getEmployeePaymentInfo(body);

    res.json(result);
  },
  async getEmployeeGeneralConfig(req, res) {
    const result = await _getEmployeeGeneralConfig();

    res.json(result);
  },
  async saveEmployeeGeneralConfig(req, res) {
    const { body } = req;
    const result = await _saveEmployeeGeneralConfig(body);

    res.json(result);
  },
  async getEmployeeConfig(req, res) {
    const { id } = req.params;
    const result = await getOneEmployeeConfig(id);

    res.json(result);
  },
  async updateEmployeeConfig(req, res) {
    const result = await updateOneEmployeeConfig(req.body);

    res.json(result);
  },
};

module.exports = AdminEmployeeController;
