const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const _searchEntitiesForPoster = require('../../commands/poster/searchEntitiesForPoster');

module.exports = {
  async searchMovie(req, res) {
    const { name, id } = req.query;
    const results = await _searchEntitiesForPoster({ entityModel: Vod, name, id });
    res.json(results);
  },
  async searchTvShow(req, res) {
    const { name, id } = req.query;
    const results = await _searchEntitiesForPoster({ entityModel: Category, name, id });
    res.json(results);
  },
};
