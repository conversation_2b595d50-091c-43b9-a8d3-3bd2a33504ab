const { HttpError } = require('@s1/api-errors');
const proxy = require('../../service/proxies');
const whitelist = require('../../service/whitelist');
const Whitelist = require('../../model/audb/Whitelist');
const WhitelistISP = require('../../model/audb/WhitelistISP');
const UserProxyCount = require('../../model/audb/UserProxyCount');
const User = require('../../model/audb/User');
const UserProxyNetwork = require('../../model/audb/UserProxyNetwork');
const cleanCache = require('../../helpers/cleanCache');

const AdminProxyController = {
  async getNetworks(req, res) {
    const { page, limit } = req.query;
    const aggregation = UserProxyNetwork.aggregate()
      .unwind('user_id')
      .group({
        _id: '$_id',
        users_count: { $sum: 1 },
        user_id: { $addToSet: '$user_id' },
        proxyNetworkIp: { $first: '$proxyNetworkIp' },
        count: { $first: '$count' },
      })
      .sort({ count: -1 });
    const result = await UserProxyNetwork.aggregatePaginate(aggregation, { page, limit });
    await Promise.all(result.data.map(network => new Promise(async (resolve, reject) => {
      try {
        const networkModel = new UserProxyNetwork(network);
        network.info = networkModel.info;
        network.whitelisted = await networkModel.isWhitelisted();
        await networkModel.populate(User.foreignPopulateOptions('users')).execPopulate();
        network.users = networkModel.users;
      } catch (e) {
        reject(e);
      }
      resolve();
    })));
    res.json({ result });
  },
  async getUsers(req, res) {
    const { page, limit } = req.query;
    const countAggregation = UserProxyCount.aggregate()
      .match({ proxy: { $gt: 0 } })
      .project({
        total: 1, user_id: 1, proxy: 1, percentage: { $multiply: [{ $divide: ['$proxy', '$total'] }, 100] },
      })
      .sort({ proxy: -1 });
    const result = await UserProxyCount.aggregatePaginate(countAggregation, { page, limit });
    let data = await UserProxyCount.populate(result.data, {
      path: 'networks',
      select: 'proxyNetworkIp info',
      populate: User.foreignPopulateOptions(),
    });
    data = await UserProxyCount.populate(data, User.foreignPopulateOptions('user'));
    result.data = data;
    await Promise
      .all(data.map(count => Promise.all(count.networks.map(network => new Promise(async (resolve, reject) => {
        try {
          network.whitelisted = await network.isWhitelisted();
        } catch (e) {
          reject(e);
        }
        resolve();
      })))));
    res.json({ result });
  },
  async getUser(req, res) {
    const { userId } = req.params;
    const countAggregation = UserProxyCount.aggregate()
      .match({ proxy: { $gt: 0 }, user_id: parseInt(userId, 10) })
      .project({
        total: 1, user_id: 1, proxy: 1, percentage: { $multiply: [{ $divide: ['$proxy', '$total'] }, 100] },
      });
    let result = await UserProxyCount.aggregatePaginate(countAggregation, { page: 1, limit: 1 });
    const data = await UserProxyCount.populate(result.data, {
      path: 'networks',
      select: 'proxyNetworkIp info',
      populate: User.foreignPopulateOptions(),
    });
    result = data[0];
    await Promise.all(result.networks.map(network => new Promise(async (resolve, reject) => {
      try {
        network.whitelisted = await network.isWhitelisted();
      } catch (e) {
        reject(e);
      }
      resolve();
    })));
    res.json({ result });
  },
  async getWhitelistIP(req, res) {
    const { query: { search } } = req;
    const searchRegex = new RegExp(search, 'i');
    const ipList = await Whitelist.find({ st: 'ipopen', _id: searchRegex });
    res.json({ error: 0, result: ipList });
  },
  async getWhitelistISP(req, res) {
    const { query: { search } } = req;
    const searchRegex = new RegExp(search, 'i');
    const ispList = await WhitelistISP.find({ status: 'ispopen', isp: searchRegex });
    res.json({ error: 0, result: ispList });
  },
  async addIPToWhitelist(req, res) {
    const { body: { cidr } } = req;
    await Whitelist.create({
      _id: cidr,
      st: 'ipopen',
      bt: 0,
    });

    cleanCache('whitelist');
    res.json({ error: 0, result: true });
  },
  async addISPToWhitelist(req, res) {
    const { body: { isp }, user } = req;
    await WhitelistISP.updateOne(
      { isp },
      { isp, status: 'ispopen', created_by: user.id },
      { upsert: true },
    );

    cleanCache('whitelistisp');
    res.json({ error: 0, result: true });
  },
  async checkIpInList(req, res, next) {
    const { query: { ip } } = req;
    const list = req.path === '/proxy/whitelist' ? whitelist : req.path === '/proxy' ? proxy : undefined;
    try {
      const result = await list.check(ip);
      res.json({ error: 0, result: !!result });
    } catch (error) { return next(error); }
  },
  async deleteIPFromWhitelist(req, res, next) {
    const { params: { id } } = req;
    const doc = await Whitelist.findOne({ _id: id });

    if (!doc) return next(new HttpError(`No record found #${id}`, 404));

    await doc.remove();
    cleanCache('whitelist');
    res.json({ error: 0, result: true });
  },
  async deleteISPFromWhitelist(req, res, next) {
    const { params: { id } } = req;
    const doc = await WhitelistISP.findOne({ _id: id });

    if (!doc) return next(new HttpError(`No record found #${id}`, 404));

    await doc.remove();
    cleanCache('whitelistisp');
    res.json({ error: 0, result: true });
  },
  async getNetwork(req, res, next) {
    const { params: { id: networkId } } = req;
    try {
      const network = await UserProxyNetwork.findOne({ _id: networkId }, { __v: 0 })
        .populate({
          path: 'users',
          select: '_id id em expires package email status Package Freeze config proxyCounts proxyNetworks',
          populate: [
            { path: 'config', options: { lean: true } },
            { path: 'Freeze', options: { lean: true } },
            { path: 'Package', select: 'id pgid name description price pricestr days epricestr -_id', options: { lean: true } },
          ],
          options: { lean: true },
        })
        .exec();
      network.whitelisted = await network.isWhitelisted();
      res.json({ error: 0, result: network });
    } catch (error) { return next(error); }
  },
};

module.exports = AdminProxyController;
