const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../../errors');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _getAll = require('../../commands/blockemailold/getAll');
const _save = require('../../commands/blockemailold/save');
const _remove = require('../../commands/blockemailold/remove');
const _crawl = require('../../commands/blockemailold/crawl');

const actConstants = {
  CRAWL: 'crawl',
  SAVE_ALL: 'saveall',
  SAVE: 'save',
  REMOVE: 'remove',
};

const BlockEmailOldAdminController = {
  async getData(req, res) {
    try {
      const load = await _getAll();
      const result = formatResponseIDs(load);
      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.CRAWL: {
          // in this case it takes list of emails from other resources
          const url = getParamFromRequest(req, 'url');
          result = await _crawl(url);
          break;
        }
        case actConstants.SAVE: {
          const save = await _save(req.body);
          result = formatResponseIDs(save);
          break;
        }
        case actConstants.SAVE_ALL: {
          const save = await _save(req.body);
          result = formatResponseIDs(save);
          break;
        }
        case actConstants.REMOVE: {
          const removeId = getParamFromRequest(req, '_id');
          const _id = removeId.$id || removeId;
          const remove = await _remove(_id);
          result = formatResponseIDs(remove);
          break;
        }
        default: {
          throw new WrongAct('Act is wrong');
        }
      }

      res.json(result);
    } catch (error) {
      if (error.message === 'crawlRequestError') {
        return res.json({ data: 'ERROR' });
      }

      res.send(error.message);
    }
  },
};

module.exports = BlockEmailOldAdminController;
