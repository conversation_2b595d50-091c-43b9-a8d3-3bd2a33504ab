const { MissingRequestArgumentError, WrongRequestArgumentError } = require('@s1/api-errors');
const Whitelist = require('../../model/audb/Whitelist');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

const actConstants = {
  LOAD: 'load',
  SAVE: 'save',
  REMOVE: 'remove',
};
const loadAll = async () => {
  const result = await Whitelist.find({}).lean().exec();

  return formatResponseIDs(result);
};
const saveData = async (data) => {
  const { bt, st, _id } = data;
  const result = await Whitelist.findOneAndUpdate(
    { _id },
    { $set: { st, bt: parseInt(bt) } },
    { new: true, upsert: true },
  ).lean().exec();

  return formatResponseIDs(result);
};
const deleteData = async (_id) => {
  await Whitelist.deleteOne({ _id }).exec();

  return [true];
};
const WhiteIpListController = {
  async loadData(req, res) {
    try {
      const { act } = req.query;
      let result;

      if (act) switch (act) {
        case actConstants.LOAD:
          result = await loadAll();
          break;
        default:
          result = new WrongRequestArgumentError("Query 'act' should be 'load'");
      }
      else result = new MissingRequestArgumentError("Query 'act' is not defined");

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },
  async modifyData(req, res) {
    try {
      const { act } = req.query;
      let result;

      if (act) switch (act) {
        case actConstants.SAVE:
          result = await saveData(req.body);
          break;
        case actConstants.REMOVE:
          result = await deleteData(req.body._id);
          break;
        default:
          result = new WrongRequestArgumentError("Query 'act' should be 'save' or 'remove'");
      }
      else result = new MissingRequestArgumentError("Query 'act' is not defined");

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = WhiteIpListController;
