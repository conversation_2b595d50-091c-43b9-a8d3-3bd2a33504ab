const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../../errors');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _imapi = require('../../commands/headercachesettings/imapi');
const _getAll = require('../../commands/headercachesettings/getAll');
const _save = require('../../commands/headercachesettings/save');
const _remove = require('../../commands/headercachesettings/remove');

const actConstants = {
  IMAPI: 'imapi',
  LOAD: 'load',
  SAVE: 'save',
  REMOVE: 'remove',
};

const APIHeaderCacheSettingsController = {
  async getData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.IMAPI: {
          // ----------------------------------------------------------------------------
          // In old api version it compares files from folder (auth/api) with IGNORE_ARRAY
          // (you can check IGNORE_ARRAY in /constants/headerCacheSettings.js)
          // and then checks if there are files not from this IGNORE_ARRAY it creates new
          // object in DB with property 'codename' as a file name;
          // -----------------------------------------------------------------------------
          const codename = getParamFromRequest(req, 'codename');
          const imapi = _imapi(codename);
          result = formatResponseIDs(imapi);
          break;
        }
        case actConstants.LOAD: {
          const load = await _getAll();
          result = formatResponseIDs(load);
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }

      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      let result;

      switch (act) {
        case actConstants.SAVE: {
          const save = await _save(req.body);
          result = formatResponseIDs(save);
          break;
        }
        case actConstants.REMOVE: {
          const id = getParamFromRequest(req, 'id');

          if (id !== '') {
            const remove = await _remove(parseInt(id));
            result = formatResponseIDs(remove);
          } else {
            result = new MissingRequestArgumentError('id');
          }

          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }

      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
};

module.exports = APIHeaderCacheSettingsController;
