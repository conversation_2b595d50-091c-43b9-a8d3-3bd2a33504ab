const log = require('@s1/log').create(__filename);
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _getSuspendedList = require('../../commands/suspend/get');
const _getSuspendedCardNumbers = require('../../commands/suspend/getCardNumbers');
const _saveSuspendedList = require('../../commands/suspend/save');
const _deleteSuspendedList = require('../../commands/suspend/delete');
const _deleteSuspendedCard = require('../../commands/suspend/deleteCardNumber');
const _deleteSuspendedUser = require('../../commands/suspend/deleteUser');
const _getSuspendedUserRelationsList = require('../../commands/suspend/getSuspendedUserRelationsList');
const suspended = require('../../constants/suspended');
const _moveSuspendedCardToWhitelist = require('../../commands/suspend/moveSuspendedCardToWhitelist');
const _getWhitelistCards = require('../../commands/suspend/getWhitelistCards');
const _saveWhitelistCard = require('../../commands/suspend/saveWhitelistCard');
const _deleteWhitelistCard = require('../../commands/suspend/deleteWhitelistCard');
const _getSuspendedUserRelationsGraph = require('../../commands/suspend/getSuspendedUserRelationsGraph');

const AdminSuspendController = {
  async getSuspended(req, res) {
    const type = getParamFromRequest(req, 'type');
    let result = {
      error: 0,
      result: [],
    };

    switch (type) {
      case suspended.suspendedTypes.uid:
        result = await _getSuspendedList(type);
        break;
      case 'cardNumber':
        result = await _getSuspendedCardNumbers();
        break;
      default:
        break;
    }

    res.json(result);
  },
  async saveSuspended(req, res) {
    const { body, user } = req;
    const result = await _saveSuspendedList(body, user);

    res.json(result);
  },
  async deleteSuspended(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const { user } = req;
    const result = await _deleteSuspendedList({ _id, adminUser: user });

    res.json(result);
  },
  async deleteSuspendedUser(req, res) {
    const userId = getParamFromRequest(req, 'id');
    const { user } = req;
    const result = await _deleteSuspendedUser({ userId, adminUser: user });

    res.json(result);
  },
  async deleteSuspendedCard(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteSuspendedCard({ number });

    res.json(result);
  },
  async getSuspendedUserRelations(req, res) {
    const { params, query } = req;
    const userId = params.id ? params.id : query.id;
    const result = await _getSuspendedUserRelationsList(parseInt(userId));

    res.json(result);
  },
  async getSuspendedUserRelationsGraph(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getSuspendedUserRelationsGraph(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'suspend/user-relations/id/graph';
    const requestData = {
      eventName,
      data: { ...result, sid, userId: parseInt(userId) },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async moveSuspendedCardToWhitelist(req, res) {
    const { body, user } = req;
    const result = await _moveSuspendedCardToWhitelist(body, user);

    res.json(result);
  },
  async getWhitelistCards(req, res) {
    const result = await _getWhitelistCards();

    res.json(result);
  },
  async saveWhitelistCard(req, res) {
    const { body, user } = req;
    const result = await _saveWhitelistCard(body, user);

    res.json(result);
  },
  async deleteWhitelistCard(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteWhitelistCard(number);

    res.json(result);
  },
};

module.exports = AdminSuspendController;
