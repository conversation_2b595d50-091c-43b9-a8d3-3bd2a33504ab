const { ObjectId } = require('mongoose').Types;
const { WrongRequestArgumentError, MissingRequestArgumentError } = require('@s1/api-errors');
const StreamingServerGroup = require('../../model/audb/StreamingServerGroup');
const StreamingServerMap = require('../../model/audb/StreamingServerMap');
const StreamingServerMapAuto = require('../../model/audb/StreamingServerMapAuto');
const StreamingServerState = require('../../model/audb/StreamingServerState');
const StreamingServerISP = require('../../model/audb/StreamingServerISP');
const StreamingServerCountry = require('../../model/audb/StreamingServerCountry');
const StreamingServerLoadBalancer = require('../../model/audb/StreamingServerLoadBalancer');
const UserConfig = require('../../model/audb/UserConfig');

const actionConstants = {
  LOAD_URGENT_NOW: 'loadurgentnow',
  DELETE_URGENT_NOW: 'deleteurgentnow',
  SAVE_URGENT_NOW: 'saveurgentnow',
  LOAD_URGENT: 'loadurgent',
  SAVE_URGENT: 'saveurgent',
  LOAD_US: 'loadus',
  SAVE_US: 'saveus',
  DELETE_US: 'deleteus',
  LOAD_COUNTRY: 'loadcountry',
  SAVE_COUNTRY: 'savecountry',
  DELETE_COUNTRY: 'deletecountry',
  LOAD_ISP: 'loadisp',
  SAVE_ISP: 'saveisp',
  DELETE_ISP: 'deleteisp',
  LOAD: 'load',
  SAVE_ALL: 'saveall',
  SAVE: 'save',
  DELETE: 'delete',
  CHECK_TRAFFIC: 'checkTraffic',
  LOAD_LOAD_BALANCER: 'loadLoadBalancer',
  SAVE_LOAD_BALANCER: 'saveLoadBalancer',
  DELETE_LOAD_BALANCER: 'deleteLoadBalancer',
  LOAD_OVER_LIMITED: 'loadOverLimited',
};
const StreamingServer = {
  async getAll(req, res) {
    const result = await StreamingServerGroup.find({}).lean().cache(100).exec();

    return res.json({ error: 0, result });
  },
  async clearUsersStreamingServer(req, res) {
    const { id } = req.params;
    const objectId = ObjectId(id);
    try {
      await UserConfig.updateMany({ speed: objectId }, { speed: null }).exec();

      return res.json({ error: 0 });
    } catch (e) {
      return res.json({ error: e.toString() });
    }
  },
  async loadData(req, res) {
    try {
      const { action } = req.query;
      let result;

      if (action) {
        switch (action) {
          case actionConstants.LOAD_URGENT_NOW:
            result = await StreamingServerMap.loadAll();
            break;
          case actionConstants.LOAD_URGENT:
            result = await StreamingServerMapAuto.loadAll();
            break;
          case actionConstants.LOAD_US:
            result = await StreamingServerState.loadAll();
            break;
          case actionConstants.LOAD_COUNTRY:
            result = await StreamingServerCountry.loadAll();
            break;
          case actionConstants.LOAD_ISP:
            result = await StreamingServerISP.loadAll();
            break;
          case actionConstants.LOAD:
            result = await StreamingServerGroup.loadAll();
            break;
          case actionConstants.LOAD_LOAD_BALANCER:
            result = await StreamingServerLoadBalancer.loadAll();
            break;
          case actionConstants.LOAD_OVER_LIMITED:
            result = await StreamingServerGroup.loadOverlimitedServers();
            break;
          default:
            return res.send(new WrongRequestArgumentError('action'));
        }
      } else {
        return res.send(new MissingRequestArgumentError('action'));
      }

      return res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { action, who } = req.query;
      const options = req.body;

      if (options._id) {
        options._id = (options._id instanceof Object) && options._id.$id
          ? options._id.$id : options._id;
      }
      if (options.map) {
        options.map = (options.map instanceof Object) && options.map.$id
          ? options.map.$id : options.map;
      }

      let result;

      if (action) {
        switch (action) {
          case actionConstants.SAVE_URGENT_NOW:
            result = await StreamingServerMap.saveOne(options);
            break;
          case actionConstants.DELETE_URGENT_NOW:
            result = await StreamingServerMap.removeOne(options._id);
            break;
          case actionConstants.SAVE_URGENT:
            result = await StreamingServerMapAuto.saveOne(options);
            break;
          case actionConstants.SAVE_US:
            result = await StreamingServerState.saveOne(options);
            break;
          case actionConstants.DELETE_US:
            result = await StreamingServerState.removeOne(options._id);
            break;
          case actionConstants.SAVE_COUNTRY:
            result = await StreamingServerCountry.saveOne(options);
            break;
          case actionConstants.DELETE_COUNTRY:
            result = await StreamingServerCountry.removeOne(options._id);
            break;
          case actionConstants.SAVE_ISP:
            result = await StreamingServerISP.saveOne(options);
            break;
          case actionConstants.DELETE_ISP:
            result = await StreamingServerISP.removeOne(options._id);
            break;
          case actionConstants.SAVE_ALL:
            result = await StreamingServerGroup.saveGroupAllOptions(options);
            break;
          case actionConstants.SAVE:
            result = await StreamingServerGroup.saveOptions(options);
            break;
          case actionConstants.DELETE:
            result = await StreamingServerGroup.deleteGroup(who, options._id);
            break;
          case actionConstants.CHECK_TRAFFIC:
            result = await StreamingServerGroup.checkTraffic(options);
            break;
          case actionConstants.SAVE_LOAD_BALANCER:
            result = await StreamingServerLoadBalancer.saveOne(options);
            break;
          case actionConstants.DELETE_LOAD_BALANCER:
            result = await StreamingServerLoadBalancer.removeOne(options);
            break;
          default:
            return res.send(new WrongRequestArgumentError('action'));
        }
      } else return res.send(new MissingRequestArgumentError('action'));

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = StreamingServer;
