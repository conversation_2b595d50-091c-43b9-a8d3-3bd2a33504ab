const {
  addUserBonusDays,
  adminLog,
  anotherTrial,
  changeUserPackage,
  changeUserTime,
  checkTrial,
  comment,
  deleteUser,
  forgetPassword,
  getUserCanPayTypes,
  load,
  updateUser,
  updateBufferSize,
  stat,
  search,
  resetBufferSize,
  loadStreamIP,
  loadHistory,
  paymentCanPay,
  paymentCanPayWithCard,
  deleteDevice,
  adminFavoriteUsers,
} = require('../../commands/userCenter');

module.exports = {
  common: async (req, res, next) => {
    const { query } = req;
    const { act } = query;

    const actionTypes = {
      deletedevice: deleteDevice,
      deleteuser: deleteUser,
      updateuser: updateUser,
      anothertrial: anotherTrial,
      changeuserpackage: changeUserPackage,
      changeusertime: changeUserTime,
      checktrial: checkTrial,
      updatebuffersize: updateBufferSize,
      resetbuffersize: resetBufferSize,
      loadsip: loadStreamIP,
      loadhistory: loadHistory,
      adminlog: adminLog,
      adminfavoriteusers: adminFavoriteUsers,
      forget: forgetPassword,
      search,
      stat,
      load,
      comment,
      paymentcanpay: paymentCanPay,
      canpaytypes: getUserCanPayTypes,
      paymentcanpaycard: paymentCanPayWithCard,
      addbonusdays: addUserBonusDays,
    };

    if (actionTypes[act]) {
      const result = await actionTypes[act](req, res, next);

      return res.json(result);
    }

    return res.json(null);
  },
};
