const log = require('@s1/log').create(__filename);
const memoizee = require('memoizee');
const { HttpError } = require('@s1/api-errors');
const UserFreeze = require('../../model/audb/UserFreeze');
const User = require('../../model/audb/User');
const UserConfig = require('../../model/audb/UserConfig');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserLogLogin = require('../../model/audb/UserLogLogin');
const PaymentLog = require('../../model/audb/PaymentLog');
const UserAdminHistory = require('../../model/audb/UserAdminHistory');
const Invoice = require('../../model/audb/Invoice');
const extractPaginationData = require('../../helpers/paginationExtractor');
const _getStreamingServersWithCounts = require('../../commands/user/config/getStreamingServersWithCounts');
const _getUsersWithMaxActivities = require('../../commands/user/getUsersWithMaxActivities');
const _usersWithSuspiciousActivities = require('../../commands/user/usersWithSuspiciousActivities');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _updateUserFreeze = require('../../commands/userFreeze/save');
const _searchUsers = require('../../commands/user/search');
const _getUserFilter = require('../../commands/user/getFilter');
const removeUserLog = require('../../commands/user/logs/removeUserLog');

const cachedUserStatistics = memoizee(User.getStatistics.bind(User), { maxAge: 5 * 60 * 1000, preFetch: true });
const logs = [UserLogVod, UserLogRecord, UserLogLive, UserLogLogin];

function paginateOnModel(model, defaultSort = '-created') {
  return async (req, res) => {
    const { page, pageSize } = extractPaginationData(req);
    const sort = req.query.sort || defaultSort;
    const data = await model.paginate({ uid: req.params.id }, { sort, page, limit: pageSize });
    res.json({ result: data });
  };
}

async function fetchUser(field, value) {
  const user = await User.findOne({ [field]: value })
    .basicPopulation()
    .exec();

  if (!user) throw new HttpError('No user found', 404);

  const userResponse = user.toObject();
  userResponse.email = user.email;

  return userResponse;
}

async function fetchUserObjectById(id) {
  const user = await User.findOne({ id }).basicPopulation().exec();

  if (!user) throw new HttpError(`No user found #${id}`, 404);

  return user;
}

const AdminUserController = {
  getLoginLogs: paginateOnModel(UserLogLogin, '-playtime'),
  getLiveLogs: paginateOnModel(UserLogLive, '-playtime'),
  getRecordLogs: paginateOnModel(UserLogRecord, '-playtime'),
  getVodLogs: paginateOnModel(UserLogVod, '-playtime'),
  getUserPayments: paginateOnModel(PaymentLog, '-created'),
  async getStreamingServersWithCounts(req, res) {
    const result = await _getStreamingServersWithCounts();
    res.json({ result });
  },
  async getStats(req, res) {
    const result = await cachedUserStatistics();
    res.json({ result });
  },
  async getInfo(req, res) {
    const { id } = req.params;
    let user = await User.findOne({ id }).basicPopulation().exec();
    user = user.toObject();
    user.lastLoginIP = '';
    try {
      user.lastLoginIP = (await UserLogLogin.findOne({ uid: id }).sort('-_id').lean().exec()).ip;
    } catch (e) {
      log.error(e.stack || e.message || e.toString());
    }
    user.lastWatchIp = '';
    user.lastLocation = '';
    user.lastISP = '';
    user.lastWatchTime = 0;
    user.lastFingerprint = '';
    try {
      let lastLog;
      const lastLogs = await Promise.all([UserLogLive, UserLogRecord, UserLogVod].map((log) => log.findOne({ uid: id }).sort('-_id').lean().exec()));
      lastLogs.forEach((log) => {
        if (!log) return;
        if (!lastLog || log.playtime > lastLog.playtime) lastLog = log;
      });
      user.lastWatchIp = lastLog.ip;
      user.lastLocation = lastLog.csst;
      user.lastISP = lastLog.isp;
      user.lastWatchTime = lastLog.playtime;
      user.lastFingerprint = lastLog.fingerprint;
    } catch (e) {
      log.error(e.stack || e.message || e.toString());
    }
    res.json({ result: user });
  },

  async getUserBy(req, res) {
    const { field, value } = req.params;
    let result;

    if (field === 'ip') {
      let uid;

      if (value.indexOf('.') > -1) {
        const logResults = await Promise.all(logs.map((log) => log.findOne({ ip: value }).exec()));
        const log = logResults.find((result) => !!result);

        if (!log) throw new HttpError('No user found', 404);

        uid = log.uid;
      } else uid = value;

      result = await fetchUser('id', uid);
    } else if (field === 'email') result = await fetchUser('em', User.encryptEmail(value));
    else if (field === 'tkey') {
      const invoice = await Invoice.findOne({ tkey: value }).exec();

      if (!invoice) {
        const paymentLog = await PaymentLog.findOne({ tkey: value }).exec();

        if (paymentLog) result = await fetchUser('id', paymentLog.uid);
      } else result = await fetchUser('id', invoice.id);
    } else if (field === 'card') {
      const paymentLog = await PaymentLog.findOne({ card: value }).exec();
      result = await fetchUser('id', paymentLog.uid);
    } else result = await fetchUser(field, value);

    res.json({ result });
  },
  async getHistory(req, res) {
    const { page, pageSize } = extractPaginationData(req);
    const offset = (page - 1) * pageSize;
    const history = await UserAdminHistory.find()
      .sort('-created')
      .select('uid created -_id')
      .populate({ path: 'User', select: 'id email em' })
      .skip(offset)
      .limit(pageSize)
      .exec();
    const result = history.map(({ uid, created, User }) => ({ id: User ? User.id : uid, email: User ? User.email : 'deleted', created }));
    res.json({ result });
  },
  async addToHistory(req, res) {
    const { uid } = req.body;
    const byid = req.user.id;
    await UserAdminHistory.updateOne({ byid, uid }, {}, { upsert: true });
    res.json({ error: 0 });
  },
  async search(req, res) {
    const { body, user } = req;
    const result = await _searchUsers({ data: body, user });

    res.send(result);
  },
  async getUser(req, res) {
    const result = await fetchUser('id', req.params.id);
    res.json({ result });
  },
  async updateUser(req, res) {
    const user = await User.findOneAndUpdate({ id: req.params.id }, req.body, { new: true }).exec();

    if (!user) throw new HttpError(`No user found #${req.params.id}`, 404);
    if (req.body.email) user.email = req.body.email;

    const userResponse = user.toObject();
    userResponse.email = user.email;
    res.json({ result: userResponse });
  },
  async getUserConfig(req, res) {
    let userConfig = await UserConfig.findOne({ uid: req.params.id }).exec();

    if (!userConfig) {
      const user = await User.findOne({ id: req.params.id }).exec();

      if (!user) throw new HttpError(`No user found #${req.params.id}`, 404);

      userConfig = await UserConfig.create({ uid: user.id });
    }

    res.json({ result: userConfig });
  },
  async updateUserConfig(req, res) {
    const uid = req.params.id;
    const userConfig = await UserConfig.assign(uid, req.body);
    res.json({ result: userConfig });
  },
  async getUserFreeze(req, res) {
    const freeze = await UserFreeze.find({ uid: req.params.id }).exec();

    res.json({ result: freeze });
  },
  async createUserFreeze(req, res) {
    const { body, user } = req;

    if (!body.uid && req.params.id) body.uid = req.params.id;

    const result = await _updateUserFreeze(body, user);

    res.json(result);
  },
  async updateUserFreeze(req, res) {
    const { body, user } = req;

    if (!body.uid && req.params.userId) body.uid = req.params.userId;

    const result = await _updateUserFreeze(body, user);

    res.json(result);
  },
  async deleteUserFreeze(req, res) {
    // TODO move to the commands/userFreeze
    const freeze = await UserFreeze.deleteOne({ uid: req.params.userId, _id: req.params.freezeId }).exec();

    res.json({ result: freeze.n > 0 });
  },
  async getUserInvoice(req, res) {
    const invoices = await Invoice.find({ id: req.params.id }).populate('package').exec();

    if (!invoices) {
      const user = await User.findOne({ id: req.params.id }).exec();

      if (!user) throw new HttpError(`No user found #${req.params.id}`, 404);

      return res.json({ result: [] });
    }

    const result = invoices;
    res.json({ result });
  },
  async getUserComments(req, res) {
    const { page, pageSize } = extractPaginationData(req);
    const pagination = { page, limit: pageSize };
    const user = await fetchUserObjectById(req.params.id);
    const comments = await user.readAdminComments(pagination);
    res.send({ result: comments });
  },
  async getUserCommentById(req, res) {
    const user = await fetchUserObjectById(req.params.id);
    const comment = await user.getAdminCommentById(req.params.commentId);

    if (!comment) throw new HttpError(`Comment not found #${req.params.commentId}`, 404);

    res.json({ result: comment });
  },
  async createComment(req, res) {
    const creator = req.user;
    const user = await fetchUserObjectById(req.params.id);
    const comment = await user.addAdminComment(req.body.comment, creator.id);
    res.json({ result: comment });
  },
  async deleteUserCommentById(req, res) {
    const user = await fetchUserObjectById(req.params.id);
    const comment = await user.deleteAdminComment(req.params.commentId);
    res.json({ result: comment });
  },
  async getUserProxyNetworks(req, res) {
    const user = await fetchUserObjectById(req.params.id);
    await user.populate('proxyCounts').execPopulate();
    await user.populate('proxyNetworks').execPopulate();
    const result = {
      counts: user.proxyCounts,
      networks: user.proxyNetworks,
    };
    res.json({ result });
  },
  async getUsersWithMaxActivities(req, res) {
    const from = getParamFromRequest(req, 'from', null);
    const to = getParamFromRequest(req, 'to', null);
    const type = getParamFromRequest(req, 'type', null);
    const result = await _getUsersWithMaxActivities(from, to, type);
    res.send(result);
  },
  async getUsersWithSuspiciousActivities(_, res) {
    const result = await _usersWithSuspiciousActivities.load();
    res.send(result);
  },
  async removeSuspiciousActivitiesFlagFromUser(req, res) {
    const userId = getParamFromRequest(req, 'userId', null);
    const result = await _usersWithSuspiciousActivities.remove(userId);
    res.send(result);
  },
  async getUserFilter(req, res) {
    const { user, params } = req;
    const viewName = params.viewName ? params.viewName : null;
    const result = await _getUserFilter({ viewName, user });

    res.json(result);
  },
  async removeUserLogLog(req, res) {
    const { params } = req;
    const { id: userId, type, logId } = params;
    const result = await removeUserLog({ userId, logId, type });

    res.json(result);
  },
};

module.exports = AdminUserController;
