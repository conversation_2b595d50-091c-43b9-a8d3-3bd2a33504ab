const { HttpError } = require('@s1/api-errors');
const getParamFromRequest = require('../../../helpers/getParamFromRequest');
const constants = require('../../../constants/blacklist');

const AAdminBlockController = Model => ({
  oldRoute: async (req, res, next) => {
    res.locals.isOld = true;
    const act = getParamFromRequest(req, 'act');
    const wrapper = AAdminBlockController(Model);
    switch (act) {
      case 'load':
        await wrapper.getAll(req, res);
        break;
      case 'remove':
        await wrapper.delete(req, res, next);
        break;
      case 'save':
        await wrapper.save(req, res, next);
        break;
      default:
        break;
    }
  },

  getAll: async (req, res) => {
    const bt = getParamFromRequest(req, 'bt');
    const query = {};

    if (bt) query.bt = bt;

    const result = await Model.find(query).sort({ st: 1, _id: 1 }).exec();
    res.json(res.locals.isOld ? result : { error: 0, result });
  },

  add: async (req, res, next) => {
    const _id = getParamFromRequest(req, 'id');
    const bt = getParamFromRequest(req, 'bt');

    if (!_id) return next(new HttpError('Parameter id was not provided', 400));
    if (await Model.findOne({ _id })) return next(new HttpError(`Blacklist record already exists #${_id}`, 400));
    if (!bt) return next(new HttpError('Parameter bt was not provided', 400));

    const BT = Object.keys(constants.BT).find(type => constants.BT[type] === bt);

    if (!BT) return next(new HttpError('Parameter bt is invalid', 400));

    const st = constants.ST[BT];
    const doc = new Model({ _id, st, bt });
    await doc.save();
    res.send({ error: 0, result: doc });
  },

  update: async (req, res, next) => {
    const _id = req.params.id;
    const doc = await Model.findOne({ _id });

    if (!doc) return next(new HttpError(`No record found #${_id}`, 404));

    const bt = getParamFromRequest(req, 'bt');

    if (!bt) return next(new HttpError('Parameter bt was not provided', 400));

    const BT = Object.keys(constants.BT).find(type => constants.BT[type] === bt);

    if (!BT) return next(new HttpError('Parameter bt is invalid', 400));

    const st = constants.ST[BT];
    await doc.update({ bt, st });
    res.send({ error: 0, result: doc });
  },

  delete: async (req, res, next) => {
    const _id = req.params.id || req.body._id;
    const doc = await Model.findOne({ _id });

    if (!doc) return next(new HttpError(`No record found #${_id}`, 404));

    await doc.remove();
    const response = res.locals.isOld ? [true] : { error: 0, result: true }; // change response for old route
    res.send(response);
  },

  save: async (req, res, next) => {
    const _id = getParamFromRequest(req, '_id');
    const bt = getParamFromRequest(req, 'bt');
    const st = getParamFromRequest(req, 'st');

    if (!bt) return next(new HttpError('Parameter bt was not provided', 400));

    const doc = { _id, bt, st, created: Date.now() };
    await Model.updateOne({ _id }, { $set: doc }, { upsert: true }).exec();
    res.send(doc);
  },
});

module.exports = AAdminBlockController;
