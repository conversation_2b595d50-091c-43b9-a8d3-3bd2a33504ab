const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const _loadAllPackages = require('../../commands/package/loadAllPackages');
const _save = require('../../commands/package/save');
const _delete = require('../../commands/package/delete');

const actionConstants = {
  SAVE: 'save',
  SAVE_ALL: 'saveall',
  DELETE: 'delete',
};

const AdminPackageController = {
  async getData(req, res) {
    try {
      const result = await _loadAllPackages();

      res.send(formatResponseIDs(result));
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { action } = req.query;
      let result;

      if (!action) {
        const packages = await _loadAllPackages();
        result = formatResponseIDs(packages);

        return res.json(result);
      }

      const schema = getParamFromRequest(req, 'who');

      switch (action) {
        case actionConstants.SAVE: {
          const updatedPackage = await _save(req.body, schema);
          result = formatResponseIDs(updatedPackage);
          break;
        }
        case actionConstants.SAVE_ALL: {
          const updatedPackages = await _save(req.body, schema);
          result = formatResponseIDs(updatedPackages);
          break;
        }
        case actionConstants.DELETE: {
          result = await _delete(getParamFromRequest(req, 'id'), schema);
          break;
        }
        default: {
          const allPackages = await _loadAllPackages();
          result = formatResponseIDs(allPackages);
        }
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
};

module.exports = AdminPackageController;
