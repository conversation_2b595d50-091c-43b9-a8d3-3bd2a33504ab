const _getPage = require('../../../commands/qa/page/get');
const _getAllPages = require('../../../commands/qa/page/getAll');
const _createPage = require('../../../commands/qa/page/create');
const _updatePage = require('../../../commands/qa/page/update');
const _removePage = require('../../../commands/qa/page/remove');

module.exports = {
  async getAll(req, res) {
    const result = await _getAllPages();
    res.json(result);
  },
  async get(req, res) {
    const { id } = req.params;

    if (!id) return res.statusCode(404).send();

    const page = await _getPage({ id });
    res.json(page);
  },
  async create(req, res) {
    const { title } = req.body;
    const result = await _createPage(title);
    res.json(result);
  },
  async update(req, res) {
    const { id, title } = req.body;
    const result = await _updatePage(id, title);
    res.json(result);
  },
  async remove(req, res) {
    const { id } = req.params;
    const result = await _removePage(id);
    res.json(result);
  },
};
