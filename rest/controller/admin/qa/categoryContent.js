const _getContentByCategory = require('../../../commands/qa/categoryContent/getByCategory');
const _updateContent = require('../../../commands/qa/categoryContent/update');
const _removeContent = require('../../../commands/qa/categoryContent/remove');
const _create = require('../../../commands/qa/categoryContent/create');
const _getContent = require('../../../commands/qa/categoryContent/get');

module.exports = {
  async getByCategory(req, res) {
    const { categoryId } = req.params;
    const result = await _getContentByCategory(categoryId);
    res.json(result);
  },

  async get(req, res) {
    const { id } = req.params;
    const result = await _getContent(id);
    res.json(result);
  },

  async update(req, res) {
    const { id, ...restParams } = req.body;
    const result = await _updateContent(id, restParams);
    res.json(result);
  },

  async remove(req, res) {
    const { id } = req.params;
    const result = await _removeContent(id);
    res.json(result);
  },

  async create(req, res) {
    const result = await _create(req.body);
    res.json(result);
  },
};
