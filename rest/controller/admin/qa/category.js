const _getCategoriesByPage = require('../../../commands/qa/category/getByPage');
const _update = require('../../../commands/qa/category/update');
const _remove = require('../../../commands/qa/category/remove');
const _create = require('../../../commands/qa/category/create');
const _get = require('../../../commands/qa/category/get');

module.exports = {
  async getByPage(req, res) {
    const { pageId } = req.params;
    const result = await _getCategoriesByPage(pageId);
    res.json(result);
  },

  async update(req, res) {
    const { id, ...restParams } = req.body;
    const result = await _update(id, restParams);
    res.json(result);
  },

  async remove(req, res) {
    const { id } = req.params;
    const result = await _remove(id);
    res.json(result);
  },

  async create(req, res) {
    const result = await _create(req.body);
    res.json(result);
  },

  async get(req, res) {
    const { id } = req.params;
    const result = await _get(id);
    res.json(result);
  },
};
