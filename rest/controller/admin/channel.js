const { WrongRequestArgumentError, MissingRequestArgumentError } = require('@s1/api-errors');
const deleteOne = require('../../../rest/commands/channel/deleteOne');
const getLostEpg = require('../../../rest/commands/channel/getLostEpg');
const loadData = require('../../../rest/commands/channel/loadData');
const sortGroups = require('../../../rest/commands/channel/sortGroups');
const saveGroup = require('../../../rest/commands/channel/saveGroup');
const bulkSave = require('../../../rest/commands/channel/bulkSave');
const updateImageVersion = require('../../../rest/commands/channel/updateImageVersion');

const actionConstants = {
  LOST_EPG: 'lostepg',
  LOAD_DATA: 'loaddata',
  BULK_SAVE: 'bulksave',
  CHANNEL_GROUP_SORT: 'chgroupsor',
  CHANNEL_GROUP_CREATE_OR_MODIFY: 'angugroup',
  BULK_SAVE: 'bulksave',
};

const actsConstants = {
  DELETE_ONE: 'deleteone',
};

const ChannelController = {
  async getChannelInfo(req, res) {
    try {
      const { action, isradio, act } = req.query;
      const { dateof } = req.body;
      let result;

      if (action) switch (action) {
        case actionConstants.LOST_EPG: {
          result = await getLostEpg(dateof);
          break;
        }
        case actionConstants.LOAD_DATA: {
          result = await loadData(isradio);
          break;
        }
        case actionConstants.BULK_SAVE: {
          switch (act) {
            case actsConstants.DELETE_ONE: {
              const { deleteid } = req.query;
              result = await deleteOne(parseInt(deleteid));
              break;
            }
            default:
              result = new WrongRequestArgumentError("'acts' should be 'deleteone'");
          }
          break;
        }
        default:
          result = new WrongRequestArgumentError("'action' should be 'loaddata' or 'lostepg' or 'bulksave'");
      }
      else result = new MissingRequestArgumentError("Query 'action' is not defined");

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },

  async modifyOrCreate(req, res) {
    try {
      const { action, isradio } = req.query;
      let result;

      if (action) switch (action) {
        case actionConstants.CHANNEL_GROUP_SORT: {
          const groups = req.body;
          result = await sortGroups(groups, isradio);
          break;
        }
        case actionConstants.CHANNEL_GROUP_CREATE_OR_MODIFY: {
          const group = req.body;
          result = await saveGroup(group);
          break;
        }
        case actionConstants.BULK_SAVE: {
          result = await bulkSave(req);
          break;
        }
        default:
          result = new WrongRequestArgumentError("'action' should be 'chgroupsor' or 'bulksave'");
      }
      else result = new MissingRequestArgumentError("Query 'action' is not defined");

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },

  async updateChannelImageVersion(req, res) {
    try {
      const { channelId } = req.body;
      const result = await updateImageVersion(channelId);

      res.json(result);
    } catch (error) {
      res.send(error.message);
    }
  },
};

module.exports = ChannelController;
