const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../../errors/index');
const BlackListCountry = require('../../model/audb/BlacklistCountry');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

const actConstants = {
  SAVE: 'save',
  REMOVE: 'remove',
};

const BlackListCountryOldController = {
  async getData(req, res) {
    try {
      const result = await BlackListCountry.find({}).lean().exec();
      res.json(formatResponseIDs(result));
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },

  async modifyData(req, res) {
    try {
      const { act } = req.query;

      if (!act) return res.send(new MissingRequestArgumentError('act'));

      const _id = getParamFromRequest(req, '_id');
      let result;
      switch (act) {
        case actConstants.SAVE: {
          const st = getParamFromRequest(req, 'st');
          const bt = parseInt(getParamFromRequest(req, 'bt'));
          result = await BlackListCountry.findOneAndUpdate(
            { _id },
            { $set: { st, bt } },
            { upsert: true, new: true },
          ).lean().exec();
          break;
        }
        case actConstants.REMOVE: {
          const deleteOperation = await BlackListCountry.deleteOne({ _id }).exec();
          result = deleteOperation.deletedCount > 0 ? [true] : [false];
          break;
        }
        default:
          throw new WrongAct('Act is wrong');
      }
      res.json(result);
    } catch (error) {
      console.log(error.message);
      res.send(error.message);
    }
  },
};

module.exports = BlackListCountryOldController;
