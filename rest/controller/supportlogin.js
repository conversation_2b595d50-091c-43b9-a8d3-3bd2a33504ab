const { LoginNeededError, ApiError } = require('@s1/api-errors');
const Session = require('../model/audb/Session');
const User = require('../model/audb/User');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const formatResponseIDs = require('../helpers/formatResponseIDs');

const CONSTANTS = {
  LOG_OUT: 'logout',
};

const logIn = async (req, res) => {
  try {
    const user = getParamFromRequest(req, 'user');
    const pass = getParamFromRequest(req, 'pass') || getParamFromRequest(req, 'password');

    if (!user || !pass) throw new LoginNeededError('please login');

    const email = User.encryptEmail(user);
    const foundUser = await User.findOne({ em: email }).exec();
    const isCorrectPassword = foundUser.checkPassword(pass);

    if (foundUser.issupport && isCorrectPassword) {
      req.session.user_id = foundUser.id;
      req.session.support = foundUser;
      foundUser._doc.sid = req.sessionID;
      foundUser._doc.email = email;

      return res.status(200).json({ errorcode: 0, result: formatResponseIDs(foundUser) });
    }

    throw new LoginNeededError('please login');
  } catch (error) {
    if (error instanceof ApiError) throw error;

    res.send(error.message);
  }
};

const logOut = async (req, res) => {
  req.session = null;
  await Session.drop(req.sessionID);

  return res.json(0);
};

const logInOut = async (req, res) => {
  const { act } = req.query;

  if (act === CONSTANTS.LOG_OUT) {
    return logOut(req, res);
  }

  return logIn(req, res);
};

module.exports = { logInOut };
