const { LoginNeededError, ApiError } = require('@s1/api-errors');
const Session = require('../model/audb/Session');
const User = require('../model/audb/User');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const isAdminOrSupportUser = require('../helpers/isAdminOrSupportUser');

const logIn = async (req, res) => {
  try {
    const user = getParamFromRequest(req, 'user');
    const pass = getParamFromRequest(req, 'pass') || getParamFromRequest(req, 'password');

    if (!user || !pass) throw new LoginNeededError('please login');

    const email = User.encryptEmail(user);
    const foundUser = await User.findOne({ em: email }).exec();
    const isCorrectPassword = foundUser.checkPassword(pass);

    if (isCorrectPassword && isAdminOrSupportUser(foundUser)) {
      req.session.user_id = foundUser.id;
      req.session.support = foundUser;
      foundUser._doc.sid = req.sessionID;
      foundUser._doc.email = email;

      return res.status(200).json({ errorcode: 0, result: formatResponseIDs(foundUser) });
    }

    throw new LoginNeededError('please login');
  } catch (error) {
    if (error instanceof ApiError) throw error;

    res.send(error.message);
  }
};

const logOut = async (req, res) => {
  const { act } = req.query;

  if (act === 'logout') {
    try {
      await Session.drop(req.sessionID);
      res.clearCookie('connect.sid');

      return res.json(0);
    } catch (error) {
      console.error(error);
    }
  } else {
    return logIn(req, res);
  }
};

module.exports = {
  logIn,
  logOut,
};
