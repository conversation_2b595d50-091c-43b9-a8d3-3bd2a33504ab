const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../errors');
const UserGlobal = require('../model/audb/UserGlobal');
const formatResponseIDs = require('../helpers/formatResponseIDs');

const ACT_CONSTANTS = {
  SAVE: 'save',
};

module.exports = {
  async getData(req, res) {
    const result = await UserGlobal.load();
    res.json(formatResponseIDs(result));
  },
  async modifyData(req, res) {
    const { act } = req.query;
    const { minbuffer, playblackbuffer } = req.body;

    if (!act) throw new MissingRequestArgumentError('act');
    if (act !== ACT_CONSTANTS.SAVE) throw new WrongAct('act is wrong');

    const result = await UserGlobal._save({ minbuffer, playblackbuffer });
    res.json(formatResponseIDs(result));
  },
};
