const DictionaryReplace = require('../model/audb/DictionaryReplace');

const DictionaryReplaceController = {
  async getAll(req, res) {
    const { page = 1 } = req.query;
    const pageSize = req.query.pagesize || 10;
    const total = await DictionaryReplace.countDocuments();
    const pages = Math.ceil(total / pageSize);
    const response = await DictionaryReplace.find().sort({ from: 1 })
      .limit(pageSize).skip((page - 1) * pageSize)
      .lean();
    res.send({ result: response, pages });
  },
  async update(req, res) {
    const { from, to = '', _id } = req.body;

    // we should not to save empty or space 'from' field, it might caused issues while replacing text
    if (!from) throw new Error('from param is required');
    if (from && (from === ' ' || from === '&nbsp;')) throw new Error('from should not be spacer');
    if (_id !== undefined) await DictionaryReplace.updateOne({ _id }, { from, to }, { upsert: true });
    else await DictionaryReplace.updateOne({ from }, { from, to }, { upsert: true });

    res.send({ result: true });
  },
  async delete(req, res) {
    const { _id } = req.query;

    if (!_id) throw new Error('_id param is required');

    await DictionaryReplace.deleteOne({ _id });
    res.send({ result: true });
  },
};

module.exports = DictionaryReplaceController;
