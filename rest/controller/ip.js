const _getIsp = require('../commands/ip/getIsp');
const _getGuestLocation = require('../commands/ip/getGuestLocation');

const ipController = {
  getIsp({ params: { ip } }, res) {
    const result = _getIsp(ip);
    res.send(result);
  },
  getGuestLocation(req, res) {
    const ip = req.params.ip || req.userIP;
    const result = _getGuestLocation(ip);
    res.send(result);
  },
};

module.exports = ipController;
