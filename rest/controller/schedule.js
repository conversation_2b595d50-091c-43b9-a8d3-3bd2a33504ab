const log = require('@s1/log').create(__filename);
const { HttpError, ApiError } = require('@s1/api-errors');
const Schedule = require('../model/audb/Schedule');
const apicache = require('../service/cache');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const _getWeekdays = require('../commands/channel/getWeekdays');
const _getRecord = require('../commands/record/get');
const _getShow = require('../commands/schedule/getShow');
const _getSchedule = require('../commands/schedule/get');
const _saveSchedule = require('../commands/schedule/save');
const _overrideSchedule = require('../commands/schedule/override');
const { clean } = require('../service/prepare/clean');
const errorMapper = require('../errors/maps/express');
const i18n = require('../helpers/geti18n');

const localesFields = {
  ename: 'name_en',
  edescription: 'description_en',
  egenre: 'genre_en',
};

const DEFAULT_PAGE_SIZE = 1000;
const channelsController = {
  async saveSchedule(req, res) {
    const { params, body } = req;
    const { source, epgs } = body;
    const { channel: channelId, date } = params;
    const result = await errorMapper(_saveSchedule({ channelId, date, source, epgs }));
    res.send({ success: true, message: result });
  },
  async updateSchedule(req, res) {
    const _id = req.params.id;
    const schedule = await Schedule.findOne({ _id }).exec();
    ['rdatetime', 'time', 'name', 'description', 'genre', 'lengthtime', 'showpic', 'ifonline', 'year', 'showCustomPicForRecords'].forEach((field) => {
      schedule[field] = req.body.hasOwnProperty(field) ? req.body[field] : schedule[field];
    });
    ['ename', 'edescription', 'egenre'].forEach((field) => {
      schedule[localesFields[field]] = req.body.hasOwnProperty(field) ? req.body[field] : schedule[localesFields[field]];
    });
    await schedule.save();
    apicache.withNamespace('schedules').flush('schedules', () => {});

    return res.json({
      error: 0,
      success: true,
      message: req.__('Schedule %s was updated!', _id),
    });
  },
  async getSchedule(req, res) {
    const { query, params, user, body = {}, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { showId, isAll } = query;
    const showDescription = !!parseInt(query.withdescription || query.withDescription || body.withdescription);
    const recordWithDate = !!parseInt(query.recordwithdate || query.recordWithDate || body.recordwithdate);
    const isLivePage = !!parseInt(query.live || body.live);
    const rawDate = query.date || params.date || body.date;
    const record = query.record || body.record;
    const locale = getParamFromRequest(req, 'locale');
    const page = parseInt(query.page || body.page) || 1;
    const isPageUndefined = typeof query.page === 'undefined' && typeof body.page === 'undefined';
    const pageSize = parseInt(query.pageSize || body.pageSize) || DEFAULT_PAGE_SIZE;
    const skip = (page - 1) * pageSize;
    const channelIdRaw = query.cid || params.channel || body.cid || null;
    let channelId = channelIdRaw && typeof channelIdRaw === 'object' ? Object.values(channelIdRaw) : channelIdRaw;
    const groupId = query.gid || params.group || body.gid || null;
    const simplify = (query.simplify && !!parseInt(query.simplify)) || false;

    if (!channelId && !groupId) channelId = 'all';

    const fewChannels = Array.isArray(channelId) || !!groupId || channelId === 'all';
    const result = await errorMapper(
      _getSchedule({
        isAll,
        channelId,
        groupId,
        user,
        rawDate,
        record,
        recordWithDate,
        pageSize,
        isPageUndefined,
        skip,
        simplify,
        showId,
        fewChannels,
        showDescription,
        isLivePage,
        locale,
        ISP,
        countryCode,
        stateCode,
      }),
    );

    if (result instanceof Error) throw result;

    res.json(result);
  },
  async getWeekdays(req, res) {
    const {
      resolved: { channel },
    } = req;
    const {
      query: { duration },
    } = req;
    const result = await _getWeekdays({ channel, duration });
    clean(result, { channel: 'channel' });
    res.send(result);
  },

  async getShow(req, res) {
    const locale = getParamFromRequest(req, 'locale');
    const simplify = (req.query.simplify && !!parseInt(req.query.simplify)) || false;
    const { channel, rdatetime } = req.params;
    const result = await _getShow({ channel, rdatetime, simplify, locale });
    res.json(clean({ result }, { schedule: 'result' }));
  },

  async getRecord(req, res) {
    const { user, userIP, userAllIPs, userLocation, resolved } = req;
    const locale = getParamFromRequest(req, 'locale');

    const { ISP, countryCode, stateCode } = userLocation;
    const { channel } = resolved;
    const channelId = getParamFromRequest(req, 'channel') || getParamFromRequest(req, 'cid');
    const startTime = +getParamFromRequest(req, 'time', 0);
    log.info({
      play: {
        record: {
          user_id: user.id.toString(),
          channel_id: channelId,
          rdatetime: +startTime,
          channel_id__rdatetime: `${channelId}_${startTime}`,
        },
      },
    });

    if (!channelId || !startTime) throw new HttpError('Bad request', 400);
    if (!channel) throw new ApiError(10404, req.__('No such channel'), undefined, { playlist: '', md: '' });

    const formattedChannel = channel.format({ locale });
    channel.name = formattedChannel.name;

    const results = await errorMapper(
      _getRecord({
        channel,
        startTime,
        user,
        userIP,
        userAllIPs,
        ISP,
        countryCode,
        stateCode,
        locale,
        sid: req.query.sid,
        req,
      }),
    );

    if (results instanceof Error) throw results;

    const responseData = { errorcode: 0, results };

    clean(responseData, { channel: 'results.channel', schedule: ['results.show', 'results.fulllinks'] });

    res.json(responseData);
  },
  async override({ params: { id: _id }, body: { path, picture } }, res) {
    const scheduleOverride = await _overrideSchedule({ _id, path, picture });

    if (!scheduleOverride) throw new HttpError(i18n.__('No such schedule #%s', _id), 404);

    res.json(scheduleOverride);
  },
  async getOneRecord(req, res) {
    const { user, userIP, userAllIPs, userLocation, params, resolved } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const { channel } = resolved;
    const channelId = params.channel || params.cid;
    const startTime = params.time;

    if (!channelId || !startTime) throw new HttpError('Bad request', 400);

    const results = await errorMapper(
      _getRecord({
        channel,
        startTime,
        user,
        userIP,
        userAllIPs,
        isAdmin: true,
        ISP,
        countryCode,
        stateCode,
        sid: req.query.sid,
        req,
      }),
    );

    if (results instanceof Error) throw results;

    const responseData = { errorcode: 0, results };

    res.json(responseData);
  },
};

module.exports = channelsController;
