const getParamFromRequest = require('../helpers/getParamFromRequest');
const forgotPasswordCommand = require('../commands/user/forgotPassword');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const authenticateCommand = require('../commands/authentication/authenticate');
const registerCommand = require('../commands/authentication/register');

const AuthenticationController = {
  async authenticate(req, res) {
    const userData = {
      request: req,
      userIP: req.userIP,
      locale: getParamFromRequest(req, 'locale'),
      session: req.session,
      sessionID: req.sessionID,
      rememberme: getParamFromRequest(req, 'rememberme'),
      remembermekey: getParamFromRequest(req, 'remembermekey'),
      user: req.user,
      username: getParamFromRequest(req, 'user'),
      pass: getParamFromRequest(req, 'pass'),
      os: getParamFromRequest(req, 'os'),
      appName: getParamFromRequest(req, 'appName'),
    };

    const result = await authenticateCommand(userData);
    const remembermekey = result.remembermekey;
    const formatedResponse = formatResponseIDs(result);
    formatedResponse.remembermekey = remembermekey;

    return res.send(formatedResponse);
  },

  async register(req, res) {
    const { userIP } = req;
    const userData = {
      ip: userIP,
      referral: getParamFromRequest(req, 'referral', false),
      email: getParamFromRequest(req, 'email'),
      locale: getParamFromRequest(req, 'locale'),
      name: getParamFromRequest(req, 'name'),
      password: getParamFromRequest(req, 'password'),
      repassword: getParamFromRequest(req, 'repassword'),
      fingerprint: getParamFromRequest(req, 'fingerprint'),
      fingerprintPayment: getParamFromRequest(req, 'fingerprintPayment'),
      macaddress: getParamFromRequest(req, 'macaddress'),
      cookiekey: getParamFromRequest(req, 'cookiekey'),
      storagekey: getParamFromRequest(req, 'storagekey'),
      flashkey: getParamFromRequest(req, 'flashkey'),
      phone: getParamFromRequest(req, 'phone'),
      phonearea: getParamFromRequest(req, 'phonearea'),
      userAgent: getParamFromRequest(req, 'userAgent', null),
    };

    const result = await registerCommand(userData);

    res.send(result);
  },
  async forgotPassword(req, res) {
    const { userIP } = req;
    const result = await forgotPasswordCommand({
      userIP,
      email: getParamFromRequest(req, 'email'),
      captcha: getParamFromRequest(req, 'captcha', ''),
      verifyCaptcha: req.session.captcha,
      locale: getParamFromRequest(req, 'locale'),
    });
    res.send(result);
  },
};

module.exports = AuthenticationController;
