// eslint-disable-next-line import/no-unresolved
const _loadSSS = require('../commands/common/loadsss');
const _load = require('../commands/common/load');
const _loadSome = require('../commands/common/loadSome');
const _delete = require('../commands/common/delete');
const _save = require('../commands/common/save');
const _saveAll = require('../commands/common/saveAll');
const _saveSSS = require('../commands/common/saveSSS');
const _runIt = require('../commands/common/runIt');

const commonController = {
  async common(req, res) {
    const { act, table: queryTable } = req.query;
    const { table: bodyTable } = req.body;
    //
    const currentTableName = queryTable || bodyTable;
    const requestBody = { ...req.body, table: currentTableName };
    //
    const actTypes = {
      loadsome: _loadSome,
      runit: _runIt,
      savesss: _saveSSS,
      loadsss: _loadSSS,
      load: _load,
      save: _save,
      saveall: _saveAll,
      delete: _delete,
    };

    if (actTypes[act]) {
      const result = await actTypes[act](requestBody, 'audb');
      res.send(result);
    }

    res.send();
  },
};

module.exports = commonController;
