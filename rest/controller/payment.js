const { PaymentNotPossibleError } = require('@s1/api-errors');
const log = require('@s1/log').create(__filename);
const getParamFromRequest = require('../helpers/getParamFromRequest');
const _getAllInvoices = require('../commands/invoice/getAll');
const _addNftgateLinks = require('../commands/invoice/addNftgateLinks');
const _upgradeUser = require('../commands/user/upgrade');
const _getInvoice = require('../commands/invoice/get');
const _removeInvoices = require('../commands/invoice/remove');
const _addPaymentActionsLog = require('../commands/payment/addActionsLog');
const _checkExistPayment = require('../commands/payment/checkExist');
const _toggleInvoiceLock = require('../commands/invoice/toggleLock');
const _getUserPaymentHistory = require('../commands/payment/history');
const _processUserPayment = require('../commands/payment/process');
const _cancelUserPayment = require('../commands/payment/cancel');
const _getAllPackages = require('../commands/package/getAll');
const _generateInvoicePDF = require('../commands/payment/generatePDF');
const _payDispute = require('../commands/payment/payDispute');
const _wonDispute = require('../commands/payment/wonDispute');
const _checkPay = require('../commands/payment/checkPay');
const _emailInvoice = require('../commands/payment/emailInvoice');
const _refunds = require('../commands/payment/refunds');
const _isPaid = require('../commands/payment/isPaid');
const _isnewForB2pay = require('../commands/payment/isnewForB2pay');
const _getBinInfo = require('../commands/payment/getBinInfo');
const _getAllDiscounts = require('../commands/discounts/getAll');
const _saveDiscount = require('../commands/discounts/save');
const _deleteDiscount = require('../commands/discounts/delete');
const _getAllPaymentGroups = require('../commands/payment/getAllGroups');
const _savePaymentGroup = require('../commands/payment/saveGroup');
const _deletePaymentGroup = require('../commands/payment/deleteGroup');
const _getAllPaymentTypes = require('../commands/payment/getAllTypes');
const _savePaymentType = require('../commands/payment/saveType');
const _deletePaymentType = require('../commands/payment/deleteType');
const _getAllPaymentTypeAccounts = require('../commands/payment/getAllPaymentTypeAccounts');
const _getAllPaymentAccounts = require('../commands/payment/getAllAccounts');
const _savePaymentAccount = require('../commands/payment/saveAccount');
const _deletePaymentAccount = require('../commands/payment/deleteAccount');
const _getUserEmailByAccount = require('../commands/payment/getUserEmailByAccount');
const _getAllBasicRulesConfigs = require('../commands/payment/getAllBasicRulesConfigs');
const _getAllPaymentBasicRules = require('../commands/payment/getAllBasicRules');
const _getUserAllPaymentBasicRules = require('../commands/payment/getUserAllPaymentBasicRules');
const _savePaymentBasicRule = require('../commands/payment/saveBasicRule');
const _deletePaymentBasicRule = require('../commands/payment/deleteBasicRule');
const _getAllPaymentGroupRules = require('../commands/payment/getAllGroupRules');
const _savePaymentGroupRule = require('../commands/payment/saveGroupRule');
const _deletePaymentGroupRule = require('../commands/payment/deleteGroupRule');
const _checkUserPaymentGroupRulesForAdminPanel = require('../commands/payment/checkUserPaymentGroupRulesForAdminPanel');
const _checkUserOnSubmitPayRulesForAdminPanel = require('../commands/payment/checkUserOnSubmitPayRulesForAdminPanel');
const _getAllPaymentOnSubmitPayRules = require('../commands/payment/getAllPaymentOnSubmitPayRules');
const _savePaymentOnSubmitPayRule = require('../commands/payment/savePaymentOnSubmitPayRule');
const _deletePaymentOnSubmitPayRule = require('../commands/payment/deletePaymentOnSubmitPayRule');
const _checkUserCanPayWithCard = require('../commands/payment/checkUserCanPayWithCard');
const _checkUserCanPayPaymentTypesOnSubmitPayRules = require('../commands/payment/checkUserCanPayPaymentTypesOnSubmitPayRules');
const _checkUserCanPayPaymentAccountsOnSubmitPayRules = require('../commands/payment/checkUserCanPayPaymentAccountsOnSubmitPayRules');
const _getPaymentGeneralConfig = require('../commands/payment/getPaymentGeneralConfig');
const _savePaymentGeneralConfig = require('../commands/payment/savePaymentGeneralConfig');
const _getPaymentCoinsConfigs = require('../commands/payment/getPaymentCoinsConfigs');
const _savePaymentCoinConfig = require('../commands/payment/savePaymentCoinConfig');
const _deletePaymentCoinConfig = require('../commands/payment/deletePaymentCoinConfig');
const _getAllAccountsConfigs = require('../commands/payment/getAllAccountsConfigs');
const _getAllPaymentBypassedUsers = require('../commands/payment/getAllPaymentBypassedUsers');
const _saveUserPaymentBypassedConfigs = require('../commands/payment/saveUserPaymentBypassedConfigs');
const _setBypassedTypesByLogs = require('../commands/payment/setBypassedTypesByLogs');
const _getUserAllowedNftTypes = require('../commands/payment/getUserAllowedNftTypes');
const _statistics = require('../commands/payment/statistics');
const _addMtpelerinAuthData = require('../commands/payment/addMtpelerinAuthData');
const _getBraintreeHash = require('../commands/payment/getBraintreeHash');
const _getBlacklist = require('../commands/paymentBlacklist/getAll');
const _saveBlacklist = require('../commands/paymentBlacklist/save');
const _deleteBlacklist = require('../commands/paymentBlacklist/delete');
const _deleteBlacklistedUsers = require('../commands/paymentBlacklist/deleteUsers');
const _getBlacklistIpReview = require('../commands/paymentBlacklist/getAllIpReview');
const _updateBlacklistIpReview = require('../commands/paymentBlacklist/updateIpReview');
const _getBlacklistGeneralConfig = require('../commands/paymentBlacklist/getGeneralConfig');
const _updateBlacklistGeneralConfig = require('../commands/paymentBlacklist/updateGeneralConfig');
const _getBlacklistFingerprint = require('../commands/paymentBlacklist/getFingerprint');
const _getWhitelistFingerprint = require('../commands/paymentBlacklist/getWhitelistFingerprint');
const _saveWhitelistFingerprint = require('../commands/paymentBlacklist/saveWhitelistFingerprint');
const _deleteWhitelistFingerprint = require('../commands/paymentBlacklist/deleteWhitelistFingerprint');
const _getIgnoreFingerprintUseragent = require('../commands/paymentBlacklist/getIgnoreFingerprintUseragent');
const _saveIgnoreFingerprintUseragent = require('../commands/paymentBlacklist/saveIgnoreFingerprintUseragent');
const _deleteIgnoreFingerprintUseragent = require('../commands/paymentBlacklist/deleteIgnoreFingerprintUseragent');
const _deleteBlacklistFingerprint = require('../commands/paymentBlacklist/deleteFingerprint');
const _getBlacklistCardNumbers = require('../commands/paymentBlacklist/getCardNumbers');
const _deleteBlacklistCardNumbers = require('../commands/paymentBlacklist/deleteCardNumbers');
const _getBlacklistUserRelationsList = require('../commands/paymentBlacklist/getBlacklistUserRelationsList');
const _getBlacklistRelatedUsers = require('../commands/paymentBlacklist/getBlacklistRelatedUsers');
const _getBlacklistUserRelationsGraph = require('../commands/paymentBlacklist/getBlacklistUserRelationsGraph');
const _getBlacklistUserRelationsBillingBlacklistedUsers = require('../commands/paymentBlacklist/getBlacklistUserRelationsBillingBlacklistedUsers');
const _generateUserInvoices = require('../commands/payment/generateUserInvoices');
const _resignInvoiceLink = require('../commands/payment/resignInvoiceLink');
const isBlacklistedUser = require('../commands/payment/helpers/rules/isBlacklistedUser');
const isSuspendedUser = require('../commands/payment/helpers/rules/isSuspendedUser');
const isStealerUser = require('../commands/payment/helpers/rules/isStealerUser');
const _shortenPaymentUrl = require('../commands/payment/shortenPaymentUrl');
const _getFullPaymentUrlByShortId = require('../commands/payment/getFullPaymentUrlByShortId');
const _getPaymentsHistory = require('../commands/payment/getPaymentsHistory');
const _checkHasUserWatchingActivities = require('../commands/payment/checkHasUserWatchingActivities');
const _getUrlInfo = require('../commands/payment/getUrlInfo');
const _saveUrlInfo = require('../commands/payment/saveUrlInfo');
const _updateUrlInfo = require('../commands/payment/updateUrlInfo');
const _deleteUrlInfo = require('../commands/payment/deleteUrlInfo');
const _getLastMercuryoEmail = require('../commands/user/getLastMercuryoEmail');
const _updateLastMercuryoEmail = require('../commands/user/updateLastMercuryoEmail');
const _createCardAutoChargeLink = require('../commands/payment/createCardAutoChargeLink');
const _encryptUserCardInfo = require('../commands/payment/encryptUserCardInfo');
const _decryptUserCardInfo = require('../commands/payment/decryptUserCardInfo');
const _getCryptoInvoice = require('../commands/payment/getCryptoInvoice');
const _saveCryptoInvoice = require('../commands/payment/saveCryptoInvoice');
const _getNgatePaymentInfo = require('../commands/payment/getNgatePaymentInfo');
const _moveBlacklistFingerprintToWhitelist = require('../commands/paymentBlacklist/moveBlacklistFingerprintToWhitelist');
const _moveBlacklistFingerprintUseragentToIgnore = require('../commands/paymentBlacklist/moveBlacklistFingerprintUseragentToIgnore');
const errorMapper = require('../errors/maps/express');
const formApiResponse = require('../helpers/formApiResponse');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const { clean } = require('../service/prepare/clean');
const { SocketClientNames } = require('../constants/socket');
const _moveBlacklistCardToWhitelist = require('../commands/paymentBlacklist/moveBlacklistCardToWhitelist');
const _saveWhitelistCard = require('../commands/paymentBlacklist/saveWhitelistCard');
const _deleteWhitelistCard = require('../commands/paymentBlacklist/deleteWhitelistCard');
const _getWhitelistCards = require('../commands/paymentBlacklist/getWhitelistCards');

const paymentController = {
  async getUpgradeInvoice(req, res) {
    const { user, sessionID } = req;
    const locale = getParamFromRequest(req, 'locale');
    const result = await _upgradeUser({ user, sessionID, locale });
    res.send(result);
  },
  async getUserInvoices(req, res) {
    const { user, query, sessionID } = req;
    const { coupon } = query;
    const { results, userCanPay } = await errorMapper(_getAllInvoices({ user, sessionID, coupon }));

    if (!userCanPay) throw new PaymentNotPossibleError({ results });

    res.send(formApiResponse(results, true));
  },
  async getUserPayments(req, res) {
    const { user, query, sessionID } = req;
    const { coupon } = query;
    const locale = getParamFromRequest(req, 'locale');
    const fromPage = getParamFromRequest(req, 'fromPage', '');
    const {
      results: { packages, extra, normal, ...data },
      userCanPay,
      addresses,
    } = await errorMapper(_getAllInvoices({ fromPage, user, sessionID, coupon, locale }));
    packages.forEach((pack) => {
      pack.features = pack.packagegroup.features;
    });
    Object.assign(data, { packages, extra: { ...extra }, basic: { ...normal }, addresses });
    const misc = userCanPay ? {} : null;

    if (!misc) throw new PaymentNotPossibleError(null, locale);

    const { userIP } = req;
    const dataWithNftgateLinks = await _addNftgateLinks({ user, userIP, data });
    Object.assign(data, dataWithNftgateLinks);

    res.send(formApiResponse(data, true, misc));
  },
  async paymentInfo(req, res) {
    const { user } = req;
    const tkey = parseInt(getParamFromRequest(req, 'tkey', 0));
    const locale = getParamFromRequest(req, 'locale');
    const fromPage = getParamFromRequest(req, 'fromPage', '');
    const saveAllowedPaymentsLog = !!['true', '1'].includes(getParamFromRequest(req, 'saveAllowedPaymentsLog', 'false'));
    const requestReturnParamsString = getParamFromRequest(req, 'returnParams', '{}');
    const requestReturnParams = JSON.parse(requestReturnParamsString);
    const isBlacklisted = requestReturnParams.hasOwnProperty('isBlacklisted') ? requestReturnParams.isBlacklisted : true;
    const relatedUsers = requestReturnParams.hasOwnProperty('relatedUsers') ? requestReturnParams.relatedUsers : true;
    const bypassedPaymentTypeNames = requestReturnParams.hasOwnProperty('bypassedPaymentTypeNames') ? requestReturnParams.bypassedPaymentTypeNames : true;
    const returnParams = {
      isBlacklisted,
      relatedUsers,
      bypassedPaymentTypeNames,
    }
    const result = await _getInvoice({ user, tkey, IPInfo: req.IPInfo, locale, fromPage, saveAllowedPaymentsLog }, returnParams);
    res.send(formApiResponse(result, false));
  },
  async checkExistPayment(req, res) {
    const { params, query } = req;
    const tkey = params.tkey ? params.tkey : query.tkey;
    const result = await errorMapper(_checkExistPayment({ tkey }));
    res.send(formApiResponse({ success: result }, false));
  },
  async checkPayment(req, res) {
    const tkey = getParamFromRequest(req, 'tkey');
    const result = await _checkPay(+tkey);
    res.send(formatResponseIDs(result));
  },
  async paymentHistory(req, res) {
    const {
      user: { id: uid },
    } = req;
    const locale = getParamFromRequest(req, 'locale');
    const result = await errorMapper(_getUserPaymentHistory({ uid, locale }));
    res.send(formApiResponse(result, true));
  },
  async updatePayment(req, res) {
    const { query, body, params } = req;
    const { txid, firstname, lastname, email, card, paymentAccountId } = body;
    const tkey = params.tkey || getParamFromRequest(req, 'tkey', '');
    const pptype = getParamFromRequest(req, 'pptype', '');
    const currency = getParamFromRequest(req, 'currency', 'usd');
    const stripeId = +body.stripeId || 1;
    const amount = +(+body.amount || 0).toFixed(2);
    const additionalFee = +(+body.includeAdditionalFees || 0).toFixed(2);
    const locale = getParamFromRequest(req, 'locale');

    const result = await _processUserPayment({
      // eslint-disable-next-line max-len
      tkey,
      pptype,
      currency,
      amount,
      additionalFee,
      txid,
      firstname,
      lastname,
      email,
      card,
      query,
      body,
      stripeId,
      paymentAccountId,
      locale,
    });

    res.send(result);
  },
  async cancelPayment(req, res) {
    const { body, params } = req;
    const tkey = params.tkey || getParamFromRequest(req, 'tkey', '');
    const locale = getParamFromRequest(req, 'locale');

    const result = await _cancelUserPayment({
      tkey,
      body,
      locale,
    });

    res.send(result);
  },
  async changeInvoiceLockState(req, res) {
    const tkey = getParamFromRequest(req, 'tkey');
    const act = getParamFromRequest(req, 'act');
    const locale = getParamFromRequest(req, 'locale');
    const result = await errorMapper(_toggleInvoiceLock({ tkey, act, locale }));

    if (result.code)
      return res.json({
        error: result.code,
        msg: result.message,
      });

    const { newLockedState, isLocked } = result;
    res.send(formApiResponse({ isLocked }, false, { msg: newLockedState }));
  },
  async getPackages(req, res) {
    const { user } = req;
    const locale = getParamFromRequest(req, 'locale');
    const fromPage = getParamFromRequest(req, 'fromPage', '');
    const result = await errorMapper(_getAllPackages({ fromPage, user, locale }));
    res.send(formApiResponse(result, true));
  },
  async generateInvoicePDF(req, res) {
    const { user, params } = req;
    const { tkey } = params;
    const pdfInvoice = await _generateInvoicePDF({ tkey, user });
    res.append('Content-Type', 'application/pdf');
    res.send(pdfInvoice);
  },
  async paydispute(req, res) {
    const { body } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { charge } = body;
    const result = await _payDispute({ charge, locale });
    res.send(formApiResponse(result, false));
  },
  async wonDispute(req, res) {
    const { body } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { charge } = body;
    const result = await _wonDispute({ charge, locale });
    res.send(formApiResponse(result, false));
  },
  async emailInvoice(req, res) {
    const invoiceID = getParamFromRequest(req, 'id');
    const locale = getParamFromRequest(req, 'locale');
    const response = await _emailInvoice({ invoiceID, locale });

    res.send(response);
  },
  async refunds(req, res) {
    const invoiceId = getParamFromRequest(req, 'invoiceId');
    const reason = getParamFromRequest(req, 'reason');
    const locale = getParamFromRequest(req, 'locale', 'en');
    const response = await _refunds({ invoiceId, reason, locale });

    res.send(response);
  },
  async isPaid(req, res) {
    const { params, query } = req;
    const tkey = params.tkey ? params.tkey : query.tkey;
    const result = await _isPaid({ tkey });
    res.send(result);
  },
  async isnewForB2pay(req, res) {
    const { user } = req;
    const result = await _isnewForB2pay({ user });
    res.send(result);
  },
  async getBinInfo(req, res) {
    const { user } = req;
    const bin = getParamFromRequest(req, 'bin', null);
    const userIP = getParamFromRequest(req, 'userIP', null);

    const result = await _getBinInfo({ bin, user, userIP });

    if (result.error) return res.json(result);

    res.json(clean({ error: 0, result }, { binlist: 'result' }));
  },
  async getDiscounts(req, res) {
    const result = await _getAllDiscounts();

    res.json(result);
  },
  async updateDiscount(req, res) {
    const { user, body } = req;
    const result = await _saveDiscount({ user, data: body });

    res.json(result);
  },
  async deleteDiscount(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deleteDiscount({ _id });

    res.json(result);
  },
  async getAllPaymentGroups(req, res) {
    const result = await _getAllPaymentGroups();

    res.json(result);
  },
  async updatePaymentGroup(req, res) {
    const { user, body } = req;
    const result = await _savePaymentGroup({ user, data: body });

    res.json(result);
  },
  async deletePaymentGroup(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentGroup({ _id });

    res.json(result);
  },
  async getAllPaymentTypes(req, res) {
    const result = await _getAllPaymentTypes();

    res.json(result);
  },
  async updatePaymentType(req, res) {
    const { user, body } = req;
    const result = await _savePaymentType({ user, data: body });

    res.json(result);
  },
  async deletePaymentType(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentType({ _id });

    res.json(result);
  },
  async getAllPaymentTypeAccounts(req, res) {
    const result = await _getAllPaymentTypeAccounts();

    res.json(result);
  },
  async getAllPaymentAccounts(req, res) {
    const result = await _getAllPaymentAccounts();

    res.json(result);
  },
  async updatePaymentAccount(req, res) {
    const { user, body } = req;
    const result = await _savePaymentAccount({ user, data: body });

    res.json(result);
  },
  async deletePaymentAccount(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentAccount({ _id });

    res.json(result);
  },
  async getUserEmailByAccount(req, res) {
    const accountId = getParamFromRequest(req, 'accountId', null);
    const userId = getParamFromRequest(req, 'userId', null);
    const result = await _getUserEmailByAccount(accountId, userId);

    res.json(result);
  },
  async getAllBasicRulesConfigs(req, res) {
    const result = await _getAllBasicRulesConfigs();

    res.json(result);
  },
  async getAllPaymentBasicRules(req, res) {
    const result = await _getAllPaymentBasicRules();

    res.json(result);
  },
  async updatePaymentBasicRule(req, res) {
    const { user, body } = req;
    const result = await _savePaymentBasicRule({ user, data: body });

    res.json(result);
  },
  async deletePaymentBasicRule(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentBasicRule({ _id });

    res.json(result);
  },
  async getUserAllPaymentBasicRules(req, res) {
    const { userId, packageId, fromPage } = req.body;
    const result = await _getUserAllPaymentBasicRules({ userId, packageId, fromPage });

    res.json(result);
  },
  async getAllPaymentGroupRules(req, res) {
    const result = await _getAllPaymentGroupRules();

    res.json(result);
  },
  async updatePaymentGroupRule(req, res) {
    const { user, body } = req;
    const result = await _savePaymentGroupRule({ user, data: body });

    res.json(result);
  },
  async deletePaymentGroupRule(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentGroupRule({ _id });

    res.json(result);
  },
  async checkUserPaymentGroupRulesForAdminPanel(req, res) {
    const { body } = req;
    const result = await _checkUserPaymentGroupRulesForAdminPanel({ data: body });

    res.json(result);
  },
  async checkUserOnSubmitPayRulesForAdminPanel(req, res) {
    const { body } = req;
    const result = await _checkUserOnSubmitPayRulesForAdminPanel({ data: body });

    res.json(result);
  },
  async getAllPaymentOnSubmitPayRules(req, res) {
    const result = await _getAllPaymentOnSubmitPayRules();

    res.json(result);
  },
  async updatePaymentOnSubmitPayRule(req, res) {
    const { user, body } = req;
    const result = await _savePaymentOnSubmitPayRule({ user, data: body });

    res.json(result);
  },
  async deletePaymentOnSubmitPayRule(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentOnSubmitPayRule({ _id });

    res.json(result);
  },
  async checkUserPaymentTypesOnSubmitPayRules(req, res) {
    const { user, body } = req;
    const userIp = req.query.ip || body.userIp || req.userIP;
    const result = await _checkUserCanPayPaymentTypesOnSubmitPayRules({ user, body, userIp });

    res.json(result);
  },
  async checkUserPaymentAccountsOnSubmitPayRules(req, res) {
    const { user, body } = req;
    const userIp = req.query.ip || body.userIp || req.userIP;
    const result = await _checkUserCanPayPaymentAccountsOnSubmitPayRules({ user, body, userIp });

    res.json(result);
  },
  async checkUserCanPayWithCard(req, res) {
    const { user, body } = req;
    const result = await _checkUserCanPayWithCard({ user, body });

    res.json(result);
  },
  async getPaymentGeneralConfig(req, res) {
    const result = await _getPaymentGeneralConfig();

    res.json(result);
  },
  async updatePaymentGeneralConfig(req, res) {
    const { user, body } = req;
    const result = await _savePaymentGeneralConfig({ user, data: body });

    res.json(result);
  },
  async getPaymentCoinsConfigs(req, res) {
    const result = await _getPaymentCoinsConfigs();

    res.json(result);
  },
  async updatePaymentCoinConfig(req, res) {
    const { user, body } = req;
    const result = await _savePaymentCoinConfig({ user, data: body });

    res.json(result);
  },
  async deletePaymentCoinConfig(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deletePaymentCoinConfig({ _id });

    res.json(result);
  },
  async getPaymentGeneralConfigForBilling(req, res) {
    const result = await _getPaymentGeneralConfig();

    res.json(result);
  },
  async getAllAccountsConfigs(req, res) {
    const result = await _getAllAccountsConfigs();

    res.json(result);
  },
  async getAllPaymentBypassedUsers(req, res) {
    const { body } = req;
    const result = await _getAllPaymentBypassedUsers(body);

    res.json(result);
  },
  async saveUserPaymentBypassedConfigs(req, res) {
    const { body } = req;
    const result = await _saveUserPaymentBypassedConfigs(body);

    res.json(result);
  },
  async setBypassedTypesByLogs(req, res) {
    const { body } = req;
    const result = await _setBypassedTypesByLogs(body);

    res.json(result);
  },
  async getUserAllowedNftTypes(req, res) {
    const { user, params } = req;
    const packageId = params.packageId || getParamFromRequest(req, 'packageId');
    const result = await _getUserAllowedNftTypes(user, packageId);

    res.json(result);
  },
  async statistics(req, res) {
    const { body } = req;
    const result = await _statistics(body);
    res.send(result);
  },
  async removeInvoice(req, res) {
    const { params } = req;
    const tkey = params.tkey || getParamFromRequest(req, 'tkey', '');
    const locale = getParamFromRequest(req, 'locale');

    const result = await _removeInvoices({ tkey, locale });

    res.send(result);
  },
  async addPaymentActionsLog(req, res) {
    const { user, body } = req;
    const tkey = getParamFromRequest(req, 'tkey', '');
    const message = body.message;
    const data = body.data ? JSON.parse(body.data) : {};
    const userIp = body.userIp || null;
    const locale = getParamFromRequest(req, 'locale');

    const result = await _addPaymentActionsLog({ tkey, user, message, data, userIp, locale });

    res.send(result);
  },
  async getBlacklist(req, res) {
    const type = getParamFromRequest(req, 'type');
    const result = await _getBlacklist(type);

    res.json(result);
  },
  async updateBlacklist(req, res) {
    const { body, user } = req;
    const result = await _saveBlacklist(body, user);

    res.json(result);
  },
  async deleteBlacklistedUsers(req, res) {
    const uids = getParamFromRequest(req, 'uids');
    const { user, session } = req;
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const result = await _deleteBlacklistedUsers({ uids, adminUser: user });

    const socketData = { data: { ...result, sid }, groupName: SocketClientNames.adminPanel, eventName: 'payment/blacklist/users' };
    global.io.emit('group', socketData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API payment/blacklist/users, sid: ${sid}`);
      }
    });

    res.json(result);
  },
  async deleteBlacklist(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const { user } = req;
    const result = await _deleteBlacklist({ _id, adminUser: user });

    res.json(result);
  },
  async getBlacklistIpReview(req, res) {
    const result = await _getBlacklistIpReview();

    res.json(result);
  },
  async updateBlacklistIpReview(req, res) {
    const { body, user } = req;
    const result = await _updateBlacklistIpReview(body, user);

    res.json(result);
  },
  async getBlacklistGeneralConfig(req, res) {
    const result = await _getBlacklistGeneralConfig();

    res.json(result);
  },
  async updateBlacklistGeneralConfig(req, res) {
    const { body, user } = req;
    const result = await _updateBlacklistGeneralConfig(body, user);

    res.json(result);
  },
  async getBlacklistFingerprint(req, res) {
    const result = await _getBlacklistFingerprint();

    res.json(result);
  },
  async deleteBlacklistFingerprint(req, res) {
    const fingerprint = getParamFromRequest(req, 'fingerprint');
    const result = await _deleteBlacklistFingerprint({ fingerprint });

    res.json(result);
  },
  async getBlacklistCardNumbers(req, res) {
    const result = await _getBlacklistCardNumbers();

    res.json(result);
  },
  async deleteBlacklistCardNumbers(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteBlacklistCardNumbers({ number });

    res.json(result);
  },
  async getBlacklistUserRelations(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getBlacklistUserRelationsList(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'payment/blacklist/user-relations/id';
    const requestData = {
      eventName,
      data: { ...result, sid, userId: parseInt(userId) },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });

    res.json(result);
  },
  async getBlacklistRelatedUsers(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getBlacklistRelatedUsers(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'payment/blacklist/user-relations/id/users';
    const requestData = {
      eventName,
      data: { ...result, sid },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async getBlacklistUserRelationsGraph(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getBlacklistUserRelationsGraph(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'payment/blacklist/user-relations/id/graph';
    const requestData = {
      eventName,
      data: { ...result, sid, userId: parseInt(userId) },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async getBlacklistUserRelationsBillingBlacklistedUsers(req, res) {
    const { params, query, session } = req;
    const userId = params.id ? params.id : query.id;
    const socketClientId = getParamFromRequest(req, 'socketClientId');
    const result = await _getBlacklistUserRelationsBillingBlacklistedUsers(parseInt(userId));
    const sid = session && session.hasOwnProperty('__sid') ? session.__sid : null;

    const eventName = 'payment/blacklist/user-relations/id/billing-blacklisted-users';
    const requestData = {
      eventName,
      data: { ...result, sid, userId },
      targetClientId: socketClientId,
    };

    global.io.emit('private', requestData, (acknowledgement) => {
      if (!acknowledgement) {
        log.error(`Cannot send socket message for the API ${eventName}, sid: ${sid}`);
      }
    });
    res.json(result);
  },
  async getBraintreeHash(req, res) {
    const { user, body } = req;
    const result = await _getBraintreeHash({ data: body, user });

    res.json(result);
  },
  async addMtpelerinAuthData(req, res) {
    const { user, body } = req;
    const tkey = getParamFromRequest(req, 'tkey', '');
    const sid = getParamFromRequest(req, 'sid', '');
    const result = await _addMtpelerinAuthData({ tkey, sid, user, body });

    res.json(result);
  },
  async isBlacklistedCardUser(req, res) {
    const { user } = req;
    // skip checking is blacklisted user with skipBlacklistPayment property
    const isCardUserBlacklisted = user.skipBlacklistPayment ? false : await isBlacklistedUser({ user });

    res.json({
      result: { isCardUserBlacklisted },
    });
  },
  async isSuspendedCardUser(req, res) {
    const { user } = req;
    const isCardUserSuspended = await isSuspendedUser({ user });

    res.json({
      result: { isCardUserSuspended },
    });
  },
  async isStealerCardUser(req, res) {
    const { user } = req;
    const isCardUserStealer = await isStealerUser({ user });

    res.json({
      result: { isCardUserStealer },
    });
  },
  async generateUserInvoices(req, res) {
    const { userId } = req.query;
    const result = await _generateUserInvoices(userId, 'adminPanel');

    res.json(result);
  },
  async resignInvoiceLink(req, res) {
    const result = await _resignInvoiceLink(req.body);

    res.json(result);
  },
  async shortenPaymentUrl(req, res) {
    const { userId, tkey } = req.query;
    const { url } = req.body;
    const result = await _shortenPaymentUrl(userId, tkey, url);

    res.json(result);
  },
  async getFullPaymentUrlByShortId(req, res) {
    const { id } = req.params;
    const result = await _getFullPaymentUrlByShortId(id);

    res.json(result);
  },
  async getPaymentsHistory(req, res) {
    const data = req.body;
    const result = await _getPaymentsHistory(data);

    res.json(result);
  },
  async checkHasUserWatchingActivities(req, res) {
    const data = req.body;
    const result = await _checkHasUserWatchingActivities(data);

    res.json(result);
  },
  async getUrlInfo(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _getUrlInfo(_id);

    res.json(result);
  },
  async saveUrlInfo(req, res) {
    const { body } = req;
    const result = await _saveUrlInfo(body);

    res.json(result);
  },
  async updateUrlInfo(req, res) {
    const { body } = req;
    const result = await _updateUrlInfo(body);

    res.json(result);
  },
  async deleteUrlInfo(req, res) {
    const _id = getParamFromRequest(req, 'id');
    const result = await _deleteUrlInfo(_id);

    res.json(result);
  },
  async getLastMercuryoEmail(req, res) {
    const userId = getParamFromRequest(req, 'userId');
    const result = await _getLastMercuryoEmail(userId);

    res.json(result);
  },
  async updateLastMercuryoEmail(req, res) {
    const { body } = req;
    const result = await _updateLastMercuryoEmail(body);

    res.json(result);
  },
  async createCardAutoChargeLink(req, res) {
    const { body } = req;
    const result = await _createCardAutoChargeLink(body);

    res.json(result);
  },
  async encryptUserCardInfo(req, res) {
    const { body } = req;
    const result = await _encryptUserCardInfo(body);

    res.json(result);
  },
  async decryptUserCardInfo(req, res) {
    const { body } = req;
    const result = await _decryptUserCardInfo(body);

    res.json(result);
  },
  async getWhitelistFingerprint(req, res) {
    const result = await _getWhitelistFingerprint();

    res.json(result);
  },
  async saveWhitelistFingerprint(req, res) {
    const { body, user } = req;
    const result = await _saveWhitelistFingerprint(body, user);

    res.json(result);
  },
  async deleteWhitelistFingerprint(req, res) {
    const fingerprint = getParamFromRequest(req, 'fingerprint');
    const result = await _deleteWhitelistFingerprint(fingerprint);

    res.json(result);
  },
  async getIgnoreFingerprintUseragent(req, res) {
    const result = await _getIgnoreFingerprintUseragent();

    res.json(result);
  },
  async saveIgnoreFingerprintUseragent(req, res) {
    const { body, user } = req;
    const result = await _saveIgnoreFingerprintUseragent(body, user);

    res.json(result);
  },
  async deleteIgnoreFingerprintUseragent(req, res) {
    const id = getParamFromRequest(req, 'id');
    const result = await _deleteIgnoreFingerprintUseragent(id);

    res.json(result);
  },
  async moveBlacklistFingerprintToWhitelist(req, res) {
    const { body, user } = req;
    const result = await _moveBlacklistFingerprintToWhitelist(body, user);

    res.json(result);
  },
  async moveBlacklistFingerprintUseragentToIgnore(req, res) {
    const { body, user } = req;
    const result = await _moveBlacklistFingerprintUseragentToIgnore(body, user);

    res.json(result);
  },
  async moveBlacklistCardToWhitelist(req, res) {
    const { body, user } = req;
    const result = await _moveBlacklistCardToWhitelist(body, user);

    res.json(result);
  },
  async getWhitelistCards(req, res) {
    const result = await _getWhitelistCards();

    res.json(result);
  },
  async saveWhitelistCard(req, res) {
    const { body, user } = req;
    const result = await _saveWhitelistCard(body, user);

    res.json(result);
  },
  async deleteWhitelistCard(req, res) {
    const number = getParamFromRequest(req, 'number');
    const result = await _deleteWhitelistCard(number);

    res.json(result);
  },
  async getCryptoInvoice(req, res) {
    const result = await _getCryptoInvoice(req.query);

    res.json(result);
  },
  async saveCryptoInvoice(req, res) {
    const result = await _saveCryptoInvoice(req.body);

    res.json(result);
  },
  async getNgatePaymentInfo(req, res) {
    const result = await _getNgatePaymentInfo(req.body);

    res.json(result);
  },
};

module.exports = paymentController;
