const SpeedTest = require('../model/audb/SpeedTest');
const getNextModelId = require('../helpers/getNextModelId');
const formatResponseIDs = require('../helpers/formatResponseIDs');
const _getSpeedtestServers = require('../commands/speedtest/getServers');

module.exports = {
  async list(req, res) {
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;

    const servers = await _getSpeedtestServers({ user, ISP, countryCode, stateCode });

    res.json(servers);
  },
  async save(req, res) {
    const { body, query, headers, sessionID } = req;
    const { user: { id: uid }, userIP, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const ua = body.useragent || headers['user-agent'];
    const data = {
      ...body,
      uid,
      sid: sessionID,
      cc: countryCode,
      st: stateCode,
      isp: ISP,
      ip: userIP,
      request: query,
      post: body,
      ua: ua.toLowerCase(),
      created: Math.floor(Date.now() / 1000),
    };

    const createdSpeedTest = await SpeedTest.create(data);
    const formattedData = formatResponseIDs([createdSpeedTest]);

    res.json(formattedData);
  },
};
