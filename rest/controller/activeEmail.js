const logger = require('@s1/log');
const { MissingRequestArgumentError } = require('@s1/api-errors');
const { WrongAct } = require('../errors');
const load = require('../commands/user/sendEmails/load');
const send = require('../commands/user/sendEmails/send');
const loadAllAndSend = require('../commands/user/sendEmails/loadAllAndSend');

const log = logger.create(__filename);

const ACT_CONSTANTS = {
  LOAD: 'load',
  SEND: 'send',
  SEND_TO_ALL: 'sendAll',
};

module.exports = {
  async execute(request, response) {
    try {
      const { act } = request.query;
      let result;

      if (!act) throw new MissingRequestArgumentError('act');

      switch (act) {
        case ACT_CONSTANTS.LOAD:
          result = await load(request);
          break;
        case ACT_CONSTANTS.SEND:
          result = await send(request);
          break;
        case ACT_CONSTANTS.SEND_TO_ALL:
          result = await loadAllAndSend(request);
          break;
        default:
          console.log('Send emails::wrong act:', act);
          log.error('Send emails::wrong act:', act);

          throw new WrongAct('act is wrong');
      }

      response.json(result);
    } catch (error) {
      throw error;
    }
  },
};
