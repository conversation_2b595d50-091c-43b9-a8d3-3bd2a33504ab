const log = require('@s1/log').create(__filename);
const { ApiError, HttpError } = require('@s1/api-errors');
const getParamFromRequest = require('../helpers/getParamFromRequest');
const _remove = require('../commands/vod/remove');
const _removeWatchLogs = require('../commands/vod/removeWatchLogs');
const _removeFavorites = require('../commands/vod/removeFavorites');
const _getVod = require('../commands/vod/get');
const _getVodWithNeighbour = require('../commands/vod/getWithNeighbour');
const _getMostWatchedVods = require('../commands/vod/getMostWatched');
const _getWhatsNew = require('../commands/vod/getWhatsNew');
const _getWhatsNewSeparated = require('../commands/vod/getWhatsNewSeparated');
const _getRecommended = require('../commands/vod/getRecommended');
const _rateVod = require('../commands/vod/rate');
const _playVod = require('../commands/vod/play');
const { clean } = require('../service/prepare/clean');
const formApiResponse = require('../helpers/formApiResponse');
const errorMapper = require('../errors/maps/express');
const devicesCheckLimitCommand = require('../commands/user/registeredDevices/checkLimit');
const i18n = require('../helpers/geti18n');

const VodController = {
  async loadVodWithNeighbour(req, res) {
    const vodId = getParamFromRequest(req, 'vodid');
    const locale = getParamFromRequest(req, 'locale');
    const append = getParamFromRequest(req, 'append').trim();
    const { userAllIPs, userLocation, user, sessionID } = req;
    const { countryCode, ISP, stateCode } = userLocation;
    const { results, istvshow, pathinfo, playlist, fulllinks } = await _getVodWithNeighbour({
      vodId,
      locale,
      append,
      userAllIPs,
      user,
      sessionID,
      countryCode,
      ISP,
      stateCode,
      req,
    });
    const responseData = formApiResponse(results, true, { istvshow, pathinfo, playlist, fulllinks });

    if (locale) clean(responseData, {
      category: 'pathinfo',
      vod: 'fulllinks',
    });

    res.json(responseData);
  },
  async play(req, res) {
    const { user, userAllIPs, sessionID, userLocation, params, query: { sid }, userIP } = req;
    const deviceInfo = {
      dName: getParamFromRequest(req, 'dName'),
      os: getParamFromRequest(req, 'os', ''),
      userAgent: getParamFromRequest(req, 'userAgent', null) || req.headers['user-agent'] || '',
      deviceId: getParamFromRequest(req, 'deviceId', null),
      appName: getParamFromRequest(req, 'appName', ''),
      deviceIsRequire: getParamFromRequest(req, 'nd', null),
      userIp: userIP,
    };

    if (!deviceInfo.deviceId) throw new ApiError(400, i18n.__('Device ID is required'));

    const locale = getParamFromRequest(req, 'locale');
    const limitResult = await errorMapper(devicesCheckLimitCommand({ user, deviceInfo, locale }));

    if (limitResult instanceof Error) throw limitResult;

    const { countryCode, ISP, stateCode } = userLocation;
    const { id } = params;
    const result = await _playVod({ id, locale, user, userAllIPs, sessionID, countryCode, ISP, stateCode, req });
    const responseData = formApiResponse(result);

    if (locale) clean(responseData, {
      vod: 'result.item',
      category: 'result.path',
    });

    res.json(responseData);
  },
  async loadVod(req, res) {
    const { user, userAllIPs, sessionID, userLocation } = req;
    const locale = getParamFromRequest(req, 'locale');
    const { countryCode, ISP, stateCode } = userLocation;
    const vodId = getParamFromRequest(req, 'vodid');
    log.info({
      play: {
        vod: {
          user_id: user.id.toString(),
          vod_id: vodId,
        },
      },
    });

    const vodData = await _getVod({ user, vodId, locale, userAllIPs, sessionID, countryCode, ISP, stateCode, req });

    if (!vodData) throw new HttpError('Vod not found', 404);

    const {
      results,
      fulllinks,
      istvshow,
      vodinfo,
      pathinfo,
      playlist,
    } = vodData;

    const responseData = formApiResponse(results, true, {
      fulllinks,
      istvshow,
      vodinfo,
      pathinfo,
      playlist,
    });

    if (locale) clean(responseData, {
      category: 'pathinfo',
      vod: 'vodinfo',
    });

    res.json(responseData);
  },
  async whatsNew(req, res) {
    const { user, userLocation } = req;
    const { countryCode, ISP, stateCode } = userLocation;
    const locale = getParamFromRequest(req, 'locale');
    const results = await _getWhatsNew({ user, locale, ISP, countryCode, stateCode });
    const responseData = { error: 0, results };

    if (locale) clean(responseData, {
      vod: ['results.newvodms.episodes', 'results.newvodms.movies'],
      category: 'results.#cates',
    });

    res.removeHeader('Set-Cookie');
    res.json(responseData);
  },
  async whatsNewSeparated(req, res) {
    const { user, userLocation } = req;
    const { countryCode, ISP, stateCode } = userLocation;
    const locale = getParamFromRequest(req, 'locale');
    const results = await _getWhatsNewSeparated({ user, locale, ISP, countryCode, stateCode });
    const responseData = { error: 0, results };
    const categoriesKeys = Object.keys(results.newvodms);

    if (locale) clean(responseData, {
      vod: categoriesKeys.map(key => `results.newvodms.${key}.vods`),
      category: 'results.#cates',
    });

    res.removeHeader('Set-Cookie');
    res.json(responseData);
  },
  async rate(req, res) {
    const { user, query } = req;
    const { vodid: vodId, tvshowid: tvShowId, tostar: rating } = query;
    const result = await _rateVod({ userId: user.id, vodId, tvShowId, rating });
    res.send([result]);
  },
  async getMostWatched(req, res) {
    const { user, userLocation } = req;
    const { ISP, countryCode, stateCode } = userLocation;
    const locale = getParamFromRequest(req, 'locale');
    const {
      mostTvShows,
      mostMovies,
      mostRated,
    } = await _getMostWatchedVods({ user, ISP, countryCode, stateCode, locale });
    res.send({
      mosttvshows: mostTvShows,
      mostmovies: mostMovies,
      mostratemovie: mostRated,
    });
  },
  getRecommended(type) {
    return async (req, res) => {
      const { user, userLocation } = req;
      const { ISP, countryCode, stateCode } = userLocation;
      const limit = getParamFromRequest(req, 'limit', 12);
      const period = getParamFromRequest(req, 'period', 'month');
      const periodLimit = getParamFromRequest(req, 'periodLimit', 100);
      const page = getParamFromRequest(req, 'page');
      const pageSize = getParamFromRequest(req, 'pageSize');
      const locale = getParamFromRequest(req, 'locale');
      const { result, count } = await _getRecommended({
        uid: user.id,
        type,
        limit,
        period,
        periodLimit,
        page,
        pageSize,
        ISP,
        countryCode,
        stateCode,
        locale,
      });
      const responseData = formApiResponse(
        result,
        false,
        page && pageSize ? { pagination: { count, page, pageSize } } : {},
      );

      res.json(responseData);
    };
  },
  /**
   * Remove VOD with users watch logs and favorites by VOD ID
   */
  async remove(req, res) {
    const { id } = req.params;
    const result = await _remove(id);
    res.json(result);
  },
  /**
   * Remove users watch logs for by VOD ID
  */
  async removeWatchLogs(req, res) {
    const { id } = req.params;
    const result = await _removeWatchLogs(id);
    res.json(result);
  },
  /**
   * Remove users favorites for by VOD ID
   */
  async removeFavorites(req, res) {
    const { id } = req.params;
    const result = await _removeFavorites(id);
    res.json(result);
  },
};

module.exports = VodController;
