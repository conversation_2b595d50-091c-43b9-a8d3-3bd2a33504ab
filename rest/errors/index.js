/* eslint-disable padding-line-between-statements */

class CannotFindInvoice extends Error {}
class CannotToggleInvoiceLock extends Error {}
class CannotToggleInvoiceLockToUnknownState extends Error {}
class ProxyForbidden extends Error {
  constructor(ip) {
    super();
    this.ip = ip;
  }
}
class InvalidInputField extends Error {
  constructor(errors) {
    super();
    this.errors = errors;
  }
}
class RegisterEmailExists extends Error {}
class RegisterEmailBlocked extends Error {}
class RegisterClosed extends Error {}
class LoginFailed extends Error {}
class LogNotFound extends Error {}
class ChannelNotFound extends Error {}
class WrongEPGs extends Error {
  constructor(message, data) {
    super(message);
    this.data = data;
  }
}
class CannotSaveSchedule extends Error {
  constructor(message, data) {
    super(message);
    this.data = data;
  }
}
class IncorrectGetScheduleOptions extends Error {}
class RefundReasonNotSet extends Error {}
class UnknownReason extends Error {}
class AlreadyReason extends Error {}
class ErrorWhileRefunding extends <PERSON>rror {}
class ErrorWhileCancelling extends Error {}
class WrongResetPassword extends Error {}
class InvoiceNotFound extends Error {}
class NoUidQuery extends Error {
  constructor(message) {
    super();
    this.message = message;
  }
}
class TrailerAssignFailed extends Error {}
class DeviceLimit extends Error {}
class WrongAct extends Error {}
class WrongTableName extends Error {}
class CheckTrialError extends Error {
  constructor(message) {
    super();
    this.message = message;
  }
}
class SessionId extends Error {
  constructor(message) {
    super();
    this.message = message;
  }
}
class WrongKeyId extends Error {}

module.exports = {
  CannotFindInvoice,
  CannotToggleInvoiceLock,
  CannotToggleInvoiceLockToUnknownState,
  ProxyForbidden,
  InvalidInputField,
  RegisterEmailExists,
  RegisterEmailBlocked,
  RegisterClosed,
  LoginFailed,
  LogNotFound,
  ChannelNotFound,
  WrongEPGs,
  CannotSaveSchedule,
  IncorrectGetScheduleOptions,
  RefundReasonNotSet,
  UnknownReason,
  AlreadyReason,
  ErrorWhileRefunding,
  ErrorWhileCancelling,
  InvoiceNotFound,
  WrongResetPassword,
  TrailerAssignFailed,
  DeviceLimit,
  WrongAct,
  WrongTableName,
  CheckTrialError,
  SessionId,
  NoUidQuery,
  WrongKeyId,
};
