const {
  InvoiceNotFoundError,
  PaymentLockAlreadyToggledError,
  PaymentLockUnknownActionError,
  ProxyForbiddenError,
  InvalidInputFieldError,
  RegisterEmailExistsError,
  RegisterEmailBlockedError,
  RegisterClosedError,
  LoginFailedError,
  WrongResetPasswordError,
  HttpError,
  ChannelNotFoundError,
  RefundReasonNotSetError,
  UnknownReasonError,
  AlreadyReasonError,
  WhileRefundingError,
  TrailerAssignFailedError,
  DeviceLimitError,
} = require('@s1/api-errors');
const {
  CannotFindInvoice,
  CannotToggleInvoiceLock,
  CannotToggleInvoiceLockToUnknownState,
  ProxyForbidden,
  InvalidInputField,
  RegisterEmailExists,
  RegisterEmailBlocked,
  RegisterClosed,
  LogNotFound,
  ChannelNotFound,
  WrongEPGs,
  CannotSaveSchedule,
  IncorrectGetScheduleOptions,
  RefundReasonNotSet,
  UnknownReason,
  AlreadyReason,
  <PERSON>rrorWhileRefunding,
  LoginFailed,
  WrongResetPassword,
  InvoiceNotFound,
  TrailerAssignFailed,
  DeviceLimit,
  WrongAct,
  WrongTableName,
  WrongKeyId,
} = require('../');
const getErrorMapper = require('../map');

const errorMap = new Map();
errorMap.set(CannotFindInvoice, () => new InvoiceNotFoundError());
errorMap.set(CannotToggleInvoiceLock, ({ message }) => new PaymentLockAlreadyToggledError(message));
errorMap.set(CannotToggleInvoiceLockToUnknownState, () => new PaymentLockUnknownActionError());
errorMap.set(ProxyForbidden, ({ ip }) => new ProxyForbiddenError(ip));
errorMap.set(InvalidInputField, ({ errors }) => new InvalidInputFieldError({ result: errors }));
errorMap.set(RegisterEmailExists, () => new RegisterEmailExistsError());
errorMap.set(RegisterEmailBlocked, () => new RegisterEmailBlockedError());
errorMap.set(RegisterClosed, () => new RegisterClosedError());
errorMap.set(LoginFailed, () => new LoginFailedError());
errorMap.set(LogNotFound, ({ message }) => new HttpError(message, 404));
errorMap.set(ChannelNotFound, ({ message }) => new ChannelNotFoundError(message));
errorMap.set(WrongEPGs, ({ message, data }) => new HttpError(message, 418, data));
errorMap.set(CannotSaveSchedule, ({ message, data }) => new HttpError(message, 406, data));
errorMap.set(IncorrectGetScheduleOptions, ({ message }) => new HttpError(message, 400));
errorMap.set(RefundReasonNotSet, ({ message }) => new RefundReasonNotSetError(message));
errorMap.set(UnknownReason, ({ message }) => new UnknownReasonError(message));
errorMap.set(AlreadyReason, ({ message }) => new AlreadyReasonError(message));
errorMap.set(ErrorWhileRefunding, ({ message }) => new WhileRefundingError(message));
errorMap.set(WrongResetPassword, locale => new WrongResetPasswordError(locale));
errorMap.set(InvoiceNotFound, ({ message }) => new HttpError(message, 404));
errorMap.set(TrailerAssignFailed, ({ message }) => new TrailerAssignFailedError(message));
errorMap.set(DeviceLimit, ({ message }) => new DeviceLimitError(message));
errorMap.set(WrongAct, ({ message }) => new WrongAct(message));
errorMap.set(WrongTableName, ({ message }) => new WrongTableName(message));
errorMap.set(WrongKeyId, ({ message }) => new WrongKeyId(message, 403));

module.exports = getErrorMapper(errorMap);
