const moment = require('moment');
const mongoose = require('mongoose');
const {
  SEARCH_REQUEST_DATA,
} = require('../../../constants/usercenter');
const User = require('../../../model/audb/User');
const { Package } = require('../../../model/audb/Package');
const PaymentLog = require('../../../model/audb/PaymentLog');
const UserCard = require('../../../model/audb/UserCard');
const UserLogLive = require('../../../model/audb/UserLogLive');
const UserLogRecord = require('../../../model/audb/UserLogRecord');
const UserLogVod = require('../../../model/audb/UserLogVod');
const UserLogLogin = require('../../../model/audb/UserLogLogin');
const Invoice = require('../../../model/audb/Invoice');
const PaymentActionsLog = require('../../../model/audb/PaymentActionsLog');
const PaymentBlacklist = require('../../../model/audb/PaymentBlacklist');
const Stealer = require('../../../model/audb/Stealer');
const md5 = require('../../../helpers/md5');
const { encrypt } = require('../../../helpers/security');
const config = require('../../../../config');

const getEmailQuery = (key, value) => {
  const operator = '$or';
  const email = value.toLowerCase();
  const emailEncrypted = User.encryptEmail(email);
  const query = [
    { em: emailEncrypted },
    { mtpelerinEm: emailEncrypted },
    { tazapayEm: emailEncrypted },
    { nftEm: emailEncrypted },
    { crossmintEm: emailEncrypted },
    { lastMercuryoEm: emailEncrypted },
  ];

  return { operator, query };
};

const getEmailsQuery = (key, value) => {
  const values = value.split('\n');
  const emailsEncrypted = values.map(email => User.encryptEmail(email.toLowerCase()));
  const operator = '$or';
  const query = [];

  emailsEncrypted.forEach((emailEncrypted) => {
    query.push({ em: emailEncrypted });
    query.push({ mtpelerinEm: emailEncrypted });
    query.push({ tazapayEm: emailEncrypted });
    query.push({ nftEm: emailEncrypted });
    query.push({ crossmintEm: emailEncrypted });
    query.push({ lastMercuryoEm: emailEncrypted });
  });

  return { operator, query };
};

const getTkeyQuery = async (tkey) => {
  const operator = 'id';

  const [PaymentLogs, Invoices] = await Promise.all([
    await PaymentLog.find({ tkey }, { uid: 1 }).lean(),
    await Invoice.find({ tkey }, { id: 1 }).lean(),
  ]);
  const uIDs = [];

  if (PaymentLogs.length) {
    PaymentLogs.forEach((paymentlog) => {
      uIDs.push(paymentlog.uid);
    });
  }
  if (Invoices.length) {
    Invoices.forEach((invoice) => {
      uIDs.push(invoice.id);
    });
  }

  return {
    operator,
    query: { $in: uIDs },
  };
};

const getExpiredQueries = (value) => {
  const query = value === 1 ? { $gt: moment().unix() } : value === 0 ? { $lt: moment().unix() } : null;

  return query;
};

const getExpiredQuery = (key, value) => {
  const operator = 'expires';
  value = parseInt(value);
  const query = getExpiredQueries(value);

  return { operator, query };
};

const getIsRoleQuery = (key, value, role) => {
  const operator = role;
  value = parseInt(value);
  let query;

  if (value === 1) query = 1;
  else if (value === 0) query = { $ne: 1 };

  return { operator, query };
};

const getIsNumberQuery = (key, value, role) => {
  const operator = role;
  value = parseInt(value);
  let query;

  if (value === 1) query = 1;
  else if (value === 0) query = { $ne: 1 };

  return { operator, query };
};

const getIsBlacklistedQuery = async (value) => {
  const operator = 'id';
  value = parseInt(value);
  let query;

  if (value === 0 || value === 1) {
    const paymentBlacklistedModels = await PaymentBlacklist.find({ uid: { $exists: true } }, { _id: 0, uid: 1 }).lean();
    const uids = paymentBlacklistedModels.map(model => model.uid);

    if (value === 1) query = { $in: uids };
    else if (value === 0) query = { $nin: uids };
  }

  return { operator, query };
};

const getIsStealerQuery = async (value) => {
  const operator = 'id';
  value = parseInt(value);
  let query;

  if (value === 0 || value === 1) {
    const stealerModels = await Stealer.find({ uid: { $exists: true } }, { _id: 0, uid: 1 }).lean();
    const uids = stealerModels.map(model => model.uid);

    if (value === 1) query = { $in: uids };
    else if (value === 0) query = { $nin: uids };
  }

  return { operator, query };
};

const getPaidTypesQuery = async (value) => {
  const operator = 'id';
  let query;

  if (value && value.length) {
    const paymentLogs = await PaymentLog.aggregate([
      {
        $match: { pptype: { $in: value }, amount: { $gt: 0 } },
      },
      {
        $group: {
          _id: '$uid',
        },
      },
    ]).exec();
    const uids = paymentLogs.map(model => model._id);

    query = { $in: uids };
  }

  return { operator, query };
};

const getPaidQuery = async () => {
  const operator = 'id';

  const paymentLogs = await PaymentLog.aggregate([
    {
      $match: { amount: { $gt: 0 } },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ]).exec();
  const uids = paymentLogs.map(model => model._id);

  const query = { $in: uids };

  return { operator, query };
};

const getUsersFromCountriesQuery = async (value) => {
  const operator = 'id';
  const countries = value.split(',').map(country => country.trim()).filter(country => !!country);
  const filterForWatchLogs = {};
  const filterForPaymentLogs = {};
  countries.forEach((country) => {
    if (!filterForWatchLogs.$or) filterForWatchLogs.$or = [];
    if (!filterForPaymentLogs.$or) filterForPaymentLogs.$or = [];

    filterForWatchLogs.$or.push({ csst: { $regex: new RegExp(`^${country}`, 'i') } });
    filterForPaymentLogs.$or.push({ 'merchantApiResponse.json.billing_details.address.country': { $regex: new RegExp(`^${country}`, 'i') } });
  });

  const aggregationForWatchLogs = [
    {
      $match: filterForWatchLogs,
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];
  const aggregationForPaymentLogs = [
    {
      $match: filterForPaymentLogs,
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];

  const [liveLogs, recordLogs, vodLogs, loginLogs, paymentLogs] = await Promise.all([
    await UserLogLive.aggregate(aggregationForWatchLogs).exec(),
    await UserLogRecord.aggregate(aggregationForWatchLogs).exec(),
    await UserLogVod.aggregate(aggregationForWatchLogs).exec(),
    await UserLogLogin.aggregate(aggregationForWatchLogs).exec(),
    await PaymentLog.aggregate(aggregationForPaymentLogs).exec(),
  ]);
  const uIDs = new Set();

  if (liveLogs.length) {
    liveLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (recordLogs.length) {
    recordLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (vodLogs.length) {
    vodLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (loginLogs.length) {
    loginLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (paymentLogs.length) {
    paymentLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }

  const query = { $in: Array.from(uIDs) };

  return { operator, query };
};

const getWatchActivitiesLastDaysQuery = async (value) => {
  const operator = 'id';
  const days = parseInt(value.toString());
  const fromTime = moment().subtract(days, days > 1 ? 'days' : 'day').unix();

  const aggregationForWatchLogs = [
    {
      $match: { playtime: { $gte: fromTime } },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];

  const [liveLogs, recordLogs, vodLogs, loginLogs] = await Promise.all([
    await UserLogLive.aggregate(aggregationForWatchLogs).exec(),
    await UserLogRecord.aggregate(aggregationForWatchLogs).exec(),
    await UserLogVod.aggregate(aggregationForWatchLogs).exec(),
    await UserLogLogin.aggregate(aggregationForWatchLogs).exec(),
  ]);
  const uIDs = new Set();

  if (liveLogs.length) {
    liveLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (recordLogs.length) {
    recordLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (vodLogs.length) {
    vodLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (loginLogs.length) {
    loginLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }

  const query = { $in: Array.from(uIDs) };

  return { operator, query };
};

const getIdQuery = (value) => {
  const operator = 'id';
  const query = value.replace(',', '');

  return { operator, query };
};

const getNameQuery = (value) => {
  const operator = 'name';
  const query = { $regex: new RegExp(value, 'i') };

  return { operator, query };
};

const getSearchIDsByTransactionId = async (txid) => {
  const [paymentLogs, paymentActionsLogs] = await Promise.all([
    await PaymentLog.find({ txid }, { uid: 1 }).lean(),
    await PaymentActionsLog.find({ 'data.txId': txid }, { uid: 1 }).lean(),
  ]);
  const uIDs = [];

  if (paymentLogs.length) {
    paymentLogs.forEach((paymentlog) => {
      uIDs.push(paymentlog.uid);
    });
  }
  if (paymentActionsLogs.length) {
    paymentActionsLogs.forEach((paymentActionsLog) => {
      uIDs.push(paymentActionsLog.uid);
    });
  }

  return { $in: uIDs };
};

const getTransactionIdQuery = async (value) => {
  const operator = 'id';
  const query = await getSearchIDsByTransactionId(value);

  return { operator, query };
};

const getSearchIDsByWalletAddress = async (walletAddress) => {
  const paymentLogAggregation = [
    {
      $match: { $or: [
        { walletAddress: { $regex: new RegExp(walletAddress, 'i') } },
        { 'merchantApiResponse.webhookData.walletAddress': { $regex: new RegExp(walletAddress, 'i') } },
        { 'post.walletAddress': { $regex: new RegExp(walletAddress, 'i') } },
        { 'merchantApiResponse.pay_address': { $regex: new RegExp(walletAddress, 'i') } },
      ] },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];
  const paymentActionsLogAggregation = [
    {
      $match: { $or: [
        { message: { $regex: new RegExp(walletAddress, 'i') } },
        { 'data.dest': { $regex: new RegExp(walletAddress, 'i') } },
        { 'data.walletAddress': { $regex: new RegExp(walletAddress, 'i') } },
        { 'webhookData.walletAddress': { $regex: new RegExp(walletAddress, 'i') } },
      ] },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];

  const [paymentLogs, paymentActionsLogs, users] = await Promise.all([
    await PaymentLog.aggregate(paymentLogAggregation).exec(),
    await PaymentActionsLog.aggregate(paymentActionsLogAggregation).exec(),
    await User.find(
      { $or: [
        { crossmintWalletAddress: { $regex: new RegExp(walletAddress, 'i') } },
        { nftWalletAddress: { $regex: new RegExp(walletAddress, 'i') } },
      ] },
      { id: 1 },
    ).lean(),
  ]);
  const uIDs = new Set();

  if (paymentLogs.length) {
    paymentLogs.forEach((paymentlog) => {
      uIDs.add(paymentlog._id);
    });
  }
  if (paymentActionsLogs.length) {
    paymentActionsLogs.forEach((paymentActionsLog) => {
      uIDs.add(paymentActionsLog._id);
    });
  }
  if (users.length) {
    users.forEach((user) => {
      uIDs.add(user.id);
    });
  }

  return { $in: Array.from(uIDs) };
};

const getWalletAddressQuery = async (value) => {
  const operator = 'id';
  const query = await getSearchIDsByWalletAddress(value);

  return { operator, query };
};

const getSearchIDsByLogsUserAgent = async (userAgent) => {
  const aggregation = [
    {
      $match: { agent: { $regex: new RegExp(userAgent, 'i') } },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];

  const [liveLogs, recordLogs, vodLogs, loginLogs] = await Promise.all([
    await UserLogLive.aggregate(aggregation).exec(),
    await UserLogRecord.aggregate(aggregation).exec(),
    await UserLogVod.aggregate(aggregation).exec(),
    await UserLogLogin.aggregate(aggregation).exec(),
  ]);
  const uIDs = new Set();

  if (liveLogs.length) {
    liveLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (recordLogs.length) {
    recordLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (vodLogs.length) {
    vodLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }
  if (loginLogs.length) {
    loginLogs.forEach((log) => {
      uIDs.add(log._id);
    });
  }

  return { $in: Array.from(uIDs) };
};

const getLogsUserAgentQuery = async (value) => {
  const operator = 'id';
  const query = await getSearchIDsByLogsUserAgent(value);

  return { operator, query };
};

const getIsTrialQuery = async (value) => {
  const operator = 'package';
  const packageTrial = await Package.findOne({ price: 0 }, { id: 1 }).lean().exec();
  const query = value === 1 ? { $ne: packageTrial.id } : (value === 0 ? packageTrial.id : null);

  return { operator, query };
};

const getSearchIDsByCard = async (card) => {
  const paymentLogsAggregation = [
    {
      $match: { card: { $regex: card } },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];
  const encryptedCardNumber = md5(encrypt(card.toString(), `${card}${config.cardDataSecuritySalt}`));
  const userCardsAggregation = [
    {
      $match: { number: encryptedCardNumber },
    },
    {
      $group: {
        _id: '$uid',
      },
    },
  ];

  const [paymentLogs, userCards] = await Promise.all([
    await PaymentLog.aggregate(paymentLogsAggregation).exec(),
    await UserCard.aggregate(userCardsAggregation).exec(),
  ]);
  const uIDs = new Set();

  if (paymentLogs.length) {
    paymentLogs.forEach((paymentlog) => {
      uIDs.add(paymentlog._id);
    });
  }
  if (userCards.length) {
    userCards.forEach((userCard) => {
      uIDs.add(userCard._id);
    });
  }

  return { $in: Array.from(uIDs) };
};

const getCardQuery = async (value) => {
  const operator = 'id';
  const query = await getSearchIDsByCard(value);

  return { operator, query };
};

const getUserPermissionGroupIdQuery = (value) => {
  const operator = '$or';
  const query = [
    { permissionGroups: mongoose.Types.ObjectId(value) },
    { permissionGroups: value },
  ];

  return { operator, query };
};

/**
 * This method returns query onject
 *
 * @param {string} key
 * @param {string|number} value
 *
 * @returns {Object} - query params
 */
module.exports = async (key, value) => {
  let response = {};

  switch (key) {
    case SEARCH_REQUEST_DATA.EMAIL: {
      response = getEmailQuery(key, value);
      break;
    }
    case SEARCH_REQUEST_DATA.EMAILS: {
      response = getEmailsQuery(key, value);
      break;
    }
    case SEARCH_REQUEST_DATA.TKEY: {
      response = await getTkeyQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.CARD: {
      response = await getCardQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.IS_EXPIRED: {
      response = getExpiredQuery(key, value);
      break;
    }
    case SEARCH_REQUEST_DATA.IS_ADMIN: {
      response = getIsRoleQuery(key, value, 'isadmin');
      break;
    }
    case SEARCH_REQUEST_DATA.IS_SUPPORT: {
      response = getIsRoleQuery(key, value, 'issupport');
      break;
    }
    case SEARCH_REQUEST_DATA.IS_DEVELOPER: {
      response = getIsRoleQuery(key, value, 'isdeveloper');
      break;
    }
    case SEARCH_REQUEST_DATA.IS_VODADMIN: {
      response = getIsRoleQuery(key, value, 'isvodadmin');
      break;
    }
    case SEARCH_REQUEST_DATA.IS_ACTIVE: {
      response = getIsNumberQuery(key, value, 'isactive');
      break;
    }
    case SEARCH_REQUEST_DATA.IS_TRIAL: {
      response = await getIsTrialQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.ID: {
      response = getIdQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.NAME: {
      response = getNameQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.TRANSACTION_ID: {
      response = await getTransactionIdQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.WALLET_ADDRESSS: {
      response = await getWalletAddressQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.USER_PERMISSION_GROUP_ID: {
      response = getUserPermissionGroupIdQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.IS_BLACKLISTED: {
      response = await getIsBlacklistedQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.IS_STEALER: {
      response = await getIsStealerQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.PAID_TYPES: {
      response = await getPaidTypesQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.PAID: {
      response = await getPaidQuery();
      break;
    }
    case SEARCH_REQUEST_DATA.USERS_FROM_COUNTRIES: {
      response = await getUsersFromCountriesQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.WATCH_ACTIVITIES_LAST_DAYS: {
      response = await getWatchActivitiesLastDaysQuery(value);
      break;
    }
    case SEARCH_REQUEST_DATA.USER_AGENT: {
      response = await getLogsUserAgentQuery(value);
      break;
    }
    default:
      break;
  }

  return response;
};
