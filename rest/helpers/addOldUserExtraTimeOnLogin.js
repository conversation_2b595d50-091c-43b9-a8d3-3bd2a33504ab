const isBlacklistedUser = require('../commands/payment/helpers/rules/isBlacklistedUser');
const Suspended = require('../model/audb/Suspended');
const hasPayments = require('../commands/payment/helpers/rules/hasPayments');
const moment = require('moment');

/**
 * Old users expired a month ago can receive free 10 days full package due to 01 Mar 2025
 * @param {Object} user
 * @returns {Boolean}
 */
module.exports = async (user) => {
  // TODO remove/disable after 01 Mar 2025
  // 1643720400 - 01 Feb 2022
  // 1734310800 - 16 Dec 2024
  // 1740790800 - 01 Mar 2025
  if (moment().unix() < 1740790800 && user.created < 1643720400 && user.expires < 1734310800) {
    const isBlacklisted = await isBlacklistedUser({ user });

    if (!isBlacklisted) {
      const suspendedUserId = await Suspended.findOne({ uid: user.id }).lean();

      if (!suspendedUserId) {
        const hasUserPaymentLogs = await hasPayments({ user, minLogs: 2 });

        if (hasUserPaymentLogs) {
          user.package = 6;
          user.expires = moment().add( 10, 'days').unix();
          user.markModified('package');
          user.markModified('expires');
          await user.save();
          await user.addAdminComment('Added 10 days - hostages back deal');

          return true;
        }
      }
    }
  }

  return false;
};
