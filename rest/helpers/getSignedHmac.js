const crypto = require('crypto');
const strtr = require('locutus/php/strings/strtr');

const HMAC_SECRET = 'IFJ8ayxdk4Jb5Yql';
const HMAC_ALGO = 'sha256';

module.exports = ({ str, hmacAlgo = HMAC_ALGO, secret = HMAC_SECRET, newVersion = true }) => {
  const hmac = crypto.createHmac(hmacAlgo, secret);
  hmac.update(str);
  const hashed = hmac.digest('base64');

  return newVersion ? strtr(hashed, '+/', '-_').replace(/=/g, '') : strtr(hashed, '+/', '-_').replace(/=/g, '%3D');
};
