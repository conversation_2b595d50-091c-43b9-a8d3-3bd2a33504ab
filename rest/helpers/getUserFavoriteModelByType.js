const { favorites: { TYPES, TYPE_BY_ALIAS } } = require('@s1/api-constants');
const UserFavoriteLive = require('../model/audb/UserFavoriteLive');
const UserFavoriteTvShow = require('../model/audb/UserFavoriteTvShow');
const UserFavoriteVod = require('../model/audb/UserFavoriteVod');

const modelTypeMapping = {
  [TYPES.LIVE]: UserFavoriteLive,
  [TYPES.RADIO]: UserFavoriteLive,
  [TYPES.VOD]: UserFavoriteVod,
  [TYPES.TVSHOW]: UserFavoriteTvShow,
};

module.exports = (typeAlias) => {
  const type = TYPE_BY_ALIAS[typeAlias];

  return modelTypeMapping[type];
};
