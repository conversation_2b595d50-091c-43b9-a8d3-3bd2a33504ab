const LOCALES = require('@s1/api-constants').locales;

/**
 * This method returns localized field value from the source object
 * with locale from params or user global locale
 *
 * @param {object} entity
 * @param {string} fieldName
 * @param {string} locale
 * @returns {string}
 */
module.exports = (entity, fieldName, locale = undefined) => {
  if (!locale || !LOCALES[locale]) return entity[fieldName] || '';

  return entity[`${fieldName}_${LOCALES[locale][1]}`] || entity[LOCALES[locale][0] + fieldName] || entity[fieldName] || '';
};

