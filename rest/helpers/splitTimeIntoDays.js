const moment = require('moment-timezone');

/**
 * Split range in seconds to range by days in seconds
 *
 * @param {number} startTimeInSeconds - time in seconds
 * @param {number} endTimeInSeconds - time in seconds
 *
 * @return {Array<{startTime, endTime}>} - array of objects {startTime, endTime}
 * */
module.exports = (startTimeInSeconds, endTimeInSeconds) => {
  const result = [];

  const startDate = moment.unix(startTimeInSeconds).tz('UTC');
  const endDate = moment.unix(endTimeInSeconds).tz('UTC');
  const endDateDay = moment.unix(endTimeInSeconds).tz('UTC')
    .startOf('day').add(1, 'day')
    .subtract(1, 'second');
  const currentDate = startDate.clone().tz('UTC').startOf('day');
  let iteration = 0;

  while (currentDate.unix() <= endDateDay.unix()) {
    const startOfDay = currentDate.clone().tz('UTC').startOf('day');
    const endOfDay = currentDate.clone().tz('UTC').startOf('day').add(1, 'day')
      .subtract(1, 'second');

    if (endOfDay.unix() >= endTimeInSeconds) {
      if (iteration === 0) {
        result.push({
          startTime: startDate.unix(),
          endTime: endDate.unix(),
        });
      } else {
        result.push({
          startTime: startOfDay.unix(),
          endTime: endDate.unix(),
        });
      }
    } else if (iteration === 0) {
      result.push({
        startTime: startDate.unix(),
        endTime: endOfDay.unix(),
      });
    } else {
      result.push({
        startTime: startOfDay.unix(),
        endTime: endOfDay.unix(),
      });
    }

    currentDate.add(1, 'day');
    iteration++;
  }

  return result;
};
