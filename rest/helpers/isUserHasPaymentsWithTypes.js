const PaymentLog = require('../model/audb/PaymentLog');

/**
 * @description Check if user has at least one payment with payment types
 *
 * @param {Number} userId - user ID
 * @param {Array} paymentTypes - payment types
 * @return {boolean|Promise} true|false
 * */
module.exports = async (userId, paymentTypes) => {
  if (!userId) return false;
  if (paymentTypes.length === 0) return true;

  const paymentLogs = await PaymentLog.find({
    uid: userId, amount: { $gt: 0 }, upback: true, pptype: { $in: paymentTypes },
  }).cache(600).lean();

  if (paymentLogs.length > 0) return true;

  return false;
};
