const { Package } = require('../model/audb/Package');
const PackageToFeatures = require('../model/audb/PackageToFeatures');

const loadOnePackage = async (id) => {
  id = parseInt(id);
  const pack = await Package.findOne({ id }).lean().exec();
  const packageToFeatures = await PackageToFeatures.findOne({ id: pack.pgid }).lean().exec();

  if (packageToFeatures) {
    for (const key of Object.keys(packageToFeatures)) {
      if (key === '_id' || key === 'id') continue;
      if (key === 'name') {
        pack.name = packageToFeatures[key];
        pack.pgname = packageToFeatures[key];
        continue;
      }

      pack[key] = packageToFeatures[key];
    }
  }

  return pack;
};

const loadAllPackages = async () => {
  const allPacks = await Package.find({}, { id: 1 }).lean().exec();
  const result = [];
  for (const pack of allPacks) {
    const pckg = await loadOnePackage(pack.id);
    result.push(pckg);
  }

  return result;
};

module.exports = { loadOnePackage, loadAllPackages };
