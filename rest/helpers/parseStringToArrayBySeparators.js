/**
 * Parse string to array by separators list
 *
 * @param {string} value
 * @param {array} separators
 *
 * @returns {array}
 */
module.exports = (value, separators) => {
  if (!value) return [];
  if (!separators || !separators.length) return [value];

  let parsedArray = [value];

  for (let i = 0; i < separators.length; ++i) {
    const separator = separators[i];

    if (value.indexOf(separator) > 0) {
      parsedArray = value.split(separator);
      break;
    }
  }

  return parsedArray;
};
