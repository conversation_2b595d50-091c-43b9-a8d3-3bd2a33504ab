const UserFavoriteVod = require('../model/audb/UserFavoriteVod');
const UserFavoriteTvShow = require('../model/audb/UserFavoriteTvShow');

module.exports = async function prepareVod(vod, { user, isTvShow }, withoutFavorites = false) {
  const favoritesModel = isTvShow ? UserFavoriteTvShow : UserFavoriteVod;
  const promises = [vod.resolvePicture()];
  promises.push((user && !withoutFavorites) ? favoritesModel.isInFavorites(user, vod) : Promise.resolve(0));
  const [showpic, isInFavorites] = await Promise.all(promises);
  vod.showpic = showpic;

  if (!withoutFavorites) vod.isinfav = +isInFavorites;
};

