/**
 * This method returns user payment addresses with all not empty fields
 */
module.exports = (user) => {
  const addresses = [];
  const userBillingAddresses = user.billingAddresses;

  if (userBillingAddresses) {
    // filter empty addresses
    Object.entries(userBillingAddresses).forEach(([, value]) => {
      // filter billing addresses for empty countries
      if (!value.hasOwnProperty('country') || !value.country || !value.country.trim()) {
        return;
      }
      if (
        (value.country === 'US' && (!value.hasOwnProperty('state') || !value.state || !value.state.trim()))
        || !value.hasOwnProperty('city') || !value.city || !value.city.trim()
        || !value.hasOwnProperty('address') || !value.address || !value.address.trim()
        || !value.hasOwnProperty('zip') || !value.zip || !value.zip.trim()
        || !value.hasOwnProperty('firstname') || !value.firstname || !value.firstname.trim()
        || !value.hasOwnProperty('lastname') || !value.lastname || !value.lastname.trim()
      ) {
        return;
      }

      addresses.push(value);
    });
  }
  if (addresses.length) return addresses[addresses.length - 1];

  return null;
};
