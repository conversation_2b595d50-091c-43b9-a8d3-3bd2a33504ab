const config = require('../../../config');

const ERRORS = {
  NAME_ERROR: {
    code: 401,
    key: 'name',
    text: 'שם מלא צריך להכיל אותיות בעברית או באנגלית בלבד',
  },
  EMAIL_ERROR: {
    code: 404,
    key: 'email',
    text: 'כתובת המייל שגויה',
  },
  REFERRAL_ERROR: {
    code: 421,
    key: 'referralerror',
    text: 'This invite code not exist or expired!',
  },
  INVALID_PHONE_ERROR: {
    code: 405,
    key: 'phone',
    text: 'שדה זה צריך להכיל מספרים בלבד',
  },
  PHONE_TOO_SHORT_ERROR: {
    code: 406,
    key: 'phone_ch',
    text: 'מספר הטלפון שהכנסת קצר מידי',
  },
  INVALID_PASSWORD_ERROR: {
    code: 407,
    key: 'password',
    text: ' סיסמה צריכה להכיל אותיות באנגלית/מספרים בלבד',
  },
  PASSWORD_TOO_SHORT_ERROR: {
    code: 408,
    key: 'password_ch',
    text: 'סיסמה צריכה להכיל לפחות 4 אותיות/מספרים ומעלה',
  },
  PASSWORD_CONFIRM_ERROR: {
    code: 409,
    key: 'password_ch',
    text: 'סיסמת אימות אינה תואמת את הסיסמה שרשמת',
  },
  NAME_REQUIRED_ERROR: {
    code: 410,
    key: 'fill1',
    text: 'שדה חובה',
  },
  EMAIL_REQUIRED_ERROR: {
    code: 412,
    key: 'fill3',
    text: 'שדה חובה',
  },
  PASSWORD_REQUIRED_ERROR: {
    code: 413,
    key: 'fill4',
    text: 'שדה חובה',
  },
  PASSWORD_CONFIRM_REQUIRED_ERROR: {
    code: 415,
    key: 'fill5',
    text: 'שדה חובה',
  },
  PHONE_REQUIRED_ERROR: {
    code: 416,
    key: 'fill6',
    text: 'שדה חובה',
  },
  CAPTCHA_REQUIRED_ERROR: {
    code: 417,
    key: 'fill7',
    text: 'שדה חובה',
  },
  INVALID_CAPTCHA_ERROR: {
    code: 417,
    key: 'fill7',
    text: 'שדה חובה',
  },
  PHONEAREA_REQUIRED_ERROR: {
    code: 418,
    key: 'phone_area',
    text: 'Phone area required',
  },
  INVALID_PHONEAREA_ERROR: {
    code: 419,
    key: 'phone_area',
    text: 'Invalid phone area',
  },
};
const REGISTER_EMAIL_DATA = {
  tagname: 'registertemplate',
  from: config.email.noReply,
};

module.exports = {
  ERRORS,
  REGISTER_EMAIL_DATA,
};
