const { ERRORS } = require('./constants');
const ReferralCode = require('../../model/audb/ReferralCode');
const validateEmail = require('../validators/emailValidator');
const validateReferralCode = require('../validators/referralCodeValidator');

class RegisterValidator {
  constructor() {
    this.results = {
      error: [],
    };
  }

  addErrorIntoResults(error) {
    this.results.error.push(error.code);
    this.results[error.key] = error.text;
  }

  validateName(name) {
    if (!name) return this.addErrorIntoResults(ERRORS.NAME_REQUIRED_ERROR);

    const regexp = /^([-a-z\u{5D0}-\u{5EA}0-9\s_-])+$/iu;

    if (!regexp.test(name)) this.addErrorIntoResults(ERRORS.NAME_ERROR);
  }

  validateEmail(email) {
    if (!email) return this.addErrorIntoResults(ERRORS.EMAIL_REQUIRED_ERROR);
    if (!validateEmail(email)) this.addErrorIntoResults(ERRORS.EMAIL_ERROR);
  }

  async validateReferral(referralCode) {
    if (!referralCode) return;
    if (!validateReferralCode(referralCode)) this.addErrorIntoResults(ERRORS.REFERRAL_ERROR);
    else {
      const codeExists = await ReferralCode.findOne({ uniquekey: referralCode }).exec();

      if (!codeExists) this.addErrorIntoResults(ERRORS.REFERRAL_ERROR);
    }
  }

  validatePhoneArea(phoneArea) {
    if (!phoneArea) return this.addErrorIntoResults(ERRORS.PHONEAREA_REQUIRED_ERROR);

    const regexp = /^(\+?\d-?){1,4}$/;

    if (!regexp.test(phoneArea)) return this.addErrorIntoResults(ERRORS.INVALID_PHONEAREA_ERROR);
  }

  validatePhone(phone) {
    if (!phone) return this.addErrorIntoResults(ERRORS.PHONE_REQUIRED_ERROR);
    if (phone.length < 5) return this.addErrorIntoResults(ERRORS.PHONE_TOO_SHORT_ERROR);

    const regexp = /^[+0-9\-()\s]*$/;

    if (!regexp.test(phone)) this.addErrorIntoResults(ERRORS.INVALID_PHONE_ERROR);
  }

  validatePassword(password, confirm) {
    if (!password) return this.addErrorIntoResults(ERRORS.PASSWORD_REQUIRED_ERROR);
    if (!confirm) return this.addErrorIntoResults(ERRORS.PASSWORD_CONFIRM_REQUIRED_ERROR);
    if (password.length < 4) return this.addErrorIntoResults(ERRORS.PASSWORD_TOO_SHORT_ERROR);
    if (password !== confirm) return this.addErrorIntoResults(ERRORS.PASSWORD_CONFIRM_ERROR);

    const regexp = /[A-Za-z0-9]{4,}/;

    if (!regexp.test(password)) this.addErrorIntoResults(ERRORS.INVALID_PASSWORD_ERROR);
  }

  validateCaptcha(captcha, confirmPhrase) {
    if (!captcha) return this.addErrorIntoResults(ERRORS.CAPTCHA_REQUIRED_ERROR);
    if (captcha !== confirmPhrase) return this.addErrorIntoResults(ERRORS.INVALID_CAPTCHA_ERROR);
  }

  async validate(userData) {
    const { name, email, password, repassword, phone, phonearea, referral = '' } = userData;

    this.validateName(name);
    this.validateEmail(email);
    await this.validateReferral(referral);
    this.validatePhone(phone);
    this.validatePhoneArea(phonearea);
    this.validatePassword(password, repassword);

    return this.results;
  }
}

module.exports = RegisterValidator;
