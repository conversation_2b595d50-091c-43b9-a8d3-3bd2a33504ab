const { UserDontExistsError } = require('@s1/api-errors');
const User = require('../model/audb/User');

/**
 * This method returns user model from request or get by ID
 *
 * @param {object} req
 * @returns {User}
 */
module.exports = async (req) => {
  const { userId } = req.params;
  let user;

  if (userId) {
    user = await User.findOne({ id: userId });

    if (!user) throw new UserDontExistsError();
  } else {
    user = req.user;
  }

  return user;
};
