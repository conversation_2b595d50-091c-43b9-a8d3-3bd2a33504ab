/**
 * Exclude path from the middleware
 *
 * @param {array} paths - list of path
 * @param middleware
 * */
const unless = function (paths, middleware) {
  return function (req, res, next) {
    for (let i = 0; i < paths.length; ++i) {
      const path = paths[i];

      if (req.path.indexOf(path) >= 0) {
        return next();
      }
    }

    return middleware(req, res, next);
  };
};

module.exports.unless = unless;
