const isMongooseObjectID = require('../helpers/isMongooseObjectID');

const formatId = id => ({ $id: id.toString() });
const isArray = item => Array.isArray(item);
const isObject = item => item !== null && !Array.isArray(item) && typeof item === 'object';

function getFormattedObject(source) {
  const leanSource = JSON.parse(JSON.stringify(source));
  const sourceKeys = Object.keys(leanSource);

  sourceKeys.forEach((key) => {
    leanSource[key] = formatResponse(leanSource[key]);
  });

  return leanSource;
}

function getFormattedArray(source) {
  const formattedArray = source.map((item) => {
    const formattedItem = formatResponse(item);

    return formattedItem;
  });

  return formattedArray;
}

function formatResponse(source) {
  if (isMongooseObjectID(source)) {
    source = formatId(source);
  } else if (isObject(source)) {
    source = getFormattedObject(source);
  } else if (isArray(source)) {
    source = getFormattedArray(source);
  }

  return source;
}

/**
 * This method returns the same response as PHP API with the correct structure '_id',
 * because PHP API returns { $id: { _id: 'uniqueKey' }},
 * but our Mongoose doesn't do it and returns - { _id: 'uniqueKey' }.
 * *Recursive*
 *
 * @param {object} response - Response from DataBase
 * @returns {object | Array} {
 *  { $id: { _id: 'uniqueKey' }},
 *  *some data*,
 * } - Returns the lean data without Mongoose methods.
 */
module.exports = (response) => {
  if (!response) {
    return null;
  }

  let responseArray = [];
  response = response._doc || response;

  if (isObject(response)) {
    responseArray.push(response);
  }
  if (isArray(response)) {
    responseArray = response;
  }

  responseArray.forEach((item) => {
    const keys = Object.keys(item);

    keys.forEach((key) => {
      item[key] = formatResponse(item[key]);
    });
  });

  if (isArray(response)) {
    return responseArray;
  }
  if (isObject(response)) {
    return responseArray[0];
  }

  return [];
};
