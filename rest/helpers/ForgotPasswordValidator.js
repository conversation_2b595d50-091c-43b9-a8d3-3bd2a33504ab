const {
  CheckEmailError,
  MissingEmailError,
  IncorrectEmailError,
  MissingCaptchaError,
  WrongCaptchaError,
  BlockedEmailError,
  PasswordValidationRequiredError,
  PasswordValidationInvalidError,
  PasswordValidationShortError,
  PasswordValidationNotEqualsError,
} = require('@s1/api-errors');
const BlockedEmail = require('../model/audb/BlockedEmail');

class ForgotPasswordValidator {
  constructor(locale) {
    this.locale = locale;
  }

  async validateEmail(email) {
    if (!email) throw new MissingEmailError(this.locale);

    const regexp = /^(?:[\w!#$%&'*+\-/=?^`{|}~]+\.)*[\w!#$%&'*+\-/=?^`{|}~]+@(?:(?:(?:[a-zA-Z0-9_](?:[a-zA-Z0-9_-](?!\.)){0,61}[a-zA-Z0-9_-]?\.)+[a-zA-Z0-9_](?:[a-zA-Z0-9_-](?!$)){0,61}[a-zA-Z0-9_]?)|(?:\[(?:(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d{1,2}|2[0-4]\d|25[0-5])]))$/;

    if (!regexp.test(email)) throw new IncorrectEmailError(this.locale);

    const emailBlocked = await BlockedEmail.isBlocked(email);

    if (emailBlocked) throw new BlockedEmailError(this.locale);
  }

  validateCaptcha(data) {
    const { captcha, verifyCaptcha } = data;

    if (!captcha) throw new MissingCaptchaError(this.locale);

    const captchaConfirmed = captcha.toLowerCase() === verifyCaptcha.toLowerCase();

    if (!captchaConfirmed) throw new WrongCaptchaError(this.locale);
  }

  validatePassword(data) {
    const { password1, password2 } = data;

    if (!password1) throw new PasswordValidationRequiredError(this.locale);

    const regexp = /[^A-Za-z0-9]/;

    if (regexp.test(password1)) throw new PasswordValidationInvalidError(this.locale);
    if (password1.length < 4) throw new PasswordValidationShortError(this.locale);
    if (password1 !== password2) throw new PasswordValidationNotEqualsError(this.locale);
  }

  validateResetPasswordKey(data) {
    const { resetPasswordKey, existingUser } = data;

    if (!resetPasswordKey || !existingUser
      || resetPasswordKey !== existingUser.resetpasswordkey) throw new CheckEmailError(this.locale);
  }

  async validateResetForgotPassword(data) {
    this.validateResetPasswordKey(data);
    this.validateCaptcha(data);
    this.validatePassword(data);
  }
}

module.exports = ForgotPasswordValidator;
