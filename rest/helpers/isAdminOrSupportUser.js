const mongoose = require('mongoose');

module.exports = (user) => {
  if (!user) return false;

  const data = user instanceof mongoose.Model ? user.toObject() : user;

  if ((data.hasOwnProperty('isadmin') && data.isadmin)
    || (data.hasOwnProperty('issupport') && data.issupport)
    || (data.hasOwnProperty('isdeveloper') && data.isdeveloper)
    || (data.hasOwnProperty('isvodadmin') && data.isvodadmin)
    || (data.hasOwnProperty('issuperadmin') && data.issuperadmin)) return true;

  return false;
};
