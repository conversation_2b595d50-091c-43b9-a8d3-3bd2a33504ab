const { Reader } = require('maxmind');
const fs = require('fs');
const maxmindConf = require('../../config').maxmind;

let ispLookup;
let ispAltLookup;
let cityLookup;
let connectionTypeLookup;
const resetCache = () => {
  const ispLookupBuffer = fs.readFileSync(maxmindConf.isp);
  ispLookup = new Reader(ispLookupBuffer);
  const ispAltLookupBuffer = fs.readFileSync(maxmindConf.ispAlt);
  ispAltLookup = new Reader(ispAltLookupBuffer);
  const cityLookupBuffer = fs.readFileSync(maxmindConf.city);
  cityLookup = new Reader(cityLookupBuffer);
  const connectionTypeLookupBuffer = fs.readFileSync(maxmindConf.connType);
  connectionTypeLookup = new Reader(connectionTypeLookupBuffer);
};
resetCache();

module.exports.getIspLookup = () => ispLookup;

module.exports.getIspAltLookup = () => ispAltLookup;

module.exports.getCityLookup = () => cityLookup;

module.exports.getConnectionTypeLookup = () => connectionTypeLookup;

module.exports.resetCache = () => resetCache();
