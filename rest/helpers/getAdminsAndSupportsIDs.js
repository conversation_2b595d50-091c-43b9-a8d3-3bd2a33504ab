module.exports = async () => {
  // import there, because of circular dependencies
  const User = require('../model/audb/User');

  const users = await User.find(
    { $or: [
      { $and: [{ isdeveloper: { $exists: true } }, { isdeveloper: 1 }] },
      { $and: [{ issupport: { $exists: true } }, { issupport: 1 }] },
      { $and: [{ isadmin: { $exists: true } }, { isadmin: 1 }] },
      { $and: [{ issuperadmin: { $exists: true } }, { issuperadmin: 1 }] },
    ] },
    { id: 1 },
  ).cache(3600).exec();

  const userIDs = users.map(user => user.id);

  return userIDs;
};
