const LOCALES = require('@s1/api-constants').locales;
const config = require('../../config');

const LOCALES_POSITION = {
  START: 'start',
  END: 'end',
};

/**
 * This method returns localized key
 * with locale from params or user global locale
 *
 * example: { key: 'name', localePosition: 'start', locale: 'en' } return ename;
 * example: { key: 'name', localePosition: 'end', locale: 'en } return name_en;
 *
 * @param {string} key
 * @param {string} locale
 * @param {string} localePosition
 * @returns {string}
 */

module.exports = (key, locale, localePosition = LOCALES_POSITION.END) => {
  if (!LOCALES[locale] || locale === config.i18n.defaultLocale) return key;
  if (localePosition === LOCALES_POSITION.START) return `${LOCALES[locale][0]}${key}`;
  if (localePosition === LOCALES_POSITION.END) return `${key}_${LOCALES[locale][1]}`;

  // default value like name_en
  return `${key}_${LOCALES[locale][1]}`;
};
