/**
 * Luhn Algorithm used to validate a variety of identification numbers, such as credit card numbers, IMEI numbers,
 * National Provider Identifier numbers etc.
 * */
module.exports = (value) => {
  const cardNumber = value.replace(/\D/g, '');

  if (/[^0-9-\s]+/.test(cardNumber)) return false;

  const len = cardNumber.length;
  const parity = len % 2;
  let sum = 0;
  for (let i = len - 1; i >= 0; i--) {
    let d = parseInt(cardNumber.charAt(i));

    if (i % 2 === parity) { d *= 2; }
    if (d > 9) { d -= 9; }

    sum += d;
  }

  return sum % 10 === 0;
};
