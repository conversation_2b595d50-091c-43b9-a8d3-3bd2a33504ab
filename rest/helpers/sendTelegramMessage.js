const axios = require('axios');
const config = require('../../config');

/**
 * Send telegram message
 *
 * @param {string|object} message
 * @returns {Promise<boolean>}
 */
module.exports = async (message) => {
  const { apiUrl, botToken, chatId } = config.telegram;

  if (!apiUrl || !botToken || !chatId) return false;

  let notificationText = message;
  let result = false;

  try {
    if (typeof message !== 'string') notificationText = JSON.stringify(message);

    const client = axios.create({ baseURL: `${apiUrl}/bot${botToken}/` });
    await client.get(`/sendMessage?&chat_id=${chatId}&text=${notificationText}`);

    result = true;
  } catch (e) {
    console.log('Cannot send telegram message, error:', e);
  }

  return result;
};
