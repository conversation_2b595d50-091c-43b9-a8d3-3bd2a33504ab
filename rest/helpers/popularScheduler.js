const schedule = require('node-schedule');
const PopularEpisodes = require('../model/audb/PopularEpisodes');
const PopularRecords = require('../model/audb/PopularRecords');
const PopularMovies = require('../model/audb/PopularMovies');

schedule.scheduleJob('10 0 0 * * *', async () => {
  await PopularEpisodes.collectDailyStats();
  await PopularRecords.collectDailyStats();
  await PopularMovies.collectDailyStats();
});
