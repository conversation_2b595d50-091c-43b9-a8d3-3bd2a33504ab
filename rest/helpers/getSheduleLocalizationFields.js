const LOCALES = require('@s1/api-constants').locales;

/**
 * This methor returns all localizations of certain field
 *
 * @param {string} fieldName
 * @returns {array} localizationArray
 */
module.exports = (schedule) => {
  const fields = ['name', 'description', 'genre'];
  const fieldsData = {};
  const getData = (field, locale) => schedule[LOCALES[locale][0] + field] || schedule[`${field}_${LOCALES[locale][1]}`] || null;
  for (const field of fields) {
    Object.entries(LOCALES).forEach(([key]) => {
      const data = getData(field, key);

      if (data) fieldsData[`${field}_${key}`] = data;
    });
  }

  return fieldsData;
};
