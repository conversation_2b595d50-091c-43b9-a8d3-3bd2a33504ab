const { APPLE_TESTERS, USER_GROUPS } = require('../constants/testers');
const isBlacklistedUser = require('../commands/payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../commands/payment/helpers/rules/isStealerUser');
const getAllPaymentTypes = require('../commands/payment/getAllTypes');
const getAppAccessByName = require('../commands/apps/getAppAccessByName');
const isUserHasPaymentsWithTypes = require('./isUserHasPaymentsWithTypes');

/**
 * we have webcam app in the app store.
 * Special apple tester user will see some dummy fake webcam application.
 * Trusted users will be able to login and use our main video app
 * Other users will not be able even login.
 *
 * @param {user as User} user - User info by session ID
 * @param {String} appName - App name
 * @param {Object} appAccess - App access config
 * @returns {boolean|Promise} true|false
 */
module.exports = async (user, appName = null, appAccess = null) => {
  if (APPLE_TESTERS.includes(user.id)) return USER_GROUPS.apple_testers;
  if (appName || appAccess) {
    if (!appAccess) appAccess = await getAppAccessByName(appName);
    if (appAccess) {
      if (appAccess.denyBlacklistedUser) {
        const isUserBlacklisted = await isBlacklistedUser({ user });

        if (isUserBlacklisted) return USER_GROUPS.regular;
      }
      if (appAccess.denyStealerUser) {
        const isUserStealer = await isStealerUser({ user });

        if (isUserStealer) return USER_GROUPS.regular;
      }

      const allPaymentTypesResult = await getAllPaymentTypes();
      const allPaymentTypes = allPaymentTypesResult.list;

      const paymentTypes = {};
      allPaymentTypes.forEach((type) => {
        paymentTypes[type._id] = type;
      });
      const allowedPaymentTypeNames = appAccess.allowedPaymentTypes ? appAccess.allowedPaymentTypes.map((_id) => {
        return paymentTypes.hasOwnProperty(_id) ? paymentTypes[_id].name : null;
      }).filter(type => !!type) : [];

      if (allowedPaymentTypeNames.length) {
        const isUserHasPayments = await isUserHasPaymentsWithTypes(user.id, allowedPaymentTypeNames);

        if (isUserHasPayments) return USER_GROUPS.trusted;

        return USER_GROUPS.regular;
      }
    }
  }

  // if appName or appAccess not exists, allow by default
  return USER_GROUPS.trusted;
};
