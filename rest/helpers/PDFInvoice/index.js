const moment = require('moment');
const { resolve } = require('path');
const PDFDocument = require('pdfkit');

const TEXT_COLOR_BASE = '#313131';
const TEXT_COLOR_LIGHT = '#818181';
const COLOR_LIGHT_BLUE = '#f4f9fb';
const COLOR_BLUE = '#0aaae3';
const COLOR_LIGHT_GREY = '#ebebeb';

module.exports = {
  /**
   * @param {Object} paymentData           Invoice data.
   * @param {string} paymentData.firstname Client name.
   * @param {string} paymentData.pptype    Payment method.
   * @param {number} paymentData.card      Card number (last 4 digits).
   * @param {number} paymentData.tkey      Payment ID.
   * @param {number} paymentData.created   "Created" timestamp in UNIX format.
   * @param {string} paymentData.epricestr Package description.
   * @param {string} paymentData.currency  Card currency.
   * @param {number} paymentData.amount    Price as numeric value.
   *
   * @return {Promise<Buffer>}
   */
  generate: (paymentData) => { // eslint-disable-line
    return new Promise(async (res) => {
      const buffers = [];
      const invoice = new PDFDocument({
        margins: {
          top: 30,
          bottom: 30,
          left: 30,
          right: 30,
        },
      });
      invoice
        .on('data', buffers.push.bind(buffers))
        .on('end', () => res(Buffer.concat(buffers)));
      invoice.rect(0, 0, 1000, 20).fillAndStroke(COLOR_BLUE, COLOR_BLUE);
      invoice.rect(0, 20, 1000, 115).fillAndStroke(COLOR_LIGHT_BLUE, COLOR_LIGHT_BLUE);
      invoice.image(resolve(__dirname, './logo.png'), 30, 40, { width: 175, height: 45 });
      invoice.fontSize(14)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text('THIS IS YOUR INVOICE', 30, 95);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_LIGHT)
        .font('Helvetica')
        .text('DigitalSatKey ltd', 485, 48, { align: 'right' });
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_LIGHT)
        .font('Helvetica')
        .text('Cihannuma Mahallesi, Saray Cad', 395, 68, { align: 'right' });
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_LIGHT)
        .font('Helvetica')
        .text('34353 Istanbul-Turkey', 455, 88, { align: 'right' });
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica-Bold')
        .text('Name', 30, 170);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(paymentData.firstname, 110, 170);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica-Bold')
        .text('Card No', 30, 195);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(paymentData.card, 110, 195);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica-Bold')
        .text('Number', 435, 170);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(`#${paymentData.tkey}`, 515, 170, { align: 'right' });
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica-Bold')
        .text('Date', 435, 195);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(moment(paymentData.created * 1000).format('DD-MM-YYYY'), 515, 195, { align: 'right' });
      invoice.rect(30, 260, 550, 2).fillAndStroke(COLOR_BLUE, COLOR_BLUE);
      invoice.fontSize(12)
        .fillColor(COLOR_BLUE)
        .font('Helvetica-Bold')
        .text('Description', 30, 285);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(paymentData.epricestr, 30, 310);
      invoice.fontSize(12)
        .fillColor(COLOR_BLUE)
        .font('Helvetica-Bold')
        .text('Amount', 510, 285, { align: 'right' });
      const currency = paymentData.currency === 'usd' ? '$' : '';
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(`${currency}${Math.abs(+paymentData.amount)}`, 510, 310, { align: 'right' });
      invoice.rect(30, 355, 550, 1).fillAndStroke(COLOR_LIGHT_GREY, COLOR_LIGHT_GREY);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica-Bold')
        .text('PAYMENT METHOD:', 30, 385);
      invoice.fontSize(12)
        .fillColor(TEXT_COLOR_BASE)
        .font('Helvetica')
        .text(paymentData.pptype, 30, 415);
      invoice.end();
    });
  },
};
