const vdfConnection = require('../model/vdf/connection');
const audbConnection = require('../model/audb/connection');

module.exports = ({ dbName = 'audb', schema, modelName }) => {
  if (!schema || !modelName) throw new Error('schema and modelName are required');
  if (dbName === 'vdf') {
    const VDFModel = vdfConnection.model(modelName, schema);

    return VDFModel;
  }

  const AudbModel = audbConnection.model(modelName, schema);

  return AudbModel;
};
