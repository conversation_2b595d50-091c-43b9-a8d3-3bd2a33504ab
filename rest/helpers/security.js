const crypto = require('crypto');
const redis = require('../service/redisClient');

const ENCRYPT_METHOD = 'AES-256-CBC';

const getConfigs = (securitySalt) => {
  const key = crypto.scryptSync(securitySalt, 'salt', 32);
  const iv = Buffer.alloc(16, 0); // Initialization vector

  return { iv, key };
};

/**
 * Encrypt data with securityKey
 *
 * @param {string} data
 * @param {string} securitySalt
 * */
const encrypt = (data, securitySalt) => {
  const { iv, key } = getConfigs(securitySalt);
  const cipher = crypto.createCipheriv(ENCRYPT_METHOD, key, iv);
  let crypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  crypted += cipher.final('hex');

  return crypted;
};

/**
 * Decrypt data with securityKey
 *
 * @param {string} data
 * @param {string} securitySalt
 * */
const decrypt = (data, securitySalt) => {
  const { iv, key } = getConfigs(securitySalt);

  const decipher = crypto.createDecipheriv(ENCRYPT_METHOD, key, iv);
  let dec = decipher.update(data, 'hex', 'utf8');
  dec += decipher.final('utf8');

  return JSON.parse(dec);
};

/**
 * Decrypt data with securityKey or get from the redis cache
 *
 * @param {string} data
 * @param {string} securitySalt
 * @param {string} redisKey
 * @param {number} cacheTime
 * */
const decryptWithRedis = async (data, securitySalt, redisKey = null, cacheTime = 3600) => {
  if (redisKey) {
    try {
      const cachedResultString = await redis.get(redisKey);

      if (cachedResultString) return cachedResultString;
    } catch (err) {
      console.log(`get redis key:${redisKey} error:`, err);
    }
  }

  const result = this.decrypt(data, securitySalt);

  if (redisKey) {
    try {
      await redis.set(redisKey, result.toString(), 'EX', cacheTime);
    } catch (err) {
      console.log(`set redis key:${redisKey} error:`, err);
    }
  }

  return result;
};

exports.encrypt = encrypt;

exports.decrypt = decrypt;

exports.decryptWithRedis = decryptWithRedis;
