const moment = require('moment');

/**
 * Returns a UNIX timestamp (in seconds) rounded down to the nearest specified minute interval.
 * Useful for generating stable cache keys or reducing timestamp precision
 * when querying time-based data within a given time window.
 *
 * @param {number} intervalMinutes - The minute interval to round down to (e.g., 10 for every 10 minutes).
 * @returns {number} - Rounded UNIX timestamp in seconds.
 */
module.exports = (intervalMinutes) => {
  const now = moment();
  const roundedMinutes = Math.floor(now.minutes() / intervalMinutes) * intervalMinutes;

  return moment(now)
    .minutes(roundedMinutes)
    .seconds(0)
    .milliseconds(0)
    .unix();
}
