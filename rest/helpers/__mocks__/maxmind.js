module.exports.getIspLookup = jest.fn().mockImplementation(() => ({
  get: jest.fn().mockImplementation((ip = '127.0.0.1') => {
    if (ip === '************') return { isp: 'Maxnet Telecom, Ltd' };
    if (ip === '*************') return { isp: 'Optimum Online' };
    if (ip === '************') return { isp: 'State Enterprise Scientific and Telecommunication' };

    return { isp: 'Vodafone bla bla' };
  }),
}));

module.exports.getIspAltLookup = jest.fn().mockImplementation(() => ({
  // eslint-disable-next-line no-unused-vars
  get: jest.fn().mockImplementation((ip = '127.0.0.1') => ({ autonomous_system_number: null })),
}));

module.exports.getCityLookup = jest.fn().mockImplementation(() => ({
  get: jest.fn().mockImplementation((ip = '127.0.0.1') => {
    if (ip === '*************') return {
      country: {
        names: { en: 'United States' },
        iso_code: 'US',
      },
      city: {
        names: {
          en: 'Piscataway',
        },
      },
      subdivisions: [
        {
          iso_code: 'NJ',
          names: { en: 'New Jersey' },
        },
      ],
      postal: { code: '08854' },
      location: {
        latitude: 40.4993,
        longitude: -74.399,
      },
    };
    if (ip === '************') return {
      country: {
        names: { en: 'Ukraine' },
        iso_code: 'UA',
      },
      subdivisions: [
        {
          iso_code: '46',
        },
      ],
    };
    if (ip === '************') return {
      country: {
        names: { en: 'Ukraine' },
        iso_code: 'UA',
      },
      subdivisions: [
        {
          iso_code: '63',
        },
      ],
    };

    return {
      country: {
        names: { en: 'USA' },
        iso_code: 'US',
      },
      subdivisions: [
        {
          iso_code: 'Sacramento',
          names: { en: 'CA' },
        },
      ],
      postal: { code: null },
    };
  }),
}));

module.exports.getConnectionTypeLookup = jest.fn().mockImplementation(() => ({
  // eslint-disable-next-line no-unused-vars
  get: jest.fn().mockImplementation((ip = '127.0.0.1') => ({
    connection_type: 'Cable/DSL',
  })),
}));

module.exports.resetCache = jest.fn().mockImplementation(() => null);
