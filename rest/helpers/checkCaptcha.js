const moment = require('moment');
const i18n = require('../helpers/geti18n');
const Captcha = require('../model/audb/Captcha');

function comparisonStrategy(value, comparator) {
  return value === comparator;
}

async function tokenStrategy(value, token) {
  const captcha = await Captcha.findOne({ token }).lean().exec();

  if (!captcha || !value) return false;
  if (captcha.expires < moment().unix()) return false;
  if (captcha.value.toLowerCase() !== value.toLowerCase()) return false;

  await Captcha.deleteOne({ _id: captcha._id });

  return true;
}

module.exports = function checkCaptcha({ value, comparator, token }) {
  if (comparator) return comparisonStrategy(value, comparator);
  if (token) return tokenStrategy(value, token);

  throw new Error(i18n.__('Captcha validation error'));
};
