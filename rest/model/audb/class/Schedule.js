const _ = require('lodash');
const moment = require('moment-timezone');
const Vod = require('@s1/vod-models/class/Vod');
const {
  schedule: { scheduleFields },
} = require('@s1/api-constants');
const i18n = require('../../../helpers/geti18n');
const sUserScheduledPosition = require('../../subscription/sUserSchedulePosition');
const sStar = require('../../subscription/sStar');
const config = require('../../../../config');
const { fillSubscriptions } = require('../../../service/subscribeForData');
const { clean } = require('../../../service/prepare/clean');
const getLocalizedQueryKey = require('../../../helpers/getLocalizedQueryKey');

const THUMBNAIL_HEIGHT = 480;
// const getTimeZoneDiff = () => (moment().isDST() ? TIMEZONE_DIFF_SUMMER : TIMEZONE_DIFF_WINTER);

class Schedule {
  set vodPath(value) {
    this.__vodPath = value;
  }

  get vodPath() {
    return this.__vodPath;
  }

  set isAlreadySeen(value) {
    this.__isAlreadySeen = value;
  }

  get isAlreadySeen() {
    return this.__isAlreadySeen;
  }

  set position(value) {
    this.__position = value;
  }

  get position() {
    return this.__position;
  }

  set chtime(value) {
    this.__chtime = value;
  }

  get chtime() {
    return this.__chtime;
  }

  set pic(value) {
    this.__pic = value;
  }

  get pic() {
    return this.__pic;
  }

  getThumbnailUrl(channel, { domain, height, position }) {
    if (this.isradio) return null;
    if (this.showpic && this.showCustomPicForRecords) return this.showpic;
    if (!position && position !== 0) position = Math.floor(this.lengthtime / 2);
    if (this.vodPath) return Vod.prototype.getImage.call({ vodlist: this.vodPath }, { domain, height, position });

    channel = channel || this.Channel;
    const { startTime, endTime } = Schedule.getServerTime(this);

    const timeFromTheMiddleOfRecord = startTime + Math.floor(this.lengthtime / 2);

    return Schedule.getRecordThumbnailUrl(channel, domain, timeFromTheMiddleOfRecord, endTime);
  }

  getEndTime() {
    return this.rdatetime + this.lengthtime;
  }

  getYear() {
    return Schedule.getYear(this);
  }

  static getYear(schedule) {
    let yearMatches = [];

    if (schedule.name && schedule.name.match(/\d{4}/g)) {
      yearMatches = schedule.name.match(/\d{4}/g);
    }
    if (schedule.description && schedule.description.match(/\d{4}/g)) {
      yearMatches = yearMatches.concat(schedule.description.match(/\d{4}/g));
    }
    if (!yearMatches.length) return null;

    const currentYear = parseInt(moment().format('YYYY'));
    for (let i = 0; i < yearMatches.length; ++i) {
      const year = parseInt(yearMatches[i]);

      if (year > 1940 && year <= currentYear) return year.toString();
    }

    return null;
  }

  isRecorded(channel) {
    channel = channel || this.Channel;
    let { recordbefore, recordbeforesecond, recordafter, recordaftersecond } = channel;
    let modifier = 0;
    recordbefore = recordbefore || 0;
    recordbeforesecond = recordbeforesecond || 0;
    recordafter = recordafter || 0;
    recordaftersecond = recordaftersecond || 0;

    if (recordafter > 0 || recordaftersecond > 0) modifier = recordafter * 60 + recordaftersecond;
    else if (recordbefore > 0 || recordbeforesecond > 0) modifier = 0 - (recordbefore * 60 + recordbeforesecond);

    const ChannelClass = require('./Channel');

    const { endTime } = ChannelClass.prototype.getSchedulesTimings.call(channel);

    return this.rdatetime + this.lengthtime + modifier < endTime;
  }

  isFinished(channel) {
    channel = channel || this.Channel;
    const ChannelClass = require('./Channel');

    const { endTime } = ChannelClass.prototype.getSchedulesTimings.call(channel);

    return this.rdatetime + this.lengthtime < endTime;
  }

  static getServerTime(schedule, endTime = null) {
    let timezoneOffset = moment().tz('Asia/Jerusalem').utcOffset() * 60;

    const startdate = moment.unix(schedule.rdatetime).format('YYYY-MM-DD HH:mm');
    // Jerusalem and Kiev are in the same timezone,
    // but Jerusalem changed summer time on 26th of March and other world in 28th of March
    // need to do correlation on time difference
    // Disabled because of rdatetime in few days are increased by 1h by some mistake
    const summerTimeCorrectionHours = 0;
    // const summerTimeCorrectionHours =
    //   (moment(startdate, 'YYYY-MM-DD HH:mm').tz('Asia/Jerusalem').utcOffset() - moment(startdate, 'YYYY-MM-DD HH:mm').tz('Europe/Kiev').utcOffset()) /
    //   60;
    // timezoneOffset -= summerTimeCorrectionHours * 3600;

    return {
      startTime: schedule.rdatetime + timezoneOffset,
      endTime: endTime ? endTime + timezoneOffset : schedule.rdatetime + Math.abs(schedule.lengthtime) + timezoneOffset,
    };
  }

  static async resolve({ channel, rdatetime }, flags, { channel: channelInstance }) {
    const schedule = await Schedule.findOne({ channel, rdatetime }).exec();
    const { afterTime, beforeTime } = channelInstance.getSchedulesTimings();
    schedule.chtime = schedule.rdatetime;
    schedule.rdatetime -= beforeTime;
    schedule.lengthtime += afterTime;
    schedule.Channel = channelInstance;
  }

  static getDefaultSchedule({ channel, scheduleDate, rdateExternal, wdayExternal, thumbnailDomain, locale = 'he' }) {
    const channelIsNumber = typeof channel === 'number';
    const rdatetime = scheduleDate.unix();
    const { rdate, wday } =
      rdateExternal && wdayExternal ? { rdate: rdateExternal, wday: +wdayExternal } : this.getTimePropertiesFromRdatetime(rdatetime);

    i18n.setLocale(locale);

    const defaultSchedule = {
      channel: channelIsNumber ? channel : channel.id,
      rdatetime,
      time: scheduleDate.format('HH:00'),
      name: i18n.__('This information is not available'),
      description: i18n.__('This information is not available'),
      wday,
      genre: '',
      rdate,
      lengthtime: 3600,
      weekno: 0,
      star: 3,
      isinfav: 0,
      startotal: 1,
      position: 0,
      isAlreadySeen: false,
      isAlreadyRated: false,
      isradio: channelIsNumber ? undefined : channel.isradio,
      ishd: channelIsNumber ? undefined : channel.ishd,
      logo: channelIsNumber ? undefined : channel.image,
      fullPathLogo: channelIsNumber ? undefined : channel.fullPathLogo,
      year: '',
      customerdate: scheduleDate.format('YYYY-MM-DD'),
      customertime: scheduleDate.format('HH:00'),
      showpic: channelIsNumber ? undefined : this.getRecordThumbnailUrl(channel, thumbnailDomain, rdatetime, rdatetime + 3600),
      audioStatus: 'success',
    };
    const { startTime } = this.getServerTime(defaultSchedule, rdatetime);
    defaultSchedule.showpic = defaultSchedule.isradio
      ? undefined
      : Schedule.getRecordThumbnailUrl(channel, thumbnailDomain, startTime, startTime + 3600);

    return defaultSchedule;
  }

  static getTimePropertiesFromRdatetime(rdatetime) {
    const startDate = moment.unix(rdatetime).subtract(6, 'hours');
    const rdate = moment(startDate).startOf('day').unix();
    const wday = startDate.day() + 1;

    return { startDate, rdate, wday };
  }

  static getTimeProperties(date) {
    const startDate = moment(date, ['DD-MM-YYYY', 'YYYY-MM-DD']).add(6, 'hour');
    const rdate = moment(startDate).startOf('day').unix();
    const wday = startDate.day() + 1;

    return { startDate, rdate, wday };
  }

  static getDefaultSchedules({ channelInfo, date, isRecord, hours = 24, thumbnailDomain, locale }) {
    const { startDate, rdate, wday } = this.getTimeProperties(date);
    const schedule = [];
    const channel = channelInfo[0];
    const now = moment().unix();
    for (let i = 0; i < hours; i++) {
      const scheduleDate = moment(startDate).add(i, 'hour');

      if (isRecord && scheduleDate.unix() > now) break;

      schedule.push(
        this.getDefaultSchedule({
          channel,
          scheduleDate,
          rdateExternal: rdate,
          wdayExternal: wday,
          thumbnailDomain,
          locale,
        }),
      );
    }

    return schedule;
  }

  static getRecordThumbnailUrl(channel, thumbnailDomain, startTime, endTime, height = THUMBNAIL_HEIGHT, position = 0) {
    return `//${thumbnailDomain}/record-thumb/${channel.tohdchannel}-1.stream/start/${startTime}/end/${endTime}/${height}p/${Number(position) * 1000}.jpg`;
  }

  static getSimplifiedDefaultSchedule(channelInstance, locale) {
    const now = moment().startOf('hour');
    const show = this.getDefaultSchedule({ channel: channelInstance, scheduleDate: now, locale });
    const { channel, rdatetime, time, name, description, wday, genre, rdate, lengthtime, weekno, star, startotal, year } = show;

    return {
      channel,
      rdatetime,
      time,
      name,
      description,
      wday,
      genre,
      rdate,
      lengthtime,
      weekno,
      star,
      startotal,
      year,
    };
  }

  static async getShowsForEPG(channel) {
    try {
      const cacheTime = 10; // cached schedules every 10 seconds, do not set very high value!
      const now = Math.floor(moment().unix() / cacheTime) * cacheTime;
      const lastStarted = await this.find({ rdatetime: { $lte: now }, channel: channel.id }, scheduleFields)
        .sort({ rdatetime: -1 })
        .limit(1)
        .lean()
        .cache(cacheTime)
        .exec();

      if (!lastStarted.length) return [this.getSimplifiedDefaultSchedule(channel)];

      return await this.find({ rdatetime: { $gte: lastStarted[0].rdatetime }, channel: channel.id }, scheduleFields)
        .sort({ rdatetime: 1 })
        .limit(2)
        .lean()
        .cache(cacheTime)
        .exec();
    } catch (e) {
      console.error(e.message);

      return [];
    }
  }

  static async search({ key, user, thumbnailOptions, locale, channelsIdsToSkip = [] }) {
    const criteria = {
      rdatetime: {
        $lte: moment().subtract(1, 'hour').unix(),
        $gte: moment().subtract(14, 'days').unix(),
      },
      channel: { $nin: channelsIdsToSkip },
    };
    const records = {};

    if (key) {
      const regexp = new RegExp(key, 'i');
      const localeQueryKeys = [];
      config.i18n.locales.forEach((_locale) => localeQueryKeys.push(getLocalizedQueryKey('name', _locale, 'end')));

      if (localeQueryKeys.length) {
        criteria.$or = [];
        localeQueryKeys.forEach((key) => criteria.$or.push({ [key]: regexp }));
      }
    }

    const schedules = await this.find(criteria, { _id: 0 })
      .populate({
        path: 'Channel',
        populate: 'channelGroup',
      })
      .sort({ rdatetime: -1 })
      .limit(config.search.record.limit)
      .exec()
      // filter active Channels only
      .then((items) => items.filter((schd) => schd.Channel && schd.Channel.ifshow === 0));
    const lastShowTime = moment(moment().subtract(5, 'minutes').format('YYYY-MM-DD HH:00:00')).unix();
    const lastChannelsTvshow = await this.aggregate([
      {
        $match: {
          rdatetime: { $lte: lastShowTime },
          channel: { $in: _.map(schedules, 'channel') },
        },
      },
      {
        $group: {
          _id: '$channel',
          rdatetime: { $max: '$rdatetime' },
        },
      },
    ]).exec();
    const subUserSchedulePosition = user ? await sUserScheduledPosition({ baseCondition: { uid: user.id } }) : undefined;
    const subStar = user ? await sStar({}, user) : undefined;
    schedules.forEach((schedule) => {
      const channelsEndTimes = {};
      let valid = true;
      const startTime = moment(moment().subtract(6, 'hours').format('YYYY-MM-DD 06:00')).unix();

      if (schedule.rdatetime >= startTime) {
        if (!(schedule.Channel.id in channelsEndTimes)) {
          let endTime = lastShowTime;
          const oneHourBefore = moment().subtract(1, 'hour').subtract(3, 'minutes').unix();
          const lastShow = _.find(lastChannelsTvshow, ['_id', schedule.channel]);

          if (lastShow) endTime = lastShow.rdatetime;
          if (oneHourBefore < endTime) endTime = oneHourBefore;

          channelsEndTimes[schedule.Channel.id] = endTime;
        }
        if (channelsEndTimes[schedule.Channel.id] >= startTime && schedule.rdatetime + schedule.lengthtime >= channelsEndTimes[schedule.Channel.id])
          valid = false;
      }
      if (valid) {
        schedule = schedule.constructor.format(schedule, { thumbnailOptions, locale });
        const time = moment(schedule.rdatetime * 1000)
          .subtract(6, 'hours')
          .format('YY-MM-DD');

        if (!records[time]) records[time] = {};
        if (!records[time][schedule.channel]) records[time][schedule.channel] = [];

        // clean({ schedule }, { schedule: 'schedule' });
        records[time][schedule.channel].push(schedule);

        clean(records, {
          schedule: {
            path: [`${time}.${schedule.channel}`],
          },
        });

        if (subUserSchedulePosition) subUserSchedulePosition.add(schedule);
        if (subStar) subStar.add(schedule);
      }
    });

    if (user) await fillSubscriptions();

    return _.isEmpty(records) ? {} : records;
  }
}

module.exports = Schedule;
