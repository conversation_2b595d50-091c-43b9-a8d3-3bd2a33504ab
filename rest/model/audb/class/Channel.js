const _ = require('lodash');
const moment = require('moment');
const { ShowAlreadyRatedError } = require('@s1/api-errors');
const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const config = require('../../../../config');
const Schedule = require('../Schedule');
const ScheduleClass = require('./Schedule');
const Star = require('../Star');
const PackageToFeatures = require('../PackageToFeatures');
const sStar = require('../../subscription/sStar');
const sScheduleOverride = require('../../subscription/sScheduleOverride');
const sUserSchedulePosition = require('../../subscription/sUserSchedulePosition');
const { fillSubscriptions } = require('../../../service/subscribeForData');
const getLocalizedEntityField = require('../../../helpers/getLocalizedEntityField');
const getLocalizedQueryKey = require('../../../helpers/getLocalizedQueryKey');

const THUMBNAIL_HEIGHT = 140;

function getWeekdayNames(locale) {
  moment.locale(locale);

  return moment.weekdays();
}

const weekdays = {
  en: getWeekdayNames('en'),
  he: getWeekdayNames('he'),
};

class Channel {
  getRecordThumbnailUrl(thumbnailDomain, startTime, endTime, height = THUMBNAIL_HEIGHT, position = 0) {
    return `//${thumbnailDomain}/record-thumb/${this.tohdchannel}-1.stream/start/${startTime}/end/${endTime}/${height}p/${Number(position) * 1000}.jpg`;
  }

  /**
   * Returns array contains record for given rdatetime as first element, and few next shows
   * @param startTime
   * @param schedulesLimit
   * @param user
   * @param thumbnailDomain
   */
  async getRecords(startTime, schedulesLimit, thumbnailDomain, user) {
    const { id: channel } = this;
    const { endTime } = this.getSchedulesTimings();
    const subStar = sStar({}, user);
    const subScheduleOverride = sScheduleOverride({});
    const subUserSchedulePosition = await sUserSchedulePosition({ baseCondition: { uid: user.id } });
    const schedules = [];
    // TODO remove subscriptions, use aggregation instead with new mongodb v3.6+, because mongodb v3.4 does not support join by few fields
    await Schedule.find(
      {
        rdatetime: {
          $gte: startTime,
          $lte: endTime,
        },
        channel,
      },
      '-_id',
    )
      .sort({ rdatetime: 1 })
      .limit(schedulesLimit)
      .lean()
      .skip(0)
      .cursor()
      .eachAsync((schedule) => {
        schedules.push(schedule);
        subStar.add(schedule);
        subScheduleOverride.add(schedule);
        subUserSchedulePosition.add(schedule);
      });
    await fillSubscriptions();

    if (schedules.length === 0)
      for (let i = 0; i < schedulesLimit; i++) {
        const scheduleDate = moment.unix(startTime).add(i, 'hour');
        const defaultSchedule = Schedule.getDefaultSchedule({
          channel: +channel,
          scheduleDate,
          rdateExternal: moment(scheduleDate).startOf('day').unix(),
          wdayExternal: scheduleDate.format('E'),
          thumbnailDomain,
        });
        delete defaultSchedule.customerdate;
        delete defaultSchedule.customertime;
        schedules.push(defaultSchedule);
      }
    if (schedules.length === 1)
      schedules.push(
        Schedule.getDefaultSchedule({
          channel,
          scheduleDate: moment(startTime * 1000),
          thumbnailDomain,
        }),
      );

    return schedules;
  }

  getSchedulesTimings() {
    const { recordbefore = 2, recordafter = 4, recordbeforesecond = 0, recordaftersecond = 30 } = this;
    const afterTime = recordafter * 60 + recordaftersecond;
    const beforeTime = recordbefore * 60 + recordbeforesecond;
    // schedules can see only after 2 mins of real time and with channel delay time
    const endTime = Math.floor(Date.now() / 1000) - 2 * 60 - afterTime;

    return { afterTime, beforeTime, endTime };
  }

  async getWeekdays(extended, duration, thumbnailDomain) {
    const today = moment().subtract(6, 'hours').format('YYYY-MM-DD');
    const realSchedules = await Schedule.find({ channel: this.id }).byDate(today).exec();
    const schedules =
      realSchedules.length > 0 ? realSchedules : Schedule.getDefaultSchedules({ channelInfo: [this], date: today, isRecord: true, thumbnailDomain });
    const modifier = schedules.some((schedule) => {
      schedule.Channel = this;

      return schedule.isRecorded ? schedule.isRecorded() : ScheduleClass.prototype.isRecorded.call(schedule);
    })
      ? 0
      : 1;

    return Channel.getWeekdays(extended, modifier, duration);
  }

  async rate(rating, uid, rdatetime, locale) {
    const { id } = this;
    const alreadyRated = await Star.findOne({
      channel: id,
      rdatetime,
      stars: {
        $elemMatch: { uid },
      },
    }).exec();

    if (alreadyRated)
      throw new ShowAlreadyRatedError(
        {
          channel: id,
          star: alreadyRated.star,
          startotal: alreadyRated.starno,
          rdatetime,
        },
        locale,
      );

    const channelToRate = await Star.findOne({ channel: id, rdatetime }).exec();
    const totalStars = _.get(channelToRate, 'star', 0) + rating;
    const ratesAmount = _.get(channelToRate, 'starno', 0) + 1;
    const stars = _.get(channelToRate, 'stars', []).concat({ uid, star: rating });
    await Star.updateOne(
      {
        channel: id,
        rdatetime,
      },
      {
        star: totalStars,
        starno: ratesAmount,
        stars,
      },
      {
        upsert: true,
      },
    );

    return { id, totalStars, ratesAmount };
  }

  static getWeekdays(extended, modifier, duration) {
    modifier = modifier || 0;
    duration = duration || 14;
    const days = [];
    for (let i = 0; i < duration; i++) {
      const day = moment()
        .subtract(6, 'hours')
        .subtract(i + modifier, 'days');
      const week = {
        he: weekdays.he[day.weekday()],
        en: weekdays.en[day.weekday()],
      };

      if (extended === true)
        days[i] = {
          date: day.format('DD/MM'),
          year: day.format('YYYY'),
          week,
        };
      else {
        days[i] = week;
        days[i].date = day.format('DD/MM/YYYY');
      }
    }

    return days;
  }

  static async checkChannel({ user, channel }) {
    const now = Math.floor(new Date() / 1000);

    if (!user || user.expires < now || !user.Package) return false;
    if (channel.extra === 0) return true;

    const pckg = await this.onePackageInfo({ user });

    if (pckg.extra) return true;

    return false;
  }

  static async onePackageInfo({ user }) {
    const pckg = { ...user.Package._doc };
    const pckgg = await PackageToFeatures.findOne({ id: pckg.pgid }).cache(1800).lean();

    return Object.keys(pckgg).reduce((acc, key) => {
      if (key === 'id' || key === '_id') return acc;

      return {
        ...acc,
        [key]: pckgg[key],
        ...(key === 'name' ? { [`pg${key}`]: pckgg[key] } : {}),
      };
    }, pckg);
  }

  static async getAllWithGroup(params = {}) {
    const result = [];
    const channelsAggregateParams = [
      {
        $lookup: {
          from: 'channel',
          localField: 'id',
          foreignField: 'gid',
          as: 'channels',
        },
      },
      { $sort: { 'channels.odid': 1 } },
    ];

    if (params.chquery && typeof params.chquery === 'object' && Object.keys(params.chquery).length >= 0) {
      let cond = Object.keys(params.chquery).length === 1 ? {} : { $and: [] };
      Object.keys(params.chquery).forEach((el) => {
        const criteria = { $eq: [`$$chans.${el}`, params.chquery[el]] };

        if (Object.keys(params.chquery).length === 1) cond = criteria;
        else cond.$and.push(criteria);
      });
      channelsAggregateParams.push({
        $addFields: {
          channels: {
            $filter: {
              input: '$channels',
              as: 'chans',
              cond,
            },
          },
        },
      });
    }

    const ChannelGroup = require('../ChannelGroup');

    const channelGroups = await ChannelGroup.aggregate(channelsAggregateParams);
    channelGroups.forEach((group) => {
      group.channels.forEach((channel) => {
        channel.isradio = group.isradio;
        channel.gname = group.gname;
        channel.egname = group.egname;
        channel.godid = group.odid;
        channel.orgodid = channel.odid;
        channel.odid = group.odid * 10000 + channel.odid;
        result.push(channel);
      });
    });

    return result;
  }

  static async getAll(params = {}) {
    const ChannelGroup = require('../ChannelGroup');

    const channelGroups = await ChannelGroup.find(params.query || {})
      .limit(1000)
      .exec();
    const locale = params.locale;
    const result = [];
    for (const group of channelGroups) {
      let query = {};

      if (params.chquery) query = params.chquery;

      query.gid = group.id;
      const channels = await this.find(query).sort({ odid: 1 }).limit(1000).lean().exec();
      channels.forEach((channel) => {
        channel.isradio = group.isradio;
        channel.name = getLocalizedEntityField(channel, 'name', locale);
        channel.description = getLocalizedEntityField(channel, 'description', locale);
        channel.gname = getLocalizedEntityField(group, 'gname', locale);
        channel.egname = group.egname;
        delete channel._id;
        result.push(channel);
      });
    }

    return result;
  }

  static getShowPic(schedule, channel) {
    const picTime = (schedule.rdatetime + schedule.lengthtime / 2) * 1000;
    const hour = moment(picTime).format('H');
    const second = moment(picTime).diff(moment(picTime).startOf('hour'), 'seconds');
    const day = moment(picTime).format('YYYY-MM-DD');
    const fmsChannelName = channel.tohdchannel || '';
    const file = `${hour}-00.jpg`;

    return `//${config.baseImagesPath}/thumbs/records/${day}/${fmsChannelName}-1.stream/${file}?second=${second}&width=384&height=216`;
  }

  static async search(options) {
    const User = require('../User');

    const { key, isRadio, user, ISP, countryCode, stateCode, locale } = options;
    const now = moment().unix();
    const query = [];
    const regexp = new RegExp(key, 'i');
    const localeQueryKeys = [];
    config.i18n.locales.forEach((_locale) => localeQueryKeys.push(getLocalizedQueryKey('name', _locale, 'start')));
    const schedulesQuery = {
      chquery: {
        isradio: +isRadio,
        ifshow: 0,
      },
      locale,
    };

    if (localeQueryKeys.length) {
      schedulesQuery.chquery.$or = [];
      localeQueryKeys.forEach((key) => schedulesQuery.chquery.$or.push({ [key]: regexp }));
    }

    const [notTvshowIds, streamingServers, channelSchedules, channels] = await Promise.all([
      this.find({ ifshow: 0 }, { id: 1 }).lean().cache(60).exec(),
      User.getStreamingServers(ISP, stateCode, countryCode, STREAM_TYPE_VOD, user ? user.config : {}, user),
      Schedule.aggregate([
        {
          $match: {
            rdatetime: {
              $lte: now,
            },
          },
        },
        {
          $group: {
            _id: '$channel',
            max: {
              $max: '$rdatetime',
            },
          },
        },
      ])
        .cache(60)
        .exec(),
      this.getAll(schedulesQuery),
    ]);
    channelSchedules.forEach(({ _id, max }) => {
      if (_.map(notTvshowIds, 'id').indexOf(_id) === -1) return;

      query.push({
        rdatetime: { $gte: max },
        channel: _id,
        $or: [{ name: regexp }, { ename: regexp }],
      });
    });
    const thumbnailDomain = streamingServers.mainServer.sip;
    const schedules = await Schedule.find({ $or: query })
      .populate({
        path: 'Channel',
        populate: 'channelGroup',
        select: '-_id',
      })
      .limit(200)
      .exec();
    schedules.forEach((schedule) => {
      if (!schedule.Channel || schedule.Channel.isradio !== isRadio || schedule.Channel.ifshow !== 0) return;

      let channel = _.find(channels, { id: schedule.Channel.id });

      if (!channel) {
        channel = schedule.Channel.format({ locale });

        if (channel.ifshow === 0) channels.push(channel);
      }
      if (!channel.schedule) channel.schedule = [];

      const formattedSchedule = Schedule.format(schedule, {
        thumbnailOptions: {
          domain: thumbnailDomain,
        },
        locale,
      });

      channel.schedule.push(formattedSchedule);
    });

    return channels;
  }

  static getAllLogos(channel = null) {
    const middlePart = 'logos';
    // version used to drop app cache
    const version = channel.imageVersion || 0;

    return {
      general: `${config.baseChannelsLogoPath}/${channel.id}.png?v=${version}`,
      dark: `${config.baseChannelsLogoPath}/${middlePart}/dark/${channel.id}.png?v=${version}`,
      light: `${config.baseChannelsLogoPath}/${middlePart}/light/${channel.id}.png?v=${version}`,
      darkSquare: `${config.baseChannelsLogoPath}/${middlePart}/dark/square/${channel.id}.png?v=${version}`,
      lightSquare: `${config.baseChannelsLogoPath}/${middlePart}/light/square/${channel.id}.png?v=${version}`,
      darkNew: `${config.baseChannelsLogoPath}/${middlePart}/dark/new/${channel.id}.png?v=${version}`,
    };
  }

  getAllLogos() {
    return this.constructor.getAllLogos(this);
  }

  static getImage(channel = null) {
    // version used to drop app cache
    const version = channel.imageVersion || 0;

    return `/logos/${channel.id}.png?v=${version}`;
  }

  getImage() {
    return this.constructor.getAllLogos(this);
  }

  static getTxtIcon(channel = null) {
    // version used to drop app cache
    const version = channel.imageVersion || 0;

    return `/logos/${channel.id}.png?v=${version}`;
  }

  getTxtIcon() {
    return this.constructor.getAllLogos(this);
  }
}

module.exports = Channel;
