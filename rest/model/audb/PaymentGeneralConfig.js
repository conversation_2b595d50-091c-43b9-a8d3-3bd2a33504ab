const timestamps = require('mongoose-unix-timestamp-plugin');
const mongoose = require('mongoose');
const connection = require('./connection');
const MongooseCacheScheme = require('../scheme/MongooseCacheScheme');

const PaymentGeneralConfigSchema = new MongooseCacheScheme(
  {
    billingBaseUrl: { type: String, required: true },
    billingDefaultDescriptor: { type: String, required: true },
    billingMaintenanceMode: { type: Boolean, required: false, default: false },
    billingLiveMode: { type: String, required: true, enum: ['test', 'pre_production', 'production'] },
    billingEnablePaymentLogs: { type: Boolean, required: false, default: false },
    billingShowApiResponseBodyInLogs: { type: Boolean, required: false, default: false },
    billingDefaultPaymentMethod: { type: String, required: false },
    billingShowNftFirstIfPaidWith: { type: Object, required: false, default: {} },
    defaultNftPaymentTypes: { type: Object, required: false, default: {} },
    additionalAmountToChargeByCard: { type: Number, required: false, default: 0 },
    additionalAmountToChargeForCryptoByCardGt30Days: { type: Number, required: false, default: 0 },
    billingCryptoStableCoins: { type: String, required: false, default: '' },
    billingCryptoDefaultSelectedCoin: { type: String, required: true },
    billingProxiesList: { type: String, required: false, default: '' },
    billingWebhookHostname: { type: String, required: true },
    mainSiteHostname: { type: String, required: true },
    cryptoApiUrl: { type: String, required: true },
    billingCryptoCardSortOrder: { type: String, required: false, default: '{}' },
    allowNftgateLinksInPackagePaymentLink: { type: Boolean, required: false, default: false },
    nftgateLinksNftPaymentTypes: { type: Object, required: false, default: {} },
    allowNftgateLinksInPackagePaymentLinkCountries: { type: String, required: false, default: '' },
    denyNftgateLinksInPackagePaymentLinkCountries: { type: String, required: false, default: '' },
    canSeeNftgateLinksInPackageRuleId: { type: mongoose.Types.ObjectId, required: false },
    canRemoveUserRuStreamingServerRuleId: { type: mongoose.Types.ObjectId, required: false },
    nftgateProvidersSortOrder: { type: String, required: false, default: '' },
    ngateNftName: { type: String, required: false, default: '' },
    ngateNftImageUrl: { type: String, required: false, default: '' },
    modifiedByUid: { type: Number, required: true, default: 0 },
    modifiedByName: { type: String, required: true, default: '' },
  },
  {
    versionKey: false,
    collection: 'paymentGeneralConfig',
  },
  'paymentGeneralConfig',
);
PaymentGeneralConfigSchema.plugin(timestamps, {
  createdAt: 'created',
  updatedAt: 'updated',
});

/**
 * collection: 'paymentGeneralConfig'
 * @type {Model<Document>}
 */
module.exports = connection.model('PaymentGeneralConfig', PaymentGeneralConfigSchema);
