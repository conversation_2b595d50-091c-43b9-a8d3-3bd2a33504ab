const connection = require('./connection');
const MongooseCacheScheme = require('../scheme/MongooseCacheScheme');

const SettingsSchema = new MongooseCacheScheme({
  payment: {
    remindDelay: { type: Number, default: 3 },
  },
  blocking: {
    identity: {
      threshold: { type: Number, default: 10 },
    },
    trial: {
      validDelay: { type: Number, default: 14 },
      repeatLimit: { type: Number, default: 1 },
    },
  },
  registration: {
    threshold: { type: Number, default: 0 },
  },
  alreadySeen: {
    limit: { type: Number, default: 180 },
    usePercents: { type: Boolean, default: false },
  },
  streaming: {
    useAsn: { type: Boolean, default: true },
  },
  isHighLoadMode: { type: Boolean, default: false },
  ui: {
    carouselSlideInterval: { type: Number, default: 10000 },
    seriesPageSize: { type: Number, default: 20 },
    whatsNewLimit: { type: Number, default: 25 },
  },
}, {
  versionKey: false,
  collection: 'settings',
}, 'settings');

/**
 * collection: 'settings'
 * @type {Model<Document>}
 */
module.exports = connection.model('Settings', SettingsSchema);
