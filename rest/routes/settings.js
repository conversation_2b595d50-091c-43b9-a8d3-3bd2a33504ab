const express = require('express');
const cacheControl = require('express-cache-controller');
const moment = require('moment-timezone');
const SettingsController = require('../controller/settings');
const addUser = require('../middleware/addUser');
const verifyAuthentication = require('../middleware/verifyAuthentication');
const path = require('path');

const router = express.Router();

router.get(
  '/ui',
  cacheControl({ noCache: true }),
  SettingsController.getUiSettings,
);

router.get('/il-utc-offset', cacheControl({ maxAge: 3600, sMaxAge: 3600 }), (req, res) => {
  res.json({
    error: 0,
    result: moment().tz('Asia/Jerusalem').utcOffset(),
  });
});

module.exports = router;
