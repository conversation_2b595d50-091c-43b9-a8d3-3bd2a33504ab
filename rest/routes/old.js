// eslint-disable-next-line padding-line-between-statements
const path = require('path');
const express = require('express');
const cacheControl = require('express-cache-controller');
const log = require('@s1/log').create(__filename);
const passport = require('../setup/passport');
const scheduleController = require('../controller/schedule');
const channelsController = require('../../rest/controller/channels');
const CategoriesController = require('../controller/categories');
const AuthenticationController = require('../controller/authentication');
const SearchController = require('../controller/search');
const UserController = require('../controller/user');
const userFavoritesController = require('../controller/user/favorites');
const UserInvitesController = require('../controller/user/invites');
const VodController = require('../controller/vod');
const ProxyController = require('../controller/proxy');
const paymentController = require('../controller/payment');
const noticeController = require('../controller/notice');
const playerController = require('../controller/player');
const HistoryController = require('../controller/history');
const SpeedController = require('../controller/speed');
const QAController = require('../controller/qa');
const MailController = require('../controller/mail');
const CaptchaController = require('../controller/captcha');
const WebcamsController = require('../controller/admin/webcams');
const verifyAuthentication = require('../middleware/verifyAuthentication');
const verifyRoles = require('../middleware/verifyRoles');
const addUser = require('../middleware/addUser');
const { blockRules } = require('../constants/blockRules');
const blockContentByRules = require('../middleware/blockContentByRules');
const blockCountries = require('../middleware/blockCountries');
const addIPInfo = require('../middleware/addIPInfo');
const verifyOnceToken = require('../middleware/verifyOnceToken');
const browser = require('../middleware/browser');
const cacheGuestOrAuth = require('../middleware/cacheGuestOrAuth');
const cachePlayGuestOrAuth = require('../middleware/cachePlayGuestOrAuth');
const regenerateSession = require('../middleware/regenerateSession');
const blockProxies = require('../middleware/blockProxies');
const blockUserAgent = require('../middleware/blockUserAgent');
const blockNgateUser = require('../middleware/blockNgateUser');
const saveUserIp = require('../middleware/saveUserIp');
const checkOldApp = require('../middleware/checkOldApp');
const savePaymentFingerprint = require('../middleware/savePaymentFingerprint');
const checkStealerUser = require('../middleware/checkStealerUser');
const checkScriptCall = require('../middleware/checkScriptCall');
const checkPaymentBlacklistedUser = require('../middleware/checkPaymentBlacklistedUser');
const checkSuspendedUser = require('../middleware/checkSuspendedUser');
const resolve = require('../middleware/resolve');
const Rememberme = require('../model/audb/Rememberme');
const checkExtraPackage = require('../middleware/checkExtraPackage');
const verifyCaptcha = require('../middleware/verifyCaptcha');
const verifyKeyId = require('../middleware/verifyKeyId');
const verifyPPKey = require('../middleware/verifyPPKey');
const blockUpdatePayment = require('../middleware/blockUpdatePayment');
const checkDevicesLimit = require('../middleware/checkDevicesLimit');
const checkUserAgent = require('../middleware/checkUserAgent');
const updateSessionDevice = require('../middleware/updateSessionDevice');
const addSuggestedAppTag = require('../middleware/addSuggestedAppTag');
const cache = require('../service/cache');
const Channel = require('../model/audb/Channel');
const countryController = require('../controller/countries');
const transactionController = require('../controller/transaction');
const SessionController = require('../controller/session');
const UserConfigController = require('../controller/user/conf');
const AdminAuthMMController = require('../controller/adminloginmm');
const UAPlayerController = require('../controller/uaplayer');
const IPController = require('../controller/user/ip');
const SupportLoginController = require('../controller/supportlogin');
const QScheduleController = require('../controller/qschedule');

const CACHE_TTL = 55;
const router = express.Router();

const getScheduleWrapper = (isAll = false) => function getScheduleWrapped(req, res) {
  req.query.isAll = isAll;
  scheduleController.getSchedule(req, res);
};
/*

/channels/groups/:id?
/channels/:id

/channels/:id/stream
/channels/:id/records/:time/stream
/vod/:id/stream

/channels/groups/:id/epg
/channels/:id/epg
/channels/groups/:id/records/:date
/channels/:id/records/:date
/channels/:id/records/weekdays

/auth/register
/auth/login enable this
/auth/logout

/user/profile
/user/

 */
router.get(
  '/channels.php',
  addUser,
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  channelsController.getGroups,
);
router.get(
  '/channels.php/all',
  addUser,
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  channelsController.getAll,
);
router.get(
  '/schbydate.php',
  cacheControl({ maxAge: 120, sMaxAge: 120 }),
  cacheGuestOrAuth({ guest: { ttl: '10 minutes' }, auth: { ttl: '10 minutes', useIp: true } }),
  addUser,
  getScheduleWrapper(false),
);
router.get(
  '/schbydateall.php',
  cacheControl({ maxAge: 120, sMaxAge: 120 }),
  cacheGuestOrAuth({ guest: { ttl: '10 minutes' }, auth: { ttl: '10 minutes', useIp: true } }),
  addUser,
  getScheduleWrapper(true),
);
router.get(
  '/weekday.php',
  cacheControl({ maxAge: 120, sMaxAge: 120 }),
  resolve(Channel, params => ({
    id: params.channel || params.cid,
  }), {
    prepareQuery: query => query.select('-_id').cache(CACHE_TTL),
  }),
  scheduleController.getWeekdays,
);
router.post(
  '/channels.php',
  addUser,
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  channelsController.getGroups,
);
router.post(
  '/schbydate.php',
  addUser,
  getScheduleWrapper(false),
);
router.post(
  '/schbydateall.php',
  addUser,
  getScheduleWrapper(true),
);
router.get('/qschedule.php', verifyKeyId(2), QScheduleController.receiveHandler);
router.post(
  '/weekday.php',
  cache.withNamespace('schedules').withTtl('5 minutes'),
  resolve(Channel, params => ({
    id: params.channel || params.cid,
  }), {
    prepareQuery: query => query.select('-_id').cache(CACHE_TTL),
  }),
  scheduleController.getWeekdays,
);
router.get(
  '/loadrecord.php',
  cacheControl({ maxAge: 20, sMaxAge: 20 }),
  addUser,
  cachePlayGuestOrAuth({ routeName: 'loadrecord', guest: { ttl: '1 day' } }),
  blockProxies,
  browser,
  verifyAuthentication({ blockInactive: true }),
  checkDevicesLimit,
  updateSessionDevice,
  checkUserAgent,
  checkScriptCall,
  addSuggestedAppTag,
  resolve(Channel, params => ({
    id: params.channel || params.cid,
  }), {
    prepareQuery: query => query.select('-_id'),
  }),
  blockContentByRules(blockRules),
  checkOldApp,
  checkUserAgent,
  scheduleController.getRecord,
);
// This is resolvers for blockContent middleware
//
// const vodResolver = resolve(Vod, params => ({
//   id: params.vodid,
// }), {
//   prepareQuery: query => query.select('-_id'),
//   cacheSeconds: 300,
// });
// const catResolver = resolve(Category, params => ({
//   id: params.resolved.vod ? params.resolved.vod._doc.cateid : null,
// }), {
//   prepareQuery: query => query.select('-_id'),
//   cacheSeconds: 300,
// });

router.get('/vodcatemain.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), CategoriesController.getMainCategories);
router.get('/vodcatesubs.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), addUser, CategoriesController.getSubCategories);
router.post('/vodcatesubs.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), addUser, CategoriesController.getSubCategories);
router.get('/vodcatelist.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), addUser, CategoriesController.getMoviesByCategory);
router.post('/vodcatelist.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), addUser, CategoriesController.getMoviesByCategory);
router.post('/vodrelative.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), addUser, CategoriesController.getVodRelative);
router.get('/myfav.php', cacheControl({ noCache: true }), addUser, verifyAuthentication(), userFavoritesController.process);
router.post('/myfav.php', addUser, verifyAuthentication(), userFavoritesController.process);
router.get('/loadplayer.php', addUser, verifyAuthentication(), playerController.loadPlayer);
router.get('/loaduser.php', cacheControl({ maxAge: 600, sMaxAge: 600 }), addUser, addIPInfo, verifyAuthentication({ passErrorToRequest: true }), UserController.getUser);
router.post('/loaduser.php', cacheControl({ maxAge: 600, sMaxAge: 600 }), addUser, addIPInfo, verifyAuthentication({ passErrorToRequest: true }), UserController.getUser);
router.get('/checkuser.php', cacheControl({ maxAge: 600, sMaxAge: 600 }), UserController.checkUser);
router.get('/checktrial.php', addUser, UserController.checkTrial);
router.post('/resetpassword.php', addUser, blockNgateUser, verifyAuthentication(), UserController.resetPassword);
router.get('/changenameandphone.php', addUser, blockNgateUser, verifyAuthentication(), UserController.changeNameAndPhone);
router.post('/changenameandphone.php', addUser, blockNgateUser, verifyAuthentication(), UserController.changeNameAndPhone);
router.get('/dismissdelay.php', addUser, UserController.unfreezeUser);
router.get('/getinvitecode.php', addUser, blockProxies, verifyAuthentication({ blockInactive: true }), UserInvitesController.getNewReferralCode);
router.get('/listinvitecode.php', addUser, blockProxies, verifyAuthentication({ blockInactive: true }), UserInvitesController.getAllReferralCodes);
router.get('/sendinvite.php', addUser, blockNgateUser, verifyAuthentication(), UserInvitesController.sendInvite);
router.get('/apple_invite.php', addUser, blockNgateUser, blockProxies, verifyAuthentication({ blockInactive: true }), UserInvitesController.processAppleInvite);
router.get('/chls.php',
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  addUser,
  cachePlayGuestOrAuth({ routeName: 'chls', guest: { ttl: '1 day' } }),
  blockProxies,
  browser,
  verifyAuthentication({ blockInactive: true }),
  checkDevicesLimit,
  updateSessionDevice,
  checkUserAgent,
  checkScriptCall,
  blockContentByRules(blockRules),
  checkOldApp,
  addSuggestedAppTag,
  channelsController.getChannel);
router.get('/starrecord.php', addUser, verifyAuthentication(), resolve(Channel, params => ({ id: params.channel })), channelsController.rateChannel);
router.get('/loadvod.php',
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  addUser,
  cachePlayGuestOrAuth({ routeName: 'loadvod', guest: { ttl: '1 day' } }),
  blockProxies,
  browser,
  verifyAuthentication({ blockInactive: true }),
  checkDevicesLimit,
  updateSessionDevice,
  checkUserAgent,
  checkScriptCall,
  checkExtraPackage,
  // turned off resolver cause blockContent doesn't work now
  // ToDo: ask about rules for blockContentByRules middleware and decide delete it or turn on or smth else
  // vodResolver,
  // catResolver,
  // blockContentByRules(blockRules),
  checkOldApp,
  addSuggestedAppTag,
  VodController.loadVod);
router.get('/loadvod2.php',
  cacheControl({ maxAge: 120, sMaxAge: 120 }),
  addUser,
  cacheGuestOrAuth({ routeName: 'loadvod2', guest: { ttl: '1 day' } }),
  blockProxies,
  browser,
  verifyAuthentication({ blockInactive: true }),
  checkDevicesLimit,
  updateSessionDevice,
  checkUserAgent,
  checkScriptCall,
  checkExtraPackage,
  // vodResolver,
  // catResolver,
  // blockContentByRules(blockRules),
  checkOldApp,
  addSuggestedAppTag,
  VodController.loadVodWithNeighbour);
router.get('/vodnew.php',
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  cacheGuestOrAuth({ guest: { ttl: '30 minutes' }, auth: { ttl: '30 minutes' } }),
  browser,
  addUser,
  VodController.whatsNew);
router.get('/vodnewseparated.php',
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  cacheGuestOrAuth({ guest: { ttl: '30 minutes' }, auth: { ttl: '30 minutes' } }),
  browser,
  addUser,
  VodController.whatsNewSeparated);
router.get('/star.php', cacheControl({ noCache: true }), browser, addUser, blockProxies, verifyAuthentication({ blockInactive: true }), VodController.rate);
router.get('/search.php', cacheControl({ noCache: true }), browser, addUser, SearchController.search);
router.get('/msmmmr.php',
  cacheControl({ maxAge: 600, sMaxAge: 600 }),
  cacheGuestOrAuth({ guest: { ttl: '1 day' }, auth: { ttl: '30 minutes' } }),
  browser,
  addUser,
  VodController.getMostWatched);
router.post('/slogin.php', browser, regenerateSession, passport.detectStrategyAndAuthenticate, addUser, saveUserIp, blockNgateUser, checkScriptCall, checkStealerUser, checkPaymentBlacklistedUser, checkSuspendedUser, blockUserAgent, blockProxies, addSuggestedAppTag, AuthenticationController.authenticate);
router.post('/logout.php', addUser, verifyAuthentication(), UserController.logout);
router.get('/register.php', browser, blockProxies, verifyCaptcha, addIPInfo, blockCountries(['RU']), AuthenticationController.register);
router.post('/register.php', browser, blockProxies, verifyCaptcha, addIPInfo, blockCountries(['RU']), AuthenticationController.register);
router.get('/sendemail.php', browser, blockProxies, MailController.send);
router.post('/sendemail.php', browser, blockProxies, MailController.send);
router.get('/forgetpassword.php', browser, AuthenticationController.forgotPassword);
router.post('/forgetpassword.php', browser, AuthenticationController.forgotPassword);
router.get('/reactivemail.php', browser, blockProxies, UserController.resendCode);
router.post('/reactivemail.php', browser, blockProxies, UserController.resendCode);
router.get('/active.php', browser, blockProxies, UserController.activate, AuthenticationController.authenticate);
router.post('/active.php', browser, blockProxies, UserController.activate, AuthenticationController.authenticate);
router.get('/upgrade.php', addUser, blockProxies, verifyAuthentication({ allowExpired: true, blockInactive: true }), paymentController.getUpgradeInvoice);
router.get('/payment.php', addUser, addIPInfo, savePaymentFingerprint({ fingerprintRequired: false }), saveUserIp, checkStealerUser, checkPaymentBlacklistedUser, checkSuspendedUser, verifyAuthentication({ allowExpired: true }), paymentController.getUserPayments);
router.get('/paymenthistory.php', addUser, verifyAuthentication(), paymentController.paymentHistory);
router.post('/paymenthistory.php', addUser, verifyAuthentication(), paymentController.paymentHistory);
router.get('/payinfo.php', verifyPPKey, addUser, addIPInfo, savePaymentFingerprint({ fingerprintRequired: false }), saveUserIp, checkStealerUser, checkPaymentBlacklistedUser, checkSuspendedUser, paymentController.paymentInfo);
router.get('/emailinvoice.php', paymentController.emailInvoice);
router.post('/paydispute', verifyPPKey, paymentController.paydispute);
router.post('/paydispute.php', verifyPPKey, paymentController.paydispute);
router.get('/checkexistpayment.php', paymentController.checkExistPayment);
router.post('/payupdate.php', verifyPPKey, blockUpdatePayment, paymentController.updatePayment);
router.post('/retrypayupdate.php', verifyPPKey, blockUpdatePayment, paymentController.updatePayment);
router.delete('/invoice.php', verifyPPKey, paymentController.removeInvoice);
router.get('/invoicelock.php', verifyPPKey, paymentController.changeInvoiceLockState);
router.get('/listpackage.php', addUser, paymentController.getPackages);
router.get('/loadsession.php', addUser, SessionController.loadSession);
router.get('/proxycheck.php', addUser, ProxyController.checkByIP);
router.get('/notice.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), cache.withNamespace('notice').withTtl('5 minutes'), noticeController.getActive);
router.get('/notice.php', cacheControl({ maxAge: 300, sMaxAge: 300 }), cache.withNamespace('notice').withTtl('5 minutes'), noticeController.getActive);
router.get('/vodhistory.php', addUser, verifyAuthentication(), HistoryController.getVodLogs);
router.delete('/vodhistory.php', addUser, verifyAuthentication(), HistoryController.deleteVodLog);
router.get('/livehistory.php', addUser, verifyAuthentication(), HistoryController.getLiveLogs);
router.post('/livehistory.php', addUser, verifyAuthentication(), HistoryController.getLiveLogs);
router.delete('/livehistory.php', addUser, verifyAuthentication(), HistoryController.deleteLiveLog);
router.get('/recordhistory.php', addUser, verifyAuthentication(), HistoryController.getRecordLogs);
router.post('/recordhistory.php', addUser, verifyAuthentication(), HistoryController.getRecordLogs);
router.delete('/recordhistory.php', addUser, verifyAuthentication(), HistoryController.deleteRecordLog);
router.get('/checkidentity.php', cacheControl({ maxAge: 600, sMaxAge: 600 }), (req, res) => res.json({
  checked: true,
}));
router.get('/checkrememberme.php', addUser, verifyAuthentication(), async (req, res) => {
  const { query: { token } } = req;

  if (!token) return res.json({
    error: 1001,
    message: 'Give me token',
  });

  res.json({
    error: 0,
    result: await Rememberme.check(token),
  });
});
router.get('/captcha', CaptchaController.generateCaptcha);
router.get('/capt.php', CaptchaController.generateCaptcha);
router.get('/safe', verifyOnceToken, addUser, verifyAuthentication(), (request, response) => {
  response.send({
    success: true,
    sid: request.sessionID,
    user: request.user,
    session: request.session,
    userIP: request.userIP,
    userLocation: request.userLocation,
  });
});
router.get('/cache/clear/:target', cacheControl({ noCache: true }), (req, res) => {
  cache.withNamespace(req.params.target).flush(`${req.params.target}`, (err) => {
    if (err) log.error(err);

    log.warn(`cache cleared for ${req.params.target}`);
    res.send(true);
  });
});
// route for test cache
router.get('/test-cache-control/:cache',
  addUser, verifyAuthentication(), verifyRoles(['admin', 'support', 'superadmin']),
  (req, res, next) => {
    const middleware = cacheControl({ maxAge: +req.params.cache, sMaxAge: +req.params.cache });

    return middleware(req, res, next);
  },
  (req, res) => {
    res.send({ cached: `for a ${req.params.cache || 0} seconds`, requestHeaders: req.headers });
  });
router.get('/alive.php', (req, res) => {
  res.send('OK');
});
router.get('/docs', addUser, verifyAuthentication(), (req, res) => {
  res.sendFile(path.resolve(`${__dirname}/../../doc/index.html`));
});
router.get('/speed.php', addUser, verifyAuthentication(), SpeedController.list);
router.post('/speedsave.php', addUser, verifyAuthentication(), SpeedController.save);
router.get('/qa/:type', cacheControl({ maxAge: 600, sMaxAge: 600 }), QAController.getData);
router.get('/iseuropecountry.php', addUser, verifyAuthentication(), countryController.isEuropeCountry);
router.get('/carousel.php', addUser, verifyAuthentication(), UserController.setCarousel);
router.post('/declined.php', verifyPPKey, addUser, transactionController.getDeclined);
router.get('/declined.php', verifyPPKey, addUser, transactionController.getDeclined);
router.get('/country/ipcapi.php', countryController.ipcapi);
router.get('/userconf.php', addUser, verifyAuthentication(), verifyKeyId(), UserConfigController.getOrModifyData);
router.post('/userconf.php', addUser, verifyAuthentication(), verifyKeyId(), UserConfigController.getOrModifyData);
router.get('/adminloginmm.php', verifyKeyId(), AdminAuthMMController.logOut);
router.post('/adminloginmm.php', verifyKeyId(), AdminAuthMMController.logIn);
router.get('/player.php', addUser, verifyKeyId(), playerController.process);
router.post('/player.php', addUser, verifyKeyId(), playerController.process);
router.get('/uaplayer.php', addUser, verifyKeyId(), UAPlayerController.common);
router.post('/uaplayer.php', addUser, verifyKeyId(), UAPlayerController.common);
router.get('/ip.php', addUser, verifyAuthentication(), addIPInfo, IPController.getIPInformation);
router.get('/supportlogin.php', verifyKeyId(), SupportLoginController.logInOut);
router.post('/supportlogin.php', verifyKeyId(), SupportLoginController.logInOut);
router.get('/paycheck.php', verifyPPKey, paymentController.checkPayment);
// Stub for older devices-limit implementation
router.get('/device-serialno.php', async (req, res) => res.send([]));
router.post('/device-serialno.php', async (req, res) => res.send([]));
router.get('/webcams', WebcamsController.get);
router.post('/webcams', addUser, verifyAuthentication(), verifyRoles(['admin', 'support', 'superadmin']), WebcamsController.set);

module.exports = router;
