const { paymentTypes } = require('./paymentType');

const FIELD_TYPES = {
  input: 'input',
  percentageInput: 'percentageInput',
  checkbox: 'checkbox',
  multiselectPackages: 'multiselectPackages',
  selectPackage: 'selectPackage',
  selectCrossmintType: 'selectCrossmintType',
  selectAlchemypayType: 'selectAlchemypayType',
  selectCrossmintErcType: 'selectCrossmintErcType',
  selectMoonpayType: 'selectMoonpayType',
  selectMoonpayFallbackAccount: 'selectMoonpayFallbackAccount',
  selectTrezorType: 'selectTrezorType',
  selectEmailType: 'selectEmailType',
  selectThirdwebType: 'selectThirdwebType',
  selectCryptoComType: 'selectCryptoComType',
  selectPaybisType: 'selectPaybisType',
  selectSwitchereType: 'selectSwitchereType',
  selectLinkByStripeType: 'selectLinkByStripeType',
  selectCryptoCurrency: 'selectCryptoCurrency',
  multiselectCryptoCurrency: 'multiselectCryptoCurrency',
  toggleSwitch: 'toggleSwitch',
  environment: 'environment',
  cryptoCoinConfigs: 'cryptoCoinConfigs',
  nftpayProvides: 'nftpayProvides',
  billingConfigs: 'billingConfigs',
};

const ACCOUNT_CONFIG_BASE = {
  name: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account name', placeholder: 'Account name', tooltip: 'Account name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Account description' },
  descriptor: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Descriptor',
    placeholder: 'Descriptor',
    tooltip: 'Descriptor for errors, invoices and success page.',
  },
  enabled: { field: FIELD_TYPES.toggleSwitch, fieldType: 'toggle-switch', fieldLabel: 'Enabled', placeholder: '', tooltip: 'Enable/disable Account' },
  useCountryProxyMapByUserIp: {
    field: FIELD_TYPES.checkbox,
    fieldType: 'checkbox',
    fieldLabel: 'Use proxy map by user IP',
    placeholder: '',
    tooltip: '',
  },
  useProxiesList: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Use proxies list', placeholder: '', tooltip: '' },
  proxies: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Proxies list',
    placeholder: '',
    tooltip:
      'List of proxies like: host1:port1:protocol1, host2:port2:protocol2 or with user and pass: host1:port1:protocol1:user1:pass1, host2:port2:protocol2:user2:pass2',
  },
};

const CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS = {
  billingConfigs: {
    field: FIELD_TYPES.billingConfigs,
    fieldLabel: 'Billing configs',
    placeholder: '',
    tooltip: '',
  },
};

const STRIPE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  defaultStripeId: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Default stripe ID', placeholder: '', tooltip: 'Default stripe ID' },
  allow3ds: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Allow 3ds', placeholder: '', tooltip: '' },
  minAmountToUse3ds: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Min amount to use 3ds', placeholder: '', tooltip: '' },
  stripeServerId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    editDisabled: true,
    fieldLabel: 'Stripe server Id',
    placeholder: '',
    tooltip: 'Unique stripe server id for load properly configs on each server',
  },
  skey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live skey',
    placeholder: '',
    tooltip: 'Stripe live skey. Example: sk_live_...',
  },
  pkey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live pkey',
    placeholder: '',
    tooltip: 'Stripe live pkey. Example: pk_live_...',
  },
  secret: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live secret',
    placeholder: '',
    tooltip: 'Stripe live secret. Example: whsec_...',
  },
  serviceIdSkey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Service Id skey',
    placeholder: '',
    tooltip: 'ID of payment service account key (stripe, paypal, crypto, etc.)',
  },
  infoProperty: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Info property',
    placeholder: '',
    tooltip: 'Info property for stripe description',
  },
  tkeyInvoicePrefix: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'tkey invoice prefix',
    placeholder: '',
    tooltip: 'Prefix for create new stripe invoice ID, should be unique for each server',
  },
  tkeyInvoiceMultipler: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'tkey invoice multipler',
    placeholder: '',
    tooltip: 'tkey multipler for create new stripe invoice ID, should be unique for each server',
  },
  hideDescription: { field: FIELD_TYPES.checkbox, fieldLabel: 'Hide description', placeholder: '', tooltip: 'Hide description' },
  invoiceIdPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Invoice Id property for stripe metadata',
    placeholder: '',
    tooltip: 'InvoiceId property for stripe metadata should be unique for each server',
  },
  userInfoPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User info property for stripe metadata',
    placeholder: '',
    tooltip: 'UserInfo property for stripe metadata should be unique for each server',
  },
  userIpPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User IP property for stripe metadata',
    placeholder: '',
    tooltip: 'UserIP property for card metadata should be unique for each server',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const BRAINTREE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  successDescriptor: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success descriptor',
    placeholder: 'Descriptor for success page',
    tooltip: '',
  },
  braintreeServerId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    editDisabled: true,
    fieldLabel: 'Braintree server Id',
    placeholder: 'Ex: 2p65oDUBilu2ugJ9',
    tooltip: 'Unique Braintree server id for load properly configs on each server. Should looks like hash string.',
  },
  isDefaultBraintreeAccount: {
    field: FIELD_TYPES.checkbox,
    fieldType: 'checkbox',
    fieldLabel: 'Is default Braintree account',
    placeholder: '',
    tooltip: 'Set main Braintree account as default',
  },
  merchantId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Merchant Id', placeholder: '', tooltip: '' },
  merchantAccountId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Merchant account Id', placeholder: '', tooltip: 'Not require' },
  sgdMerchant: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Is SGN merchant', placeholder: '', tooltip: '' },
  publicKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Public key', placeholder: '', tooltip: '' },
  privateKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Private Key', placeholder: '', tooltip: '' },
  environment: { field: FIELD_TYPES.environment, fieldType: '', fieldLabel: 'Environment', placeholder: '', tooltip: 'sandbox or production' },
  discountUsd: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Discount', placeholder: '', tooltip: 'Discount in USD' },
  allow3mPackageForCardCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allow 3 month package for card countries',
    placeholder: '',
    tooltip:
      "For the second servers required, will redirect payment to the second server by country. Countries separated by comma in format 'XX'. Default is 'ALL'",
  },
  deny3mPackageForCardCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Deny 3 month package for card countries',
    placeholder: '',
    tooltip: "On main server only required! Countries separated by comma in format 'XX'.",
  },
  allowedCardBrands: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allowed card brands',
    placeholder: '',
    tooltip: 'Card brands separated by comma. Example: VISA,MASTERCARD',
  },
  cardCountriesForLoadBalancer: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Card countries for load balancer',
    placeholder: '',
    tooltip: "Countries separated by comma in format 'XX'.",
  },
  maxPaidTotalAmountForLoadBalancer: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Max paid total amount for load balancer',
    placeholder: '',
    tooltip: 'After reach max paid total amount it will works as usual without load balancer.',
  },
  percentageForLoadBalancer: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Load balancer percentage',
    placeholder: '',
    tooltip: 'Load balancer percentage',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const B2PAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  accountId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account ID', placeholder: '', tooltip: '' },
  encryptionIv: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Encryption Iv', placeholder: '', tooltip: '' },
  encryptionPassword: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Encryption password', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const CROSSMINT_NFT_CARD_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  successDescriptor: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success descriptor',
    placeholder: 'Descriptor for success page',
    tooltip: '',
  },
  paymentKeyLive: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Payment key live',
    placeholder: '',
    tooltip: 'Stripe payment key live for Crossmint NFT with card',
  },
  ercType: { field: FIELD_TYPES.selectCrossmintErcType, fieldLabel: 'Erc type', placeholder: '', tooltip: 'Crossmint erc type' },
  clientId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Client ID', placeholder: '', tooltip: 'Client ID' },
  clientName: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Client name',
    placeholder: '',
    tooltip: 'Client Name like: client-sdk-react-ui&clientVersion=0.2.0',
  },
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price! Info field only.',
  },
  nftPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Nft price', placeholder: '', tooltip: 'Nft price' },
  tokenIdFrom: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID from', placeholder: '', tooltip: 'Token ID from' },
  tokenIdTo: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID to', placeholder: '', tooltip: 'Token ID to' },
  allowedCardCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allowed card countries',
    placeholder: '',
    tooltip: "Countries with states separated by comma in format 'XX_YY' for USA or 'XX' for other",
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const MOONPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectMoonpayType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Moonpay sub type' },
  sign: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Sign', placeholder: '', tooltip: 'Is sign account or not' },
  fallbackAccountId: {
    field: FIELD_TYPES.selectMoonpayFallbackAccount,
    fieldLabel: 'Fallback account',
    placeholder: '',
    tooltip: 'Moonpay fallback account on fail',
  },
  apiKeys: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api keys', placeholder: '', tooltip: 'Multi keys, separate by comma' },
  contractAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Contract address', placeholder: '', tooltip: 'Contract address' },
  dxToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'DX token', placeholder: '', tooltip: 'DX token' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  quotesCacheTime: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Quotes cache time',
    placeholder: '',
    tooltip: 'Time to store moonpay quotes (sec)',
  },
  generalCacheTime: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'General cache time',
    placeholder: '',
    tooltip: 'Time to store generated moonpay urls, etc (sec)',
  },
  defaultCurrencies: {
    field: FIELD_TYPES.multiselectCryptoCurrency,
    fieldType: 'text',
    fieldLabel: 'Moonpay coin currencies',
    placeholder: '',
    tooltip: '',
  },
  paymentTolerancePercentage: {
    field: FIELD_TYPES.percentageInput,
    fieldType: 'number',
    fieldLabel: 'Payment tolerance percentage',
    placeholder: '',
    tooltip:
      'Defines the allowable percentage of deviation from the total payment amount. This parameter specifies the minimum acceptable amount a user must pay for get a whole package days. For example, if the total amount is 100 and the tolerance is set to 5%, the user must pay at least 95',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const TRANSAK_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api key', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const TREZOR_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectTrezorType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Trezor sub type' },
  sign: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Sign', placeholder: '', tooltip: 'Is sign account or not' },
  widgetId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget ID', placeholder: '', tooltip: 'Widget ID for Metamask' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  quotesWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Quotes wallet address',
    placeholder: '',
    tooltip: 'Wallet address should be for the same currency as default currency',
  },
  contractId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract ID',
    placeholder: '',
    tooltip: 'Contract ID for the selected default currency',
  },
  denyUserIpCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Denied user IP countries',
    placeholder: '',
    tooltip: "Countries with states separated by comma in format 'XX_YY' for USA or 'XX' for other",
  },
  paymentTolerancePercentage: {
    field: FIELD_TYPES.percentageInput,
    fieldType: 'number',
    fieldLabel: 'Payment tolerance percentage',
    placeholder: '',
    tooltip:
      'Defines the allowable percentage of deviation from the total payment amount. This parameter specifies the minimum acceptable amount a user must pay for get a whole package days. For example, if the total amount is 100 and the tolerance is set to 5%, the user must pay at least 95',
  },
  proxyHost: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Proxy host', placeholder: '', tooltip: 'Example: serv.host.com' },
  proxyPort: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Proxy port', placeholder: '', tooltip: 'Example: 3322' },
  proxyType: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Proxy type', placeholder: '', tooltip: 'One of: socks or http or https' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const SENDWYRE_CARD_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  accountId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account Id', placeholder: '', tooltip: '' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  denyCreditCardCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Deny credit card countries',
    placeholder: '',
    tooltip: 'List of denied credit card countries separated by comma',
  },
  supportedUserAddressCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Supported user address countries',
    placeholder: '',
    tooltip: 'List of supported user address countries separated by comma',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const SENDWYRE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  accountId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account Id', placeholder: '', tooltip: '' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const APPLEPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  accountId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account Id', placeholder: '', tooltip: '' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const SIMPLEX_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  uid: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'User Id', placeholder: '', tooltip: 'Simplex user Id' },
  redirector: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Redirector', placeholder: '', tooltip: 'Payment url redirector' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const SWITCHERE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectSwitchereType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Switchere sub type' },
  partnerKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Partner key', placeholder: '', tooltip: 'Switchere partner key' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const WERT_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  partnerId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Partner Id', placeholder: '', tooltip: 'Wert Partner Id' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const MTPELERIN_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  pushId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Push Id', placeholder: '', tooltip: 'Mtpelerin push ID' },
  payoutAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Payout address', placeholder: '', tooltip: 'Mtpelerin payout address' },
  addressSignature: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Address signature',
    placeholder: '',
    tooltip: 'Mtpelerin address signature',
  },
  referrer: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrer', placeholder: '', tooltip: 'Mtpelerin referrer' },
  referrerLink: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrer link', placeholder: '', tooltip: 'Mtpelerin referrer link' },
  verificationCode: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Verification code',
    placeholder: '',
    tooltip: 'Mtpelerin verification code',
  },
  basicToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Basic token', placeholder: '', tooltip: 'Mtpelerin basic token' },
  paymentKeyLive: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Payment key live',
    placeholder: '',
    tooltip: 'Mtpelerin payment key live',
  },
  proxies: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Proxies',
    placeholder: '',
    tooltip: 'List of proxies like: host1:port1:type1,host2:port2:type2',
  },
  allowedCardCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allowed card countries',
    placeholder: '',
    tooltip: "Countries with states separated by comma in format 'XX_YY' for USA or 'XX' for other",
  },
  allowedCardBrands: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allowed card brands',
    placeholder: '',
    tooltip: 'Card brands separated by comma. Example: VISA,MASTERCARD',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const ALCHEMYPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectAlchemypayType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Alchemypay sub type' },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api Key', placeholder: '', tooltip: '' },
  appId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'App ID', placeholder: '', tooltip: 'App ID' },
  channelId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Channel ID', placeholder: '', tooltip: 'Channel ID' },
  widgetId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget ID', placeholder: '', tooltip: 'Widget ID' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  tokenAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Token Address', placeholder: '', tooltip: 'Token address' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const FATPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  partnerId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Partner ID', placeholder: '', tooltip: 'Partner ID' },
  tokenId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Token ID',
    placeholder: '',
    tooltip: 'Token ID, ID of current crypto currency in fatpay',
  },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const PAYBIS_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectPaybisType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Paybis sub type' },
  widgetId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget ID', placeholder: '', tooltip: 'Widget ID' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  quotesWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Quotes wallet address',
    placeholder: '',
    tooltip: 'Wallet address should be for the same currency as default currency',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  enabledWalletChecker: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Enabled wallet checker', placeholder: '', tooltip: '' },
  walletCheckerApiKey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Wallet checker apiKey',
    placeholder: '',
    tooltip: 'Wallet checker apiKey (polygonscan)',
  },
  providerContractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider contract address',
    placeholder: '',
    tooltip: 'Provider contract address to check available wallet balance',
  },
  providerWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider wallet address',
    placeholder: '',
    tooltip: 'Provider wallet address to check available wallet balance',
  },
  providerWalletAddressMinAmount: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Provider wallet address min amount',
    placeholder: '',
    tooltip: 'Min amount to check has the provider wallet address enough balance',
  },
  paymentTolerancePercentage: {
    field: FIELD_TYPES.percentageInput,
    fieldType: 'number',
    fieldLabel: 'Payment tolerance percentage',
    placeholder: '',
    tooltip:
      'Defines the allowable percentage of deviation from the total payment amount. This parameter specifies the minimum acceptable amount a user must pay for get a whole package days. For example, if the total amount is 100 and the tolerance is set to 5%, the user must pay at least 95',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const UTORG_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api Key', placeholder: '', tooltip: '' },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const TOPPER_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api Key', placeholder: '', tooltip: '' },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  defaultCurrency: {
    field: FIELD_TYPES.selectCryptoCurrency,
    fieldType: 'text',
    fieldLabel: 'Default currency',
    placeholder: '',
    tooltip: 'Should be one of the coin config names (ctypto API coinConfigs.js): BTC, BUSDBSC, USDTTRC20, MATIC, USDCMATIC',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const UNLIMIT_CRYPTO_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api Key', placeholder: '', tooltip: '' },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  defaultCurrency: {
    field: FIELD_TYPES.selectCryptoCurrency,
    fieldType: 'text',
    fieldLabel: 'Default currency',
    placeholder: '',
    tooltip: 'Should be one of the coin config names (ctypto API coinConfigs.js): BTC, BUSDBSC, USDTTRC20, MATIC, USDCMATIC',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const SARDINE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  defaultCurrency: {
    field: FIELD_TYPES.selectCryptoCurrency,
    fieldType: 'text',
    fieldLabel: 'Default currency',
    placeholder: '',
    tooltip: 'Should be one of the coin config names (ctypto API coinConfigs.js): BTC, BUSDBSC, USDTTRC20, MATIC, USDCMATIC',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const CRYPTO_COM_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectCryptoComType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Crypto Com sub type' },
  price: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Price',
    placeholder: 'Example: 28.83',
    tooltip: 'Custom amount in USD so users will not pay the fees',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  publishableKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Publishable key', placeholder: '', tooltip: 'pk_live_...' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  quotesWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Quotes wallet address',
    placeholder: '',
    tooltip: 'Wallet address should be for the same currency as default currency',
  },
  enabledWalletChecker: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Enabled wallet checker', placeholder: '', tooltip: '' },
  walletCheckerApiKey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Wallet checker apiKey',
    placeholder: '',
    tooltip: 'Wallet checker apiKey (polygonscan)',
  },
  providerContractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider contract address',
    placeholder: '',
    tooltip: 'Provider contract address to check available wallet balance',
  },
  providerWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider wallet address',
    placeholder: '',
    tooltip: 'Provider wallet address to check available wallet balance',
  },
  providerWalletAddressMinAmount: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Provider wallet address min amount',
    placeholder: '',
    tooltip: 'Min amount to check has the provider wallet address enough balance',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const TAZAPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  basicToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Basic token', placeholder: '', tooltip: 'Tazapay basic token' },
  referrenceId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrence ID', placeholder: '', tooltip: 'Tazapay referrence ID' },
  referrer: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrer', placeholder: '', tooltip: 'Tazapay referrer' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  allowedUserIpCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Allowed user IP countries',
    placeholder: '',
    tooltip: "Countries with states separated by comma in format 'XX_YY' for USA or 'XX' for other",
  },
  environment: { field: FIELD_TYPES.environment, fieldType: '', fieldLabel: 'Environment', placeholder: '', tooltip: 'sandbox or production' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const TAZAPAY_CARD_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  basicToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Basic token', placeholder: '', tooltip: 'Tazapay card basic token' },
  referrenceId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrence ID', placeholder: '', tooltip: 'Tazapay card referrence ID' },
  referrer: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Referrer', placeholder: '', tooltip: 'Tazapay card referrer' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  allowedUserIpCountries: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Allowed user IP countries',
    placeholder: '',
    tooltip: "Countries with states separated by comma in format 'XX_YY' for USA or 'XX' for other",
  },
  environment: { field: FIELD_TYPES.environment, fieldType: '', fieldLabel: 'Environment', placeholder: '', tooltip: 'sandbox or production' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const NOWPAYMENTS_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api key', placeholder: '', tooltip: '' },
  email: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account email', placeholder: '', tooltip: '' },
  password: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Account password', placeholder: '', tooltip: '' },
  webhookDomain: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Webhook domain',
    placeholder: '',
    tooltip: 'Example: https://webhook.url',
  },
  invoiceExpireTimeMins: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Invoice expiration time (mins)',
    placeholder: '',
    tooltip: 'Invoice expiration time in mins',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const CRYPTO_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  invoiceExpireTimeMins: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Invoice expiration time (mins)',
    placeholder: '',
    tooltip: 'Invoice expiration time in mins',
  },
  invoiceCurrency: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Invoice currency', placeholder: '', tooltip: 'Default USD' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  coinConfigs: { field: FIELD_TYPES.cryptoCoinConfigs, fieldLabel: 'Coin configs', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const MERCURYO_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  widgetId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget ID', placeholder: '', tooltip: 'Widget ID' },
  sdkPartnerToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'SDK partner token', placeholder: '', tooltip: 'SDK partner token' },
  minAmount: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Minimal amount', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const WINTER_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const RAMPER_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet address', placeholder: '', tooltip: 'Wallet address' },
  network: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Network', placeholder: '', tooltip: 'Network. Example: one of matic, etc' },
  fiatCurrency: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Fiat currency',
    placeholder: '',
    tooltip: 'Fiat currency. Example: one of: USD, EUR, etc.',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const NFTPAY_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  tokenIdFrom: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID from', placeholder: '', tooltip: 'Token ID from' },
  tokenIdTo: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID to', placeholder: '', tooltip: 'Token ID to' },
  providerName: { field: FIELD_TYPES.nftpayProvides, fieldType: 'text', fieldLabel: 'Provider name', placeholder: '', tooltip: 'сircle or stripe' },
  stripeApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Stripe API key', placeholder: '', tooltip: 'Stripe API key' },
  enabledWalletChecker: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Enabled wallet checker', placeholder: '', tooltip: '' },
  walletCheckerApiKey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Wallet checker apiKey',
    placeholder: '',
    tooltip: 'Wallet checker apiKey (polygonscan)',
  },
  providerContractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider contract address',
    placeholder: '',
    tooltip: 'Provider contract address to check available wallet balance',
  },
  providerWalletAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Provider wallet address',
    placeholder: '',
    tooltip: 'Provider wallet address to check available wallet balance',
  },
  providerWalletAddressMinAmount: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Provider wallet address min amount',
    placeholder: '',
    tooltip: 'Min amount to check has the provider wallet address enough balance',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const CROSSMINT_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectCrossmintType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Crossmint sub type' },
  customContract: {
    field: FIELD_TYPES.toggleSwitch,
    fieldType: 'toggle-switch',
    fieldLabel: 'Custom contract',
    placeholder: '',
    tooltip: 'Custom contract',
  },
  ercType: { field: FIELD_TYPES.selectCrossmintErcType, fieldLabel: 'Erc type', placeholder: '', tooltip: 'Crossmint erc type' },
  projectId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project ID', placeholder: '', tooltip: 'Project ID' },
  clientId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Client ID', placeholder: '', tooltip: 'Client ID' },
  clientName: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Client name',
    placeholder: '',
    tooltip: 'Client Name like: client-sdk-react-ui&clientVersion=0.2.0',
  },
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price! Info field only.',
  },
  usdcPrice: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'USDC price',
    placeholder: '',
    tooltip: 'USDC price using for non custom contract only',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const CROSSMINT_BONFIRE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  projectId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project ID', placeholder: '', tooltip: 'Project ID' },
  clientId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Client ID', placeholder: '', tooltip: 'Client ID' },
  to: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'To', placeholder: '', tooltip: '' },
  edition: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Edition', placeholder: '', tooltip: '' },
  attributionId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Attribution ID', placeholder: '', tooltip: '' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const PAPER_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  contractId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract ID',
    placeholder: '',
    tooltip: 'Contract ID price should be the same as package price!',
  },
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  usdcPrice: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'USDC price',
    placeholder: '',
    tooltip: 'USDC price using for non custom contract only',
  },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api key', placeholder: '', tooltip: 'Paper api key' },
  apiToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api token', placeholder: '', tooltip: 'Paper api token' },
  title: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Title', placeholder: '', tooltip: 'Title' },
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  successCallbackUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Success callback url', placeholder: '', tooltip: '' },
  cancelCallbackUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Cancel callback url', placeholder: '', tooltip: '' },
  proxies: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Proxies',
    placeholder: '',
    tooltip: 'List of proxies like: host1:port1:type1,host2:port2:type2',
  },
  packageName: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Package name',
    placeholder: '',
    tooltip: 'List of nft package names separated by comma',
  },
  tokenIdFrom: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID from', placeholder: '', tooltip: 'Token ID from' },
  tokenIdTo: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID to', placeholder: '', tooltip: 'Token ID to' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const PAPER_CRYPTO_CARD_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  customContract: {
    field: FIELD_TYPES.toggleSwitch,
    fieldType: 'toggle-switch',
    fieldLabel: 'Custom contract',
    placeholder: '',
    tooltip: 'Custom contract',
  },
  contractId: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract ID',
    placeholder: '',
    tooltip: 'Contract ID price should be the same as package price!',
  },
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  usdcPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'USDC price', placeholder: '', tooltip: 'USDC price' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api key', placeholder: '', tooltip: 'Paper api key' },
  apiToken: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Api token', placeholder: '', tooltip: 'Paper api token' },
  title: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Title', placeholder: '', tooltip: 'Title' },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  proxies: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Proxies',
    placeholder: '',
    tooltip: 'List of proxies like: host1:port1:type1,host2:port2:type2',
  },
  packageName: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Package name',
    placeholder: '',
    tooltip: 'List of nft package names separated by comma',
  },
  tokenIdFrom: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID from', placeholder: '', tooltip: 'Token ID from' },
  tokenIdTo: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID to', placeholder: '', tooltip: 'Token ID to' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const BITSTORE_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet address', placeholder: '', tooltip: 'Wallet address' },
  nftPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Nft price', placeholder: '', tooltip: 'Nft price' },
  proxies: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Proxies',
    placeholder: '',
    tooltip: 'List of proxies like: host1:port1:type1,host2:port2:type2',
  },
  emailType: { field: FIELD_TYPES.selectEmailType, fieldLabel: 'Email type', placeholder: '', tooltip: 'Email type' },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const THIRDWEB_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectThirdwebType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Thirdweb sub type' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  contractId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Contract ID', placeholder: 'Contract id', tooltip: '' },
  collectionName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Collection name', placeholder: 'Collection name', tooltip: '' },
  collectionDescription: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Collection description',
    placeholder: 'Collection description',
    tooltip: '',
  },
  collectionAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Collection address',
    placeholder: 'Collection address',
    tooltip: '',
  },
  expectedMintPrice: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Expected mint price',
    placeholder: 'Expected mint price',
    tooltip: '',
  },
  value: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Value', placeholder: 'Value', tooltip: '' },
  animationUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Animation URL', placeholder: 'Animation URL', tooltip: '' },
  activityId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Activity ID', placeholder: 'Activity ID', tooltip: '' },
  imageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Image URL', placeholder: 'Image URL', tooltip: '' },
  externalUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'External URL', placeholder: 'External URL', tooltip: '' },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const MOONGATE_CONFIG = {
  projectName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Project name', placeholder: 'Project name', tooltip: '' },
  ...ACCOUNT_CONFIG_BASE,
  basicUsername: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Particle API username', placeholder: '', tooltip: '' },
  basicPassword: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Particle API password', placeholder: '', tooltip: '' },
  nftName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'NFT Name', placeholder: '', tooltip: '' },
  nftImageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'NFT Image URL', placeholder: '', tooltip: '' },
  contractAddress: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Contract address',
    placeholder: '',
    tooltip: 'Contract address price should be the same as package price!',
  },
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  additionalQueryParam: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Additional query param',
    placeholder: '',
    tooltip: 'Additional query param name with tkey and user id',
  },
  enabledWalletChecker: { field: FIELD_TYPES.checkbox, fieldType: 'checkbox', fieldLabel: 'Enabled wallet checker', placeholder: '', tooltip: '' },
  walletCheckerApiKey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Wallet checker apiKey',
    placeholder: '',
    tooltip: 'Wallet checker apiKey (polygonscan)',
  },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const LINK_BY_STRIPE_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  subType: { field: FIELD_TYPES.selectLinkByStripeType, fieldLabel: 'Sub type', placeholder: '', tooltip: 'Moonpay sub type' },
  price: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'Price',
    placeholder: 'Example: 28.83',
    tooltip: 'Custom amount in USD so users will not pay the fees',
  },
  secretKey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Secret key (client id / secret key / api key)',
    placeholder: '',
  },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  defaultCurrency: {
    field: FIELD_TYPES.selectCryptoCurrency,
    fieldType: 'text',
    fieldLabel: 'Default currency',
    placeholder: '',
    tooltip: 'Should be one of the coin config names (ctypto API coinConfigs.js): BTC, BUSDBSC, USDTTRC20, MATIC, USDCMATIC',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Account package', placeholder: '', tooltip: 'Select package for this payment account' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const NFTGATE_EPIK_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  skey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live skey',
    placeholder: '',
    tooltip: 'Stripe live skey. Example: sk_live_...',
  },
  pkey: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live pkey',
    placeholder: '',
    tooltip: 'Stripe live pkey. Example: pk_live_...',
  },
  secret: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Stripe live secret',
    placeholder: '',
    tooltip: 'Stripe live secret. Example: whsec_...',
  },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  tkeyInvoicePrefix: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'tkey invoice prefix',
    placeholder: '',
    tooltip: 'Prefix for create new stripe invoice ID, should be unique for each server',
  },
  tkeyInvoiceMultipler: {
    field: FIELD_TYPES.input,
    fieldType: 'number',
    fieldLabel: 'tkey invoice multipler',
    placeholder: '',
    tooltip: 'tkey multipler for create new stripe invoice ID, should be unique for each server',
  },
  invoiceIdPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Invoice Id property for stripe metadata',
    placeholder: '',
    tooltip: 'InvoiceId property for stripe metadata should be unique for each server',
  },
  userInfoPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User info property for stripe metadata',
    placeholder: '',
    tooltip: 'UserInfo property for stripe metadata should be unique for each server',
  },
  userIpPropertyForMetadata: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User IP property for stripe metadata',
    placeholder: '',
    tooltip: 'UserIP property for card metadata should be unique for each server',
  },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const NFTGATE_PAYPAL_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  environment: { field: FIELD_TYPES.environment, fieldType: '', fieldLabel: 'Environment', placeholder: '', tooltip: 'sandbox or production' },
  clientId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Client Id', placeholder: '', tooltip: '' },
  clientSecret: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Client secret', placeholder: '', tooltip: '' },
  webhookId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Webhook Id', placeholder: '', tooltip: '' },
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
};

const TRANSAK_NFT_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Package', placeholder: '', tooltip: 'Select package for this payment account' },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Transak API key', placeholder: '', tooltip: '' },
  apiSecret: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Transak API secret', placeholder: '', tooltip: '' },
  contractId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Contract Id', placeholder: '', tooltip: '' },
  cryptoCurrencyCode: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'CryptoCurrency Code', placeholder: '', tooltip: '' },
  nftName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'NFT name', placeholder: '', tooltip: '' },
  nftType: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'NFT type', placeholder: '', tooltip: '' },
  nftPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'NFT price', placeholder: '', tooltip: '' },
  tokenId: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Token ID', placeholder: '1,2,3,4 or 5', tooltip: '' },
  gasLimit: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Gas Limit', placeholder: 'Estimated gas limit', tooltip: '' },
  collectionAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Collection address', placeholder: '', tooltip: '' },
  imageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Image URL', placeholder: '', tooltip: '' },
  widgetNftName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT name', placeholder: '', tooltip: '' },
  widgetNftImageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT image URL', placeholder: '', tooltip: '' },
};

const WYNPAY_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Package', placeholder: '', tooltip: 'Select package for this payment account' },
  merchantId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Merchant Id', placeholder: '', tooltip: '' },
  secretKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Secret Key', placeholder: '', tooltip: '' },
  walletAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Wallet Address', placeholder: '', tooltip: '' },
  mintContractAddress: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Mint Contract Address', placeholder: '', tooltip: '' },
  fiatCurrency: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Fiat Currency Code', placeholder: '', tooltip: '' },
  cryptoCurrency: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Crypto Currency Code', placeholder: '', tooltip: '' },
  iframeUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Iframe URL', placeholder: '', tooltip: '' },
  widgetNftName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT name', placeholder: '', tooltip: '' },
  widgetNftImageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT image URL', placeholder: '', tooltip: '' },
  price: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Price', placeholder: '', tooltip: '' },
  environment: { field: FIELD_TYPES.environment, fieldType: '', fieldLabel: 'Environment', placeholder: '', tooltip: 'sandbox or production' },
};

const COMETH_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  userEmailDomains: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'User email domains',
    placeholder: '',
    tooltip: 'Email domains for generate user nft emails. List of domains separated by comma',
  },
  emailDomain: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email domain', placeholder: '', tooltip: 'Email domain' },
  emailApiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Sender email api key', placeholder: '', tooltip: 'Email domain api key' },
  emailFrom: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Email sent from',
    placeholder: '',
    tooltip: 'Email sent from. Example: "Excited User <<EMAIL>>"',
  },
  emailTemplateSuccess: {
    field: FIELD_TYPES.input,
    fieldType: 'text',
    fieldLabel: 'Success email template name',
    placeholder: '',
    tooltip: 'Success email template name',
  },
  nftPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'NFT price', placeholder: '', tooltip: '' },
  fullPrice: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Full price', placeholder: '', tooltip: '' },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'API Key', placeholder: '', tooltip: '' },
  apiSecret: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'API Secret', placeholder: '', tooltip: '' },
  productId: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Product Id', placeholder: '', tooltip: '' },
  widgetNftName: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT name', placeholder: '', tooltip: '' },
  widgetNftImageUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Widget NFT image URL', placeholder: '', tooltip: '' },
  packageId: { field: FIELD_TYPES.selectPackage, fieldLabel: 'Package', placeholder: '', tooltip: 'Select package for this payment account' },
};

const RAMP_NETWORK_CONFIG = {
  ...ACCOUNT_CONFIG_BASE,
  additionalFee: { field: FIELD_TYPES.input, fieldType: 'number', fieldLabel: 'Additional fee', placeholder: '', tooltip: '' },
  paymentTolerancePercentage: {
    field: FIELD_TYPES.percentageInput,
    fieldType: 'number',
    fieldLabel: 'Payment tolerance percentage',
    placeholder: '',
    tooltip:
      'Defines the allowable percentage of deviation from the total payment amount. This parameter specifies the minimum acceptable amount a user must pay for get a whole package days. For example, if the total amount is 100 and the tolerance is set to 5%, the user must pay at least 95',
  },
  apiKey: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'API Key', placeholder: '', tooltip: '' },
  hostLogoUrl: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Host Logo URL', placeholder: '', tooltip: '' },
  defaultCurrency: { field: FIELD_TYPES.selectCryptoCurrency, fieldType: 'text', fieldLabel: 'Default currency', placeholder: '', tooltip: '' },
  packageIds: {
    field: FIELD_TYPES.multiselectPackages,
    fieldLabel: 'Allowed packages',
    placeholder: '',
    tooltip: 'Select allowed packages for this payment account',
  },
  ...CRYPTO_WITH_CARD_ADDITIONAL_CONFIGS,
};

const paymentAccountsConfigsByName = {
  [paymentTypes.stripe]: STRIPE_CONFIG,
  [paymentTypes.braintree]: BRAINTREE_CONFIG,
  [paymentTypes.nowpayments]: NOWPAYMENTS_CONFIG,
  [paymentTypes.b2pay]: B2PAY_CONFIG,
  [paymentTypes.crossmintNftCard]: CROSSMINT_NFT_CARD_CONFIG,
  [paymentTypes.moonpay]: MOONPAY_CONFIG,
  [paymentTypes.transak]: TRANSAK_CONFIG,
  [paymentTypes.trezor]: TREZOR_CONFIG,
  [paymentTypes.sendwyre]: SENDWYRE_CONFIG,
  [paymentTypes.sendwyreCard]: SENDWYRE_CARD_CONFIG,
  [paymentTypes.applepay]: APPLEPAY_CONFIG,
  [paymentTypes.simplex]: SIMPLEX_CONFIG,
  [paymentTypes.crypto]: CRYPTO_CONFIG,
  [paymentTypes.switchere]: SWITCHERE_CONFIG,
  [paymentTypes.wert]: WERT_CONFIG,
  [paymentTypes.alchemypay]: ALCHEMYPAY_CONFIG,
  [paymentTypes.fatpay]: FATPAY_CONFIG,
  [paymentTypes.mtpelerin]: MTPELERIN_CONFIG,
  [paymentTypes.paybis]: PAYBIS_CONFIG,
  [paymentTypes.UTORG]: UTORG_CONFIG,
  [paymentTypes.cryptoCom]: CRYPTO_COM_CONFIG,
  [paymentTypes.tazapay]: TAZAPAY_CONFIG,
  [paymentTypes.tazapayCard]: TAZAPAY_CARD_CONFIG,
  [paymentTypes.mercuryo]: MERCURYO_CONFIG,
  [paymentTypes.winter]: WINTER_CONFIG,
  [paymentTypes.ramper]: RAMPER_CONFIG,
  [paymentTypes.nftpay]: NFTPAY_CONFIG,
  [paymentTypes.crossmint]: CROSSMINT_CONFIG,
  [paymentTypes.crossmintBonfire]: CROSSMINT_BONFIRE_CONFIG,
  [paymentTypes.paper]: PAPER_CONFIG,
  [paymentTypes.paperCryptoCard]: PAPER_CRYPTO_CARD_CONFIG,
  [paymentTypes.bitstore]: BITSTORE_CONFIG,
  [paymentTypes.thirdweb]: THIRDWEB_CONFIG,
  [paymentTypes.topper]: TOPPER_CONFIG,
  [paymentTypes.unlimitCrypto]: UNLIMIT_CRYPTO_CONFIG,
  [paymentTypes.sardine]: SARDINE_CONFIG,
  [paymentTypes.moongate]: MOONGATE_CONFIG,
  [paymentTypes.linkByStripe]: LINK_BY_STRIPE_CONFIG,
  [paymentTypes.nftgateEpik]: NFTGATE_EPIK_CONFIG,
  [paymentTypes.nftgatePaypal]: NFTGATE_PAYPAL_CONFIG,
  [paymentTypes.transakNft]: TRANSAK_NFT_CONFIG,
  [paymentTypes.wynpay]: WYNPAY_CONFIG,
  [paymentTypes.cometh]: COMETH_CONFIG,
  [paymentTypes.rampNetwork]: RAMP_NETWORK_CONFIG,
};

const paymentAccountsConfigsByType = {
  stripe: STRIPE_CONFIG,
  braintree: BRAINTREE_CONFIG,
  nowpayments: NOWPAYMENTS_CONFIG,
  b2pay: B2PAY_CONFIG,
  crossmintNftCard: CROSSMINT_NFT_CARD_CONFIG,
  moonpay: MOONPAY_CONFIG,
  transak: TRANSAK_CONFIG,
  trezor: TREZOR_CONFIG,
  sendwyre: SENDWYRE_CONFIG,
  sendwyreCard: SENDWYRE_CARD_CONFIG,
  applepay: APPLEPAY_CONFIG,
  simplex: SIMPLEX_CONFIG,
  crypto: CRYPTO_CONFIG,
  switchere: SWITCHERE_CONFIG,
  wert: WERT_CONFIG,
  mtpelerin: MTPELERIN_CONFIG,
  paybis: PAYBIS_CONFIG,
  utorg: UTORG_CONFIG,
  tazapay: TAZAPAY_CONFIG,
  alchemypay: ALCHEMYPAY_CONFIG,
  fatpay: FATPAY_CONFIG,
  cryptoCom: CRYPTO_COM_CONFIG,
  mercuryo: MERCURYO_CONFIG,
  winter: WINTER_CONFIG,
  ramper: RAMPER_CONFIG,
  nftpay: NFTPAY_CONFIG,
  crossmint: CROSSMINT_CONFIG,
  crossmintBonfire: CROSSMINT_BONFIRE_CONFIG,
  paper: PAPER_CONFIG,
  paperCryptoCard: PAPER_CRYPTO_CARD_CONFIG,
  bitstore: BITSTORE_CONFIG,
  thirdweb: THIRDWEB_CONFIG,
  topper: TOPPER_CONFIG,
  unlimitCrypto: UNLIMIT_CRYPTO_CONFIG,
  sardine: SARDINE_CONFIG,
  moongate: MOONGATE_CONFIG,
  linkByStripe: LINK_BY_STRIPE_CONFIG,
  nftgateEpik: NFTGATE_EPIK_CONFIG,
  nftgatePaypal: NFTGATE_PAYPAL_CONFIG,
  transakNft: TRANSAK_NFT_CONFIG,
  wynpay: WYNPAY_CONFIG,
  cometh: COMETH_CONFIG,
  rampNetwork: RAMP_NETWORK_CONFIG,
};

const paymentAccountsConfigsList = Object.values(paymentAccountsConfigsByType);

module.exports = {
  paymentAccountsConfigsByName,
  paymentAccountsConfigsByType,
  paymentAccountsConfigsList,
};
