const moment = require('moment');

const IS_AUTHORIZED_USER_TYPE = 'isAuthorizedUser';
const IS_OLD_USER_TYPE = 'isOldUser';
const IS_BLACKLISTED_USER_TYPE = 'isBlacklistedUser';
const CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_TYPE = 'canUserBypassPaymentBlacklistRules';
const IS_ACTIVE_USER_TYPE = 'isActiveUser';
const IS_PACKAGE_DAYS_TYPE = 'isPackageDays';
const IS_CRYPTO_COIN_TYPE = 'isCryptoCoin';
const IS_EXPIRED_IN_LAST_XX_DAYS_TYPE = 'isExpiredInLastXXDays';
const WAS_PAID_LAST_MONTHS_TYPE = 'wasPaidLastMonths';
const HAS_WATCHING_ACTIVITIES_TYPE = 'hasWatchingActivities';
const HAS_PAYMENTS_TYPE = 'hasPayments';
const HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_TYPE = 'hasPaymentsExcludePaymentTypes';
const HAS_NO_PAYMENTS_TYPE = 'hasNoPayments';
const HAS_1_AND_3_MONTH_PAYMENT_LOGS_TYPE = 'hasOneAndThreeMonthPaymentLogs';
const HAS_CARDS_PAYMENTS_ONLY_TYPE = 'hasCardsPaymentsOnly';
// const HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_TYPE = 'hasBypassedPaymentTypeOrAccount';
const BYPASS_ALL_TYPE = 'bypassAll';
const PAID_WITH_PAYMENT_TYPES_TYPE = 'paidWithPaymentTypes';
const FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_TYPE = 'failedWithPaymentTypesCurrentInvoice';
const PAGE_FROM_TYPE = 'pageFrom';
const HAS_PAYMENT_DISPUTE = 'hasPaymentDispute';
const HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES = 'hasXXPaymentsWithPaymentTypes';

const IS_AUTHORIZED_USER_NAME = 'Is authorized user';
const IS_OLD_USER_NAME = 'Is old user';
const IS_BLACKLISTED_USER_NAME = 'Is blacklisted user';
const CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_NAME = 'Can user bypass payment blacklist rules';
const IS_ACTIVE_USER_NAME = 'Is active user';
const IS_PACKAGE_DAYS_NAME = 'Is XX days package';
const IS_CRYPTO_COIN_NAME = 'Is COIN_NAME crypto coin';
const WAS_PAID_LAST_MONTHS_NAME = 'Was paid in last XX months';
const HAS_WATCHING_ACTIVITIES_NAME = 'Has XX watching activities in last YY days';
const HAS_PAYMENTS_NAME = 'Has XX payments';
const HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_NAME = 'Has XX payments exclude payment types';
const HAS_NO_PAYMENTS_NAME = 'Has no payments';
const HAS_1_AND_3_MONTH_PAYMENT_LOGS_NAME = 'Has 1 and 3 month packages payment logs';
const HAS_CARDS_PAYMENTS_ONLY_NAME = 'Has card payments only';
// const HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_TYPE = 'hasBypassedPaymentTypeOrAccount';
const BYPASS_ALL_NAME = 'Bypass all';
const PAID_WITH_PAYMENT_TYPES_NAME = 'Paid with payment types';
const FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_NAME = 'Failed with payment types current invoice';
const PAGE_FROM_NAME = 'From PAGE_NAME page';
const HAS_PAYMENT_DISPUTE_NAME = 'Has payment dispute';
const HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES_NAME = 'Has XX payments with payment types';

const paymentBasicRuleTypes = {
  isAuthorizedUser: IS_AUTHORIZED_USER_TYPE,
  isOldUser: IS_OLD_USER_TYPE,
  isBlacklistedUser: IS_BLACKLISTED_USER_TYPE,
  canUserBypassPaymentBlacklistRules: CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_TYPE,
  isActiveUser: IS_ACTIVE_USER_TYPE,
  isPackageDays: IS_PACKAGE_DAYS_TYPE,
  isCryptoCoin: IS_CRYPTO_COIN_TYPE,
  isExpiredInLastXXDays: IS_EXPIRED_IN_LAST_XX_DAYS_TYPE,
  wasPaidLastMonths: WAS_PAID_LAST_MONTHS_TYPE,
  hasWatchingActivities: HAS_WATCHING_ACTIVITIES_TYPE,
  hasPayments: HAS_PAYMENTS_TYPE,
  hasPaymentsExcludePaymentTypes: HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_TYPE,
  hasNoPayments: HAS_NO_PAYMENTS_TYPE,
  hasOneAndThreeMonthPaymentLogs: HAS_1_AND_3_MONTH_PAYMENT_LOGS_TYPE,
  hasCardsPaymentsOnly: HAS_CARDS_PAYMENTS_ONLY_TYPE,
  // hasBypassedPaymentTypeOrAccount: HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_TYPE,
  bypassAll: BYPASS_ALL_TYPE,
  paidWithPaymentTypes: PAID_WITH_PAYMENT_TYPES_TYPE,
  failedWithPaymentTypesCurrentInvoice: FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_TYPE,
  pageFrom: PAGE_FROM_TYPE,
  hasPaymentDispute: HAS_PAYMENT_DISPUTE,
  hasXXPaymentsWithPaymentTypes: HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES,
};

const FIELD_TYPES = {
  input: 'input',
  date: 'date',
  checkbox: 'checkbox',
  hidden: 'hidden',
  toggleSwitch: 'toggleSwitch',
  selectPaymentType: 'selectPaymentType',
  multiSelectPaymentType: 'multiSelectPaymentType',
  selectPageFrom: 'selectPageFrom',
};

const IS_AUTHORIZED_USER_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_AUTHORIZED_USER_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_AUTHORIZED_USER_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const IS_OLD_USER_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_OLD_USER_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_OLD_USER_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
  dateTo: { field: FIELD_TYPES.date, value: moment().unix(), fieldLabel: 'Date to', placeholder: '', tooltip: '' },
};

const IS_BLACKLISTED_USER_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_BLACKLISTED_USER_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_BLACKLISTED_USER_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const IS_ACTIVE_USER_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_ACTIVE_USER_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_ACTIVE_USER_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const IS_PACKAGE_DAYS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_PACKAGE_DAYS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_PACKAGE_DAYS_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  packageDays: { field: FIELD_TYPES.input, fieldType: 'number', value: 30, fieldLabel: 'Package days', placeholder: '', tooltip: 'Package days' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const IS_CRYPTO_COIN_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_CRYPTO_COIN_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_CRYPTO_COIN_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  coin: { field: FIELD_TYPES.input, fieldType: 'text', value: '', fieldLabel: 'Coin config name', placeholder: '', tooltip: 'Should be one of the coin config names (ctypto API coinConfigs.js): BTC, BUSDBSC, USDTTRC20, MATIC, USDCMATIC' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const IS_EXPIRED_IN_LAST_XX_DAYS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: IS_EXPIRED_IN_LAST_XX_DAYS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: IS_EXPIRED_IN_LAST_XX_DAYS_TYPE, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  days: { field: FIELD_TYPES.input, fieldType: 'number', value: 7, fieldLabel: 'Days', placeholder: '', tooltip: 'Expired in last XX days' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const WAS_PAID_LAST_MONTHS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: WAS_PAID_LAST_MONTHS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: WAS_PAID_LAST_MONTHS_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  lastMonthsPeriod: { field: FIELD_TYPES.input, fieldType: 'number', value: 6, fieldLabel: 'Last months period', placeholder: '', tooltip: 'Period for check in months' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_WATCHING_ACTIVITIES_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_WATCHING_ACTIVITIES_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_WATCHING_ACTIVITIES_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  minLogs: { field: FIELD_TYPES.input, fieldType: 'number', value: 15, fieldLabel: 'Min logs', placeholder: '', tooltip: 'Min required count of logs for selected period' },
  maxLogsPerDays: { field: FIELD_TYPES.input, fieldType: 'number', value: 5, fieldLabel: 'Max logs per day', placeholder: '', tooltip: 'Max calculated logs per day' },
  calculationDays: { field: FIELD_TYPES.input, fieldType: 'number', value: 7, fieldLabel: 'Calculation days', placeholder: '', tooltip: 'Calculate logs for the last XX days' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_PAYMENTS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_PAYMENTS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_PAYMENTS_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  minLogs: { field: FIELD_TYPES.input, fieldType: 'number', value: 1, fieldLabel: 'Min logs', placeholder: '', tooltip: 'Min required total user payments' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  minLogs: { field: FIELD_TYPES.input, fieldType: 'number', value: 1, fieldLabel: 'Min logs', placeholder: '', tooltip: 'Min required total user payments' },
  paymentTypes: { field: FIELD_TYPES.multiSelectPaymentType, fieldLabel: 'Exclude payment types', placeholder: '', tooltip: 'Exclude payment types' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_NO_PAYMENTS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_NO_PAYMENTS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_NO_PAYMENTS_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_1_AND_3_MONTH_PAYMENT_LOGS_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_1_AND_3_MONTH_PAYMENT_LOGS_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_1_AND_3_MONTH_PAYMENT_LOGS_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_CARDS_PAYMENTS_ONLY_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_CARDS_PAYMENTS_ONLY_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_CARDS_PAYMENTS_ONLY_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

// const HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_CONFIG = {
//   methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
//   name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_TYPE, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
//   description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
//   paymentType: { field: FIELD_TYPES.selectPaymentType, value: '', fieldLabel: 'Payment type', placeholder: '', tooltip: 'Bypassed payment type for check' },
//   editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
// };

const CAN_BYPASS_ALL_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: BYPASS_ALL_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: BYPASS_ALL_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const PAID_WITH_PAYMENT_TYPES_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: PAID_WITH_PAYMENT_TYPES_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: PAID_WITH_PAYMENT_TYPES_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  paymentTypes: { field: FIELD_TYPES.multiSelectPaymentType, fieldLabel: 'Payment types', placeholder: '', tooltip: 'Payment types' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  minLogs: { field: FIELD_TYPES.input, fieldType: 'number', value: 1, fieldLabel: 'Min logs', placeholder: '', tooltip: 'Min required total user payments' },
  paymentTypes: { field: FIELD_TYPES.multiSelectPaymentType, fieldLabel: 'Payment types', placeholder: '', tooltip: 'Payment types' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  paymentTypes: { field: FIELD_TYPES.multiSelectPaymentType, fieldLabel: 'Payment types', placeholder: '', tooltip: 'Payment types' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const PAGE_FROM_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: PAGE_FROM_TYPE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: PAGE_FROM_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  pageFrom: { field: FIELD_TYPES.selectPageFrom, fieldType: 'text', fieldLabel: 'Page from', placeholder: 'Page from', tooltip: 'authCode or empty' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: true, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const HAS_PAYMENT_DISPUTE_CONFIG = {
  methodName: { field: FIELD_TYPES.input, disabled: true, value: HAS_PAYMENT_DISPUTE, fieldLabel: 'Method', placeholder: '', tooltip: '' },
  name: { field: FIELD_TYPES.input, fieldType: 'text', value: HAS_PAYMENT_DISPUTE_NAME, fieldLabel: 'Rule name', placeholder: 'Rule name', tooltip: 'Basic rule name' },
  description: { field: FIELD_TYPES.input, fieldType: 'text', fieldLabel: 'Description', placeholder: 'Description', tooltip: 'Basic rule description' },
  editable: { field: FIELD_TYPES.hidden, hidden: true, value: false, fieldLabel: 'Editable config', placeholder: '', tooltip: '' },
};

const paymentBasicRulesConfigsByName = {
  isAuthorizedUser: IS_AUTHORIZED_USER_CONFIG,
  isOldUser: IS_OLD_USER_CONFIG,
  isBlacklistedUser: IS_BLACKLISTED_USER_CONFIG,
  canUserBypassPaymentBlacklistRules: CAN_USER_BYPASS_PAYMENT_BLACKLIST_RULES_CONFIG,
  isActiveUser: IS_ACTIVE_USER_CONFIG,
  isPackageDays: IS_PACKAGE_DAYS_CONFIG,
  isCryptoCoin: IS_CRYPTO_COIN_CONFIG,
  isExpiredInLastXXDays: IS_EXPIRED_IN_LAST_XX_DAYS_CONFIG,
  wasPaidLastMonths: WAS_PAID_LAST_MONTHS_CONFIG,
  hasWatchingActivities: HAS_WATCHING_ACTIVITIES_CONFIG,
  hasPayments: HAS_PAYMENTS_CONFIG,
  hasPaymentsExcludePaymentTypes: HAS_PAYMENTS_EXCLUDE_PAYMENT_TYPES_CONFIG,
  hasNoPayments: HAS_NO_PAYMENTS_CONFIG,
  hasOneAndThreeMonthPaymentLogs: HAS_1_AND_3_MONTH_PAYMENT_LOGS_CONFIG,
  hasCardsPaymentsOnly: HAS_CARDS_PAYMENTS_ONLY_CONFIG,
  // hasBypassedPaymentTypeOrAccount: HAS_BYPASSED_PAYMENT_TYPE_OR_ACCOUNT_CONFIG,
  bypassAll: CAN_BYPASS_ALL_CONFIG,
  paidWithPaymentTypes: PAID_WITH_PAYMENT_TYPES_CONFIG,
  failedWithPaymentTypesCurrentInvoice: FAILED_WITH_PAYMENT_TYPES_CURRENT_INVOICE_CONFIG,
  pageFrom: PAGE_FROM_CONFIG,
  hasPaymentDispute: HAS_PAYMENT_DISPUTE_CONFIG,
  hasXXPaymentsWithPaymentTypes: HAS_XX_PAYMENTS_WITH_PAYMENT_TYPES_CONFIG,
};

const paymentAccountsConfigsList = Object.values(paymentBasicRulesConfigsByName);

module.exports = {
  paymentBasicRuleTypes,
  paymentBasicRulesConfigsByName,
  paymentAccountsConfigsList,
};
