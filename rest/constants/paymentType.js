const STRIPE_TYPE = 'ST-card';
const BRAINTREE_TYPE = 'BT-card';
const NOWPAYMENTS_TYPE = 'nowpayments';
const B2PAY_TYPE = 'b2pay';
const CROSSMINT_NFT_CARD_TYPE = 'CR-card';
const MOONPAY_TYPE = 'moonpay';
const TREZOR_TYPE = 'trezor';
const SENDWYRE_TYPE = 'sendwyre';
const SENDWYRE_CARD_TYPE = 'sendwyre-card';
const APPLEPAY_TYPE = 'applepay';
const SIMPLEX_TYPE = 'simplex';
const CRYPTO_TYPE = 'crypto';
const SWITCHERE_TYPE = 'switchere';
const WERT_TYPE = 'wert';
const ALCHEMYPAY_TYPE = 'alchemypay';
const FATPAY_TYPE = 'fatpay';
const MTPELERIN_TYPE = 'mtpelerin';
const PAYBIS_TYPE = 'paybis';
const CRYPTO_COM_TYPE = 'crypto.com';
const TAZAPAY_TYPE = 'tazapay';
const TAZAPAY_CARD_TYPE = 'tazapay-card';
const MERCURYO_TYPE = 'mercuryo';
const WINTER_TYPE = 'winter';
const RAMPER_TYPE = 'ramper';
const NFTPAY_TYPE = 'nftpay';
const CROSSMINT_TYPE = 'crossmint';
const CROSSMINT_BONFIRE_TYPE = 'crossmint-bonfire';
const PAPER_TYPE = 'paper';
const PAPER_CRYPTO_CARD_TYPE = 'paper-crypto-card';
const BITSTORE_TYPE = 'bitstore';
const UTORG_TYPE = 'UTORG';
const THIRDWEB_TYPE = 'thirdweb';
const TRANSAK_TYPE = 'transak';
const TOPPER_TYPE = 'topper';
const UNLIMIT_CRYPTO_TYPE = 'unlimit-crypto';
const SARDINE_TYPE = 'sardine';
const MOONGATE_TYPE = 'moongate';
const LINK_BY_STRIPE_TYPE = 'link-by-stripe';
const NFTGATE_EPIK_TYPE = 'nftgate-epik';
const NFTGATE_PAYPAL_TYPE = 'nftgate-paypal';
const TRANSAK_NFT_TYPE = 'transak-nft';
const WYNPAY_TYPE = 'wynpay';
const COMETH_TYPE = 'cometh';
const RAMP_NETWORK_TYPE = 'ramp-network';

const paymentTypes = {
  stripe: STRIPE_TYPE,
  braintree: BRAINTREE_TYPE,
  nowpayments: NOWPAYMENTS_TYPE,
  b2pay: B2PAY_TYPE,
  crossmintNftCard: CROSSMINT_NFT_CARD_TYPE,
  tazapay: TAZAPAY_TYPE,
  tazapayCard: TAZAPAY_CARD_TYPE,
  moonpay: MOONPAY_TYPE,
  transak: TRANSAK_TYPE,
  trezor: TREZOR_TYPE,
  sendwyre: SENDWYRE_TYPE,
  sendwyreCard: SENDWYRE_CARD_TYPE,
  applepay: APPLEPAY_TYPE,
  simplex: SIMPLEX_TYPE,
  mtpelerin: MTPELERIN_TYPE,
  paybis: PAYBIS_TYPE,
  cryptoCom: CRYPTO_COM_TYPE,
  crypto: CRYPTO_TYPE,
  switchere: SWITCHERE_TYPE,
  wert: WERT_TYPE,
  alchemypay: ALCHEMYPAY_TYPE,
  fatpay: FATPAY_TYPE,
  mercuryo: MERCURYO_TYPE,
  winter: WINTER_TYPE,
  ramper: RAMPER_TYPE,
  nftpay: NFTPAY_TYPE,
  crossmint: CROSSMINT_TYPE,
  crossmintBonfire: CROSSMINT_BONFIRE_TYPE,
  paper: PAPER_TYPE,
  paperCryptoCard: PAPER_CRYPTO_CARD_TYPE,
  bitstore: BITSTORE_TYPE,
  UTORG: UTORG_TYPE,
  thirdweb: THIRDWEB_TYPE,
  topper: TOPPER_TYPE,
  unlimitCrypto: UNLIMIT_CRYPTO_TYPE,
  sardine: SARDINE_TYPE,
  moongate: MOONGATE_TYPE,
  linkByStripe: LINK_BY_STRIPE_TYPE,
  nftgateEpik: NFTGATE_EPIK_TYPE,
  nftgatePaypal: NFTGATE_PAYPAL_TYPE,
  transakNft: TRANSAK_NFT_TYPE,
  wynpay: WYNPAY_TYPE,
  cometh: COMETH_TYPE,
  rampNetwork: RAMP_NETWORK_TYPE,
};

const paymentTypesList = Object.values(paymentTypes);

module.exports = {
  paymentTypes,
  paymentTypesList,
};
