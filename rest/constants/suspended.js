const { ignoredCardNumbersWithEncrypted } = require('./ignoredCardNumbers');

const UID_TYPE = 'uid';

const CREATION_METHOD_MANUAL = 'manual';
const CREATION_METHOD_AUTOMATIC = 'automatic';

const suspendedTypes = {
  uid: UID_TYPE,
};

const creationMethods = {
  manual: CREATION_METHOD_MANUAL,
  automatic: CREATION_METHOD_AUTOMATIC,
};

const suspendedTypesList = Object.values(suspendedTypes);
const creationMethodsList = Object.values(creationMethods);
const ignoredCardNumbersList = Object.keys(ignoredCardNumbersWithEncrypted);
const ignoredCardNumbersEncryptedList = Object.values(ignoredCardNumbersWithEncrypted);

module.exports = {
  suspendedTypes,
  suspendedTypesList,
  creationMethods,
  creationMethodsList,
  ignoredCardNumbersWithEncrypted,
  ignoredCardNumbersList,
  ignoredCardNumbersEncryptedList,
};
