const firstFeb2022 = 1643670001;
const blockedUserIds = ['18'];
const blockedChannelIds = ['8', '9', '10'];
const blockedCategoryIds = ['189', '2242'];
const blockedCategoryIdsRegexp = [/,189$/g, /,189,/g, /,2242$/g, /,2242,/g];

const allRules = [
  {
    route: '/chls.php',
    rule: {
      logic: 'and',
      conditions: [
        {
          logic: 'or',
          conditions: [
            {
              reqProperty: 'user._doc.created',
              operator: '>',
              target: firstFeb2022,
            },
            {
              reqProperty: 'user._doc.id',
              operator: '=',
              target: {
                operator: 'or',
                values: blockedUserIds,
              },
            },
          ],
        },
        {
          reqProperty: 'query.cid',
          operator: '=',
          target: {
            operator: 'or',
            values: blockedChannelIds,
          },
        },
      ],
    },
  },
  {
    route: '/loadrecord.php',
    rule: {
      logic: 'and',
      conditions: [
        {
          logic: 'or',
          conditions: [
            {
              reqProperty: 'user._doc.created',
              operator: '>',
              target: firstFeb2022,
            },
            {
              reqProperty: 'user._doc.id',
              operator: '=',
              target: {
                operator: 'or',
                values: blockedUserIds,
              },
            },
          ],
        },
        {
          reqProperty: 'resolved.channel._doc.id',
          operator: '=',
          target: {
            operator: 'or',
            values: blockedChannelIds,
          },
        },
      ],
    },
  },
  {
    route: ['/loadvod.php', '/loadvod2.php'],
    rule: {
      logic: 'and',
      conditions: [
        {
          logic: 'or',
          conditions: [
            {
              reqProperty: 'user._doc.created',
              operator: '>',
              target: firstFeb2022,
            },
            {
              reqProperty: 'user._doc.id',
              operator: '=',
              target: {
                operator: 'or',
                values: blockedUserIds,
              },
            },
          ],
        },
        {
          logic: 'or',
          conditions: [
            {
              reqProperty: 'resolved.vod._doc.cateid',
              operator: '=',
              target: {
                operator: 'or',
                values: blockedCategoryIds,
              },
            },
            {
              reqProperty: 'resolved.category._doc.topath',
              operator: '~',
              target: {
                operator: 'or',
                values: blockedCategoryIdsRegexp,
              },
            },
          ],
        },
      ],
    },
  },
];

module.exports = {
  blockRules: allRules,
  blockedUserIds,
};
