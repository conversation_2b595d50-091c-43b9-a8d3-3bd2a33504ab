const { ROLES } = require('./user');

const ADMIN_PANEL_NOTIFICATONS = {
  approveEmployeeWorkActivity: {
    id: 1,
    roles: [ROLES.isadmin, ROLES.issuperadmin],
    type: 'approveEmployeeWorkActivity',
    label: 'Awaiting employee work activities for approval',
  },
};

const ADMIN_PANEL_NOTIFICATONS_LIST = Object.keys(ADMIN_PANEL_NOTIFICATONS);

module.exports = {
  ADMIN_PANEL_NOTIFICATONS,
  ADMIN_PANEL_NOTIFICATONS_LIST,
};
