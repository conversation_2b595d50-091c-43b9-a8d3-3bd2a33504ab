// keys should be string type, because encrypt string and number return different encrypted data
/* eslint-disable */
const ignoredCardNumbersWithEncrypted = {
  // custom cards
  '****************': 'b18da7de138aff424c5f086f82afe898',
  // stripe test cards
  '****************': 'f69a0970b7eb4f6061c5484e13f02fce',
  '****************': 'bd52270f10b54143ed84925afc5350a8',
  '****************': 'f2a0cc693d1147c320be52af41072d2c',
  '2223003122003222': '5aa805e7a7a86e9722b61863eca5550a',
  '****************': '8f21be07936d35ca4450b1a53478a78b',
  '****************': 'caef55cc19bd1b376d7edb0435e2f924',
  '***************': '7fa8219033ee3a4e8c3d197866d6debf',
  '***************': '767d5263972ea627bbadf311ded94ff2',
  '****************': '83430ab9ca819421f0d98a40360e6f4d',
  '****************': '2e64bb0935f29a9c80b2f972faba4eee',
  '****************': 'b9ef680870b46159ae6f753f2fdf34a3',
  '3056930009020004': 'dc3ca3b94d42f767c51a7c48d8a8d276',
  '36227206271667': '9e3c80259c2ff9d5f74bade363a924f7',
  '3566002020360505': '47ae78ea2dce5e68de93247b277b0576',
  '6200000000000005': '592a9cfdc5d4e086fc8cd78c9c8a48cb',
  '6200000000000047': '589b0ffbae96d1d5cb465cae51b321ce',
  '****************': 'a983f42ae1cd1f5754e5f28365a0f09f',
  '5555552500001001': '4a58366ed7681464fff5a21c22797df4',
  '4000050360000001': '43aae86bcbd10838e7a61eb1205c82de',
  '5555050360000080': '6fd98e5b64383384b83c735985f896d0',
  '4000000760000002': '4ec541df5f6abab04c4a9aa0d09321ed',
  '4000001240000000': 'd22a3bb46ee878db080e53c93684c991',
  '4000004840008001': '8e504c23ce7014baa156f7d4185ba0fa',
  '4000007840000001': '74071d89516c363b33cf235698c36dea',
  '5200007840000022': '374befb2403c008418dc348c60bbf0f9',
  '4000000400000008': '163509501fc21abb7c0e2a1190ed0f1a',
  '4000000560000004': '204d8fda6dd8cdd4e3df46d635134110',
  '4000001000000000': '4080b2b8af42f822d0e263f2d21cc568',
  '4000001120000005': 'd712c7921f8642096b1110faeaf562bf',
  '4000001910000009': '185f0240fc162d0c36f81a717abf19a6',
  '4000001960000008': '5a7cb351c04cd400a8a2fab158d0e282',
  '4000002030000002': '8ca91ac952966e78453c213fe246ef44',
  '4000002080000001': '8be5aad12d721a2860392be1b27e50ec',
  '4000002330000009': '68c46f5f4e206784f6a57a35573ee44c',
  '4000002460000001': '25a967bd969f3cb7c9a3704b7cd5698d',
  '4000002500000003': '9f7f697e023f003ac9ef5843562a0214',
  '4000002760000016': 'b2f5d6293ffe4ed940339f9f1b096e57',
  '4000002920000005': '203f274c3ece22d5e2608851a3b7d8bd',
  '4000003000000030': '5b704508caeb3483d09d1f3c4285cc7c',
  '4000003480000005': 'd8f8076e2a75a16cc1d47f82aef2addc',
  '4000003720000005': 'e80b4e536dceb7f0abeb6f4f14866db0',
  '4000003800000008': '1ffedfd115aab046c4850ce3b1f95f09',
  '4000004280000005': '8095187dae296fbfbea382fda8fc493b',
  '4000004380000004': '96b043f0cced7f05a96cb18c7c71bbef',
  '4000004400000000': '52b8042c551fcf532976a6e04b38d7b3',
  '4000004420000006': '86af312591297abffcad167692502dce',
  '4000004700000007': '5dad40a13d2b5a4e87935d9832fe33b9',
  '4000005280000002': 'ac1e0b0426449b5253e4adb10fbaf199',
  '4000005780000007': '2800d9839224780668da763abd45958c',
  '4000006160000005': 'b5ca0bc19cc5022b5ad3b5a53f1ce854',
  '4000006200000007': '6defa4c475313c0c9c9d88df72c12d19',
  '4000006420000001': '29c99736e1f63088cea0efbcfecaded2',
  '4000006820000007': 'f52fb411de8516bc05da4bfe0fd9ba90',
  '4000007050000006': '66d3cd53ad0f8f6841811662bc73becb',
  '4000007030000001': 'b49d92c1029dd464759afafc36d58f8b',
  '4000007240000007': '21c1998def885342a61f22cb343b89d3',
  '4000007520000008': 'f7296e968a10d1625517b5a340027867',
  '4000007560000009': '08b341832287e63b852ba4b59821a97c',
  '4000008260000000': '3c1f0ff4a3ea267dc479bd97393c6eea',
  '4000058260000005': 'fb34e0538dc21990c6a4b44955c1ed75',
  '5555558265554449': '6f21d2e54d1e3acab11c636527305e11',
  '4000000360000006': 'fc5f91543409f366f88a1eba31edfd0c',
  '4000001560000002': 'b7fa587b80638f7fdc06841a64e0bb5d',
  '4000003440000004': '725b608df03d1f61ff4a7e6b60851a2f',
  '4000003560000008': 'aa20a57935575a2e338c19ca3e842e4a',
  '4000003920000003': 'cc681677a2f6aba5ba62cab114662537',
  '3530111333300000': '4f514bd41ce25f015af1c057c8d01cc8',
  '4000004580000002': '547ea241dc0d6febbbefd178bf7c09b0',
  '4000005540000008': '1b2e265c4d8081fff0e9e3593e95e328',
  '4000007020000003': 'c91cae0afe047cd8b5f9e32334169855',
  '4000007640000003': 'f987fe56453820020a27a9ffd7ad9683',
  '4000057640000008': '7fe0d4986f0bae541da0a1e26261e038',
  '4000000000000002': 'db6f4224c37ddf5812bba9813e7e693f',
  '4000000000009995': 'ce8aa053a70c1679fa4f4aaa3c3fe0e0',
  '4000000000009987': '7baa720dba94a93d12fec88db5a9b137',
  '4000000000009979': '3a9945b5d31abc4a01ca206f5f16227e',
  '4000000000000069': '6137768d85c1e6567840b6ff6e04b7ae',
  '4000000000000127': '1eb13df86d8e843f7cf3075c6ad4443a',
  '4000000000000119': '230b1c19faaf1ba4ce95404fcf7ae0eb',
  '4242424242424241': 'af018204b746f6783a3bfa799f7b445f',
  '4000000000000341': 'fa69981005c9c6bfab6e3c70ac26a021',
  '4100000000000019': 'be2afd5766fc0bd1d8ffd781883c1ec3',
  '4000000000004954': '8ea3c6e149edd010dd3ed93350b0b314',
  '4000000000009235': '82ecc864638cb8b98bce73da8beed5f4',
  '4000000000000101': '268be57619b0c18360543a166a2bdb52',
  '4000000000000036': '3ed770303b8b7bc0ee6cff92b73b8463',
  '4000000000000028': 'd98b9e4860b680fd3d66d98beef42750',
  '4000000000000010': '0f361d0e3687f051625d6a9e31e9b620',
  '4000000000000044': '9791f01ab0420586b6ee3a013a182e04',
  '4000000000000259': 'e392a4ddd13462dd54563839720acd12',
  '4000000000002685': 'fcdb6544597a58434770b242545ed4ac',
  '4000000000001976': 'c9630b09871a31e51b5039fcf01b8935',
  '4000000000005423': '824dde6fb1d51a4e21d5e48ad5f6acc5',
  '4000000000007726': 'd5c5ac3e57268058cc7393f3b7c0a6e6',
  '4000000000005126': '44d651bbf750c6b9f893e11cf1461871',
  '4000000000000077': '139f8cd4949b250a713b4d3b6a34d1d8',
  '4000003720000278': '1a24240605da2bd2b3651264d6e90a59',
  '4000002500003155': 'cc276a515369cdcecc5827e42e41b69c',
  '4000002760003184': '0a2e1eeec7b2eec1a710c13651f524bf',
  '4000003800000446': 'c2a55ffb780450c2b0bc89975a88e010',
  '4000008260003178': '2296ebed8f7da70308191ca2bc05492a',
  '4000000000003220': 'dbddb112480ea61ccd5698b1ca1377be',
  '4000000000003063': '2996f47177604f15c34ed54281466fc2',
  '4000008400001629': 'fabd9d37271a1321741942c2d0fad865',
  '4000008400001280': 'cce97776b7a6bca5170b6955c463a89e',
  '4000000000003055': '680d33aa092eb142ea14beacadceed95',
  '****************': '53d31a869a9c71bb43d2113e37a7a077',
  '****************': 'c82d506f12828c0d9cfccae21edc46a2',
  '****************': '4c42e5141dda682f4ef181b95a978a80',
  '****************': '6e750b06bbfea5c1c891dfe0bc4d860c',
  '****************': '50c8f0ce0c8c4a872bc87e58effecba9',
  '****************': '99e8c7b56b64e232e8834f5175d6a9b1',
  '****************': '8a60d849a96db8a77debb4d3ea0a505b',
  '****************': '29a51768406f5dbace2b86b467f63faa',
// Braintree cards
  '36259600000004': '04728c93125bee6be2226435c06002d1',
  '****************': 'ffc6330e0f11b713804d5430eb4cbc5c',
  '6304000000000000': 'dbbcd97dc0a957bdb49fd7f48bf05526',
  '2223000048400011': '8005700ecd5aa945c9478e4063de1fac',
  '****************': 'bae7b5d84020e00a638ec35d6ee15533',
  '****************': '59ae59a2f262a876bd183ef01bbf3553',
  '****************': 'a1249b5331fd84119e8201af07e2f689',
  '****************': '448d457c47339840d7203d66a113e67a',
  '****************': '8e1d580e0b3001db7be76fbaf8a45cb5',
  '****************': 'd349d8891159786f8207c9bb40dfbab0',
  '****************': 'b30eb07ef55ac4bb9cb8c0ad2aa445fb',
  '****************': '539fb12aa0ea7c3a61470c14ed5c85d9',
  '6243030000000001': 'dce89ee2a198b3c5b908576130fea199',
  '6221261111117766': '717ec4ba28cee26e6dccb4b2a1836318',
  '6223164991230014': 'd63222ef4f735a6038c799de7a48ac57',
  '****************': '3a4db440fe2f0710db352d647a3f492a',
  '***************': '6ff249c4623b82c7112512fa6d21dc82',
  '38520000009814': '963585dda642b7c7e15760b1fd1c9f2c',
  '4009040000000009': 'da17f87f676e952fe8834092b6dc37c4',
  '4012000033330125': 'a96154f139180133599e761c4b967b21',
  '4012000033330224': 'ccf89b58c814f5705bf3d8d8f6bff03b',
  '4012000033330422': '81b5d568304c1dedf840ebfcbca41825',
  '4012000033330323': 'cedfe161d5b7b30a2fa0dcda8e38bd52',
  '4012000033330620': '6a5c6a0e9138a8935eaf9182ba5702e5',
  '****************': 'ccc58c923d4749d8135a23c7c3ede8cd',
  '****************': 'bff556b1f8dac4afba9b7c2c23dfcfd5',
  '****************': '6a9d69afc6aabf17985bf2c6319a5eed',
  '****************': '7ab1d84d833ede7f3855d4fc2107263d',
  '***************': '0a605439bcdda7d147c1c1696fc53578',
  '***************': '69100d4d3daf75ced61162f41514bab1',
  '****************': 'ddfa6074e7df2c08dd99ed2c90b47ef7',
  '****************': 'd492e464b6732f68b792f14225b2886a',
};
/* eslint-enable */

const ignoredCardNumbersEncryptedList = Object.values(ignoredCardNumbersWithEncrypted);

module.exports = { ignoredCardNumbersWithEncrypted, ignoredCardNumbersEncryptedList };
