const firstFeb2022 = 1643670001;
const blockedChannelIds = ['37', '41', '565'];
const blockedCategoryIds = ['189', '8889350'];
const blockedCategoryIdsRegexp = [/,189$/g, /,189,/g, /,8889350$/g, /,8889350,/g];

/**
 * There are block rules for routes.
 * If rule of route will be resolved as true - client will get the Source was deleted error.
 * You can build nested conditions. Here is an example:
 *  {
 *    logic: 'or'|'and', # or - at least one condition should be true, and - all conditions should be true
 *    conditions: [
 *      {
 *        reqProperty: string, # path to request object value. For example: user._doc.id
 *        operator: '='|'!='|'>'|'<'|'<='|'>='|'~', # = and != works without string compare, ~ prop matched by regexp
 *        target: string|number|object{operator: string, values: array<string|number>},
 *      },
 *      {
 *        reqProperty: 'user._doc.id',
 *        operator: '=',
 *        target: {
 *          operator: 'or',
 *          values: blockedUserIds,
 *        },
 *      },
 *    ],
 *  }
 *
 */
const allRules = [
  // {
  //   route: '/chls.php',
  //   rule: {
  //     logic: 'and',
  //     conditions: [
  //       {
  //         logic: 'or',
  //         conditions: [
  //           {
  //             reqProperty: 'user._doc.created',
  //             operator: '>',
  //             target: firstFeb2022,
  //           },
  //           {
  //             reqProperty: 'user._doc.id',
  //             operator: '=',
  //             target: {
  //               operator: 'or',
  //               values: blockedUserIds,
  //             },
  //           },
  //         ],
  //       },
  //       {
  //         reqProperty: 'query.cid',
  //         operator: '=',
  //         target: {
  //           operator: 'or',
  //           values: blockedChannelIds,
  //         },
  //       },
  //     ],
  //   },
  // },
  // {
  //   route: '/loadrecord.php',
  //   rule: {
  //     logic: 'and',
  //     conditions: [
  //       {
  //         logic: 'or',
  //         conditions: [
  //           {
  //             reqProperty: 'user._doc.created',
  //             operator: '>',
  //             target: firstFeb2022,
  //           },
  //           {
  //             reqProperty: 'user._doc.id',
  //             operator: '=',
  //             target: {
  //               operator: 'or',
  //               values: blockedUserIds,
  //             },
  //           },
  //         ],
  //       },
  //       {
  //         reqProperty: 'resolved.channel._doc.id',
  //         operator: '=',
  //         target: {
  //           operator: 'or',
  //           values: blockedChannelIds,
  //         },
  //       },
  //     ],
  //   },
  // },
  // {
  //   route: ['/loadvod.php', '/loadvod2.php'],
  //   rule: {
  //     logic: 'and',
  //     conditions: [
  //       {
  //         logic: 'or',
  //         conditions: [
  //           {
  //             reqProperty: 'user._doc.created',
  //             operator: '>',
  //             target: firstFeb2022,
  //           },
  //           {
  //             reqProperty: 'user._doc.id',
  //             operator: '=',
  //             target: {
  //               operator: 'or',
  //               values: blockedUserIds,
  //             },
  //           },
  //         ],
  //       },
  //       {
  //         logic: 'or',
  //         conditions: [
  //           {
  //             reqProperty: 'resolved.vod._doc.cateid',
  //             operator: '=',
  //             target: {
  //               operator: 'or',
  //               values: blockedCategoryIds,
  //             },
  //           },
  //           {
  //             reqProperty: 'resolved.category._doc.topath',
  //             operator: '~',
  //             target: {
  //               operator: 'or',
  //               values: blockedCategoryIdsRegexp,
  //             },
  //           },
  //         ],
  //       },
  //     ],
  //   },
  // },
];

module.exports = {
  blockRules: allRules,
};
