const { ignoredCardNumbersWithEncrypted } = require('./ignoredCardNumbers');

const IP_TYPE = 'ip';
const UID_TYPE = 'uid';
const BILLING_DETAILS_TYPE = 'billingDetails';

const CREATION_METHOD_MANUAL = 'manual';
const CREATION_METHOD_AUTOMATIC = 'automatic';

const stealerTypes = {
  ip: IP_TYPE,
  uid: UID_TYPE,
  billingDetails: BILLING_DETAILS_TYPE,
};

const creationMethods = {
  manual: CREATION_METHOD_MANUAL,
  automatic: CREATION_METHOD_AUTOMATIC,
};

const ipReviewStatuses = {
  new: 'new',
  ignore: 'ignore',
  block: 'block',
  unblock: 'unblock',
};

const stealerTypesList = Object.values(stealerTypes);
const creationMethodsList = Object.values(creationMethods);
const ipReviewStatusesList = Object.values(ipReviewStatuses);
const ignoredCardNumbersList = Object.keys(ignoredCardNumbersWithEncrypted);
const ignoredCardNumbersEncryptedList = Object.values(ignoredCardNumbersWithEncrypted);

module.exports = {
  stealerTypes,
  stealerTypesList,
  creationMethods,
  creationMethodsList,
  ipReviewStatuses,
  ipReviewStatusesList,
  ignoredCardNumbersWithEncrypted,
  ignoredCardNumbersList,
  ignoredCardNumbersEncryptedList,
};
