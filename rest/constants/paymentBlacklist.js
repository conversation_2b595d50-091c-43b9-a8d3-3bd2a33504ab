const { ignoredCardNumbersWithEncrypted } = require('./ignoredCardNumbers');

const IP_TYPE = 'ip';
const UID_TYPE = 'uid';
const BILLING_DETAILS_TYPE = 'billingDetails';

const CREATION_METHOD_MANUAL = 'manual';
const CREATION_METHOD_AUTOMATIC = 'automatic';

const paymentBlacklistTypes = {
  ip: IP_TYPE,
  uid: UID_TYPE,
  billingDetails: BILLING_DETAILS_TYPE,
};

const creationMethods = {
  manual: CREATION_METHOD_MANUAL,
  automatic: CREATION_METHOD_AUTOMATIC,
};

const ipReviewStatuses = {
  new: 'new',
  ignore: 'ignore',
  block: 'block',
  unblock: 'unblock',
};

const paymentBlacklistTypesList = Object.values(paymentBlacklistTypes);
const creationMethodsList = Object.values(creationMethods);
const ipReviewStatusesList = Object.values(ipReviewStatuses);
const ignoredCardNumbersList = Object.keys(ignoredCardNumbersWithEncrypted);
const ignoredCardNumbersEncryptedList = Object.values(ignoredCardNumbersWithEncrypted);

module.exports = {
  paymentBlacklistTypes,
  paymentBlacklistTypesList,
  creationMethods,
  creationMethodsList,
  ipReviewStatuses,
  ipReviewStatusesList,
  ignoredCardNumbersWithEncrypted,
  ignoredCardNumbersList,
  ignoredCardNumbersEncryptedList,
};
