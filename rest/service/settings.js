const SettingsModel = require('../model/audb/Settings');

class Settings {
  static async get() {
    const settings = await SettingsModel.findOne({}).cache(3600, 'settings').exec();

    if (!settings) return (new SettingsModel()).toObject();

    return settings.toObject();
  }

  static async set(patch) {
    let settings = await SettingsModel.findOne({}).exec();

    if (!settings) settings = new SettingsModel(patch);
    else settings.set(patch);

    await settings.save();

    return settings.toObject();
  }
}

module.exports = Settings;
