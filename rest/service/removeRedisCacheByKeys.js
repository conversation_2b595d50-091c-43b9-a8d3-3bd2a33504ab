const logger = require('@s1/log');
const redisClient = require('./redisClient');

const log = logger.create(__filename);

const baseCachemanPrefix = 'cacheman:cacheman:cachegoose-cache:';
const STREAM_BATCH_SIZE = 1000;
const DELETE_BATCH_SIZE = 500;

const removeKey = (value, useCachemanPrefix) => {
  const keyPattern = useCachemanPrefix ? `${baseCachemanPrefix}${value}*` : `${value}*`;

  const scanStream = redisClient.scanStream({
    match: keyPattern,
    count: STREAM_BATCH_SIZE,
  });

  return new Promise((resolve, reject) => {
    const pipeline = redisClient.pipeline();
    const buffer = [];

    scanStream.on('data', (keys) => {
      buffer.push(...keys);

      while (buffer.length >= DELETE_BATCH_SIZE) {
        const chunk = buffer.splice(0, DELETE_BATCH_SIZE);
        chunk.forEach((k) => pipeline.del(k));
      }
    });

    scanStream.on('end', async () => {
      try {
        if (buffer.length > 0) {
          buffer.forEach((k) => pipeline.del(k));
        }

        await pipeline.exec();
        resolve();
      } catch (err) {
        log.error('Error executing Redis pipeline:', err);
        reject(err);
      }
    });

    scanStream.on('error', (err) => {
      log.error('Error scanning Redis keys:', err);
      reject(err);
    });
  });
};

/**
 * Method remove redis cache by matching for each of keys
 *
 * @param {array<string>} keys - list of keys for remove from the redis cache
 * @param {boolean} useCachemanPrefix - use prefix like 'cacheman:cacheman:cachegoose-cache:'
 * */
module.exports = async (keys, useCachemanPrefix = true) => {
  if (!Array.isArray(keys) || keys.length === 0) return;

  await Promise.all(
    keys.map((key) => removeKey(key, useCachemanPrefix))
  );
};
