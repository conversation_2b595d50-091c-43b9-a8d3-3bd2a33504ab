const Redis = process.env.NODE_ENV !== 'test' ? require('ioredis') : require('redis-mock');
const commands = require('redis-commands');
const configs = require('../../config');

require('@s1/log').level(configs.loglevel);

// eslint-disable-next-line import/order
const log = require('@s1/log').create(__filename);

const redisType = configs.redis.type;
let exportModule;

if (redisType === 'replica' || redisType === undefined) {
  const redisOptions = {
    enable_offline_queue: configs.redis.enable_offline_queue,
    retry_strategy: function retry(options) {
      log.debug(options);

      if (options.error.code === 'ECONNREFUSED') log.debug('connection refused');
      if (options.total_retry_time > 1000 * 60 * 60) return new Error('Retry time exhausted');

      return Math.max(options.attempt * 100, 2000);
    },
  };

  const master = Redis.createClient(Object.assign({}, configs.redis.master, redisOptions));
  const slave = Redis.createClient(Object.assign({}, configs.redis.slave, redisOptions));
  const redisErrors = ['ECONNREFUSED'];

  master.on('error', (err) => {
    if (redisErrors.includes(err.code)) process.env.MASTER_REDIS_CONNECTED = 'false';
  });
  slave.on('error', (err) => {
    if (redisErrors.includes(err.code)) process.env.SLAVE_REDIS_CONNECTED = 'false';
  });

  master.on('connect', () => {
    process.env.MASTER_REDIS_CONNECTED = 'true';
  });
  slave.on('connect', () => {
    process.env.SLAVE_REDIS_CONNECTED = 'true';
  });

  master.on('disconnect', () => {
    process.env.MASTER_REDIS_CONNECTED = 'false';
  });
  slave.on('disconnect', () => {
    process.env.SLAVE_REDIS_CONNECTED = 'false';
  });

  if (process.env.NODE_ENV !== 'test') {
    const originalSendCommand = slave.sendCommand.bind(slave);
    slave.sendCommand = function sendCommandWrapper(command, stream) {
      const isCommandReadOnly = commands.exists(command.name) && commands.hasFlag(command.name, 'readonly');

      if (!isCommandReadOnly) log.debug(`redis writing: ${command.name} ${command.args && command.args[0]}`);
      // else log.debug(`redis reading: ${command.name} ${command.args && command.args[0]}`);

      return isCommandReadOnly ? originalSendCommand(command, stream) : master.sendCommand(command, stream);
    };
  }

  exportModule = slave;
} else if (redisType === 'sentinel') {
  exportModule = new Redis({
    sentinels: configs.redis.sentinelHosts,
    name: configs.redis.masterName,
    enableAutoPipelining: true,
  });
  exportModule.on('error', (err) => {
    console.error(err);

    if (err instanceof Redis.ReplyError) process.env.MASTER_REDIS_CONNECTED = 'false';
  });
  exportModule.on('connect', () => {
    console.log('REDIS CONNECTED');
    process.env.MASTER_REDIS_CONNECTED = 'true';
  });
  exportModule.on('close', () => {
    console.log('REDIS CONNECTION WAS CLOSED');
    process.env.MASTER_REDIS_CONNECTED = 'true';
  });
}

module.exports = exportModule;
