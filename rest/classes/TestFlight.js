const jwt = require('jsonwebtoken');
const axios = require('axios');
const config = require('../../config');

class NewTestFlight {
  constructor({ aud, iss, keyid, cert, algorithm }) {
    const exp = Math.floor(Date.now() / 1000) + 20 * 60;
    this.token = jwt.sign({ aud, iss, exp }, cert, { keyid, algorithm });
    this.headers = { Authorization: `Bearer ${this.token}"` };
    this.baseUrl = config.appleInvite.baseUrl;
  }

  async getAppGroups(app) {
    const { data: { data: groups } } = await axios.request({
      method: 'get',
      headers: this.headers,
      url: app.relationships.betaGroups.links.related,
    });

    app.groups = groups.filter(group => group.attributes.name !== 'internal');

    return app;
  }

  async getAllAppsWithGroups() {
    try {
      const { data: { data: apps } } = await axios.request({
        method: 'get',
        headers: this.headers,
        url: `${this.baseUrl}/apps`,
        params: { },
      });

      const promises = [];
      apps.forEach(app => promises.push(this.getAppGroups(app)));
      await Promise.all(promises);

      return apps;
    } catch (error) {
      console.log(error);

      throw new Error('Can not load apps');
    }
  }

  async getGroup({ appId, groupName }) {
    try {
      const { data: { data: groups } } = await axios.request({
        method: 'get',
        headers: this.headers,
        url: `${this.baseUrl}/betaGroups`,
        params: { 'filter[app]': appId, 'filter[name]': groupName },
      });

      if (!groups.length) throw new Error('Can not find group');

      return groups[0];
    } catch (error) {
      console.log(error);

      throw new Error('Can not find group');
    }
  }

  async getUserAppId(user) {
    const { data: apps } = await axios.get(user.relationships.apps.links.self, { headers: this.headers });
    user.apps = apps.data;

    return user;
  }

  /**
   * Find user by email and app.
   * @param email
   * @param appId
   * @returns {Promise<any>}
   */
  async findBetaTester({ email, appId }) {
    const { data: { data: users } } = await axios.get(`${this.baseUrl}/betaTesters`, {
      headers: this.headers,
      params: {
        'filter[email]': email,
      },
    });

    const promises = [];
    for (let i = 0; i < users.length; ++i) {
      promises.push(this.getUserAppId(users[i]));
    }

    const usersWithApps = await Promise.all(promises);
    // filter users without apps and with current app
    const filteredUsers = usersWithApps
      .filter(user => !!user.apps.length)
      .filter(user => user.apps.some(app => app.id.toString() === appId.toString()));

    return filteredUsers.length ? filteredUsers[0] : null;
  }

  /**
   * Find user by email and app.
   * @param email
   * @param appId
   * @returns {Promise<any>}
   */
  async registerBetaTester({ email, firstName, lastName, groupId }) {
    const betaTesterData = {
      data: {
        attributes: { email, firstName, lastName },
        relationships: { betaGroups: { data: [{ type: 'betaGroups', id: groupId }] } },
        type: 'betaTesters',
      },
    };
    const { data: { data: user } } = await axios.request({
      method: 'post',
      data: betaTesterData,
      headers: this.headers,
      url: `${this.baseUrl}/betaTesters`,
    });

    return user;
  }

  /**
   * @param userId
   * @param appId
   * @returns {Promise<any>}
   */
  async removeUserFromApp({ userId, appId }) {
    await axios.request({
      method: 'DELETE',
      headers: this.headers,
      url: `${this.baseUrl}/betaTesters/${userId}/relationships/apps`,
      data: {
        data: [
          {
            id: appId.toString(),
            type: 'apps',
          },
        ],
      },
    });
  }

  async addTesterToTheGroup({ groupId, userId }) {
    // add user to the group
    const dataForGroup = {
      data: [{
        id: userId, type: 'betaTesters',
      }],
    };
    const result = await axios.request({
      method: 'post',
      data: dataForGroup,
      headers: this.headers,
      url: `${this.baseUrl}/betaGroups/${groupId}/relationships/betaTesters`,
    });

    return result;
  }

  async addTester({ appId, groupId, email, firstName, lastName }) {
    try {
      let tester = await this.findBetaTester({ email, appId });

      if (tester) {
        // remove tester from the APP to be able to received email on register again
        await this.removeUserFromApp({ userId: tester.id, appId });
      }

      tester = await this.registerBetaTester({ email, firstName, lastName, groupId });

      if (!tester) return {
        error: 0,
        success: false,
        message: 'Cannot create tester',
      };

      const data = {
        data: {
          type: 'betaTesterInvitations',
          relationships: {
            app: { data: { id: appId, type: 'apps' } },
            betaTester: { data: { id: tester.id, type: 'betaTesters' } },
          },
        },
      };
      const result = await axios.request({
        method: 'post',
        data,
        headers: this.headers,
        url: `${this.baseUrl}/betaTesterInvitations`,
      });

      if (result.statusText === 'Created') return {
        error: 0,
        success: true,
      };

      return {
        error: 1,
        success: false,
        message: result.statusText,
        response: result.hasOwnProperty('response') ? result.response : result,
      };
    } catch (error) {
      if (error.hasOwnProperty('title')) {
        return {
          error: 1,
          success: false,
          message: error.title,
          response: error,
        };
      }
      if (error && error.response && error.response.data && error.response.data.errors
        && error.response.data.errors.length && error.response.data.errors[0].title) {
        return {
          error: 1,
          success: false,
          message: error.response.data.errors[0].title,
          response: error.response.data.errors[0],
        };
      }

      return {
        error: 1,
        success: false,
        message: 'Can not add user',
        response: error,
      };
    }
  }
}

module.exports = NewTestFlight;
