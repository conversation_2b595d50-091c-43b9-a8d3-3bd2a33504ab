const UserCard = require('../../../model/audb/UserCard');
const config = require('../../../../config');
const { ignoredCardNumbersEncryptedList } = require('../../../constants/ignoredCardNumbers');
const StealerWhitelistCard = require('../../../model/audb/StealerWhitelistCard');

const getWhitelistedCards = async () => {
  const whitelistedCardModels = await StealerWhitelistCard.find().lean();
  const cards = whitelistedCardModels.map(model => model.number);

  return cards;
};

/**
 * Method load stealer user card numbers
 *
 * @param {User} user - user model|object
 * @param {array<string>} userCardNumbers - list of card numbers
 *
 * @return {array<string>} - list of stealer user card numbers
 * */
module.exports = async (user, userCardNumbers) => {
  if (!user || !userCardNumbers || !userCardNumbers.length) return [];

  const stealerUserCards = [];
  let filteredCardNumbers = process.env.NODE_ENV === 'production' ? userCardNumbers.filter(card => !ignoredCardNumbersEncryptedList.includes(card)) : userCardNumbers;
  const whitelistedCards = await getWhitelistedCards();
  filteredCardNumbers = filteredCardNumbers.filter(cardNumber => !whitelistedCards.includes(cardNumber));

  for (let i = 0; i < filteredCardNumbers.length; ++i) {
    const stealerUserCardModel = await UserCard.findOne(
      { number: filteredCardNumbers[i], isStealer: true },
      { number: 1, first6: 1, last4: 1 },
    ).lean().cache(config.stealerList.cache, `userCard_stealer_cardNumber_${filteredCardNumbers[i]}`);

    if (stealerUserCardModel) stealerUserCards.push(stealerUserCardModel);
  }

  return stealerUserCards;
};
