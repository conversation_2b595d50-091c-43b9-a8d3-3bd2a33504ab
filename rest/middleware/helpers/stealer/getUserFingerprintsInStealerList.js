const UserFingerprintPayment = require('../../../model/audb/UserFingerprintPayment');
const config = require('../../../../config');

/**
 * Method load user payment fingerprints from stealer list
 *
 * @param {User} user - user model|object
 * @param {array<string>} fingerprints - list of fingerprints
 *
 * @return {array<string>} - list of stealer user fingerprints
 * */
module.exports = async (user, fingerprints) => {
  if (!user || !fingerprints || !fingerprints.length) return [];
  if (!config.stealerList.fingerprints.enabled) return [];

  const stealerUserFingerprints = [];

  for (let i = 0; i < fingerprints.length; ++i) {
    const stealerUserFingerprintModel = await UserFingerprintPayment.findOne(
      { isStealer: true, fingerprint: fingerprints[i] },
      { fingerprint: 1 },
    ).lean().sort({ _id: 1 })
      .cache(config.stealerList.cache, `userFingerprintPayment_stealer_fingerprint_${fingerprints[i]}`);

    if (stealerUserFingerprintModel) stealerUserFingerprints.push(stealerUserFingerprintModel);
  }

  return stealerUserFingerprints;
};
