const isStealerUser = require('../../../commands/payment/helpers/rules/isStealerUser');
const addUserToStealerList = require('../../../commands/stealer/addUserToStealerList');
const addAllUserIpsToStealerList = require('../../../commands/stealer/addAllUserIpsToStealerList');
const addAllUserPaymentFingerprintsToStealerList = require('../../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const addAllUserCardsToStealerList = require('../../../commands/stealer/addAllUserCardsToStealerList');
const addCookieToResponse = require('../paymentBlacklist/addCookieToResponse');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user billing addresses in the stealer list.
 * On found stealer user we add him and his all IPs to the stealer list and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const userDataBillingAddress = { billingAddresses: user.billingAddresses };
  const hasStealerBillingAddress = await isStealerUser({ user: userDataBillingAddress });

  if (hasStealerBillingAddress) {
    const userDescription = `Detected by stealer billingAddress, rule: ${hasStealerBillingAddress.rule}, address: ${hasStealerBillingAddress.data}`;
    const otherDescription = `Detected by stealer billingAddress for the user ${user.id} ${user.email}, rule: ${hasStealerBillingAddress.rule}, address: ${hasStealerBillingAddress.data}`;
    await addUserToStealerList(user, userDescription);
    // no need to wait for add to the stealer list ips, etc
    addAllUserIpsToStealerList(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToStealerList(userFingerprints, otherDescription);
    addAllUserCardsToStealerList(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
