const addUserToStealerList = require('../../../commands/stealer/addUserToStealerList');
const addAllUserIpsToStealerList = require('../../../commands/stealer/addAllUserIpsToStealerList');
const addAllUserPaymentFingerprintsToStealerList = require('../../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const addAllUserCardsToStealerList = require('../../../commands/stealer/addAllUserCardsToStealerList');
const addCookieToResponse = require('../paymentBlacklist/addCookieToResponse');
const getUserIpsInStealerList = require('./getUserIpsInStealerList');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user IPs in the stealer list.
 * On found stealer user we add him and his all IPs to the stealer list and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const userStealerIps = await getUserIpsInStealerList(user, userIPs);

  if (userStealerIps.length) {
    const stealerIp = userStealerIps[0].ip;
    const userDescription = `Detected by stealer IP ${stealerIp}`;
    const otherDescription = `Detected by stealer IP ${stealerIp} for the user ${user.id} ${user.email}`;
    await addUserToStealerList(user, userDescription);
    // no need to wait for add to the stealer list ips, etc
    addAllUserIpsToStealerList(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToStealerList(userFingerprints, otherDescription);
    addAllUserCardsToStealerList(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
