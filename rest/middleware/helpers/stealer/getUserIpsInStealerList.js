const Stealer = require('../../../model/audb/Stealer');
const config = require('../../../../config');

/**
 * Method load user IPs objects list from stealer list
 * */
module.exports = async (user, userIPs) => {
  if (!user || !userIPs || !userIPs.length) return [];

  const userStealerIps = [];

  for (let i = 0; i < userIPs.length; ++i) {
    const stealerIpModel = await Stealer.findOne({ ip: userIPs[i] }, { ip: 1 })
      .lean().cache(config.stealerList.cache, `stealer_ip_${userIPs[i]}`);

    if (stealerIpModel) userStealerIps.push(stealerIpModel);
  }

  return userStealerIps;
};
