const addUserToStealerList = require('../../../commands/stealer/addUserToStealerList');
const addAllUserIpsToStealerList = require('../../../commands/stealer/addAllUserIpsToStealerList');
const addAllUserPaymentFingerprintsToStealerList = require('../../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const addAllUserCardsToStealerList = require('../../../commands/stealer/addAllUserCardsToStealerList');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');
const addCookieToResponse = require('../paymentBlacklist/addCookieToResponse');
const Stealer = require('../../../model/audb/Stealer');
const config = require('../../../../config');

/**
 * Method check user IP in stealer list.
 * On found stealer user we add him and his all IPs to the stealer list and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userIP, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const stealerUserIp = await Stealer.findOne({ ip: userIP })
    .lean().cache(config.stealerList.cache, `stealer_ip_${userIP}`);

  if (stealerUserIp) {
    const userDescription = `Detected by stealer IP ${userIP}`;
    const otherDescription = `Detected by stealer IP ${userIP} for the user ${user.id} ${user.email}`;
    await addUserToStealerList(user, userDescription);
    // no need to wait for add to the stealer ips, etc
    addAllUserIpsToStealerList(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToStealerList(userFingerprints, otherDescription);
    addAllUserCardsToStealerList(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
