const config = require('../../../../config');
const checkRelatedUsersByEncryptedData = require('./checkRelatedUsersByEncryptedData');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user relations storage key and add user and related users and their IPs, cards to the stealer list
 * and renew cookie expires date in response
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const { userRelationsKey } = config.cookies;

  if (request.query && request.query[userRelationsKey] || request.cookies && request.cookies[userRelationsKey]) {
    const relatedUsersStorageValue = request.query[userRelationsKey] || request.cookies[userRelationsKey];
    const checkRelatedUsersResult = await checkRelatedUsersByEncryptedData(
      request, response, user, userIPs, userFingerprints, userCardNumbers, relatedUsersStorageValue,
    );

    return checkRelatedUsersResult;
  }

  return false;
};
