const config = require('../../../../config');
const Stealer = require('../../../model/audb/Stealer');
const addAllUserIpsToStealerList = require('../../../commands/stealer/addAllUserIpsToStealerList');
const addAllUserPaymentFingerprintsToStealerList = require('../../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const addAllUserCardsToStealerList = require('../../../commands/stealer/addAllUserCardsToStealerList');
const addCookieToResponse = require('../paymentBlacklist/addCookieToResponse');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');
const moveUserToStealerPermissionGroup = require('../../../commands/stealer/moveUserToStealerPermissionGroup');

/**
 * Method check stealer user by ID.
 * On found stealer user we add him and his all IPs to the stealer list and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const stealerUserId = await Stealer.findOne({ uid: user.id })
    .lean().cache(config.stealerList.cache, `stealer_id_${user.id}`);

  if (stealerUserId) {
    const description = `Detected by stealer user ${user.id} ${user.email}`;
    // no need to wait for add to the stealer ips or id
    addAllUserIpsToStealerList(user, userIPs, description);
    addAllUserPaymentFingerprintsToStealerList(userFingerprints, description);
    addAllUserCardsToStealerList(user, userCardNumbers, description);
    moveUserToStealerPermissionGroup(user);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
