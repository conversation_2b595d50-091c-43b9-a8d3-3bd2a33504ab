const addUserToStealerList = require('../../../commands/stealer/addUserToStealerList');
const addAllUserIpsToStealerList = require('../../../commands/stealer/addAllUserIpsToStealerList');
const addAllUserPaymentFingerprintsToStealerList = require('../../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const addAllUserCardsToStealerList = require('../../../commands/stealer/addAllUserCardsToStealerList');
const addCookieToResponse = require('../paymentBlacklist/addCookieToResponse');
const isStealerUser = require('../../../commands/payment/helpers/rules/isStealerUser');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user firstname and lastname in the stealer list.
 * On found stealer user we add him and his all IPs to the stealer list and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const [firstname, lastname] = user.name.split(' ');
  const userDataName = { firstname, lastname };
  const hasStealerName = await isStealerUser({ user: userDataName });

  if (hasStealerName) {
    const userDescription = `Detected by stealer name ${user.name}, stealer rule: ${hasStealerName.rule}`;
    const otherDescription = `Detected by stealer name ${user.name} for the user ${user.id} ${user.email}, stealer rule: ${hasStealerName.rule}`;
    await addUserToStealerList(user, userDescription);
    // no need to wait for add to the stealer list ips, etc
    addAllUserIpsToStealerList(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToStealerList(userFingerprints, otherDescription);
    addAllUserCardsToStealerList(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
