const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const isBlacklistedUser = require('../../../commands/payment/helpers/rules/isBlacklistedUser');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user firstname and lastname in the payment blacklist.
 * On found blacklisted user we add him and his all IPs to the blacklist and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const [firstname, lastname] = user.name.split(' ');
  const userDataName = { firstname, lastname };
  const hasBlacklistedName = await isBlacklistedUser({ user: userDataName });

  if (hasBlacklistedName) {
    const userDescription = `Blocked by blacklisted name ${user.name}, blacklisted rule: ${hasBlacklistedName.rule}`;
    const otherDescription = `Blocked by blacklisted name ${user.name} for the user ${user.id} ${user.email}, blacklisted rule: ${hasBlacklistedName.rule}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
