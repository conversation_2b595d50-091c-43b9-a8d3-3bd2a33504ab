const PaymentBlacklistIpReview = require('../../../model/audb/PaymentBlacklistIpReview');
const config = require('../../../../config');

/**
 * Method load user IPs exists in payment blacklist IPs for review
 *
 * @param {User} user - user model|object
 * @param {array<string>} userIPs - list of fingerprints
 *
 * @return {array<string>} list of user IPs exists in review for payment blacklist
 * */
module.exports = async (user, userIPs) => {
  if (!user || !userIPs || !userIPs.length) return [];

  const userReviewedIpsModels = [];

  for (let i = 0; i < userIPs.length; ++i) {
    const reviewedIpModel = await PaymentBlacklistIpReview.findOne({ ip: userIPs[i] }, { ip: 1 })
      .lean().cache(config.paymentBlacklist.cache, `paymentBlacklistIpReview_ip_${userIPs[i]}`);

    if (reviewedIpModel) userReviewedIpsModels.push(reviewedIpModel);
  }

  const userReviewedIps = userReviewedIpsModels.map(model => model.ip);

  return userReviewedIps;
};
