const UserCard = require('../../../model/audb/UserCard');
const config = require('../../../../config');
const { ignoredCardNumbersEncryptedList } = require('../../../constants/paymentBlacklist');

/**
 * Method load user blacklisted card numbers
 *
 * @param {User} user - user model|object
 * @param {array<string>} userCardNumbers - list of card numbers
 *
 * @return {array<string>} - list of blacklisted user card numbers
 * */
module.exports = async (user, userCardNumbers) => {
  if (!user || !userCardNumbers || !userCardNumbers.length) return [];

  const blacklistedUserCards = [];
  const filteredCardNumbers = process.env.NODE_ENV === 'production' ? userCardNumbers.filter(card => !ignoredCardNumbersEncryptedList.includes(card)) : userCardNumbers;

  for (let i = 0; i < filteredCardNumbers.length; ++i) {
    const blacklistedUserCardModel = await UserCard.findOne(
      { number: filteredCardNumbers[i], isBlacklisted: true },
      { number: 1, first6: 1, last4: 1 },
    ).lean().sort({ _id: 1 })
      .cache(config.paymentBlacklist.cache, `userCard_blacklist_cardNumber_${filteredCardNumbers[i]}`);

    if (blacklistedUserCardModel) blacklistedUserCards.push(blacklistedUserCardModel);
  }

  return blacklistedUserCards;
};
