const UserFingerprintPayment = require('../../../model/audb/UserFingerprintPayment');
const config = require('../../../../config');

/**
 * Method load user payment fingerprints from payment blacklist
 *
 * @param {User} user - user model|object
 * @param {array<string>} fingerprints - list of fingerprints
 *
 * @return {array<string>} - list of blacklisted user fingerprints
 * */
module.exports = async (user, fingerprints) => {
  if (!user || !fingerprints || !fingerprints.length) return [];
  if (!config.paymentBlacklist.fingerprints.enabled) return [];

  const blacklistedUserFingerprints = [];

  for (let i = 0; i < fingerprints.length; ++i) {
    const blacklistedFingerprintModel = await UserFingerprintPayment.findOne(
      { isBlacklisted: true, fingerprint: fingerprints[i] },
      { fingerprint: 1 },
    ).lean().sort({ _id: 1 })
      .cache(config.paymentBlacklist.cache, `userFingerprintPayment_blacklist_fingerprint_${fingerprints[i]}`);

    if (blacklistedFingerprintModel) blacklistedUserFingerprints.push(blacklistedFingerprintModel);
  }

  return blacklistedUserFingerprints;
};
