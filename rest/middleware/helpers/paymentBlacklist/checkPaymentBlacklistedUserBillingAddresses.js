const isBlacklistedUser = require('../../../commands/payment/helpers/rules/isBlacklistedUser');
const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user billing addresses in the payment blacklist.
 * On found blacklisted user we add him and his all IPs to the blacklist and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const userDataBillingAddress = { billingAddresses: user.billingAddresses };
  const hasBlacklistedBillingAddress = await isBlacklistedUser({ user: userDataBillingAddress });

  if (hasBlacklistedBillingAddress) {
    const userDescription = `Blocked by blacklisted billingAddress, rule: ${hasBlacklistedBillingAddress.rule}, address: ${hasBlacklistedBillingAddress.data}`;
    const otherDescription = `Blocked by blacklisted billingAddress for the user ${user.id} ${user.email}, rule: ${hasBlacklistedBillingAddress.rule}, address: ${hasBlacklistedBillingAddress.data}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
