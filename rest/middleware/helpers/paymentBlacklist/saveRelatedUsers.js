const config = require('../../../../config');
const { decryptWithRedis } = require('../../../helpers/security');
const UserRelations = require('../../../model/audb/UserRelations');
const getUserRelatedUsersIds = require('../../../commands/payment/getUserRelatedUsersIds');
const removeRedisCacheByKeys = require('../../../service/removeRedisCacheByKeys');

/**
 * Method save related usersIDs for the current user
 * */
module.exports = async (user, relatedUsersStorageValue) => {
  try {
    const { userRelationsSalt } = config.cookies;
    const relatedUserIDsFromCookie = await Promise.all(relatedUsersStorageValue.split('.').filter(userId => !!userId && userId !== 'undefined' && userId !== 'null').map(async id => parseInt(await decryptWithRedis(id, userRelationsSalt))));
    const userRelatedUsersIDs = await getUserRelatedUsersIds(user.id);
    const newRelatedUserIDs = relatedUserIDsFromCookie.filter(userId => userId !== user.id && !userRelatedUsersIDs.includes(userId));
    const allUsersIds = Array.from(new Set(relatedUserIDsFromCookie.concat(userRelatedUsersIDs)));

    if (newRelatedUserIDs.length) {
      // load all exist user pairs
      const allUserPairs = await UserRelations.find(
        { $or: [{ userId: { $in: allUsersIds } }, { relatedUserId: { $in: allUsersIds } }] },
        { _id: 0, userId: 1, relatedUserId: 1 },
      ).lean();
      const existsUsersPairIDs = {};
      allUserPairs.forEach((pair) => {
        existsUsersPairIDs[`${pair.userId}_${pair.relatedUserId}`] = true;
      });

      try {
        const relationsForInsert = [];
        const redisKeys = [];

        // insert relations to the current and related users
        newRelatedUserIDs.forEach((newRelatedUserID) => {
          if (!existsUsersPairIDs.hasOwnProperty(`${user.id}_${newRelatedUserID}`)) {
            relationsForInsert.push({ userId: user.id, relatedUserId: newRelatedUserID });
            redisKeys.push(`userRelations_id_${newRelatedUserID}`);
            redisKeys.push(`userRelations_id_${user.id}`);
          }
          if (!existsUsersPairIDs.hasOwnProperty(`${newRelatedUserID}_${user.id}`)) {
            relationsForInsert.push({ userId: newRelatedUserID, relatedUserId: user.id });
            redisKeys.push(`userRelations_id_${newRelatedUserID}`);
            redisKeys.push(`userRelations_id_${user.id}`);
          }

          // insert relations between all related users
          allUsersIds.forEach((relatedUserId) => {
            if (relatedUserId !== newRelatedUserID && relatedUserId !== user.id) {
              if (!existsUsersPairIDs.hasOwnProperty(`${relatedUserId}_${newRelatedUserID}`)) {
                relationsForInsert.push({ userId: relatedUserId, relatedUserId: newRelatedUserID });
                redisKeys.push(`userRelations_id_${newRelatedUserID}`);
                redisKeys.push(`userRelations_id_${relatedUserId}`);
              }
              if (!existsUsersPairIDs.hasOwnProperty(`${newRelatedUserID}_${relatedUserId}`)) {
                relationsForInsert.push({ userId: newRelatedUserID, relatedUserId });
                redisKeys.push(`userRelations_id_${newRelatedUserID}`);
                redisKeys.push(`userRelations_id_${relatedUserId}`);
              }
            }
          });
        });

        if (redisKeys.length) await removeRedisCacheByKeys(Array.from(new Set(redisKeys)));
        if (relationsForInsert.length) await UserRelations.insertMany(relationsForInsert, { ordered: false });
      } catch (e) {
        // some relations might be already added and we can get an error
        console.error(`Cannot add new related users for the current user# ${user.id}`, e);
      }
    }

    const usersIDsSet = new Set([...userRelatedUsersIDs, user.id]);

    return Array.from(usersIDsSet);
  } catch (e) {
    console.log(`Cannot decrypt related users for the user# ${user.id}, error:`, e);

    throw new Error('API error');
  }
};
