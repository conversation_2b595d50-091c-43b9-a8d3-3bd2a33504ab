const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const getUserCardsInPaymentBlacklist = require('./getUserCardsInPaymentBlacklist');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');
const User = require('../../../model/audb/User');
const UserCard = require('../../../model/audb/UserCard');
const { ignoredCardNumbersEncryptedList } = require('../../../constants/paymentBlacklist');

/**
 * Method check blacklisted user by card number and add user and his card numbers, fingerprints, IPs to the blacklist
 * and add blacklisted cookie
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const userBlacklistedCardModels = await getUserCardsInPaymentBlacklist(user, userCardNumbers);
  const filteredCardNumbers = process.env.NODE_ENV === 'production' ? userBlacklistedCardModels.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userBlacklistedCardModels;

  if (filteredCardNumbers.length) {
    let cardNumbersLabel = '';

    // create cardNumbersLabel like: iaepBRayYK3CcFtMT8O3-1 (userId-1,userId-2,userId-3) iaepBRayYK3CcFtMT8O3-2 (userId-4,userId-5)
    for (let i = 0; i < filteredCardNumbers.length; ++i) {
      cardNumbersLabel += `${filteredCardNumbers[i].first6.substring(0, 4)}-****-****-${filteredCardNumbers[i].last4}`;

      const userCardModels = await UserCard.aggregate([
        {
          $match: { number: filteredCardNumbers[i].number },
        },
        {
          $project: { uid: 1 },
        },
        {
          $lookup: {
            from: 'tuser',
            localField: 'uid',
            foreignField: 'id',
            as: 'User',
          },
        },
        { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
        {
          $project: { uid: 1, em: '$User.em' },
        },
      ]);

      if (userCardModels.length) {
        cardNumbersLabel += ' (';

        for (let f = 0; f < userCardModels.length; ++f) {
          if (f !== 0) cardNumbersLabel += ', ';

          cardNumbersLabel += userCardModels[f].uid;
          const email = userCardModels[f].em ? await User.decryptEmailWithRedis(userCardModels[f].em) : '';
          cardNumbersLabel += email ? ` ${email}` : '';
        }

        cardNumbersLabel += ')';
      }
    }

    const userDescription = `Blocked by card number ${cardNumbersLabel}`;
    const otherDescription = `Blocked by card number ${cardNumbersLabel} for the user ${user.id} ${user.email}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, filteredCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
