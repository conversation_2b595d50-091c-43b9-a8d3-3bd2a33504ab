const moment = require('moment');
const config = require('../../../../config');
const { encrypt } = require('../../../helpers/security');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

const prepareCookie = (key, value, expireDays) => {
  const now = moment().unix();
  const expiresCookie = new Date((now + expireDays * 24 * 3600) * 1000).toUTCString();
  const cookie = `${key}=${value}; Path=/; Expires=${expiresCookie};`;

  return cookie;
};

/**
 * Method add user relations cookie key to the response
 *
 * @param {Object} user - user model
 * @param {Object} request
 * @param {Object} response
 * */
module.exports = async (user, request, response) => {
  if (!user || isAdminOrSupportUser(user)) return;

  const { userRelationsExpireDays, userRelationsKey, userRelationsSalt } = config.cookies;
  // store current user ID only into the cookies
  const cookieValue = encrypt(user.id, userRelationsSalt);

  // set new cookie if not exist blacklistedUserKey cookie with a proper value or update with current userID
  if (!request.cookies || !request.cookies[userRelationsKey] || request.cookies[userRelationsKey] !== cookieValue) {
    const cookie = prepareCookie(userRelationsKey, cookieValue, userRelationsExpireDays);

    response.append('Set-Cookie', cookie);
  }
};
