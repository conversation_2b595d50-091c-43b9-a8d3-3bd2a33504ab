const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');
const addCookieToResponse = require('./addCookieToResponse');
const PaymentBlacklist = require('../../../model/audb/PaymentBlacklist');
const config = require('../../../../config');

/**
 * Method check user IP in payment blacklist.
 * On found blacklisted user we add him and his all IPs to the blacklist and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userIP, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const paymentBlacklistedUserIp = await PaymentBlacklist.findOne({ ip: userIP })
    .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_ip_${userIP}`);

  if (paymentBlacklistedUserIp) {
    const userDescription = `Blocked by blacklisted IP ${userIP}`;
    const otherDescription = `Blocked by blacklisted IP ${userIP} for the user ${user.id} ${user.email}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
