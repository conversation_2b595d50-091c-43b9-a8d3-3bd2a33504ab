const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const getUserIpsInPaymentBlacklist = require('./getUserIpsInPaymentBlacklist');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check user IPs in the payment blacklist.
 * On found blacklisted user we add him and his all IPs to the blacklist and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const userBlacklistedIps = await getUserIpsInPaymentBlacklist(user, userIPs);

  if (userBlacklistedIps.length) {
    const blacklistedIp = userBlacklistedIps[0].ip;
    const userDescription = `Blocked by blacklisted IP ${blacklistedIp}`;
    const otherDescription = `Blocked by blacklisted IP ${blacklistedIp} for the user ${user.id} ${user.email}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
