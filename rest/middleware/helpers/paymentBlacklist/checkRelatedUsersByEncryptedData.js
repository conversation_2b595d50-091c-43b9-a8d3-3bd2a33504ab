const config = require('../../../../config');
const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const getAndAddAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/getAndAddAllUserCardsToPaymentBlacklist');
const getAndAddAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/getAndAddAllUserIpsToPaymentBlacklist');
const getAndAddAllUserFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/getAndAddAllUserFingerprintsToPaymentBlacklist');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');
const addCookieToResponse = require('./addCookieToResponse');
const saveRelatedUsers = require('./saveRelatedUsers');
const PaymentBlacklist = require('../../../model/audb/PaymentBlacklist');
const User = require('../../../model/audb/User');

const addNotBlacklistedUserToPaymentBlacklist = async (user, notBlacklistedUsersIds, userDescription, otherDescription) => {
  if (!user || isAdminOrSupportUser(user)) return;

  const blacklistPromises = [];
  const notBlacklistedUsers = await User.find(
    { id: { $in: notBlacklistedUsersIds } },
    { id: 1, em: 1, cookiekey: 1, storagekey: 1, flashkey: 1 },
  ).lean();

  notBlacklistedUsers.forEach((notBlacklistedUser) => {
    blacklistPromises.push(addUserToPaymentBlacklist(notBlacklistedUser, userDescription));

    // only for related users
    if (notBlacklistedUser.id !== user.id) {
      blacklistPromises.push(getAndAddAllUserIpsToPaymentBlacklist(notBlacklistedUser, otherDescription));
      blacklistPromises.push(getAndAddAllUserFingerprintsToPaymentBlacklist(notBlacklistedUser, otherDescription));
      blacklistPromises.push(getAndAddAllUserCardsToPaymentBlacklist(notBlacklistedUser.id, otherDescription));
    }
  });
  await Promise.all(blacklistPromises);
};

/**
 * Method check user relations and add user and his IPs, cards to the payment blacklist
 * and renew cookie expires date in response
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers, relatedUsersEncrypted) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const relatedUserIDs = await saveRelatedUsers(user, relatedUsersEncrypted);

  if (user.skipBlacklistPaymentCache) return false;

  const promises = [];
  relatedUserIDs.forEach(userId => promises.push(PaymentBlacklist.findOne({ uid: userId })
    .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_id_${userId}`)));
  const promisesResponse = await Promise.all(promises);
  const blacklistedResults = promisesResponse.filter(result => !!result);

  if (blacklistedResults && blacklistedResults.length) {
    const blacklistedUsersIdsEmails = await Promise.all(blacklistedResults.map(async (blacklistedUser) => {
      const userEmail = await User.decryptEmailWithRedis(blacklistedUser.em);

      return `${blacklistedUser.uid} ${userEmail}`;
    }));
    const userDescription = `Blocked by cookie by related blacklisted users ${blacklistedUsersIdsEmails.join(', ')}`;
    const otherDescription = `Blocked by cookie by related blacklisted users ${blacklistedUsersIdsEmails.join(', ')} for the user ${user.id} ${user.email}`;

    const blacklistedUsersIds = blacklistedResults.map(blacklistedUser => blacklistedUser.uid);
    let notBlacklistedUsersIds = relatedUserIDs.filter(relatedUserId => !blacklistedUsersIds.includes(relatedUserId));

    if (notBlacklistedUsersIds.includes(user.id)) {
      // wait for block current user only
      await addNotBlacklistedUserToPaymentBlacklist(user, [user.id], userDescription, otherDescription);
      notBlacklistedUsersIds = notBlacklistedUsersIds.filter(userId => userId !== user.id);
    }
    if (notBlacklistedUsersIds.length) {
      // no need to wait for add to the blacklist
      addNotBlacklistedUserToPaymentBlacklist(user, notBlacklistedUsersIds, userDescription, otherDescription);
    }

    // no need to wait for add to the blacklist ips or id
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);

    // we need to wait to add cookie
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
