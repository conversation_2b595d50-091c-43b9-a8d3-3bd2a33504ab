const config = require('../../../../config');
const addUserToPaymentBlacklist = require('../../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const getUserFingerprintsInPaymentBlacklist = require('./getUserFingerprintsInPaymentBlacklist');
const User = require('../../../model/audb/User');
const UserFingerprintPayment = require('../../../model/audb/UserFingerprintPayment');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check blacklisted user by fingerprint and add user and his fingerprints, IPs to the blacklist
 * and add blacklisted cookie
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!config.paymentBlacklist.fingerprints.enabled) return false;
  if (!user || isAdminOrSupportUser(user)) return false;

  const userBlacklistedFingerprintModels = await getUserFingerprintsInPaymentBlacklist(user, userFingerprints);

  if (userBlacklistedFingerprintModels.length) {
    const userBlacklistedFingerprints = userBlacklistedFingerprintModels.map(model => model.fingerprint);
    let fingerprintsLabel = '';

    // create fingerprintsLabel like: iaepBRayYK3CcFtMT8O3-1 (userId-1,userId-2,userId-3) iaepBRayYK3CcFtMT8O3-2 (userId-4,userId-5)
    for (let i = 0; i < userBlacklistedFingerprints.length; ++i) {
      fingerprintsLabel += ` ${userBlacklistedFingerprints[i]}`;

      const fingerprintUsersModels = await UserFingerprintPayment.aggregate([
        {
          $match: { fingerprint: userBlacklistedFingerprints[i] },
        },
        {
          $project: { uid: 1 },
        },
        {
          $lookup: {
            from: 'tuser',
            localField: 'uid',
            foreignField: 'id',
            as: 'User',
          },
        },
        { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
        {
          $project: { uid: 1, em: '$User.em' },
        },
      ]);

      if (fingerprintUsersModels.length) {
        fingerprintsLabel += ' (';

        for (let f = 0; f < fingerprintUsersModels.length; ++f) {
          if (f !== 0) fingerprintsLabel += ', ';

          fingerprintsLabel += fingerprintUsersModels[f].uid;
          const email = fingerprintUsersModels[f].em ? await User.decryptEmailWithRedis(fingerprintUsersModels[f].em) : '';
          fingerprintsLabel += email ? ` ${email}` : '';
        }

        fingerprintsLabel += ')';
      }
    }

    const userDescription = `Blocked by fingerprint ${fingerprintsLabel}`;
    const otherDescription = `Blocked by fingerprint ${fingerprintsLabel} for the user ${user.id} ${user.email}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, otherDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, otherDescription);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, otherDescription);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
