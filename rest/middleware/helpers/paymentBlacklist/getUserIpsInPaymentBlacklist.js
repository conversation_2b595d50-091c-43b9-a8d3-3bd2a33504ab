const PaymentBlacklist = require('../../../model/audb/PaymentBlacklist');
const config = require('../../../../config');

/**
 * Method load user IPs objects list from payment blacklist
 * */
module.exports = async (user, userIPs) => {
  if (!user || !userIPs || !userIPs.length) return [];

  const userBlacklistedIps = [];

  for (let i = 0; i < userIPs.length; ++i) {
    const blacklistedIpModel = await PaymentBlacklist.findOne({ ip: userIPs[i] }, { ip: 1 })
      .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_ip_${userIPs[i]}`);

    if (blacklistedIpModel) userBlacklistedIps.push(blacklistedIpModel);
  }

  return userBlacklistedIps;
};
