const config = require('../../../../config');
const PaymentBlacklist = require('../../../model/audb/PaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addAllUserCardsToPaymentBlacklist = require('../../../commands/paymentBlacklist/addAllUserCardsToPaymentBlacklist');
const addCookieToResponse = require('./addCookieToResponse');
const isAdminOrSupportUser = require('../../../helpers/isAdminOrSupportUser');

/**
 * Method check blacklisted user by ID.
 * On found blacklisted user we add him and his all IPs to the blacklist and renew cookie expires date
 * */
module.exports = async (request, response, user, userIPs, userFingerprints, userCardNumbers) => {
  if (!user || isAdminOrSupportUser(user)) return false;

  const paymentBlacklistedUserId = await PaymentBlacklist.findOne({ uid: user.id })
    .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_id_${user.id}`);

  if (paymentBlacklistedUserId) {
    const description = `Blocked by blacklisted user ${user.id} ${user.email}`;
    // no need to wait for add to the blacklist ips or id
    addAllUserIpsToPaymentBlacklist(user, userIPs, description);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, description);
    addAllUserCardsToPaymentBlacklist(user, userCardNumbers, description);
    await addCookieToResponse(user, request, response);

    return true;
  }

  return false;
};
