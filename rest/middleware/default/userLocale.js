const getParamFromRequest = require('../../helpers/getParamFromRequest');
const config = require('../../../config');

module.exports = function exports(req, res, next) {
  let locale = getParamFromRequest(req, 'locale');

  // check if current locale supported
  if (!config.i18n.locales.includes(locale)) locale = config.i18n.defaultLocale;

  // set locale to translate from req.__('text')
  req.locale = locale;

  return next();
};
