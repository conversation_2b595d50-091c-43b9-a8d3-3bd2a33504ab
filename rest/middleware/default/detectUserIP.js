const isLocal = require('is-local-ip');
const { OUR_SERVERS_IPS } = require('../../constants/ip');

// function clearIPstring(string) {
//   if (string.substr(0, 7) === '::ffff:') string = string.substr(7);
//
//   return string;
// }

module.exports = function detectUserIP(req, res, next) {
  const userAllIPs = [];

  // for local test added user IP, because some routes might fail without userIP
  if (process.env.NODE_ENV === 'local' && process.env.USER_IP_FOR_LOCAL_TESTS) userAllIPs.push(process.env.USER_IP_FOR_LOCAL_TESTS);

  function addIpIfValid(ip) {
    if (ip
      && !isLocal(ip)
      && !OUR_SERVERS_IPS.has(ip)
      && userAllIPs.indexOf(ip) === -1) {
      userAllIPs.push(ip);
    }
  }

  // ToDo add some secret header with secret value from the php websites, and compare it
  /*
    function isValidatedPhpRequest() {
       return req.headers['x-ip'] && req.headers['x-secret-php-header'] === 'secretValue';
    }
  */
  function isRequestFromPhp(req) {
    return !!req.headers['x-ip'];
  }

  if (isRequestFromPhp(req)) {
    addIpIfValid(req.headers['x-ip']);

    if (req.headers['x-forwarded-for']) {
      const xffUserIps = req.headers['x-forwarded-for'].replace(/\s/g, '').split(',');
      addIpIfValid(xffUserIps[0]);
    }
  } else {
    addIpIfValid(req.headers['cf-connecting-ip']);
  }

  req.userIP = userAllIPs.length > 0 ? userAllIPs[0] : '';
  req.userAllIPs = userAllIPs;

  return next();
};
