/* eslint-disable no-unused-vars */
const { ApiError, HttpError } = require('@s1/api-errors');
const { LOGIN_NEEDED_ERROR, WRONG_SID_ERROR } = require('@s1/api-errors/types');
const log = require('@s1/log').create(__filename);

const logLevels = {
  10: 'trace',
  20: 'debug',
  30: 'info',
  40: 'warn',
  50: 'error',
  60: 'fatal',
};

/**
 * Method check if network errors exists
 *
 * @param {object} error - error object
 * @returns true|false - return true if network error exist
 * */
const checkExistNetworkErrors = (error) => {
  const networkErrorRegex = new RegExp('(connection.+closed)|(connection.+timed.+out)|(read.+ETIMEDOUT)|(read.+ECONNRESET)|(interrupted.+shutdown)|(Connection.+error)');
  const existNetworkError = networkErrorRegex.test(error.stack) || networkErrorRegex.test(error.message);
  // skip specific error codes from log, like mongo error network connection
  const errorCodes = [11600];
  const errorNames = ['MongoNetworkError', 'HttpError'];

  return existNetworkError
    || (error.name && errorNames.includes(error.name))
    || (error.code && errorCodes.includes(error.code));
};

function getErrorResult(error, locale) {
  const logError = { internal_error_code: error.code || error.status };

  if (error.status !== 404) {
    if (error instanceof ApiError) {
      error.changeMessageLocalization('en');
      logError.message = error.message;
      logError.errmsg = error.stack;
      error.changeMessageLocalization(error.locale);
    } else {
      logError.message = error.message;
      logError.errmsg = error.stack;
    }
  }
  // do not log network errors
  if (!checkExistNetworkErrors(error)) log[logLevels[error.logLevel] || 'error'](logError);
  // set 504 status code for network, mongodb, redis errors
  else {
    delete error.stack;
  }
  if (error instanceof ApiError) {
    error.changeMessageLocalization(locale);

    return {
      errorcode: error.code,
      error: error.code,
      errormsg: error.message,
      msg: error.message,
      result: error.message,
      results: error.message,
      ...(error.data ? { ...error.data } : {}),
    };
  }

  return {
    error: true,
    message: error.message,
    result: [],
  };
}

module.exports.notFoundError = async function notFoundError(req, res, next) {
  throw new HttpError('Not found', 404);
};

module.exports.devErrorHandler = function devErrorHandler(err, req, res, next) {
  const result = getErrorResult(err, req.locale);
  result.stack = err.stack ? err.stack.split('\n') : undefined;

  if (err.code === LOGIN_NEEDED_ERROR || err.code === WRONG_SID_ERROR) {
    res.clearCookie('connect.sid');

    const regex = /\b\/login\b/;

    if (!regex.test(req.url)) res.setHeader('Cache-Control', 'max-age=3600, public, s-maxage=3600');
  } else {
    res.set('Cache-Control', 'no-store');
  }
  if (res.hasOwnProperty('cacheControl') && res.cacheControl) delete res.cacheControl;

  res.status(err.status || 500);
  res.json(result);
};

module.exports.prodErrorHandler = function prodErrorHandler(err, req, res, next) {
  const result = getErrorResult(err, req.locale);

  if (res.hasOwnProperty('cacheControl') && res.cacheControl) delete res.cacheControl;
  if (err.code === LOGIN_NEEDED_ERROR || err.code === WRONG_SID_ERROR) {
    const regex = /\b\/login\b/;

    if (!regex.test(req.url)) res.setHeader('Cache-Control', 'max-age=3600, public, s-maxage=3600');
  } else {
    res.set('Cache-Control', 'no-store');
  }

  res.status(err.status || 500);
  res.json(result);
};
