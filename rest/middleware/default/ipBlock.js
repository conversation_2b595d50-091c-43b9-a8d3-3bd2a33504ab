const { IpBlockedError, IspOrCountryError, HttpError } = require('@s1/api-errors');
const isLocal = require('is-local-ip');
const BlacklistCountry = require('../../model/audb/BlacklistCountry');
const Blacklist = require('../../model/audb/Blacklist');
const { countriesFullNames } = require('../../../rest/constants/blacklist');

module.exports = async function exports(request, response, next) {
  const { userIP } = request;

  if (isLocal(userIP)) return next();
  if (request.path === '/alive.php') return next();

  try {
    if (await Blacklist.check(userIP, null)) return next(new IpBlockedError());

    let userCountry = request.userLocation.countryName.toLowerCase();

    if (countriesFullNames[userCountry]) {
      userCountry = countriesFullNames[userCountry];
    }
    if (await BlacklistCountry.check(userCountry)) {
      return next(new IspOrCountryError());
    }

    return next();
  } catch (e) {
    return next(new HttpError(e.message));
  }
};
