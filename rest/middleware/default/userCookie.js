/* eslint-disable import/no-extraneous-dependencies */
const signature = require('cookie-signature');
const cookie = require('cookie');
const isValidSidValue = require('../helpers/isValidSidValue');

module.exports = function exports(req, res, next) {
  if (!req.query.sid) return next();
  if (Array.isArray(req.query.sid)) req.query.sid = req.query.sid[0];
  if (!isValidSidValue(req.query.sid)) return next();

  const cookieSignature = `s:${signature.sign(req.query.sid, 'secret')}`;
  req.headers.cookie = cookie.serialize('connect.sid', cookieSignature, 'secret');
  res.cookie('connect.sid', cookieSignature);

  return next();
};
