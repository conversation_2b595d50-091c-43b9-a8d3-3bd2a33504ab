module.exports = function detectClientHeaders(req, res, next) {
  const ipHeaders = {};

  if (req.headers['x-site']) {
    ipHeaders.hostname = req.headers['x-site'];
  }
  if (req.headers['x-ip']) {
    ipHeaders.x_ip = req.headers['x-ip'];
  }
  if (req.headers['x-php-h']) {
    ipHeaders.from_php = req.headers['x-php-h'];
  }
  if (req.headers['x-real-ip']) {
    ipHeaders.x_real_ip = req.headers['x-real-ip'];
  }
  if (req.headers['cf-connecting-ip']) {
    ipHeaders.cf = req.headers['cf-connecting-ip'];
  }
  if (req.headers['x-cdn-user-ip']) {
    ipHeaders.cdn_usr_ip = req.headers['x-cdn-user-ip'];
  }
  if (req.headers['x-forwarded-for']) {
    ipHeaders.xff = req.headers['x-forwarded-for'];
  }
  if (req.headers['client-ip']) {
    ipHeaders.clientip = req.headers['client-ip'];
  }

  req.clIpHeaders = ipHeaders;

  return next();
};
