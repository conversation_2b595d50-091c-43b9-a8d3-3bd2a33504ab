const signature = require('cookie-signature');
const Session = require('../../model/audb/Session');
const isValidSidValue = require('../helpers/isValidSidValue');

module.exports = async function exports(req, res, next) {
  const cookies = {};
  // eslint-disable-next-line no-unused-expressions
  req.headers && req.headers.cookie && req.headers.cookie.split(';').forEach((cookie) => {
    const parts = cookie.match(/(.*?)=(.*)$/);
    cookies[parts[1].trim()] = (parts[2] || '').trim();
  });

  if (!cookies['connect.sid']) return next();

  const cookieSignature = decodeURIComponent(cookies['connect.sid']).slice(2, cookies['connect.sid'].length - 2);
  const sid = signature.unsign(cookieSignature, 'secret');

  if (!isValidSidValue(sid)) return next();

  const result = await Session.findOne({ __sid: sid });

  if (!result || !result.data) {
    try {
      delete res.cookies['connect.sid'];
    } catch (e) {}
    try {
      delete req.cookies['connect.sid'];
    } catch (e) {}
    delete req.query.sid;
    delete req.headers.cookie;
  }

  return next();
};
