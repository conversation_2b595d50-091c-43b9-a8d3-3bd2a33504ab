const config = require('../../../config');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const getUserFingerprintsPayment = require('../../commands/user/getUserFingerprintsPayment');

module.exports = async (user, description = '') => {
  if (!config.paymentBlacklist.fingerprints.enabled) return;

  const userFingerprintModels = await getUserFingerprintsPayment(user.id);
  const userFingerprints = userFingerprintModels.map(model => model.fingerprint);

  await addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, description);
};
