const { WrongParamsError, ApiError } = require('@s1/api-errors');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
const { ipReviewStatuses, ipReviewStatusesList, creationMethods } = require('../../constants/paymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const blockUsersByIp = require('./blockUsersByIp');

module.exports = async (data, user) => {
  if (!data || !data.id || !data.status) throw new WrongParamsError();
  if (!ipReviewStatusesList.includes(data.status)) throw new ApiError(903, 'Invalid status');

  const paymentBlacklistIpReview = await PaymentBlacklistIpReview.findOne({ _id: data.id }).exec();

  if (!paymentBlacklistIpReview) throw new ApiError(903, 'Payment blacklist IP for review not found');
  if (user) {
    paymentBlacklistIpReview.modifiedByUid = user.id;
    paymentBlacklistIpReview.modifiedByName = user.name;
  }

  switch (data.status) {
    case ipReviewStatuses.block:
    case ipReviewStatuses.ignore:
      // do not remove ignored and blocked IPs for review
      if (paymentBlacklistIpReview.createdForTtlIndex) delete paymentBlacklistIpReview.createdForTtlIndex;

      break;
    default:
      paymentBlacklistIpReview.createdForTtlIndex = new Date();
      break;
  }

  paymentBlacklistIpReview.status = data.status;
  await paymentBlacklistIpReview.save();

  let paymentBlacklist;
  const ip = paymentBlacklistIpReview.ip || paymentBlacklistIpReview._doc.ip;

  switch (data.status) {
    case ipReviewStatuses.block:
      paymentBlacklist = new PaymentBlacklist();
      paymentBlacklist.ip = ip;
      paymentBlacklist.description = paymentBlacklistIpReview.description;
      paymentBlacklist.creationMethod = creationMethods.automatic;

      if (user) {
        paymentBlacklist.addedByUid = user.id;
        paymentBlacklist.addedByName = user.name;
      }

      try {
        await removeRedisCacheByKeys([`paymentBlacklist_ip_${ip}`]);
        await paymentBlacklist.save();
        await blockUsersByIp(ip);
      } catch (e) {
        // already blocked by other rules (from manual mode)
        // need to clear value, not need to update on admin UI
        paymentBlacklist = null;
      }
      break;
    case ipReviewStatuses.unblock:
      paymentBlacklist = await PaymentBlacklist.findOne({ ip }).exec();

      if (paymentBlacklist) {
        await removeRedisCacheByKeys([`paymentBlacklist_ip_${ip}`]);
        await paymentBlacklist.remove();
      }

      break;
    default:
      break;
  }

  return {
    error: 0,
    paymentBlacklistIpReview: { ...paymentBlacklistIpReview.toObject() },
    paymentBlacklist: paymentBlacklist ? { ...paymentBlacklist.toObject(), type: ip } : null,
  };
};
