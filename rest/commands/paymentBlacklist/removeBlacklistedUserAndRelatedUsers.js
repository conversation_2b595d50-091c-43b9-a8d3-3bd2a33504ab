const getRelatedUsersIDsToCurrentBlacklistedUser = require('./getRelatedUsersIDsToCurrentBlacklistedUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ipReviewStatuses } = require('../../constants/paymentBlacklist');

/**
 * Remove all user fingerprints from the blacklist.
 * Clean cache keys for userFingerprintPayment_id and userFingerprintPayment_blacklist_fingerprint.
 * */
const removeUserBlacklistedFingerprints = async (userId) => {
  // remove all user fingerprints from the blacklist
  const userFingerprintsCacheKeys = [];
  const userFingerprintsUpdate = await UserFingerprintPayment.find({ uid: userId, isBlacklisted: true }, { fingerprint: 1 }).lean();
  const userBlacklistedFingerprints = [];
  userFingerprintsUpdate.forEach((model) => {
    userFingerprintsCacheKeys.push(`userFingerprintPayment_blacklist_fingerprint_${model.fingerprint}`);
    userBlacklistedFingerprints.push(model.fingerprint);
  });

  if (userBlacklistedFingerprints.length) {
    // update fingerprints for all users
    await UserFingerprintPayment.updateMany(
      { fingerprint: { $in: userBlacklistedFingerprints } },
      { $set: { isBlacklisted: false, blacklistDescription: '' } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userFingerprintPayment_id_${userId}`, ...userFingerprintsCacheKeys]);
  }
};

/**
 * Remove all user cards from the blacklist.
 * Clean cache keys for userCard_uid and userCard_blacklist_cardNumber.
 * */
const removeUserBlacklistedCards = async (userId) => {
  // remove all user card numbers from the blacklist
  const userCardsCacheKeys = [];
  const userCardsForUpdate = await UserCard.find({ uid: userId, isBlacklisted: true }, { number: 1 }).lean();
  const userBlacklistedCards = [];
  userCardsForUpdate.forEach((model) => {
    userCardsCacheKeys.push(`userCard_blacklist_cardNumber_${model.number}`);
    userBlacklistedCards.push(model.number);
  });

  if (userBlacklistedCards.length) {
    // update cards for all users
    await UserCard.updateMany(
      { number: { $in: userBlacklistedCards } },
      { $set: { isBlacklisted: false, blacklistDescription: '' } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Get all users IPS
 * */
const getAllUsersIPs = async (usersIds) => {
  const userIPModels = await UserIp.find({ uid: { $in: usersIds } }).lean();
  const usersIPs = userIPModels.map(model => model.ip);

  return usersIPs;
};

/**
 * Remove all ips from the blacklist and unblock from the IPs from review list.
 * Clean cache keys for paymentBlacklist ips and paymentBlacklistIpReview.
 * */
const removeBlacklistedIPs = async (ips) => {
  // remove all user card numbers from the blacklist
  const userIPsCacheKeys = [];
  ips.forEach((ip) => {
    userIPsCacheKeys.push(`paymentBlacklist_ip_${ip}`);
    userIPsCacheKeys.push(`paymentBlacklistIpReview_ip_${ip}`);
  });

  if (ips.length) {
    const dateNow = new Date();
    await PaymentBlacklist.deleteMany({ ip: { $in: ips } }).exec();
    // for removed blacklisted IPs we need to unlock them from the review list
    await PaymentBlacklistIpReview.updateMany(
      { ip: { $in: ips } },
      { $set: { status: ipReviewStatuses.unblock, createdForTtlIndex: dateNow } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys(userIPsCacheKeys);
  }
};

/**
 * Add admin comment to the user on remove him from the blacklist
 * */
const addUserAdminCommentOnRemoveFromBlacklist = async (userId, adminUser) => {
  const comment = `User removed from the blacklist by ${adminUser.name} (${adminUser.id})`;
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async (currentUserId, adminUser) => {
  const usersIds = await getRelatedUsersIDsToCurrentBlacklistedUser(currentUserId);
  const redisKeys = [`paymentBlacklist_id_${currentUserId}`];

  for (let i = 0; i < usersIds.length; ++i) {
    const userId = usersIds[i];
    await removeUserBlacklistedFingerprints(userId);
    await removeUserBlacklistedCards(userId);
    await addUserAdminCommentOnRemoveFromBlacklist(userId, adminUser);
    redisKeys.push(`paymentBlacklist_id_${userId}`);
  }

  await removeRedisCacheByKeys(redisKeys);
  const allUsersIPs = await getAllUsersIPs(usersIds);
  await removeBlacklistedIPs(allUsersIPs);
  await PaymentBlacklist.deleteMany({ uid: { $in: usersIds } }).exec();
};
