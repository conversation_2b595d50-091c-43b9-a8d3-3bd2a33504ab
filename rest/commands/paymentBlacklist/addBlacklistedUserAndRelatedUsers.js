const config = require('../../../config');
const getRelatedUsersToCurrentBlacklistedUser = require('./getRelatedUsersToCurrentBlacklistedUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const User = require('../../model/audb/User');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAndAddAllUserIpsToPaymentBlacklist = require('../../commands/paymentBlacklist/getAndAddAllUserIpsToPaymentBlacklist');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const { creationMethods, ignoredCardNumbersEncryptedList } = require('../../constants/paymentBlacklist');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

/**
 * Add user to the blacklist
 * Clean cache keys for paymentBlacklist_id_${userId}
 * */
const addUserToBlacklist = async (userId, description = '') => {
  try {
    const paymentBlacklist = new PaymentBlacklist();
    paymentBlacklist.creationMethod = creationMethods.automatic;
    paymentBlacklist.description = description;
    paymentBlacklist.uid = userId;
    await removeRedisCacheByKeys([`paymentBlacklist_id_${userId}`]);
    await paymentBlacklist.save();
  } catch (e) {
    console.error(`Cannot add user# ${userId} to the payment blacklist`, e);
  }
};

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

/**
 * Add all user fingerprints from the blacklist.
 * Clean cache keys for userFingerprintPayment_id and userFingerprintPayment_blacklist_fingerprint.
 * */
const addUserFingerprintsToBlacklist = async (userId, description = '') => {
  if (!config.paymentBlacklist.fingerprints.enabled) return;

  const userFingerprintsCacheKeys = [];
  const userFingerprintsUpdate = await UserFingerprintPayment.find({ uid: userId, isBlacklisted: false }, { fingerprint: 1, userAgent: 1 }).lean();

  // filter ignored and whitelisted fingerprints
  const whitelistedFingerprints = await getWhitelistedFingerprints();
  const ignoredUseragents = await getIgnoredUseragents();
  const filteredUserFingerprintsUpdate = userFingerprintsUpdate
    .filter(model => !whitelistedFingerprints.includes(model.fingerprint))
    .filter(model => !ignoredUseragents.includes(model.userAgent));

  const userFingerprints = [];
  filteredUserFingerprintsUpdate.forEach((model) => {
    userFingerprintsCacheKeys.push(`userFingerprintPayment_blacklist_fingerprint_${model.fingerprint}`);
    userFingerprints.push(model.fingerprint);
  });

  if (userFingerprints.length) {
    const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
    // update fingerprints for all users
    await UserFingerprintPayment.updateMany(
      { fingerprint: { $in: userFingerprints }, uid: { $nin: adminsAndSupportsIDs } },
      { $set: { isBlacklisted: true, blacklistDescription: description } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userFingerprintPayment_id_${userId}`, ...userFingerprintsCacheKeys]);
  }
};

/**
 * Add all user cards to the blacklist.
 * Clean cache keys for userCard_uid and userCard_blacklist_cardNumber.
 * */
const addUserCardsToBlacklist = async (userId, description) => {
  const userCardsCacheKeys = [];
  const userCardsForUpdate = await UserCard.find({ uid: userId, isBlacklisted: false }, { number: 1 }).lean();
  let filteredCards = process.env.NODE_ENV === 'production' ? userCardsForUpdate.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCardsForUpdate;
  const whitelistedCards = await getWhitelistCardNumbers();
  filteredCards = filteredCards.filter(card => !whitelistedCards.includes(card.number));

  const userCards = [];
  filteredCards.forEach((model) => {
    userCardsCacheKeys.push(`userCard_blacklist_cardNumber_${model.number}`);
    userCards.push(model.number);
  });

  if (userCards.length) {
    await UserCard.updateMany(
      { uid: userId, number: { $in: userCards } },
      { $set: { isBlacklisted: true, blacklistDescription: description } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Add admin comment to the user on add him to the blacklist
 * */
const addUserAdminCommentOnAddToBlacklist = async (userId, comment) => {
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async (currentUserId, adminUser, description = '') => {
  const currentUser = await User.findOne({ id: currentUserId }).lean();

  // for admin or support do not need to check is blacklisted or block him
  if (!currentUser || isAdminOrSupportUser(currentUser)) return;

  const currentUserEmail = await User.decryptEmailWithRedis(currentUser.em);
  const reason = description ? ` by reason: ${description}` : '';
  const mainUserDescription = `User ${currentUser.id} ${currentUserEmail} manually added to the blacklist by ${adminUser.name} (${adminUser.id})${reason}`;
  const users = await getRelatedUsersToCurrentBlacklistedUser(currentUser, mainUserDescription);

  for (let i = 0; i < users.length; ++i) {
    const user = users[i].user;
    const userDescription = users[i].description;

    if (!user || isAdminOrSupportUser(user)) continue;
    if (currentUserId !== user.id) {
      await addUserToBlacklist(user.id, userDescription);
      await addUserFingerprintsToBlacklist(user.id, userDescription);
      await addUserCardsToBlacklist(user.id, userDescription);
      await addUserAdminCommentOnAddToBlacklist(user.id, userDescription);
      await getAndAddAllUserIpsToPaymentBlacklist(user, userDescription);
    }
  }

  await addUserFingerprintsToBlacklist(currentUserId, mainUserDescription);
  await addUserCardsToBlacklist(currentUserId, mainUserDescription);
  await addUserAdminCommentOnAddToBlacklist(currentUserId, mainUserDescription);
  await getAndAddAllUserIpsToPaymentBlacklist(currentUser, mainUserDescription);
};
