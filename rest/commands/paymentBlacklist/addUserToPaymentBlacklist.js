const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const User = require('../../model/audb/User');
const config = require('../../../config');
const { creationMethods } = require('../../constants/paymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

const addUserToPaymentBlacklist = async (user, description = '') => {
  if (!user || isAdminOrSupportUser(user)) return;

  let paymentBlacklistedUser = await PaymentBlacklist.findOne({ uid: user.id })
    .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_id_${user.id}`);

  if (!paymentBlacklistedUser) {
    paymentBlacklistedUser = new PaymentBlacklist({
      uid: user.id,
      creationMethod: creationMethods.automatic,
      description,
    });
    await removeRedisCacheByKeys([`paymentBlacklist_id_${user.id}`]);
    await paymentBlacklistedUser.save();

    try {
      const users = await User.getRelatedUsersUsedTheSameDevice(user);
      const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
      const filteredUsers = users ? users.filter(user => !adminsAndSupportsIDs.includes(user.id)) : [];

      if (filteredUsers && filteredUsers.length) {
        const userEmail = await User.decryptEmailWithRedis(user.em);
        const relatedUserDescription = `Blocked by registered keys data for the user ${user.id} ${userEmail} <-- ${description}`;
        filteredUsers.forEach(relatedUser => addUserToPaymentBlacklist(relatedUser, relatedUserDescription));
      }
    } catch (e) {
      console.error(`Cannot get related users used the same device for the user ${user.id}`, e);
    }
  }
};

module.exports = addUserToPaymentBlacklist;
