const { ApiError } = require('@s1/api-errors');
const _ = require('lodash');
const User = require('../../model/audb/User');
const UserIp = require('../../model/audb/UserIp');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const getRelatedUsersIDsToCurrentBlacklistedUser = require('./getRelatedUsersIDsToCurrentBlacklistedUser');
const { paymentBlacklistTypes } = require('../../constants/paymentBlacklist');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const { getUserLocation } = require('../../service/maxmind');
const getUserWatchingScore = require('../history/getUserWatchingScore');

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentBlacklistedUser(userId);
  const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });
  const users = await PaymentBlacklist.aggregate([
    { $match: { uid: { $in: usersIds } } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$uid',
        uid: { $first: '$uid' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$addedByName' },
        addedByUid: { $first: '$addedByUid' },
        em: { $first: '$User.em' },
        regtime: { $first: '$User.regtime' },
        expires: { $first: '$User.expires' },
        packageId: { $first: '$User.package' },
      },
    },
    {
      $project: { _id: '$id', uid: 1, creationMethod: 1, description: 1, updated: 1, created: 1, addedByName: 1, addedByUid: 1, em: 1, regtime: 1, expires: 1, packageId: 1, type: paymentBlacklistTypes.uid },
    },
    { $sort: { created: -1 } },
  ])
    .then(blacklistedUsers => Promise.all(blacklistedUsers.map(async (blacklistedUser) => {
      blacklistedUser.email = blacklistedUser.em ? await User.decryptEmailWithRedis(blacklistedUser.em) : '';
      blacklistedUser.paidTypes = await getUserPaidTypes(blacklistedUser.uid);
      blacklistedUser.watchingScore = await getUserWatchingScore(blacklistedUser.uid);
      blacklistedUser.package = packagesObject.hasOwnProperty(blacklistedUser.packageId) ? packagesObject[blacklistedUser.packageId] : null;
      blacklistedUser.lastPayments = await PaymentLog.find({
        uid: blacklistedUser.uid,
        amount: { $gt: 0 },
        pptype: { $ne: 'manual' },
      }, { pptype: 1, package: 1 }).sort({ _id: -1 }).limit(3).lean()
        .cache(300)
        .then(logs => (_.map(logs, (log) => {
          if (packagesObject.hasOwnProperty(log.package)) {
            const { id, ...pack } = packagesObject[log.package];
            log = Object.assign(log, pack);
          }

          delete log._id;

          return log;
        })));

      const userIPModel = await UserIp.findOne({ uid: blacklistedUser.uid }).lean();

      if (userIPModel) {
        const { countryName, countryCode, stateCode, ISP } = getUserLocation(userIPModel.ip);
        blacklistedUser.ipInfo = { ip: userIPModel.ip, countryName, countryCode, stateCode, ISP };
      } else {
        blacklistedUser.ipInfo = null;
      }

      return blacklistedUser;
    })));

  return {
    error: 0,
    users,
  };
};
