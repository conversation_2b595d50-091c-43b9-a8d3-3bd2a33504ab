const { ApiError } = require('@s1/api-errors');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserIp = require('../../model/audb/UserIp');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ipReviewStatuses } = require('../../constants/paymentBlacklist');

/**
 * Get all users IPS
 * */
const getAllUsersIPs = async (usersIds) => {
  const userIPModels = await UserIp.find({ uid: { $in: usersIds } }).lean();
  const usersIPs = userIPModels.map(model => model.ip);

  return usersIPs;
};

/**
 * Remove all ips from the blacklist and unblock from the IPs from review list.
 * Clean cache keys for paymentBlacklist ips and paymentBlacklistIpReview.
 * */
const removeBlacklistedIPs = async (ips) => {
  // remove all user IPs from the blacklist
  if (ips.length) {
    const dateNow = new Date();
    await PaymentBlacklist.deleteMany({ ip: { $in: ips } }).exec();
    // for removed blacklisted IPs we need to unlock them from the review list
    await PaymentBlacklistIpReview.updateMany(
      { ip: { $in: ips } },
      { $set: { status: ipReviewStatuses.unblock, createdForTtlIndex: dateNow } },
      { upsert: false },
    ).exec();

    const userIPsCacheKeys = [];
    ips.forEach((ip) => {
      userIPsCacheKeys.push(`paymentBlacklist_ip_${ip}`);
      userIPsCacheKeys.push(`paymentBlacklistIpReview_ip_${ip}`);
    });
    await removeRedisCacheByKeys(userIPsCacheKeys);
  }
};

/**
 * Add admin comment to the user on remove him from the blacklist
 * */
const addUserAdminCommentOnRemoveFromBlacklist = async (userId, adminUser) => {
  const comment = `User removed from the blacklist by ${adminUser.name} (${adminUser.id})`;
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async ({ uids, adminUser }) => {
  if (!uids) throw new ApiError(903, 'uids are required');
  if (typeof uids === 'string' || typeof uids === 'number') uids= [uids];

  await PaymentBlacklist.deleteMany({ uid: { $in: uids } }).exec();
  await UserFingerprintPayment.updateMany(
    { uid: { $in: uids }, isBlacklisted: true },
    { $set: { isBlacklisted: false, blacklistDescription: '' } },
    { upsert: false },
  ).exec();
  await UserCard.updateMany(
    { uid: { $in: uids }, isBlacklisted: true },
    { $set: { isBlacklisted: false, blacklistDescription: '' } },
    { upsert: false },
  ).exec();
  const allUsersIPs = await getAllUsersIPs(uids);
  await removeBlacklistedIPs(allUsersIPs);

  for (let i = 0; i < uids.length; ++i) {
    await addUserAdminCommentOnRemoveFromBlacklist(uids[i], adminUser);
  }

  const redisKeys = ['paymentBlacklist', 'userFingerprintPayment', 'userCard'];
  await removeRedisCacheByKeys(redisKeys);

  return {
    error: 0,
    success: true,
  };
};
