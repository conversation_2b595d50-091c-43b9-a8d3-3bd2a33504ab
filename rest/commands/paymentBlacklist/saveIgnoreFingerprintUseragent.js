const { WrongParamsError, ApiError } = require('@s1/api-errors');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.userAgent) throw new ApiError(903, 'userAgent is required');

  let paymentIgnoreFingerprintUseragent = await PaymentIgnoreFingerprintUseragent.findOne({ userAgent: data.userAgent }).exec();

  if (!paymentIgnoreFingerprintUseragent) {
    paymentIgnoreFingerprintUseragent = new PaymentIgnoreFingerprintUseragent();
    paymentIgnoreFingerprintUseragent.userAgent = data.userAgent;
    paymentIgnoreFingerprintUseragent.addedByUid = admin.id;
    paymentIgnoreFingerprintUseragent.addedByName = admin.name;

    await paymentIgnoreFingerprintUseragent.save();
    await removeRedisCacheByKeys(['paymentIgnoreFingerprintUseragent']);
    await removeRedisCacheByKeys(['userFingerprintPayment_blacklist_fingerprint']);
    await removeRedisCacheByKeys(['userFingerprintPayment_stealer_fingerprint']);
  }

  await UserFingerprintPayment.updateMany({ userAgent: data.userAgent }, { $set: { isBlacklisted: false, isStealer: false } }, { upsert: false }).exec();
  await removeRedisCacheByKeys(['userFingerprintPayment_blacklist_fingerprint']);
  await removeRedisCacheByKeys(['userFingerprintPayment_stealer_fingerprint']);

  return {
    error: 0,
    ignoredUseragent: paymentIgnoreFingerprintUseragent,
  };
};
