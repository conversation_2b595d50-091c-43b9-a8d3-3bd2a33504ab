const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const User = require('../../model/audb/User');
const getUserRelatedUsersIds = require('../../commands/payment/getUserRelatedUsersIds');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const { ignoredCardNumbersEncryptedList } = require('../../constants/paymentBlacklist');
const config = require('../../../config');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

const getUserFingerprintModels = async (userId) => {
  const userFingerprintModels = await UserFingerprintPayment.find({ uid: userId }).lean();

  return userFingerprintModels;
};

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

const getUserCards = async (userId) => {
  const userCardModels = await UserCard.find({ uid: userId }).lean();

  return userCardModels;
};

const getUserIPs = async (userId) => {
  const userIPModels = await UserIp.find({ uid: userId }).lean();
  const userIPs = userIPModels.map(model => model.ip);

  return userIPs;
};

const filterUserIPsInBlacklist = async (userIPs) => {
  if (!userIPs || !userIPs.length) return [];

  const blacklistedIPModels = await PaymentBlacklist.find({ ip: { $exists: true } }).cache(config.paymentBlacklist.cache, 'paymentBlacklist_ip').lean();
  const blacklistedIPs = blacklistedIPModels.map(model => model.ip);

  const userBlacklistedIPs = userIPs.filter(ip => blacklistedIPs.includes(ip));

  return userBlacklistedIPs;
};

const getUsersByFingerprint = async (fingerprint, adminsAndSupportsIDs) => {
  const fingerprintModels = await UserFingerprintPayment.find({ fingerprint, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = fingerprintModels.map(model => model.uid);

  return usersIds;
};

const getUsersByCard = async (card, adminsAndSupportsIDs) => {
  const cardModels = await UserCard.find({ number: card, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = cardModels.map(model => model.uid);

  return usersIds;
};

const getUsersByIP = async (ip, adminsAndSupportsIDs) => {
  const userIPsModels = await UserIp.find({ ip, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = userIPsModels.map(model => model.uid);

  return usersIds;
};

const searchRelatedUsers = async (user, description, adminsAndSupportsIDs) => {
  const userId = user.id;
  const userEmail = await User.decryptEmailWithRedis(user.em);
  // get user fingerprints, cards, IPs
  const userFingerprintModels = await getUserFingerprintModels(userId);
  const whitelistedFingerprints = await getWhitelistedFingerprints();
  const ignoredUseragents = await getIgnoredUseragents();

  const userBlacklistedFingerprints = userFingerprintModels
    .filter(model => !whitelistedFingerprints.includes(model.fingerprint))
    .filter(model => !ignoredUseragents.includes(model.userAgent))
    .map(model => model.fingerprint);

  const [userCards, whitelistedCards] = await Promise.all([
    getUserCards(userId),
    getWhitelistCardNumbers(),
  ]);
  // do not filter userCardNumbers for local and dev tests
  let filteredCards = process.env.NODE_ENV === 'production' ? userCards.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCards;
  filteredCards = filteredCards.filter(card => !whitelistedCards.includes(card.number));
  const userIPs = await getUserIPs(userId);
  const userBlacklistedIPs = await filterUserIPsInBlacklist(userIPs);

  const foundUsers = [];
  // get usersIds by blacklisted fingerprints, cards, IPs

  if (config.paymentBlacklist.fingerprints.enabled) {
    const userFingerprintsPromises = [];
    userBlacklistedFingerprints.forEach((userFingerprint) => {
      userFingerprintsPromises.push((async () => {
        const usersIDsByFingerprints = await getUsersByFingerprint(userFingerprint, adminsAndSupportsIDs);

        if (usersIDsByFingerprints && usersIDsByFingerprints.length) {
          const fingerprintUsers = await User.find({ id: { $in: usersIDsByFingerprints } }).lean();
          await Promise.all(fingerprintUsers.map(async (fingerprintUser) => {
            const cardUserEmail = await User.decryptEmailWithRedis(fingerprintUser.em);
            const userBlockedDescription = `User ${fingerprintUser.id} ${cardUserEmail} blocked by fingerprint ${userFingerprint} from ${userId} ${userEmail} <-- ${description}`;
            foundUsers.push({ user: fingerprintUser, description: userBlockedDescription });
          }));
        }
      })());
    });
    await Promise.all(userFingerprintsPromises);
  }

  const userCardsPromises = [];
  filteredCards.forEach((userCard) => {
    userCardsPromises.push((async () => {
      const blacklistedUsersIDsByCard = await getUsersByCard(userCard.number, adminsAndSupportsIDs);

      if (blacklistedUsersIDsByCard && blacklistedUsersIDsByCard.length) {
        const cardUsers = await User.find({ id: { $in: blacklistedUsersIDsByCard } }).lean();
        await Promise.all(cardUsers.map(async (cardUser) => {
          const cardUserEmail = await User.decryptEmailWithRedis(cardUser.em);
          const userBlockedDescription = `User ${cardUser.id} ${cardUserEmail} blocked by card number ${userCard.first6}******${userCard.last4} (${userCard.number}) from ${userId} ${userEmail} <-- ${description}`;
          foundUsers.push({ user: cardUser, description: userBlockedDescription });
        }));
      }
    })());
  });
  await Promise.all(userCardsPromises);

  const userIPsPromises = [];
  userBlacklistedIPs.forEach((userIP) => {
    userIPsPromises.push((async () => {
      const blacklistedUsersIDsByIP = await getUsersByIP(userIP, adminsAndSupportsIDs);

      if (blacklistedUsersIDsByIP && blacklistedUsersIDsByIP.length) {
        const ipUsers = await User.find({ id: { $in: blacklistedUsersIDsByIP } }).lean();
        await Promise.all(ipUsers.map(async (ipUser) => {
          const ipUserEmail = await User.decryptEmailWithRedis(ipUser.em);
          const userBlockedDescription = `User ${ipUser.id} ${ipUserEmail} blocked by IP ${userIP} <-- ${description}`;
          foundUsers.push({ user: ipUser, description: userBlockedDescription });
        }));
      }
    })());
  });
  await Promise.all(userIPsPromises);

  const userRelatedUsersIDs = await getUserRelatedUsersIds(userId, adminsAndSupportsIDs);

  if (userRelatedUsersIDs.length) {
    const userRelatedUsers = await User.find({ id: { $in: userRelatedUsersIDs } }).lean();
    await Promise.all(userRelatedUsers.map(async (relatedUser) => {
      const relatedUserEmail = await User.decryptEmailWithRedis(relatedUser.em);
      const userBlockedDescription = `User ${relatedUser.id} ${relatedUserEmail} related to the ${userId} ${userEmail} <-- ${description}`;
      foundUsers.push({ user: relatedUser, description: userBlockedDescription });
    }));
  }

  // load related users by registered keys (the same devices)
  try {
    const users = await User.getRelatedUsersUsedTheSameDevice(user);

    if (users && users.length) {
      await Promise.all(users.map(async (relatedUser) => {
        const relatedUserEmail = await User.decryptEmailWithRedis(relatedUser.em);
        const userBlockedDescription = `User ${relatedUser.id} ${relatedUserEmail} related to the ${userId} ${userEmail} by registered device <-- ${description}`;
        foundUsers.push({ user: relatedUser, description: userBlockedDescription });
      }));
    }
  } catch (e) {
    console.error(`Cannot get related users used the same device for the user ${userId}`, e);
  }

  return foundUsers;
};

const getRelatedUsersToCurrentUser = async (user, allUsersIDs, allUsers, description, adminsAndSupportsIDs) => {
  // for admin or support do not need to check is blacklisted or block him
  if (!user || isAdminOrSupportUser(user)) return allUsers;
  if (!adminsAndSupportsIDs || !adminsAndSupportsIDs.length) adminsAndSupportsIDs = await getAdminsAndSupportsIDs();

  const foundUsers = await searchRelatedUsers(user, description, adminsAndSupportsIDs);
  const filteredUsers = foundUsers.filter(user => !adminsAndSupportsIDs.includes(user.id));

  if (filteredUsers.length) {
    const uniqueUsers = [];
    filteredUsers.forEach((foundUser) => {
      if (!allUsersIDs.includes(foundUser.user.id)) {
        uniqueUsers.push(foundUser);
        allUsers.push(foundUser);
        allUsersIDs.push(foundUser.user.id);
      }
    });

    if (uniqueUsers.length) {
      for (let i = 0; i < uniqueUsers.length; ++i) {
        const uniqueUser = uniqueUsers[i].user;
        const uniqueUserDescription = uniqueUsers[i].description;
        const foundUsersByUser = await getRelatedUsersToCurrentUser(uniqueUser, allUsersIDs, allUsers, uniqueUserDescription, adminsAndSupportsIDs);
        const filteredFoundUsersByUser = foundUsersByUser.filter(user => !adminsAndSupportsIDs.includes(user.id));

        if (filteredFoundUsersByUser.length) {
          filteredFoundUsersByUser.forEach((foundUser) => {
            if (!allUsersIDs.includes(foundUser.user.id)) {
              allUsers.push(foundUser);
              allUsersIDs.push(foundUser.user.id);
            }
          });
        }
      }
    }
  }

  return allUsers;
};

/**
 *  @param {User} user  - user model
 *  @param {string} description
 * */
module.exports = async (user, description = '') => {
  // initially put current user ID to the all users list
  const allUsersWithDescription = await getRelatedUsersToCurrentUser(user, [user.id], [{ user, description }], description);

  return allUsersWithDescription || [{ user, description }];
};
