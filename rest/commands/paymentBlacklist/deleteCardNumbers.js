const { ApiError } = require('@s1/api-errors');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ number }) => {
  if (!number) throw new ApiError(903, 'number is required');

  const blacklistedCardNumbers = await UserCard.find({ number, isBlacklisted: true }, { uid: 1 }).lean();

  if (blacklistedCardNumbers.length) {
    const removeResult = await UserCard.updateMany(
      { number, isBlacklisted: true },
      { $set: { isBlacklisted: false }, $unset: { blacklistDescription: 1 } },
      { upsert: false },
    ).exec();

    if (removeResult) {
      // on success remove all cache for blacklisted card numbers
      const cacheKeyForRemove = [];
      blacklistedCardNumbers.forEach(model => cacheKeyForRemove.push(`userCard_uid_${model.uid}`));
      cacheKeyForRemove.push(`userCard_blacklist_cardNumber_${number}`);
      await removeRedisCacheByKeys(cacheKeyForRemove);
    }

    return {
      error: 0,
      success: !!removeResult,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
