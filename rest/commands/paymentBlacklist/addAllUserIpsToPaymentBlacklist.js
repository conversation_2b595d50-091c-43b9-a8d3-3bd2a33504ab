const isLocal = require('is-local-ip');
const moment = require('moment');
const log = require('@s1/log').create(__filename);
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
// const { creationMethods } = require('../../constants/paymentBlacklist');
const { OUR_SERVERS_IPS, PAYMENT_WHITELISTED_IPS } = require('../../constants/ip');
const isCellularIp = require('../../helpers/proxy/isCellularIp');
const isAppleRelay = require('../../helpers/proxy/isAppleRelay');
const isProxy = require('../../helpers/proxy/isProxy');
const getUserIpsInPaymentBlacklist = require('../../middleware/helpers/paymentBlacklist/getUserIpsInPaymentBlacklist');
const getUserIpsInPaymentReview = require('../../middleware/helpers/paymentBlacklist/getUserIpsInPaymentReview');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

module.exports = async (user, userIPs, description = '') => {
  if (!user || isAdminOrSupportUser(user)) return;

  // limit only last 15 IPs
  const lastUserIPs = userIPs.length > 15 ? userIPs.slice(0, 15) : userIPs;
  const userBlacklistedIpModels = await getUserIpsInPaymentBlacklist(user, lastUserIPs);
  const userBlacklistedIps = userBlacklistedIpModels.map(ipModel => ipModel.ip);
  const userBlacklistedReviewIps = await getUserIpsInPaymentReview(user, lastUserIPs);
  // filter IPs already added to the blacklist or to review
  const notExistBlacklistedIps = lastUserIPs.filter(userIp => !userBlacklistedIps.includes(userIp)
    && !userBlacklistedReviewIps.includes(userIp));
  const ipsForInsert = [];
  const cacheKeysForRemove = [];
  const now = moment().unix();
  const dateNow = new Date();

  // add only new not cellular IPs to the blacklist
  for (let i = 0; i < notExistBlacklistedIps.length; ++i) {
    const userIp = notExistBlacklistedIps[i];

    if (!isLocal(userIp) && !isCellularIp(userIp) && !isAppleRelay(userIp) && !await isProxy(userIp)
      && !OUR_SERVERS_IPS.has(userIp) && !PAYMENT_WHITELISTED_IPS.has(userIp)) {
      // disabled to save to PaymentBlacklist
      // ipsForInsert.push({ ip: userIp, creationMethod: creationMethods.automatic, description });

      // save user IPs to the temporary table for review before add to the blacklist
      ipsForInsert.push({ ip: userIp, description, created: now, updated: now, createdForTtlIndex: dateNow });
      cacheKeysForRemove.push(`paymentBlacklist_ip_${userIp}`);
    }
  }

  if (ipsForInsert.length) {
    try {
      await PaymentBlacklistIpReview.insertMany(ipsForInsert, { ordered: false });
    } catch (e) {
      // some IPs might be already added and we can get an error
      log.info(`Cannot add new blacklisted IPs for review, error: ${e.stack || e.message}`);
    }
    await removeRedisCacheByKeys(cacheKeysForRemove);
  }
};
