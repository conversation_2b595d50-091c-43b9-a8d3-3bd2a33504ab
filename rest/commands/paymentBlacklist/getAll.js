const { ApiError } = require('@s1/api-errors');
const _ = require('lodash');
const User = require('../../model/audb/User');
const UserIp = require('../../model/audb/UserIp');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');
const { paymentBlacklistTypesList, paymentBlacklistTypes } = require('../../constants/paymentBlacklist');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const getUserWatchingScore = require('../history/getUserWatchingScore');
const { getUserLocation } = require('../../service/maxmind');

const getBlacklistedIPs = async () => {
  const aggregation = [
    { $match: { ip: { $exists: true } } },
    {
      $lookup: {
        from: 'userIp',
        localField: 'ip',
        foreignField: 'ip',
        as: 'UserIp',
      },
    },
    { $unwind: { path: '$UserIp', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'UserIp.uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$ip',
        ip: { $first: '$ip' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$modifiedByName' },
        addedByUid: { $first: '$modifiedByUid' },
        users: { $push: { uid: '$User.id', em: '$User.em' } },
      },
    },
    {
      $project: { _id: '$id', ip: 1, creationMethod: 1, description: 1, updated: 1, created: 1, modifiedByName: 1, modifiedByUid: 1, users: 1, type: paymentBlacklistTypes.ip },
    },
    { $sort: { created: -1 } },
  ];

  const blacklistedIPs = await PaymentBlacklist.aggregate(aggregation)
    .then(blacklistedIps => Promise.all(blacklistedIps.map(async (blacklistedIp) => {
      // if some dependencies not found users will include empty objects
      blacklistedIp.users = blacklistedIp.users.filter(user => user.hasOwnProperty('uid'));
      blacklistedIp.users = await Promise.all(blacklistedIp.users.map(async (user) => {
        user.email = user.em ? await User.decryptEmailWithRedis(user.em) : '';
        user.uidEmail = user.uid + (user.email ? `(${user.email})` : '');
        delete user.em;

        return user.uidEmail;
      }));
      blacklistedIp.users = blacklistedIp.users.join(', ');

      return blacklistedIp;
    })));

  return blacklistedIPs;
};

const getBlacklistedUsers = async () => {
  const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });

  const aggregation = [
    { $match: { uid: { $exists: true } } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$uid',
        uid: { $first: '$uid' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$addedByName' },
        addedByUid: { $first: '$addedByUid' },
        em: { $first: '$User.em' },
        regtime: { $first: '$User.regtime' },
        expires: { $first: '$User.expires' },
        packageId: { $first: '$User.package' },
        permissionGroups: { $first: '$User.permissionGroups' },
      },
    },
    {
      $project: { _id: '$id', uid: 1, creationMethod: 1, description: 1, updated: 1, created: 1, addedByName: 1, addedByUid: 1, em: 1, regtime: 1, expires: 1, packageId: 1, type: paymentBlacklistTypes.uid, permissionGroups: 1 },
    },
    { $sort: { created: -1 } },
  ];

  const permissionGroups = await UserPermissionGroup.find({}, { _id: 1, name: 1 }).lean();
  const permissionGroupsObject = {};
  let defaultPermissionGroup;
  permissionGroups.forEach((group) => {
    permissionGroupsObject[group._id] = group;

    if (group.isUserDefaultGroup) defaultPermissionGroup = group;
  });

  const blacklistedUsers = await PaymentBlacklist.aggregate(aggregation)
    .then(blacklistedUsers => Promise.all(blacklistedUsers.map(async (blacklistedUser) => {
      blacklistedUser.email = blacklistedUser.em ? await User.decryptEmailWithRedis(blacklistedUser.em) : '';
      blacklistedUser.paidTypes = await getUserPaidTypes(blacklistedUser.uid);
      blacklistedUser.watchingScore = await getUserWatchingScore(blacklistedUser.uid);
      blacklistedUser.package = packagesObject.hasOwnProperty(blacklistedUser.packageId) ? packagesObject[blacklistedUser.packageId] : null;
      blacklistedUser.lastPayments = await PaymentLog.find({
        uid: blacklistedUser.uid,
        amount: { $gt: 0 },
        pptype: { $ne: 'manual' },
      }, { pptype: 1, package: 1 }).sort({ _id: -1 }).limit(3).lean()
        .cache(300)
        .then(logs => (_.map(logs, (log) => {
          if (packagesObject.hasOwnProperty(log.package)) {
            const { id, ...pack } = packagesObject[log.package];
            log = Object.assign(log, pack);
          }

          delete log._id;

          return log;
        })));
      blacklistedUser.UserPermissionGroups = blacklistedUser.permissionGroups && blacklistedUser.permissionGroups.length
        ? blacklistedUser.permissionGroups.map(groupId => permissionGroupsObject[groupId])
        : [defaultPermissionGroup];
      const userIPModel = await UserIp.findOne({ uid: blacklistedUser.uid }).lean();

      if (userIPModel) {
        const { countryName, countryCode, stateCode, ISP } = getUserLocation(userIPModel.ip);
        blacklistedUser.ipInfo = { ip: userIPModel.ip, countryName, countryCode, stateCode, ISP };
      } else {
        blacklistedUser.ipInfo = null;
      }

      return blacklistedUser;
    })));

  return blacklistedUsers;
};

const getBlacklistedBillingDetails = async (virtualType) => {
  const blacklistedBillingDetails = await PaymentBlacklist.find({ ip: { $exists: false }, uid: { $exists: false } }).lean(virtualType);

  return blacklistedBillingDetails;
};

module.exports = async (type) => {
  let paymentBlacklist = [];
  const virtualType = { virtuals: ['type'] };

  if (!type) {
    paymentBlacklist = await PaymentBlacklist.find().sort({ _id: -1 }).populate('User').lean(virtualType)
      .then(items => Promise.all(items.map(async (item) => {
        item.email = item.User ? await User.decryptEmailWithRedis(item.User.em) : '';
        delete item.User;

        return item;
      })));
  } else if (!paymentBlacklistTypesList.includes(type)) {
    return new ApiError(903, 'Invalid payment blacklist type');
  } else {
    switch (type) {
      case paymentBlacklistTypes.ip:
        paymentBlacklist = await getBlacklistedIPs();
        break;
      case paymentBlacklistTypes.uid:
        paymentBlacklist = await getBlacklistedUsers();
        break;
      case paymentBlacklistTypes.billingDetails:
        paymentBlacklist = await getBlacklistedBillingDetails(virtualType);
        break;
      default:
        break;
    }
  }

  return {
    error: 0,
    paymentBlacklist,
  };
};
