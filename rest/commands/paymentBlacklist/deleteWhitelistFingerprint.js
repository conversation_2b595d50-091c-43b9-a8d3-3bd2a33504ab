const { ApiError } = require('@s1/api-errors');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (fingerprint) => {
  if (!fingerprint) throw new ApiError(903, 'fingerprint is required');

  const paymentWhitelistFingerprint = await PaymentWhitelistFingerprint.findOne({ fingerprint }).lean();

  if (paymentWhitelistFingerprint) {
    await PaymentWhitelistFingerprint.deleteOne({ fingerprint }).exec();
    await removeRedisCacheByKeys(['paymentWhitelistFingerprint']);

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
