const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const getRelatedUsersIDsToCurrentBlacklistedUser = require('./getRelatedUsersIDsToCurrentBlacklistedUser');
const isBlacklistedUser = require('../../commands/payment/helpers/rules/isBlacklistedUser');

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentBlacklistedUser(userId);
  const users = await User.find({ id: { $in: usersIds } }).lean();
  const blacklistedUsersByBillingAddress = [];

  for (let i = 0; i < users.length; ++i) {
    const user = users[i];
    const [firstname, lastname] = user.name.split(' ');
    const userDataName = { firstname, lastname };
    const hasBlacklistedName = await isBlacklistedUser({ user: userDataName });

    if (hasBlacklistedName) {
      blacklistedUsersByBillingAddress.push({
        id: user.id,
        name: user.name,
        email: await User.decryptEmailWithRedis(user.em),
        blacklistedByRule: `Blacklisted user name: ${user.name}`,
      });
      continue;
    }
    if (user.hasOwnProperty('billingAddresses') && user.billingAddresses) {
      const userDataBillingAddress = { billingAddresses: user.billingAddresses };
      const hasBlacklistedBillingAddress = await isBlacklistedUser({ user: userDataBillingAddress });

      if (hasBlacklistedBillingAddress) {
        blacklistedUsersByBillingAddress.push({
          id: user.id,
          name: user.name,
          email: await User.decryptEmailWithRedis(user.em),
          blacklistedByRule: `Blacklisted user billingAddress: ${hasBlacklistedBillingAddress.data}, rule: ${hasBlacklistedBillingAddress.rule}`,
        });
      }
    }
  }

  return {
    error: 0,
    blacklistedUsersByBillingAddress,
  };
};
