const { ApiError } = require('@s1/api-errors');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
const { ipReviewStatuses } = require('../../constants/paymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const removeBlacklistedUserAndRelatedUsers = require('./removeBlacklistedUserAndRelatedUsers');

module.exports = async ({ _id, adminUser }) => {
  if (!_id) throw new ApiError(903, 'ID is required');

  const removeResult = await PaymentBlacklist.findOneAndDelete({ _id }).exec();

  let paymentBlacklistIpReview;

  if (removeResult) {
    if (removeResult.ip) {
      paymentBlacklistIpReview = await PaymentBlacklistIpReview.findOne({ ip: removeResult.ip }).exec();
      await removeRedisCacheByKeys([`paymentBlacklist_ip_${removeResult.ip}`]);

      if (paymentBlacklistIpReview) {
        paymentBlacklistIpReview.status = ipReviewStatuses.unblock;
        paymentBlacklistIpReview.createdForTtlIndex = new Date();
        await paymentBlacklistIpReview.save();
      }
    } else if (removeResult.uid) {
      await removeBlacklistedUserAndRelatedUsers(removeResult.uid, adminUser);
    } else {
      await removeRedisCacheByKeys(['paymentBlacklist_billingDetails']);
    }
  }

  return {
    error: 0,
    success: !!removeResult,
    paymentBlacklist: removeResult,
    paymentBlacklistIpReview: paymentBlacklistIpReview ? { ...paymentBlacklistIpReview.toObject() } : null,
  };
};
