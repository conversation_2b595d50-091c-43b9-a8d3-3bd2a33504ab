const moment = require('moment');
const log = require('@s1/log').create(__filename);
const UserCard = require('../../model/audb/UserCard');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const config = require('../../../config');
const User = require('../../model/audb/User');
const { ignoredCardNumbersEncryptedList } = require('../../constants/paymentBlacklist');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

module.exports = async (user, userCardNumbers, description = '') => {
  if (!user || isAdminOrSupportUser(user)) return;

  const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
  // do not filter userCardNumbers for local and dev tests
  let filteredCardNumbers = process.env.NODE_ENV === 'production' ? userCardNumbers.filter(card => !ignoredCardNumbersEncryptedList.includes(card)) : userCardNumbers;
  const whitelistedCards = await getWhitelistCardNumbers();
  filteredCardNumbers = filteredCardNumbers.filter(cardNumber => !whitelistedCards.includes(cardNumber));
  // search card numbers not yet blacklisted for all users, exclude admins adn supports
  const notBlacklistedUserCardNumberModels = await UserCard.find(
    { uid: { $nin: adminsAndSupportsIDs }, number: { $in: filteredCardNumbers }, isBlacklisted: false },
    { uid: 1, number: 1, first6: 1, last4: 1 },
  ).lean();
  // search for card numbers will be added to the black list to block their users too
  const notBlacklistedUserCardNumbersUIDs = notBlacklistedUserCardNumberModels.map(model => model.uid);
  const usersUIDs = Array.from(new Set(notBlacklistedUserCardNumbersUIDs));
  const now = moment().unix();
  // update card numbers not yet blacklisted for all users, exclude admins adn supports
  await UserCard.updateMany(
    { number: { $in: filteredCardNumbers }, isBlacklisted: false, uid: { $nin: adminsAndSupportsIDs } },
    { $set: { isBlacklisted: true, blacklistDescription: description, updated: now } },
    { upsert: false },
  ).exec();
  // need to clean cache keys for blocked users only
  const userCardNumberKeys = [];
  // cache keys by each user
  usersUIDs.forEach(uid => userCardNumberKeys.push(`userCard_uid_${uid}`));
  await removeRedisCacheByKeys(userCardNumberKeys);

  // add all users IDs for blacklisted card numbers to the blacklist
  if (notBlacklistedUserCardNumberModels.length) {
    const paymentBlacklistedUids = [];

    for (let i = 0; i < usersUIDs.length; ++i) {
      const paymentBlacklistedUidModel = await PaymentBlacklist.findOne({ uid: usersUIDs[i] })
        .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_id_${usersUIDs[i]}`);

      if (paymentBlacklistedUidModel) paymentBlacklistedUids.push(paymentBlacklistedUidModel.uid);
    }

    const uidsForInsert = [];
    const cacheKeysForRemove = [];
    // insert only not blacklisted user ids
    for (let i = 0; i < notBlacklistedUserCardNumberModels.length; ++i) {
      const model = notBlacklistedUserCardNumberModels[i];

      if (!paymentBlacklistedUids.includes(model.uid)) {
        // create cardNumbersLabel like: iaepBRayYK3CcFtMT8O3 (userId-1 email-1, userId-2 email-2, userId-3 email-3)
        let cardNumbersLabel = `${model.first6.substring(0, 4)}-****-****-${model.last4}`;

        // eslint-disable-next-line max-len
        const userCardModels = await UserCard.aggregate([
          {
            $match: { isBlacklisted: true, number: model.number },
          },
          {
            $lookup: {
              from: 'tuser',
              localField: 'uid',
              foreignField: 'id',
              as: 'User',
            },
          },
          { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
          {
            $project: { uid: 1, em: '$User.em' },
          },
        ]);

        if (userCardModels.length) {
          cardNumbersLabel += ' (';

          for (let f = 0; f < userCardModels.length; ++f) {
            if (f !== 0) cardNumbersLabel += ', ';

            cardNumbersLabel += userCardModels[f].uid;
            const email = userCardModels[f].em ? await User.decryptEmailWithRedis(userCardModels[f].em) : '';
            cardNumbersLabel += email ? ` ${email}` : '';
          }

          cardNumbersLabel += ')';
        }

        uidsForInsert.push({
          uid: model.uid,
          description: `Blocked by card number: ${cardNumbersLabel} ${description ? ` <-- ${description}` : ''}`,
          created: now,
          updated: now,
        });

        cacheKeysForRemove.push(`paymentBlacklist_id_${model.uid}`);
      }
    }

    if (uidsForInsert.length) {
      try {
        await PaymentBlacklist.insertMany(uidsForInsert, { ordered: false });
      } catch (e) {
        // some users might be already added and we can get an error
        log.info(`Cannot add new blacklisted users, error: ${e.stack || e.message}`);
      }
      await removeRedisCacheByKeys(cacheKeysForRemove);
    }
  }
};
