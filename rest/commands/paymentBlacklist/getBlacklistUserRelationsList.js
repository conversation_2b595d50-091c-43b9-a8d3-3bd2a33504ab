const { ApiError } = require('@s1/api-errors');
const _ = require('lodash');
const User = require('../../model/audb/User');
const getRelatedUsersIDsToCurrentBlacklistedUser = require('./getRelatedUsersIDsToCurrentBlacklistedUser');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const { getUserLocation } = require('../../service/maxmind');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');
const { ignoredCardNumbersEncryptedList } = require('../../constants/ignoredCardNumbers');

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentBlacklistedUser(userId);
  const relatedUsers = usersIds.filter(_userId => _userId !== userId);

  const users = await User.find({ id: { $in: relatedUsers } }, { em: 1, name: 1 }).lean()
    .then(users => Promise.all(users.map(async (user) => {
      user.email = await User.decryptEmailWithRedis(user.em);
      delete user.em;
      delete user._id;

      return user;
    })));

  const ips = await UserIp.aggregate([
    { $match: { uid: { $in: relatedUsers } } },
    { $group: { _id: '$ip' } },
  ]).then(ips => (_.map(ips, doc => doc._id)));

  const whitelistedCards = await getWhitelistCardNumbers();
  const cards = await UserCard.aggregate([
    { $match: { uid: { $in: relatedUsers } } },
    { $group: { _id: { number: '$number', first6: '$first6', last4: '$last4' } } },
  ]).then((cards) => {
    let filteredCards = cards.filter(card => (
      process.env.NODE_ENV === 'production'
        ? !ignoredCardNumbersEncryptedList.includes(card._id.number)
        : true
    ));
    filteredCards = filteredCards.filter(card => !whitelistedCards.includes(card._id.number));

    const mappedCards = filteredCards.map(doc => `${doc._id.first6}******${doc._id.last4} (${doc._id.number})`);

    return mappedCards;
  });

  const ipsWithInfo = [];
  ips.forEach((ip) => {
    const {
      countryName, cityName, connectionType,
    } = getUserLocation(ip);

    ipsWithInfo.push({
      ip,
      countryName,
      cityName,
      connectionType,
    });
  });

  return {
    error: 0,
    users,
    ips: ipsWithInfo,
    cards,
  };
};
