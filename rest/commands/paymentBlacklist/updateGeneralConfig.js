const { WrongParamsError } = require('@s1/api-errors');
const PaymentBlacklistGeneralConfig = require('../../model/audb/PaymentBlacklistGeneralConfig');

module.exports = async (data, user) => {
  if (!data || !data.streamingServers) throw new WrongParamsError();

  let paymentBlacklistGeneralConfig = await PaymentBlacklistGeneralConfig.findOne({}).exec();

  if (!paymentBlacklistGeneralConfig) paymentBlacklistGeneralConfig = new PaymentBlacklistGeneralConfig();
  if (user) {
    paymentBlacklistGeneralConfig.modifiedByUid = user.id;
    paymentBlacklistGeneralConfig.modifiedByName = user.name;
  }

  paymentBlacklistGeneralConfig.streamingServers = data.streamingServers;
  await paymentBlacklistGeneralConfig.save();

  return {
    error: 0,
    generalConfig: paymentBlacklistGeneralConfig,
  };
};
