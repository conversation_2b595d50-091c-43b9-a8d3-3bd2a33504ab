const { ApiError } = require('@s1/api-errors');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (_id) => {
  if (!_id) throw new ApiError(903, '_id is required');

  const paymentIgnoreFingerprintUseragent = await PaymentIgnoreFingerprintUseragent.findOne({ _id }).lean();

  if (paymentIgnoreFingerprintUseragent) {
    await PaymentIgnoreFingerprintUseragent.deleteOne({ _id }).exec();
    await removeRedisCacheByKeys(['paymentIgnoreFingerprintUseragent']);

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
