const moment = require('moment');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ignoredCardNumbersEncryptedList } = require('../../constants/paymentBlacklist');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

module.exports = async (userId, description = '') => {
  const now = moment().unix();
  const whitelistCardNumbers = await getWhitelistCardNumbers();
  // update card numbers not yet blacklisted for all users
  await UserCard.updateMany(
    { uid: userId, isBlacklisted: false, number: { $nin: [...whitelistCardNumbers, ...ignoredCardNumbersEncryptedList] } },
    { $set: { isBlacklisted: true, blacklistDescription: description, updated: now } },
    { upsert: false },
  ).exec();
  // need to clean cache key for current user
  await removeRedisCacheByKeys([`userCard_uid_${userId}`]);
};
