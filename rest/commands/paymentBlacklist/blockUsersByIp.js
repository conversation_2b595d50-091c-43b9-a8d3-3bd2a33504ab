const moment = require('moment');
const log = require('@s1/log').create(__filename);
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const UserIp = require('../../model/audb/UserIp');
const { creationMethods } = require('../../constants/paymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');

module.exports = async (ip) => {
  // find and block all users by IP
  const users = await UserIp.find({ ip }, { uid: 1 }).lean();

  if (!users.length) return;

  const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
  const usersIds = users.map(user => user.uid);
  const filteredUsersIds = usersIds.filter(userId => !adminsAndSupportsIDs.includes(userId));
  const paymentBlacklistedUidsModels = await PaymentBlacklist.find({ uid: { $in: filteredUsersIds } }, { uid: 1 }).lean();
  const paymentBlacklistedUids = paymentBlacklistedUidsModels.map(user => user.uid);
  const uidsForInsert = [];
  const cacheKeysForRemove = [];
  const now = moment().unix();

  // insert only not blacklisted user ids
  filteredUsersIds.forEach((id) => {
    if (!paymentBlacklistedUids.includes(id)) {
      uidsForInsert.push({
        creationMethod: creationMethods.automatic,
        description: `Automatic blocked by added IP: ${ip}`,
        uid: id,
        created: now,
        updated: now,
      });

      cacheKeysForRemove.push(`paymentBlacklist_id_${id}`);
    }
  });

  if (uidsForInsert.length) {
    try {
      await PaymentBlacklist.insertMany(uidsForInsert, { ordered: false });
    } catch (e) {
      // some users might be already added and we can get an error
      log.info(`Cannot add new blacklisted users, error: ${e.stack || e.message}`);
    }
    await removeRedisCacheByKeys(cacheKeysForRemove);
  }
};
