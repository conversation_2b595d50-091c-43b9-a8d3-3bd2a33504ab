const config = require('../../../config');
const PaymentBlacklistGeneralConfig = require('../../model/audb/PaymentBlacklistGeneralConfig');

module.exports = async () => {
  const generalConfig = await PaymentBlacklistGeneralConfig.findOne().lean().cache(config.paymentBlacklist.cache, 'paymentBlacklistGeneralConfig');

  if (generalConfig) return {
    error: 0,
    generalConfig,
  };

  return {
    error: 0,
    generalConfig: new PaymentBlacklistGeneralConfig(),
  };
};
