const getPreviousUserIPs = require('../../middleware/helpers/getPreviousUserIPs');
const addAllUserIpsToPaymentBlacklist = require('../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');

module.exports = async (user, description = '') => {
  const userIPModels = await getPreviousUserIPs(user.id);
  const userIPs = userIPModels.map(model => model.ip);

  await addAllUserIpsToPaymentBlacklist(user, userIPs, description);
};
