const { ApiError } = require('@s1/api-errors');
const moment = require('moment');
const _ = require('lodash');
const config = require('../../../config');
const User = require('../../model/audb/User');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserRelations = require('../../model/audb/UserRelations');
const { Package } = require('../../model/audb/Package');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentLog = require('../../model/audb/PaymentLog');
const getRelatedUsersIDsToCurrentBlacklistedUser = require('./getRelatedUsersIDsToCurrentBlacklistedUser');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const getUserWatchingScore = require('../history/getUserWatchingScore');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

const formatUsersDataForDuplicates = (users) => {
  const allFingerprints = [];
  const allIPs = [];
  const allCardNumbers = [];
  const allCookiekey = [];
  const allStoragekey = [];
  const allFlashkey = [];

  // collect users data
  for (let i = 0; i < users.length; ++i) {
    if (users[i].fingerprints && users[i].fingerprints.length) allFingerprints.push(...users[i].fingerprints);
    if (users[i].IPs && users[i].IPs.length) allIPs.push(...users[i].IPs);
    if (users[i].cardNumbers && users[i].cardNumbers.length) allCardNumbers.push(...users[i].cardNumbers);
    if (users[i].cookiekey && users[i].cookiekey.length) allCookiekey.push(...users[i].cookiekey);
    if (users[i].storagekey && users[i].storagekey.length) allStoragekey.push(...users[i].storagekey);
    if (users[i].flashkey && users[i].flashkey.length) allFlashkey.push(...users[i].flashkey);
  }

  // filter duplicated only for show in graph, we do not need unique IPs, cards, fingerprints, etc
  const duplicatedFingerprintsCount = {};
  const duplicatedIPsCount = {};
  const duplicatedCardNumbersCount = {};
  const duplicatedCookiekeyCount = {};
  const duplicatedStoragekeyCount = {};
  const duplicatedFlashkeyCount = {};

  allFingerprints.forEach((fingerprint) => {
    duplicatedFingerprintsCount[fingerprint] = (duplicatedFingerprintsCount[fingerprint] || 0) + 1;
  });
  allIPs.forEach((ip) => {
    duplicatedIPsCount[ip] = (duplicatedIPsCount[ip] || 0) + 1;
  });
  allCardNumbers.forEach((number) => {
    duplicatedCardNumbersCount[number] = (duplicatedCardNumbersCount[number] || 0) + 1;
  });
  allCookiekey.forEach((number) => {
    duplicatedCookiekeyCount[number] = (duplicatedCookiekeyCount[number] || 0) + 1;
  });
  allStoragekey.forEach((number) => {
    duplicatedStoragekeyCount[number] = (duplicatedStoragekeyCount[number] || 0) + 1;
  });
  allFlashkey.forEach((number) => {
    duplicatedFlashkeyCount[number] = (duplicatedFlashkeyCount[number] || 0) + 1;
  });

  const duplicatedFingerprints = [];
  const duplicatedIPs = [];
  const duplicatedCardNumbers = [];
  const duplicatedCookiekey = [];
  const duplicatedStoragekey = [];
  const duplicatedFlashkey = [];

  Object.entries(duplicatedFingerprintsCount).forEach(([key, value]) => {
    if (value > 1) duplicatedFingerprints.push(key);
  });
  Object.entries(duplicatedIPsCount).forEach(([key, value]) => {
    if (value > 1) duplicatedIPs.push(key);
  });
  Object.entries(duplicatedCardNumbersCount).forEach(([key, value]) => {
    if (value > 1) duplicatedCardNumbers.push(key);
  });
  Object.entries(duplicatedCookiekeyCount).forEach(([key, value]) => {
    if (value > 1) duplicatedCookiekey.push(key);
  });
  Object.entries(duplicatedStoragekeyCount).forEach(([key, value]) => {
    if (value > 1) duplicatedStoragekey.push(key);
  });
  Object.entries(duplicatedFlashkeyCount).forEach(([key, value]) => {
    if (value > 1) duplicatedFlashkey.push(key);
  });

  // create links between users
  for (let i = 0; i < users.length - 1; ++i) {
    // exclude unique data
    users[i].fingerprints = users[i].fingerprints.filter(fp => duplicatedFingerprints.includes(fp));
    users[i].IPs = users[i].IPs.filter(ip => duplicatedIPs.includes(ip));
    users[i].cardNumbers = users[i].cardNumbers.filter(number => duplicatedCardNumbers.includes(number));
    users[i].cookiekey = users[i].cookiekey.filter(key => duplicatedCookiekey.includes(key));
    users[i].storagekey = users[i].storagekey.filter(key => duplicatedStoragekey.includes(key));
    users[i].flashkey = users[i].flashkey.filter(key => duplicatedFlashkey.includes(key));
  }

  return users;
};

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentBlacklistedUser(userId);
  const usersObject = {};

  const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });

  await User.find(
    { id: { $in: usersIds } }, { id: 1, em: 1, name: 1, regtime: 1, expires: 1, package: 1, cookiekey: 1, storagekey: 1, flashkey: 1 },
  ).lean()
    .then(users => Promise.all(users.map(async (user) => {
      user.email = await User.decryptEmailWithRedis(user.em);
      delete user.em;
      delete user._id;
      user.fingerprints = [];
      user.IPs = [];
      user.cards = [];
      user.cardNumbers = [];
      user.relatedUserIds = [];
      usersObject[user.id] = user;
      user.cookiekey = user.cookiekey ? user.cookiekey.split('.') : [];
      user.storagekey = user.storagekey ? user.storagekey.split('.') : [];
      user.flashkey = user.flashkey ? user.flashkey.split('.') : [];
      user.lat = Math.random() * 160 - 80;
      user.lon = Math.random() * 320 - 160;
      user.package = packagesObject.hasOwnProperty(user.package) ? packagesObject[user.package] : null;
      user.registeredDate = moment(user.regtime * 1000).locale('en').format('DD MMM YYYY, HH:mm:ss');
      user.expiresDate = moment(user.expires * 1000).locale('en').format('DD MMM YYYY, HH:mm:ss');
      const color = moment().unix() > user.expires ? 'red' : 'black';
      user.expiresDateLabel = `<span style="color: ${color}">${user.expiresDate}</span>`;
      user.packageLabel = `${user.package.epricestr}${user.package.price ? ` $${user.package.price}` : ''}`;
      user.isInitialUser = false;
      user.paidTypes = await getUserPaidTypes(user.id);
      user.watchingScore = await getUserWatchingScore(user.id);
      user.lastPayments = await PaymentLog.find({
        uid: user.id,
        amount: { $gt: 0 },
        pptype: { $ne: 'manual' },
      }, { pptype: 1, package: 1 }).sort({ _id: -1 }).limit(3).lean()
        .cache(300)
        .then(logs => (_.map(logs, (log) => {
          if (packagesObject.hasOwnProperty(log.package)) {
            const { id, ...pack } = packagesObject[log.package];
            log = Object.assign(log, pack);
          }

          delete log._id;

          return log;
        })));

      return user;
    })));

  const blacklistedIPModels = await PaymentBlacklist.find({ ip: { $exists: true } }).cache(config.paymentBlacklist.cache, 'paymentBlacklist_ip').lean();
  const blacklistedIPs = blacklistedIPModels.map(model => model.ip);
  await UserIp.aggregate([
    { $match: { uid: { $in: usersIds } } },
    { $sort: { _id: -1 } },
    { $group: { _id: '$uid', IPs: { $push: '$ip' } } },
  ]).then(logs => (_.map(logs, (doc) => {
    const log = {
      userId: doc._id,
      IPs: doc.IPs.filter(ip => blacklistedIPs.includes(ip)),
    };
    usersObject[log.userId].IPs = log.IPs.slice(0, 15);

    return log;
  })));

  const [whitelistedFingerprints, ignoredUseragents, whitelistedCards] = await Promise.all([
    getWhitelistedFingerprints(),
    getIgnoredUseragents(),
    getWhitelistCardNumbers(),
  ]);

  await UserFingerprintPayment.aggregate([
    { $match: { uid: { $in: usersIds }, fingerprint: { $nin: whitelistedFingerprints }, userAgent: { $nin: ignoredUseragents } } },
    { $group: { _id: '$uid', fingerprints: { $push: '$fingerprint' } } },
  ]).then(logs => (_.map(logs, (doc) => {
    const log = {
      userId: doc._id,
      fingerprints: doc.fingerprints,
    };
    usersObject[log.userId].fingerprints = log.fingerprints;

    return log;
  })));

  await UserCard.aggregate([
    { $match: { uid: { $in: usersIds }, number: { $nin: whitelistedCards } } },
    { $group: { _id: '$uid', cards: { $push: { number: '$number', first6: '$first6', last4: '$last4' } } } },
  ]).then(cards => (_.map(cards, (doc) => {
    const log = {
      userId: doc._id,
      cards: doc.cards,
    };

    usersObject[log.userId].cards = doc.cards;
    usersObject[log.userId].cardNumbers = doc.cards.map(card => card.number);
    usersObject[log.userId].cardsObject = {};
    doc.cards.forEach((card) => {
      usersObject[log.userId].cardsObject[card.number] = `${card.first6}******${card.last4}`;
    });

    return log;
  })));

  await UserRelations.aggregate([
    { $match: { userId: { $in: usersIds } } },
    { $group: { _id: '$userId', relatedUserIds: { $push: '$relatedUserId' } } },
  ]).then(relations => (_.map(relations, (doc) => {
    const log = {
      userId: doc._id,
      relatedUserIds: doc.relatedUserIds,
    };

    if (log.relatedUserIds && log.relatedUserIds.length) usersObject[log.userId].relatedUserIds = log.relatedUserIds;

    return log;
  })));

  // get initial blacklisted user
  const blacklistedUsers = await PaymentBlacklist.find({ uid: { $in: usersIds } }, { _id: 1, uid: 1 }).sort({ _id: 1 }).lean();
  const initialBlacklistedUser = blacklistedUsers[0];

  if (usersObject.hasOwnProperty(initialBlacklistedUser.uid)) {
    usersObject[initialBlacklistedUser.uid].isInitialUser = true;
  }

  const parsedUsers = Object.values(usersObject);
  const formattedUsers = formatUsersDataForDuplicates(parsedUsers);

  return {
    error: 0,
    titleLabel: `User ${usersObject[userId].email} (${userId}) relations`,
    subTitleLabel: formattedUsers.length > 1 ? `Total ${formattedUsers.length} users, press on ${usersObject[userId].email} point to display the all related users` : 'User has no related users',
    users: formattedUsers,
  };
};
