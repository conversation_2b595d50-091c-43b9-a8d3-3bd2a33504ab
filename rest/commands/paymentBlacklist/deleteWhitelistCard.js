const { ApiError } = require('@s1/api-errors');
const PaymentWhitelistCard = require('../../model/audb/PaymentWhitelistCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (number) => {
  if (!number) throw new ApiError(903, 'number is required');

  const whitelistCard = await PaymentWhitelistCard.findOne({ number }).lean();

  if (whitelistCard) {
    await PaymentWhitelistCard.deleteOne({ number }).exec();
    await removeRedisCacheByKeys(['paymentWhitelistCard']);

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
