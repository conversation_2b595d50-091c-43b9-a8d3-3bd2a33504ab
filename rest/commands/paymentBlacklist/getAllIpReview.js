const _ = require('lodash');
const PaymentBlacklistIpReview = require('../../model/audb/PaymentBlacklistIpReview');
const User = require('../../model/audb/User');

module.exports = async () => {
  const paymentBlacklistIpReview = await PaymentBlacklistIpReview.aggregate([
    {
      $lookup: {
        from: 'userIp',
        localField: 'ip',
        foreignField: 'ip',
        as: 'UserIp',
      },
    },
    { $unwind: { path: '$UserIp', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'UserIp.uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$ip',
        ip: { $first: '$ip' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        status: { $first: '$status' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        modifiedByName: { $first: '$modifiedByName' },
        modifiedByUid: { $first: '$modifiedByUid' },
        users: { $push: { uid: '$User.id', em: '$User.em' } },
      },
    },
    {
      $project: { _id: '$id', ip: 1, status: 1, description: 1, updated: 1, created: 1, modifiedByName: 1, modifiedByUid: 1, users: 1 },
    },
    { $sort: { created: -1 } },
  ])
    .then(ipReviews => Promise.all(ipReviews.map(async (ipReview) => {
      // if some dependencies not found users will include empty objects
      ipReview.users = ipReview.users.filter(user => user.hasOwnProperty('uid'));
      ipReview.users = await Promise.all(ipReview.users.map(async (user) => {
        user.email = user.em ? await User.decryptEmailWithRedis(user.em) : '';
        user.uidEmail = user.uid + (user.email ? `(${user.email})` : '');
        delete user.em;

        return user.uidEmail;
      }));
      ipReview.users = ipReview.users.join(', ');

      return ipReview;
    })));

  return {
    error: 0,
    paymentBlacklistIpReview,
  };
};
