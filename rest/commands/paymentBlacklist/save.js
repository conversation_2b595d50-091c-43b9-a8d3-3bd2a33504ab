const { WrongParamsError, ApiError } = require('@s1/api-errors');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const User = require('../../model/audb/User');
const { paymentBlacklistTypes, paymentBlacklistTypesList, creationMethods } = require('../../constants/paymentBlacklist');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const blockUsersByIp = require('./blockUsersByIp');
const addBlacklistedUserAndRelatedUsers = require('../../commands/paymentBlacklist/addBlacklistedUserAndRelatedUsers');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.type) throw new ApiError(903, 'Type is required');
  if (!paymentBlacklistTypesList.includes(data.type)) throw new ApiError(903, 'Invalid payment blacklist type');
  if (data.type === paymentBlacklistTypes.ip) {
    const ipRegex = /\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){4}\b/;

    // eslint-disable-next-line no-prototype-builtins
    if (!data.hasOwnProperty('ip') || !data.ip.match(ipRegex)) throw new ApiError(903, 'Wrong IP address');
  }

  let paymentBlacklist;

  if (data._id) {
    paymentBlacklist = await PaymentBlacklist.findOne({ _id: data._id }).exec();
    paymentBlacklist.description = data.description || '';

    if (data.type === paymentBlacklistTypes.uid) paymentBlacklist.creationMethod = data.creationMethod || creationMethods.manual;
    if (admin && (!paymentBlacklist.addedByUid || !paymentBlacklist.addedByName)) {
      paymentBlacklist.addedByUid = admin.id;
      paymentBlacklist.addedByName = admin.name;
    }
  } else {
    paymentBlacklist = new PaymentBlacklist();
    paymentBlacklist.creationMethod = creationMethods.manual;
    paymentBlacklist.description = data.description;

    if (admin) {
      paymentBlacklist.addedByUid = admin.id;
      paymentBlacklist.addedByName = admin.name;
    }
  }

  switch (data.type) {
    case paymentBlacklistTypes.ip:
      paymentBlacklist.ip = data.ip;
      await removeRedisCacheByKeys([`paymentBlacklist_ip_${data.ip}`]);
      break;
    case paymentBlacklistTypes.uid:
      // eslint-disable-next-line no-case-declarations
      const userId = parseInt(data.uid);
      // eslint-disable-next-line no-case-declarations
      const currentUser = await User.findOne({ id: userId }).lean();

      // for admin or support do not need to check is blacklisted or block him
      if (!currentUser || isAdminOrSupportUser(currentUser)) throw new ApiError(903, 'Cannot block admin or support user');

      paymentBlacklist.uid = userId;
      await removeRedisCacheByKeys([`paymentBlacklist_id_${userId}`]);
      break;
    default:
      if (admin) {
        paymentBlacklist.modifiedByUid = admin.id;
        paymentBlacklist.modifiedByName = admin.name;
      }

      for (const key of Object.keys(data)) {
        paymentBlacklist[key] = data[key];
      }

      await removeRedisCacheByKeys(['paymentBlacklist_billingDetails']);
      break;
  }

  await paymentBlacklist.save();

  // add all user fingerprints to the payment blacklist
  if (data.type === paymentBlacklistTypes.uid) {
    await addBlacklistedUserAndRelatedUsers(parseInt(data.uid), admin, data.description || '');
  } else if (data.type === paymentBlacklistTypes.ip) {
    await blockUsersByIp(data.ip);
  }

  return {
    error: 0,
    paymentBlacklist: { ...paymentBlacklist.toObject(), type: data.type },
  };
};
