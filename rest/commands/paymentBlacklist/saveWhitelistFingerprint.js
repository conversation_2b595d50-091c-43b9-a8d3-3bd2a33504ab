const { WrongParamsError, ApiError } = require('@s1/api-errors');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.fingerprint) throw new ApiError(903, 'fingerprint is required');

  let paymentWhitelistFingerprint = await PaymentWhitelistFingerprint.findOne({ fingerprint: data.fingerprint }).exec();

  if (!paymentWhitelistFingerprint) {
    paymentWhitelistFingerprint = new PaymentWhitelistFingerprint();
    paymentWhitelistFingerprint.fingerprint = data.fingerprint;
    paymentWhitelistFingerprint.userAgent = data.userAgent || null;
    paymentWhitelistFingerprint.addedByUid = admin.id;
    paymentWhitelistFingerprint.addedByName = admin.name;

    await paymentWhitelistFingerprint.save();
    await removeRedisCacheByKeys(['paymentWhitelistFingerprint']);
  }

  await UserFingerprintPayment.updateMany({ fingerprint: data.fingerprint }, { $set: { isBlacklisted: false, isStealer: false } }, { upsert: false }).exec();
  await removeRedisCacheByKeys([`userFingerprintPayment_blacklist_fingerprint_${data.fingerprint}`]);
  await removeRedisCacheByKeys([`userFingerprintPayment_stealer_fingerprint_${data.fingerprint}`]);

  return {
    error: 0,
    paymentWhitelistFingerprint,
  };
};
