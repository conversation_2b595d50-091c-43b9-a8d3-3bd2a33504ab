const User = require('../../model/audb/User');
const UserCard = require('../../model/audb/UserCard');
const { decryptWithRedis } = require('../../helpers/security');
const config = require('../../../config');

module.exports = async () => {
  const cardNumbers = await UserCard.aggregate([
    {
      $match: { isBlacklisted: true },
    },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $group: {
        _id: '$number',
        number: { $first: '$number' },
        first6: { $first: '$first6' },
        last4: { $first: '$last4' },
        type: { $first: '$type' },
        brand: { $first: '$brand' },
        country: { $first: '$country' },
        bank: { $first: '$bank' },
        blacklistDescription: { $first: '$blacklistDescription' },
        updated: { $first: '$updated' },
        users: { $push: { uid: '$uid', em: '$User.em' } },
        owners: { $push: '$owner' },
      },
    },
    {
      $project: {
        _id: 0, number: 1, first6: 1, last4: 1, type: 1, brand: 1, country: 1, bank: 1, blacklistDescription: 1, updated: 1, users: 1, owners: 1,
      },
    },
    {
      $sort: { updated: -1 },
    },
  ])
    .then(cardNumbers => Promise.all(cardNumbers.map(async (cardNumber) => {
      // if some dependencies not found users will include empty objects
      cardNumber.users = cardNumber.users.filter(user => user.hasOwnProperty('uid'));
      cardNumber.users = await Promise.all(cardNumber.users.map(async (user) => {
        user.email = user.em ? await User.decryptEmailWithRedis(user.em) : '';
        user.uidEmail = `${user.uid}${user.email ? `(${user.email})` : ''}`;
        delete user.em;

        return user.uidEmail;
      }));
      cardNumber.users = cardNumber.users.join(', ');

      cardNumber.owners = cardNumber.owners.filter(owner => !!owner);
      cardNumber.owners = await Promise.all(cardNumber.owners.map(async (owner) => {
        const redisKey = `card_owner_${owner}`;
        const decryptedOwner = owner ? await decryptWithRedis(owner, config.cardDataSecuritySalt, redisKey, 30 * 24 * 3600) : '';

        return decryptedOwner;
      }));
      cardNumber.owners = cardNumber.owners.join(', ');
      cardNumber.cardLabel = `${cardNumber.first6}******${cardNumber.last4}`;

      return cardNumber;
    })));

  return {
    error: 0,
    cardNumbers,
  };
};
