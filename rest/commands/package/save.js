const connection = require('../../model/audb/connection');
const getNextModelId = require('../../helpers/getNextModelId');
const isSet = require('../../helpers/isSet');
const { SchemaConstants, QueryConstants } = require('../../constants/package');

const deleteUsefulValues = (pckg) => {
  delete pckg._id;
  delete pckg.editing;
  delete pckg.$$haskey;
  delete pckg.created;
  delete pckg.updated;

  return pckg;
};

module.exports = async (packageData, schema = '') => {
  const isArray = packageData => Array.isArray(packageData);
  const packageDataResponseArray = [];
  let packageDataArray = [];

  if (isArray(packageData)) packageDataArray = packageData;
  else packageDataArray.push(packageData);

  let packageSchema;

  if (schema === QueryConstants.PACKAGE) packageSchema = SchemaConstants.PACKAGE;
  else if (schema === QueryConstants.PACKAGE_TO_FEATURES) packageSchema = SchemaConstants.PACKAGE_TO_FEATURES;
  else if (schema === QueryConstants.PACKAGE_FEATURE) packageSchema = SchemaConstants.PACKAGE_FEATURE;

  for (let pckg of packageDataArray) {
    if (schema === SchemaConstants.PACKAGE) pckg.price = parseFloat(pckg.price);
    if (isSet(pckg.sor)) pckg.sor = parseInt(pckg.sor);
    if (!pckg.id) pckg.id = await getNextModelId(connection.model(packageSchema));

    pckg = deleteUsefulValues(pckg);

    const updatedPackage = await connection.model(packageSchema).findOneAndUpdate(
      { id: pckg.id },
      { $set: pckg },
      { upsert: true, new: true },
    ).lean().exec();
    packageDataResponseArray.push(updatedPackage);
  }

  return packageDataResponseArray.length === 1 ? packageDataResponseArray[0] : packageDataResponseArray;
};
