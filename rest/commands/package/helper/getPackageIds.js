const { Package } = require('../../../model/audb/Package');

/**
 * @description Method return package IDs by filters, by default return all
 *
 * @param {Object} option
 * @param {Boolean} option.includeUpgrade - by default include upgrade packages
 * @param {Boolean} option.includeTrial - by default include trial packages
 * @param {Boolean} option.shortTermsOnly - return short terms packages, false by default
 * @param {Boolean} option.longTermsOnly - return long terms packages, false by default
 *
 * @return {Promise} return list of package IDs
 * */
module.exports = async ({ includeUpgrade = true, includeTrial = true, shortTermsOnly = false, longTermsOnly = false }) => {
  const query = {};

  if (shortTermsOnly) {
    query.days = 30;
  } else if (longTermsOnly) {
    query.days = { $gte: 90 };
  } else {
    if (!includeUpgrade) query.isUpgrade = false;
    if (!includeTrial) {
      query.days = { $gt: 0 };
      query.price = { $gt: 0 };
    }
  }

  const allPackages = await Package.find(query).cache(600).lean();

  return allPackages.map(pack => pack.id);
};
