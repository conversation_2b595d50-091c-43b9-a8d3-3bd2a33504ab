const connection = require('../../model/audb/connection');
const { SchemaConstants } = require('../../constants/package');

module.exports = async () => {
  const packages = await connection.model(SchemaConstants.PACKAGE_TO_FEATURES).find().lean()
    .exec();
  const features = await connection.model(SchemaConstants.PACKAGE_FEATURE).find().sort({ sor: 1 }).lean()
    .exec();
  for (const item of packages) {
    item.package = await connection.model(SchemaConstants.PACKAGE).find({ pgid: item.id }).sort({ sor: 1 }).lean()
      .exec();
  }

  return {
    gg: packages,
    feature: features,
  };
};
