const { Package } = require('../../model/audb/Package');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');
const PackageFeature = require('../../model/audb/PackageFeature');
const Channel = require('../../model/audb/Channel');
const filterPackagesByRules = require('../payment/helpers/filterPackagesByRules');

module.exports = async ({ fromPage = '', user, locale }) => {
  const ptf = await PackageToFeatures.find({})
    .populate({
      path: 'package',
      options: { sort: { sor: 1 } } })
    .lean()
    .exec();

  await Promise.all(ptf.map(async (packToFeature) => {
    // new users should not be able to see and pay long terms packages
    packToFeature.package = await filterPackagesByRules({
      user, packages: packToFeature.package, fromPage,
    });
    packToFeature.package = packToFeature.package.filter(pack => !pack.isUpgrade);
  }));

  const packagesToFeatures = PackageToFeatures.formatAll(ptf, { locale });
  const features = PackageFeature.formatAll(
    await PackageFeature.find().sort({ sor: 1 }).select('-_id').exec(),
    { showId: false, locale },
  );
  const channels = await Channel.getAll({ locale });
  const extra = {};
  const basic = {};
  channels.forEach((channel) => {
    if (channel.extra === 1) extra[channel.odid - 1] = channel;
    else if (channel.extra === 0) basic[channel.odid - 1] = channel;
  });
  packagesToFeatures.forEach((packagesToFeature) => {
    delete packagesToFeature._id;
    packagesToFeature.package = Package.formatAll(packagesToFeature.package, { locale });
    packagesToFeature.package.forEach((pack) => {
      delete pack._id;
    });
  });

  return {
    packageinfo: packagesToFeatures,
    features,
    channels: {
      extra,
      basic,
    },
  };
};
