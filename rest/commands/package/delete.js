const connection = require('../../model/audb/connection');
const { SchemaConstants, QueryConstants } = require('../../constants/package');

module.exports = async (id, schema = '') => {
  if (schema === QueryConstants.PACKAGE) {
    await connection.model(SchemaConstants.PACKAGE).deleteOne({ id }).exec();
  } else if (schema === QueryConstants.PACKAGE_TO_FEATURES) {
    await connection.model(SchemaConstants.PACKAGE_TO_FEATURES).deleteOne({ id }).exec();
    await connection.model(SchemaConstants.PACKAGE).deleteOne({ pgid: id }).exec();
  } else if (schema === QueryConstants.PACKAGE_FEATURE) {
    await connection.model(SchemaConstants.PACKAGE_FEATURE).deleteOne({ id }).exec();
    await connection.model(SchemaConstants.PACKAGE_TO_FEATURES).updateMany(
      {},
      { $pull: { features: { $in: [id] } } },
      { multi: true },
    ).exec();
  }

  return {
    errorcode: 0,
    result: true,
  };
};
