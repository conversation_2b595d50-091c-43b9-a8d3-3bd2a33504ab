const axios = require('axios');
const rangeParser = require('parse-numeric-range');
const HttpsProxyAgent = require('https-proxy-agent');
const { SocksProxyAgent } = require('socks-proxy-agent');
const { getUserLocation } = require('../../service/maxmind');
const shuffle = require('../../helpers/shuffleArray');

const TIMEOUT = 30;

module.exports = async (data, userIP) => {
  if (!data.name) throw new Error('Proxy name is required');
  if (!data.host) throw new Error('Proxy host is required');
  if (!data.port) throw new Error('Proxy port is required');
  if (!data.protocol) throw new Error('Proxy protocol is required');
  if (data.user && !data.pass) throw new Error('Proxy pass is required');
  if (data.pass && !data.user) throw new Error('Proxy user is required');

  const axiosConfig = { timeout: TIMEOUT * 1000 };

  const ports = rangeParser(data.port);
  const shuffledPorts = shuffle(ports);
  const port = shuffledPorts[0];
  const { countryCode } = getUserLocation(userIP);

  const proxyPath = data.user && data.pass ? `${data.user}:${data.pass.replace('COUNTRY_CODE', countryCode || 'US')}@${data.host}:${port}` : `${data.host}:${port}`;

  if (data.protocol === 'socks') {
    const proxyOptions = `socks5://${proxyPath}`;
    axiosConfig.httpsAgent = new SocksProxyAgent(proxyOptions, { timeout: TIMEOUT * 1000 });
  } else {
    axiosConfig.proxy = false;
    axiosConfig.httpsAgent = new HttpsProxyAgent(`${data.protocol}://${proxyPath}`);
  }

  const url = 'https://geolocation-db.com/json/';
  const axiosCall = axios.get(url, axiosConfig);

  const response = await axiosCall
    .then(res => res.data)
    .catch(error => error);

  if (!response || response.hasOwnProperty('error') || response.hasOwnProperty('message')
    || !response.IPv4) return { error: 'cannot check proxy', success: false };

  const userLocation = getUserLocation(response.IPv4);

  if (userLocation) {
    return {
      error: 0,
      success: true,
      ipInfo: `${response.IPv4} - ${userLocation.countryName} - ${userLocation.ISP}`,
    };
  }

  return {
    error: 0,
    success: true,
    ipInfo: `${response.IPv4} - N/A`,
  };
};
