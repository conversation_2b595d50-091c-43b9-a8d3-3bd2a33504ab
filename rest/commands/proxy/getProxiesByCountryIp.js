const rangeParser = require('parse-numeric-range');
const CountryProxyMap = require('../../model/audb/CountryProxyMap');
const Proxy = require('../../model/audb/Proxy');
const { getUserLocation } = require('../../service/maxmind');
const _getEuropeCountry = require('../../commands/country/getEuropeCountry');
const config = require('../../../config');
const shuffle = require('../../helpers/shuffleArray');

const DEFAULT_PORTS_COUNT = 5;

const sortArrayByArrayIds = (list, sort) => list.sort((a, b) => sort.indexOf(a._id.toString()) - sort.indexOf(b._id.toString()));

const parseProxies = (value, countryCode) => {
  const proxies = [];

  value.forEach((proxy) => {
    const parsedProxy = Object.assign({}, proxy);
    delete parsedProxy.port;
    parsedProxy.pass = parsedProxy.pass.replace('COUNTRY_CODE', countryCode);
    const ports = proxy.port ? rangeParser(proxy.port) : [];
    const defaultPortsCount = parsedProxy.defaultPortsCount || DEFAULT_PORTS_COUNT;
    const portsCount = defaultPortsCount > ports.length ? ports.length : defaultPortsCount;
    const shuffledPorts = shuffle(ports);
    const slicedPorts = shuffledPorts.slice(0, portsCount);

    slicedPorts.forEach((port) => {
      proxies.push(Object.assign({ port }, parsedProxy));
    });
  });

  return proxies;
};

/**
 * Load list of proxies by country IP
 * */
module.exports = async (ip) => {
  const { countryCode } = getUserLocation(ip);

  if (countryCode) {
    // get proxies by IP country
    const countryProxyMap = await CountryProxyMap.findOne({ country: countryCode }).cache(60).lean();

    if (countryProxyMap && countryProxyMap.list && countryProxyMap.list.length) {
      const proxies = await Proxy.find({ _id: { $in: countryProxyMap.list } }).cache(60).lean();
      const sortedProxies = sortArrayByArrayIds(proxies, countryProxyMap.list);

      return {
        error: 0,
        proxies: parseProxies(sortedProxies, countryCode),
      };
    }
  }

  // get Europe country
  const { country: euroCountry } = await _getEuropeCountry({ code: countryCode });

  if (euroCountry) {
    // get proxies for Europe countries
    const countryProxyMap = await CountryProxyMap.findOne({ country: 'euro' }).cache(60).lean();

    if (countryProxyMap && countryProxyMap.list && countryProxyMap.list.length) {
      const proxies = await Proxy.find({ _id: { $in: countryProxyMap.list } }).cache(60).lean();
      const sortedProxies = sortArrayByArrayIds(proxies, countryProxyMap.list);

      return {
        error: 0,
        proxies: parseProxies(sortedProxies, countryCode),
      };
    }
  }

  const asiaAndOcean = config.countries.asiaAndOcean;

  // get proxies for Asia and Ocean
  if (asiaAndOcean && asiaAndOcean.split(',').includes(countryCode)) {
    // get proxies for Europe countries
    const countryProxyMap = await CountryProxyMap.findOne({ country: 'asia_ocean' }).cache(60).lean();

    if (countryProxyMap && countryProxyMap.list && countryProxyMap.list.length) {
      const proxies = await Proxy.find({ _id: { $in: countryProxyMap.list } }).cache(60).lean();
      const sortedProxies = sortArrayByArrayIds(proxies, countryProxyMap.list);

      return {
        error: 0,
        proxies: parseProxies(sortedProxies, countryCode),
      };
    }
  }

  // get proxies for other countries
  const countryProxyMap = await CountryProxyMap.findOne({ country: 'all' }).cache(60).lean();

  if (countryProxyMap && countryProxyMap.list && countryProxyMap.list.length) {
    const proxies = await Proxy.find({ _id: { $in: countryProxyMap.list } }).cache(60).lean();
    const sortedProxies = sortArrayByArrayIds(proxies, countryProxyMap.list);

    return {
      error: 0,
      proxies: parseProxies(sortedProxies, countryCode),
    };
  }

  return {
    error: 0,
    proxies: [],
  };
};
