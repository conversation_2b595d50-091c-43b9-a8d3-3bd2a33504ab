const Proxy = require('../../model/audb/Proxy');
const CountryProxyMap = require('../../model/audb/CountryProxyMap');

const sortArrayByArrayIds = (list, sort) => list.sort((a, b) => sort.indexOf(a._id.toString()) - sort.indexOf(b._id.toString()));

module.exports = async () => {
  const countryProxyMaps = await CountryProxyMap.find().sort({ country: 1 }).lean()
    .then(async countries => await Promise.all(countries.map(async (country) => {
      const proxyIds = country.list;
      country.list = await Proxy.find({ _id: { $in: proxyIds } }).lean();
      country.list = sortArrayByArrayIds(country.list, proxyIds);

      return country;
    })))
    .then(countries => countries.sort((a, b) => {
      if (a.country === 'all' || a.country === 'euro' || a.country === 'asia_ocean') { return -1; }
      if (b.country === 'all' || b.country === 'euro' || b.country === 'asia_ocean') { return 1; }
      // sort by country name
      if (a.country < b.country) { return -1; }
      if (a.country > b.country) { return 1; }

      return 0;
    }));

  return {
    error: 0,
    list: countryProxyMaps,
  };
};
