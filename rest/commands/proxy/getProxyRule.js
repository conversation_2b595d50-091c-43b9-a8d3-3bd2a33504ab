const Whitelist = require('../../model/audb/Whitelist');
const Blacklist = require('../../model/audb/Blacklist');
const BlacklistCountry = require('../../model/audb/BlacklistCountry');
const WhitelistISP = require('../../model/audb/WhitelistISP');
const { countriesFullNames } = require('../../constants/blacklist');

module.exports = async ({ userIP, userISP, userLocation }) => {
  let rule = 'none';

  if (await WhitelistISP.check(userISP)) {
    rule = 'whitelistedISP';
  } else if (await Whitelist.check(userIP, userISP)) {
    rule = await Whitelist.check(userIP, userISP) === userIP ? 'whitelistedIP' : 'whitelistedISP';
  } else {
    let userCountry = userLocation.countryName.toLowerCase();

    if (countriesFullNames[userCountry]) {
      userCountry = countriesFullNames[userCountry];
    }
    if (await Blacklist.check(userIP, null)) {
      rule = 'blockedIP';
    } else if (await Blacklist.check(null, userISP)) {
      rule = 'blockedISP';
    } else if (await BlacklistCountry.check(userCountry)) {
      rule = 'blockedCountry';
    }
  }

  return rule;
};
