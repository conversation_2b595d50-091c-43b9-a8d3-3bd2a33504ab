const Whitelist = require('../../model/audb/Whitelist');
const Blacklist = require('../../model/audb/Blacklist');
const BlacklistCountry = require('../../model/audb/BlacklistCountry');

module.exports = async ({ user, userIP, countryName, ISP }) => {
  if (user && user.UserConfig && user.UserConfig.skipbidproxy) return 0;

  const whitelist = await Whitelist.findOne({ _id: userIP }).exec();

  if (whitelist) return 0;

  const blockedCountry = await BlacklistCountry.findOne({ _id: countryName }).exec();

  if (blockedCountry) return blockedCountry.bt;

  const blockedISP = await Blacklist.findOne({ _id: ISP }).exec();

  if (blockedISP) return blockedISP.bt;

  const blockedIP = await Blacklist.findOne({ _id: userIP }).exec();

  if (blockedIP) return blockedIP.bt;

  return 0;
};
