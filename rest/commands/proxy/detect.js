const proxydetect = require('../../service/proxydetect');
const { getUserLocation } = require('../../service/maxmind');

module.exports = async ({ ip, appendLocation }) => {
  // do not check proxy for now, only for allowed APPs
  // if (force) return {
  //   error: 0,
  //   result: false,
  //   details: 'whitelisted',
  // };

  const data = await proxydetect.check(ip);

  if (appendLocation) data.userLocation = getUserLocation(ip);

  return data;
};
