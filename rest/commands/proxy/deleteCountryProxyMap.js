const CountryProxyMap = require('../../model/audb/CountryProxyMap');

module.exports = async (_id) => {
  if (!_id) throw new Error('CountryProxyMap ID is required');

  const countryProxyMap = await CountryProxyMap.findOne({ _id }).exec();

  if (!countryProxyMap) throw new Error('CountryProxyMap has been deleted');

  await countryProxyMap.remove();

  return {
    error: 0,
    success: true,
  };
};
