const Proxy = require('../../model/audb/Proxy');

module.exports = async (data) => {
  if (!data.name) throw new Error('Proxy name is required');
  if (!data.host) throw new Error('Proxy host is required');
  if (!data.port) throw new Error('Proxy port is required');
  if (!data.protocol) throw new Error('Proxy protocol is required');
  if (data.user && !data.pass) throw new Error('Proxy pass is required');
  if (data.pass && !data.user) throw new Error('Proxy user is required');

  let proxy;
  const dataForSave = Object.assign({}, data);
  delete dataForSave.created;
  delete dataForSave.updated;

  if (data._id) {
    proxy = await Proxy.findOneAndUpdate({ _id: data._id }, { $set: dataForSave }, { upsert: false, new: true, runValidators: true });
  } else {
    proxy = new Proxy(dataForSave);
    await proxy.save();
  }

  return {
    error: 0,
    proxy: { ...proxy._doc },
  };
};
