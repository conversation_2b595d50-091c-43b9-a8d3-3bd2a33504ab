const CountryProxyMap = require('../../model/audb/CountryProxyMap');

module.exports = async (data) => {
  if (!data.country) throw new Error('CountryProxyMap country is required');
  if (!data.list) throw new Error('CountryProxyMap list is required');

  let countryProxyMap;
  const dataForSave = Object.assign({}, data);
  dataForSave.list = data.list.map(proxy => proxy._id);
  delete dataForSave.created;
  delete dataForSave.updated;

  if (data._id) {
    countryProxyMap = await CountryProxyMap.findOneAndUpdate(
      { _id: data._id },
      { $set: dataForSave },
      { upsert: false, new: true, runValidators: true },
    );
  } else {
    countryProxyMap = new CountryProxyMap(dataForSave);
    await countryProxyMap.save();
  }

  const responseData = { ...countryProxyMap._doc };
  responseData.list = data.list;

  return {
    error: 0,
    countryProxyMap: responseData,
  };
};
