const Category = require('@s1/vod-models/Category');
const Vod = require('@s1/vod-models/Vod');

module.exports = async (id) => {
  const { name, ename, picture } = await Category
    .findOne(
      { id },
      { name: 1, ename: 1, picture: 1 },
    ).lean();

  const url = picture.length ? picture[0].big : null;

  const { description, edescription, id: seasonId } = await Category
    .findOne(
      { parent_id: id, ifonline: 1, name: { $not: /VIP/ } },
      { description: 1, edescription: 1, id: 1 },
      { sort: { sorder: -1 } },
    ).lean();

  const { year, genre, egenre } = await Vod
    .findOne(
      { cateid: seasonId },
      { year: 1, genre: 1, egenre: 1 },
    ).lean();

  return {
    source_subcategory_id: id,
    name,
    ename,
    picture: url,
    year,
    genre,
    egenre,
    description,
    edescription,
  };
};
