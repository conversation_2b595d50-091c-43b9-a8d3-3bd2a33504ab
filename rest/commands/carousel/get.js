const _ = require('lodash');
const CarouselItem = require('../../model/audb/CarouselItem');
const UserFavoriteVod = require('../../model/audb/UserFavoriteVod');
const UserFavoriteTvShow = require('../../model/audb/UserFavoriteTvShow');

const addIsInFav = async (items, user) => {
  const vodIds = [];
  const subCategoryIds = [];

  items.forEach((item) => {
    if (item.source_vod_id) vodIds.push(item.source_vod_id);
    else if (item.source_subcategory_id) subCategoryIds.push(item.source_subcategory_id);
  });

  const [favoriteVods, favoriteSubCategories] = await Promise.all([
    UserFavoriteVod.find({ chvod: { $in: vodIds }, uid: user.id }),
    UserFavoriteTvShow.find({ chvod: { $in: subCategoryIds }, uid: user.id }),
  ]);

  const favoriteVodsIds = favoriteVods.map(vod => vod.chvod);
  const favoriteSubCategoriesIds = favoriteSubCategories.map(vod => vod.chvod);

  return items.map((item) => {
    item.isinfav = false;

    if ((item.source_vod_id && favoriteVodsIds.includes(item.source_vod_id))
      || (item.source_subcategory_id && favoriteSubCategoriesIds.includes(item.source_subcategory_id))) {
      item.isinfav = true;
    }

    return item;
  });
};

module.exports = async ({ categoryId, useAllLocales, user, locale }) => {
  const filter = categoryId ? { category_id: categoryId } : {};

  let items = await CarouselItem.find(filter).sort({ sorder: 1 }).exec()
    .then(carouselItems => (_.map(carouselItems, carouselItem => carouselItem.format({ locale }))));

  if (!useAllLocales && user) {
    items = await addIsInFav(items, user);
  }

  return items;
};
