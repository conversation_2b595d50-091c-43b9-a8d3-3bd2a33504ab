const CarouselItem = require('../../model/audb/CarouselItem');
const getSeriesInfo = require('./getSeriesInfo');
const getMoviesInfo = require('./getMoviesinfo');

module.exports = async (isVod, linkTo, categoryId) => {
  try {
    const info = isVod ? await getMoviesInfo(linkTo) : await getSeriesInfo(linkTo);
    const data = { category_id: categoryId, ...info };
    await CarouselItem.create(data);

    return true;
  } catch (e) {
    throw new Error('On add carousel item error occurred');
  }
};
