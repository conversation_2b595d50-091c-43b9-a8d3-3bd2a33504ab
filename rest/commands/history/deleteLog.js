const { LogNotFound } = require('../../errors');
const i18n = require('../../helpers/geti18n');

const deleteLiveOrRadio = async ({ LogModel, uid, ISP, countryCode, stateCode, isradio = 0, locale }) => {
  await LogModel.find({ uid }).populate({
    path: 'Channel',
    populate: { path: 'channelGroup' },
  }).exec((err, logs) => {
    if (err) throw new Error(err);

    logs.forEach(async (log) => {
      if (log.Channel.isradio === isradio) await LogModel.deleteOne({ uid, _id: log.id });
    });
  });

  return LogModel.getHistory(uid, ISP, countryCode, stateCode, locale);
};

module.exports = async ({ LogModel, uid, removeOne, ISP, countryCode, stateCode, locale, vodid }) => {
  switch (removeOne) {
    case 'all':
      await LogModel.deleteMany({ uid });

      return LogModel.getHistory(uid, ISP, countryCode, stateCode, locale);
    case 'allLive':
      return await deleteLiveOrRadio({ LogModel, uid, ISP, countryCode, stateCode, locale });
    case 'allRadio':
      return await deleteLiveOrRadio({ LogModel, uid, ISP, countryCode, stateCode, isradio: 1, locale });
    default:
      const log = await LogModel.findOne({ _id: removeOne, uid }).exec();
      i18n.setLocale(locale);

      if (!log) throw new LogNotFound(i18n.__('%s not found #%s', LogModel.modelName, removeOne));

      await log.deleteOne();

      return LogModel.getHistory(uid, ISP, countryCode, stateCode, locale);
  }
};
