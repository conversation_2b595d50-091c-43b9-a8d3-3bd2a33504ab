const moment = require('moment');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserDailyStatistics = require('../../model/audb/UserDailyStatistics');

const MAX_LOGS_PER_DAY = 5;
const TOTAL_CALCULATED_DAYS = 90;
const MAX_SCORE = 10;
const MAX_WATCHES = 100;
const COLORS_SCORE_10 = [
  'rgb(255, 0, 0)',
  'rgb(190, 38, 0)',
  'rgb(190, 76, 0)',
  'rgb(255, 140, 0)',
  'rgb(220, 200, 0)',
  'rgb(200, 200, 0)',
  'rgb(114, 152, 0)',
  'rgb(76, 152, 0)',
  'rgb(38, 152, 0)',
  'rgb(0, 152, 0)',
  'rgb(0, 114, 0)',
];

const getAggregationStats = (userId, calculationDays) => {
  const dateFrom = moment().add(`-${calculationDays}`, 'days').startOf('day').unix();
  const dateTo = moment().subtract(1, 'day').endOf('day').unix();

  const aggregation = [
    { $match: {
      $and: [
        { userId },
        { date: { $gte: dateFrom } },
        { date: { $lte: dateTo } },
      ],
    } },
  ];

  return aggregation;
};

const getAggregationTodayLogs = (fieldName, userId) => {
  const dateTo = moment().unix();
  const dateFrom = moment().startOf('day').unix();

  return [
    { $match: {
      $and: [
        { uid: userId },
        { playtime: { $gte: dateFrom } },
        { playtime: { $lte: dateTo } },
      ],
    } },
    {
      $addFields: {
        sourceDate: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: {
              $add: [
                new Date(0),
                { $multiply: [1000, '$playtime'] },
              ],
            },
          },
        },
      },
    },
    // Count all occurrences
    { $group: {
      _id: {
        sourceId: `$${fieldName}`,
        time: '$sourceDate',
      },
      count: { $sum: 1 },
    } },
    // Sum all occurrences and count distinct
    { $group: {
      _id: {
        date: '$_id.time',
      },
      totalCount: { $sum: '$count' },
      distinctCount: { $sum: 1 },
    } },
  ];
};

const addCountsByDay = (watchesByDate, watchLogs) => {
  watchLogs.forEach((watchLog) => {
    if (watchesByDate.hasOwnProperty(watchLog._id.date)) {
      watchesByDate[watchLog._id.date] += watchLog.distinctCount;
    } else {
      watchesByDate[watchLog._id.date] = watchLog.distinctCount;
    }
  });

  return watchesByDate;
};

/**
 * @param {number} watchingLogsCount
 * @return {object} - { score, color }
 * */
function calculateScoreAndColor(watchingLogsCount) {
  const normalizedCount = Math.min(watchingLogsCount, MAX_WATCHES);
  const score = Math.round((normalizedCount / MAX_WATCHES) * MAX_SCORE);

  const color = COLORS_SCORE_10[score];

  return { score, color, watchingLogsCount, calculatedDays: TOTAL_CALCULATED_DAYS };
}

/**
 * @description Method calculate watching activities score and color from the last user activities for last XX days with max per days
 *
 * @param {number} userId
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (userId) => {
  if (!userId) return calculateScoreAndColor(0);

  const vodLogs = await UserLogVod.aggregate(getAggregationTodayLogs('vodid', userId)).cache(300).exec();
  const liveLogs = await UserLogLive.aggregate(getAggregationTodayLogs('channel', userId)).cache(300).exec();
  const recordLogs = await UserLogRecord.aggregate(getAggregationTodayLogs('show._id', userId)).cache(300).exec();

  let watchesByDate = {};
  watchesByDate = addCountsByDay(watchesByDate, vodLogs);
  watchesByDate = addCountsByDay(watchesByDate, liveLogs);
  watchesByDate = addCountsByDay(watchesByDate, recordLogs);

  let activities = 0;

  for (const [, value] of Object.entries(watchesByDate)) {
    if (value > MAX_LOGS_PER_DAY) activities += MAX_LOGS_PER_DAY;
    else activities += value;
  }

  const stats = await UserDailyStatistics.aggregate(getAggregationStats(userId, TOTAL_CALCULATED_DAYS)).cache(300).exec();

  for (const stat of stats) {
    const totalUniqueSources = (stat.uniqueChannelsCount || 0) + (stat.uniqueRecordsCount || 0) + (stat.uniqueVodsCount || 0);

    if (totalUniqueSources > MAX_LOGS_PER_DAY) activities += MAX_LOGS_PER_DAY;
    else activities += totalUniqueSources;
  }

  const scoreResult = calculateScoreAndColor(activities);

  return scoreResult;
};
