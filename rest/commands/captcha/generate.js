const moment = require('moment');
const uuid = require('uuid-mongodb');
const svgCaptcha = require('svg-captcha');
const Captcha = require('../../model/audb/Captcha');

module.exports = async ({ width = 140, height = 34 }) => {
  const captcha = svgCaptcha.create({ width, height, fontSize: 34, noise: 2 });
  const expires = moment().add(5, 'minutes').unix();
  const token = uuid.v4();
  await Captcha.create({ value: captcha.text, token, expires });

  return { image: captcha.data, token };
};
