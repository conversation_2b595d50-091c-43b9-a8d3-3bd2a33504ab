const getAllAppsAccesses = require('./getAllAppsAccesses');
const User = require('../../model/audb/User');
const getAllPaymentTypes = require('../payment/getAllTypes');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../payment/helpers/rules/isStealerUser');
const isUserHasPaymentsWithTypes = require('../../helpers/isUserHasPaymentsWithTypes');
const { IOS_TYPES_LIST, ANDROID_TYPES_LIST } = require('../../constants/app');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');
const { USER_GROUPS } = require('../../constants/testers');

const checkUserAppAccess = async (user, appAccess, paymentTypes) => {
  if (appAccess.denyBlacklistedUser) {
    const isUserBlacklisted = await isBlacklistedUser({ user });

    if (isUserBlacklisted) return { appAccess, allowed: false };
  }
  if (appAccess.denyStealerUser) {
    const isUserStealer = await isStealerUser({ user });

    if (isUserStealer) return { appAccess, allowed: false };
  }
  if (user.isNgateUser) return { appAccess, allowed: false };

  const allowedPaymentTypeNames = appAccess.allowedPaymentTypes ? appAccess.allowedPaymentTypes.map((_id) => {
    return paymentTypes.hasOwnProperty(_id) ? paymentTypes[_id].name : null;
  }).filter(type => !!type) : [];

  if (allowedPaymentTypeNames.length) {
    const isUserHasPayments = await isUserHasPaymentsWithTypes(user.id, allowedPaymentTypeNames);

    if (isUserHasPayments) {
      const appName = appAccess.AppVersion.appName;
      let allowed = true;
      let webcug;

      if (ANDROID_TYPES_LIST.includes(appAccess.AppVersion.osType)) {
        webcug = await getUserGroupForWebcamApp(user, appName, appAccess);
      } else if (IOS_TYPES_LIST.includes(appAccess.AppVersion.osType)) {
        webcug = await getUserGroupForWebcamApp(user, appName);
      }
      if (webcug === USER_GROUPS.regular) {
        allowed = false;
      }

      return { appAccess, allowed };
    }
  }

  return { appAccess, allowed: false };
};

module.exports = async (userId) => {
  const appsAccessesResult = await getAllAppsAccesses();
  const user = await User.findOne({ id: userId }).exec();
  const allPaymentTypesResult = await getAllPaymentTypes();
  const allPaymentTypes = allPaymentTypesResult.list;
  const paymentTypes = {};
  allPaymentTypes.forEach((type) => {
    paymentTypes[type._id] = type;
  });

  const promises = [];

  appsAccessesResult.list.forEach((appAccess) => {
    promises.push(checkUserAppAccess(user, appAccess, paymentTypes));
  });
  const results = (await Promise.all(promises))
    .filter(result => result.allowed)
    .map((result) => {
      const app = result.appAccess.AppVersion;
      delete app.modifiedByName;
      delete app.modifiedByUid;
      delete app.updated;

      return app;
    });

  return { error: 0, list: results };
};
