const AppImage = require('../../model/audb/AppImage');
const config = require('../../../config');

module.exports = async () => {
  const result = await AppImage.findOne({}).lean() || {};

  if (result.androidTvBackground) result.androidTvBackgroundUrl = `https://${config.baseImagesPath}/images/tinyfm${result.androidTvBackground}`;
  if (result.androidMobileBackground) result.androidMobileBackgroundUrl = `https://${config.baseImagesPath}/images/tinyfm${result.androidMobileBackground}`;

  return { error: 0, result };
};
