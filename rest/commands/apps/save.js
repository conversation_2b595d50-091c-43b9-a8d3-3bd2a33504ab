const { App<PERSON><PERSON><PERSON>, AndroidAppVersion, IosAppVersion, RokuAppVersion } = require('../../model/audb/AppVersion');
const { FILTER_OS_TYPES } = require('../../constants/app');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

/**
 * Save or update APP
 *
 * @param {Object} user - admin user
 * @param {Object} data - data for save
 * */
module.exports = async ({ user, data }) => {
  if (!user) throw new Error('Not authorized');
  if (!data.osType) throw new Error('APP OS type is required');

  let app;
  let Model;

  switch (data.osType) {
    case FILTER_OS_TYPES.ios:
      Model = IosAppVersion;
      break;
    case FILTER_OS_TYPES.android:
      if (!data.appLink) throw new Error('APP link is required');
      if (!data.type) throw new Error('APP type is required');
      if (!data.version) throw new Error('APP version is required');
      if (data.force && !data.forceVersion) throw new Error('APP force version is required');

      Model = AndroidAppVersion;
      break;
    case FILTER_OS_TYPES.roku:
      Model = RokuAppVersion;
      break;
    default:
      Model = AppVersion;
      break;
  }

  if (data._id) app = await Model.findOne({ _id: data._id }).exec();
  else app = new Model();

  Object.entries(data).forEach(([key, value]) => {
    app[key] = value;
  });

  if (!data.hasOwnProperty('force') || !data.force) app.forceVersion = undefined;

  app.modifiedByUid = user.id;
  app.modifiedByName = user.name;

  await app.save();
  await removeRedisCacheByKeys(['appAccess']);

  return { error: 0, app: { ...app._doc } };
};
