const getCombinedPostersCommand = require('../../poster/getCombined');
const getContinueMoviesCommand = require('../../continue/getMovies');
const getContinueEpisodesCommand = require('../../continue/getEpisodes');
const getContinueRecordsCommand = require('../../continue/getRecords');
const getPopularVodsCommand = require('../../popular/getVods');
const getPopularRecordsCommand = require('../../popular/getRecords');
const getSubCategoriesCommand = require('../../vod/getSubCategories');
const getGroupsCommand = require('../../channel/getGroups');
const getRecommendedCommand = require('../../vod/getRecommended');
const getWhatsNewVod = require('../../vod/getWhatsNew');
const {
  CHANNELS_REGEXP,
  POSTERS_REGEXP,
  WHATS_NEW_REGEXP,
  RECOMMENDED_REGEXP,
  SUBCATEGORIES_REGEXP,
  CONTINUE_REGEXP,
  AVAILABLE_ITEMS,
  POPULAR_REGEXP,
} = require('./homepageItems');
const PopularEpisodes = require('../../../model/audb/PopularEpisodes');
const PopularMovies = require('../../../model/audb/PopularMovies');

const KEY_VALUE_DELIMITER = ':';
const DEFAULT_DAYS_FOR_POPULAR_RECORDS = 1;
const DEFAULT_PAGE_SIZE = 10;

class HomepageCommandFactory {
  constructor(baseOptions) {
    this.baseOptions = baseOptions;
  }

  getCommand(key) {
    let command = () => { };
    const resolvedOptions = {};

    if (!AVAILABLE_ITEMS.some(regexp => regexp.test(key))) return;
    if (CHANNELS_REGEXP.test(key)) command = getGroupsCommand;
    if (POSTERS_REGEXP.test(key)) command = getCombinedPostersCommand;
    if (WHATS_NEW_REGEXP.test(key)) command = getWhatsNewVod;
    if (WHATS_NEW_REGEXP.test(key)) {
      const [, limit] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.limit = Number(limit) || DEFAULT_PAGE_SIZE;
    }
    if (CHANNELS_REGEXP.test(key) || POSTERS_REGEXP.test(key)) {
      const [, page, pageSize] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.page = Number(page) || 1;
      resolvedOptions.pageSize = Number(pageSize) || (POSTERS_REGEXP.test(key) ? 5 : DEFAULT_PAGE_SIZE);
    }
    if (RECOMMENDED_REGEXP.test(key)) {
      const [, type, page, pageSize] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.type = type;
      resolvedOptions.page = Number(page) || 1;
      resolvedOptions.pageSize = Number(pageSize) || DEFAULT_PAGE_SIZE;
      command = getRecommendedCommand;
    }
    if (SUBCATEGORIES_REGEXP.test(key)) {
      const [, id, page, pageSize] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.id = id;
      resolvedOptions.page = Number(page) || 1;
      resolvedOptions.pageSize = Number(pageSize) || DEFAULT_PAGE_SIZE;
      command = getSubCategoriesCommand;
    }
    if (CONTINUE_REGEXP.test(key)) {
      const [, type, page, pageSize] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.page = Number(page) || 1;
      resolvedOptions.pageSize = Number(pageSize) || DEFAULT_PAGE_SIZE;

      switch (type) {
        case 'movies':
          command = getContinueMoviesCommand;
          break;
        case 'episodes':
          command = getContinueEpisodesCommand;
          break;
        case 'records':
          command = getContinueRecordsCommand;
          break;
        default:
          break;
      }
    }
    if (POPULAR_REGEXP.test(key)) {
      const [, type, days, page, pageSize] = key.split(KEY_VALUE_DELIMITER);
      resolvedOptions.days = days || DEFAULT_DAYS_FOR_POPULAR_RECORDS;
      resolvedOptions.page = Number(page) || 1;
      resolvedOptions.pageSize = Number(pageSize) || (type === 'records' ? 5 : DEFAULT_PAGE_SIZE);
      switch (type) {
        case 'movies':
          resolvedOptions.Model = PopularMovies;
          command = getPopularVodsCommand;
          break;
        case 'episodes':
          resolvedOptions.Model = PopularEpisodes;
          command = getPopularVodsCommand;
          break;
        case 'records':
          command = getPopularRecordsCommand;
          break;
        default:
          break;
      }
    }

    return extraOptions => command({
      ...this.baseOptions,
      ...extraOptions,
      ...resolvedOptions,
    });
  }
}

module.exports = HomepageCommandFactory;
