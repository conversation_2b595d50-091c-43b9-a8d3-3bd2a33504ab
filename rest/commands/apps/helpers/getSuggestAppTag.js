const isBlacklistedUser = require('../../payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../../payment/helpers/rules/isStealerUser');
const getUserAllowedApps = require('../../apps/getUserAllowedApps');

const isAndroidOS = (userAgent) => /android|andr0id/.test(userAgent);

const isIphoneIpad = (userAgent) => /iphone|ipad/.test(userAgent);

const isMobile = (userAgent) => /tablet|mobile|iphone|ipad|ipod|webOS|blackberry|windows phone|opera mini|iemobile/.test(userAgent);

const isTv = (userAgent) => /tv|smarttv|smart-tv|hbbtv|appletv|googletv|netcast|vestel|vstvb|viera|mibox|omi|playstation|bravia|dlnadoc|sonydtv|inettvbrowser|webos|tizen|jiosphere/.test(userAgent)
  || /aft\w+/.test(userAgent)
  || (/samsung/.test(userAgent) && !/samsungbrowser/.test(userAgent));

/**
 * Check allowed app by name
 * @example appName = 'EcamSync_IOS' (appName + '_' + osType);
 *
 * @param {object} user
 * @param {string} appName
 * @return Promise<boolean>
 * */
const hasAppAccess = async ({ user, appName }) => {
  const allowedAppsResult = await getUserAllowedApps(user.id);

  const allowedAppsNames = (allowedAppsResult.list || []).map((app) => `${app.appName}_${app.osType}`);

  return allowedAppsNames.includes(appName);
};

/**
 * Check user agent is there available suggested apps or sites for main site
 *
 * @param {object} option.user - current user
 * @param {string} option.userAgent - current user userAgent
 * */
const getMainSiteNoticeTag = async ({ user, userAgent }) => {
  let tag = null;

  if (isAndroidOS(userAgent)) {
    if (isTv(userAgent)) {
      tag = 'suggest_app_android_tv';
    } else {
      tag = 'suggest_app_android_mobile';
    }
  } else if (isMobile(userAgent) && isIphoneIpad(userAgent)) {
    const hasAccess = await hasAppAccess({ user, appName: 'EcamSync_IOS' });

    if (hasAccess) tag = 'suggest_app_ios_mobile';
  } else if (isTv(userAgent)) {
    tag = 'suggest_app_tv_site';
  }

  return tag;
};

/**
 * Check user agent is there available suggested apps or sites for tv site
 *
 * @param {object} option.user - current user
 * @param {string} option.userAgent - current user userAgent
 * */
const getTvSiteNoticeTag = async ({ user, userAgent }) => {
  let tag = null;

  if (isAndroidOS(userAgent)) {
    if (isTv(userAgent)) {
      tag = 'suggest_app_android_tv';
    } else {
      tag = 'suggest_app_android_mobile';
    }
  } else if (isMobile(userAgent) && isIphoneIpad(userAgent)) {
    const hasAccess = await hasAppAccess({ user, appName: 'EcamSync_IOS' });

    if (hasAccess) tag = 'suggest_app_ios_mobile';
  }

  return tag;
};

/**
 * Check user agent is there available suggested apps or sites for user visited page
 *
 * @param {object} user
 * @param {object} data
 * @param {string} data.userAgent - required
 * @param {string} data.from - required, user visited page or other source (mainSite or tvSite)
 * */
module.exports = async (user, data) => {
  if (!user || !data || !data.userAgent || !data.from) return { error: 0, notice: null };

  const userAgent = data.userAgent.toLowerCase();
  let tag = null;
  const [isBlacklisted, isStealer] = await Promise.all([
    user.skipBlacklistPayment ? false : isBlacklistedUser({ user }),
    isStealerUser({ user }),
  ]);

  if (!isBlacklisted && !isStealer) {
    switch (data.from) {
      case 'mainSite':
        tag = await getMainSiteNoticeTag({ user, userAgent });
        break;
      case 'tvSite':
        tag = await getTvSiteNoticeTag({ user, userAgent });
        break;
      default:
        break;
    }
  }

  return tag;
};
