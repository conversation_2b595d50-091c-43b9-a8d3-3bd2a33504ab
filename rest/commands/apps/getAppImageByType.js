const AppImage = require('../../model/audb/AppImage');
const config = require('../../../config');

module.exports = async (type) => {
  if (!type) throw new Error('Image type is required');

  const result = await AppImage.findOne({}).lean();

  if (!result || !result.hasOwnProperty(type) || !result[type]) throw new Error('The image does not exist');

  const imagePath = result[type];

  return { error: 0, path: `https://${config.baseImagesPath}/images/tinyfm${imagePath}` };
};
