const moment = require('moment');
const Notice = require('../../model/audb/Notice');
const getSuggestAppTag = require('./helpers/getSuggestAppTag');

const getSuggestedNotices = async () => {
  const now = moment().unix();

  const notices = await Notice.find({
    $and: [
      { starttime: { $lte: now } },
      { endtime: { $gte: now } },
      { tagname: { $exists: true } },
      { tagname: /suggest_app_/ },
    ]
  }).cache(300).lean();

  return notices;
};

/**
 * Check user agent is there available suggested apps or sites for user visited page
 *
 * @param {object} user
 * @param {object} data
 * @param {string} data.userAgent - required
 * @param {string} data.from - required, user visited page or other source (mainSite or tvSite)
 * */
module.exports = async (user, data) => {
  if (!user || !data || !data.userAgent || !data.from) return { error: 0, notice: null };

  let notice = null;
  const tag = await getSuggestAppTag(user, data);

  if (tag) {
    const notices = await getSuggestedNotices();
    notice = notices.find(value => value.tagname === tag);

    if (!notice) {
      console.error(`Notice is not configured for tag: ${tag}`);
    }
  }

  return { error: 0, notice };
};
