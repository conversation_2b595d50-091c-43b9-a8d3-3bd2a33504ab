const HomepageCommandFactory = require('./helpers/HomepageCommandFactory');

module.exports = async ({ items, user, ISP, countryCode, stateCode, locale, sid }) => {
  const commandFactory = new HomepageCommandFactory({ user, ISP, stateCode, countryCode, locale, sid });
  const commands = items.map(item => ({
    key: item,
    executor: commandFactory.getCommand(item),
  }));
  const extraOptions = {
    uid: user.id,
    page: 1,
    pageSize: 10,
    liteQuery: false,
    allchannel: false,
    isradio: 0,
    period: 'month',
    periodLimit: 100,
    limit: 10,
  };
  const result = {};
  await Promise.all(commands.map(async (command) => {
    const [key, subkey] = command.key.split(':');

    // checking for single key with limits, paginations and without subkey
    // channels, posters:1:10, new:10 - without subkey
    // popular:records, continue:records - with subkey
    if (subkey && !Number(subkey)) {
      if (!result[key]) result[key] = {};

      result[key][subkey] = await command.executor(extraOptions);
    } else result[key] = await command.executor(extraOptions);
  }));

  return result;
};
