const AppAccess = require('../../model/audb/AppAccess');

/**
 * Save or update APP access
 * @param {Object} data - data for save
 * @param {Object} user - admin user
 * */
module.exports = async (data, user) => {
  if (!user) throw new Error('Not authorized');
  if (!data.appId) throw new Error('APP is required');
  if (!data.loginUrl) throw new Error('APP login url is required');

  let appAccess;

  if (data._id) {
    appAccess = await AppAccess.findOne({ _id: data._id }).exec();
  } else {
    appAccess = new AppAccess();
  }

  Object.entries(data).forEach(([key, value]) => {
    appAccess[key] = value;
  });

  appAccess.modifiedByUid = user.id;
  appAccess.modifiedByName = user.name;

  await appAccess.save();

  return { error: 0, appAccess: appAccess.toObject() };
};
