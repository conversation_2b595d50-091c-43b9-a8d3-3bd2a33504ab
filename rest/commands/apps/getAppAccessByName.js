const AppAccess = require('../../model/audb/AppAccess');
const { AppVersion } = require('../../model/audb/AppVersion');

/**
 * @description Get APP access configs by appName
 *
 * @param {String} appName
 * */
module.exports = async (appName) => {
  if (!appName) return false;

  const appVersion = await AppVersion.findOne({ appName }).lean();

  if (appVersion) {
    const appAccess = await AppAccess.findOne({ appId: appVersion._id }).populate('AppVersion').cache(3600, `appAccess_appId_${appVersion._id}`).lean();

    if (appAccess) return appAccess;
  }

  return null;
};
