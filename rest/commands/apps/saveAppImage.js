const AppImage = require('../../model/audb/AppImage');

/**
 * Save or update APP access
 * @param {Object} data - data for save
 * @param {Object} user - admin user
 * */
module.exports = async (data, user) => {
  if (!user) throw new Error('Not authorized');

  let appImage;

  if (data._id) {
    appImage = await AppImage.findOne({ _id: data._id }).exec();
  } else {
    appImage = new AppImage();
  }

  Object.entries(data).forEach(([key, value]) => {
    appImage[key] = value;
  });

  await appImage.save();

  return { error: 0, success: true };
};
