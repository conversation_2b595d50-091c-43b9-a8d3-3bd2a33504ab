const AppAccess = require('../../model/audb/AppAccess');
const { AppVersion } = require('../../model/audb/AppVersion');

/**
 * @description Get APP access configs by appName
 *
 * @param {String} appName
 * */
module.exports = async (packageName, versionName) => {
  if (!packageName || !versionName) return null;

  const appVersion = await AppVersion.findOne({ packageName, versionName }).lean();

  if (appVersion) {
    const appAccess = await AppAccess.findOne({ appId: appVersion._id }).populate('AppVersion').cache(3600, `appAccess_appId_${appVersion._id}`).lean();

    if (appAccess) return appAccess;
  }

  return null;
};
