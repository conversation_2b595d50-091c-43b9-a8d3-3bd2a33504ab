const { AppVersion } = require('../../model/audb/AppVersion');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('App ID is required');

  const app = await AppVersion.findOne({ _id }).exec();

  if (!app) throw new Error('App has been deleted');

  await app.remove();
  await removeRedisCacheByKeys(['appAccess']);

  return {
    error: 0,
    success: true,
  };
};
