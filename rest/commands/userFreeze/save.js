const { WrongParamsError, <PERSON><PERSON><PERSON><PERSON><PERSON>, DelayEndTimeExpiredError, DelayCannotFreezeExpiredError } = require('@s1/api-errors');
const moment = require('moment');
const UserFreeze = require('../../model/audb/UserFreeze');
const User = require('../../model/audb/User');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

const getParsedUnixDate = (date) => {
  const unixDate = moment(date).unix();

  return unixDate;
};

/**
 * This method changes the user's freeze field. You can set when the user will be blocked.
 *
 * @async
 *
 * @param {Object} data - post body
 * @param {Object} admin - admin user
 * @param {number} data.uid - User ID
 * @param {string} data.starttime - String date - '2020-02/28 00:00'
 * @param {string} data.endtime - String date - '2021-02/27 00:00'
 *
 * @returns {Promise<Object>}
 */
module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();

  const { uid } = data;

  if (!uid) throw new ApiError(903, 'User ID is required');

  const userId = parseInt(uid);
  const currentUser = await User.findOne({ id: userId }).lean();

  if (!currentUser) throw new ApiError(903, 'User not found');
  // for admin or support do not need to check is blacklisted or block him
  if (isAdminOrSupportUser(currentUser)) throw new ApiError(903, 'Cannot freeze admin or support user');

  let { starttime, endtime } = data;
  const nowTime = moment().unix();
  const endTime = moment().add(1, 'year').unix();

  if (starttime && typeof starttime === 'string') starttime = getParsedUnixDate(starttime) || nowTime;
  if (endtime && typeof endtime === 'string') endtime = getParsedUnixDate(endtime) || endTime;
  if (!starttime) starttime = nowTime;
  if (!endtime) endtime = endTime;

  starttime = starttime < nowTime ? nowTime : starttime;

  if (endtime <= starttime) throw new DelayEndTimeExpiredError();
  if (currentUser.expires <= nowTime) throw new DelayCannotFreezeExpiredError();

  let userFreeze;

  const [user, activeFreeze] = await Promise.all([
    User.findOne({ id: uid }, { expires: 1 }).exec(),
    UserFreeze.findOne({ uid, working: true }).exec(),
  ]);

  if (data._id) {
    userFreeze = await UserFreeze.findOne({ _id: data._id }).exec();
    userFreeze.orgtime = user.expires;
    userFreeze.newtime = user.expires - nowTime + endtime;
  } else {
    if (activeFreeze) throw new Error('User already has active freeze');

    userFreeze = new UserFreeze();
    userFreeze.uid = userId;
    userFreeze.starttime = starttime;
    userFreeze.endtime = endtime;
    userFreeze.orgtime = user.expires;
    userFreeze.newtime = user.expires - nowTime + endtime;
    userFreeze.working = true;

    if (admin) {
      userFreeze.addedByUid = admin.id;
      userFreeze.addedByName = admin.name;
    }
  }
  if (admin) {
    userFreeze.modifiedByUid = admin.id;
    userFreeze.modifiedByName = admin.name;
  }

  await removeRedisCacheByKeys([`paymentBlacklist_id_${userId}`]);

  await userFreeze.save();

  return {
    error: 0,
    result: userFreeze.toObject(),
  };
};
