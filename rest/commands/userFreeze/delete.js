const { ApiError } = require('@s1/api-errors');
const UserFreeze = require('../../model/audb/UserFreeze');
// const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ _id, adminUser }) => {
  if (!_id) throw new ApiError(903, 'ID is required');

  // TODO move logic from the dismissdelay.php
  const removeResult = await UserFreeze.findOneAndDelete({ _id }).exec();

  return {
    error: 0,
    success: !!removeResult,
    result: removeResult,
  };
};
