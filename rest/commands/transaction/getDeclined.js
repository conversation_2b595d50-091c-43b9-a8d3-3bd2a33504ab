const moment = require('moment');
const DeclinedTransaction = require('../../model/audb/DeclinedTransaction');
const User = require('../../model/audb/User');

module.exports = async ({ daysLeft }) => {
  const firstFailTime = moment().add(-12, 'hours').unix();
  const allDeclines = await DeclinedTransaction.find({ firstFailTime: { $gt: firstFailTime } }).exec();
  const usersTransactions = [];
  const usersTransactionsIds = [];
  allDeclines.forEach((decline) => {
    usersTransactions[Number(decline.user)] = decline;
    usersTransactionsIds.push(decline.user);
  });
  const dateThreshold = moment().add(daysLeft, 'days').unix();
  const users = await User.find({ id: { $in: usersTransactionsIds }, expires: { $lt: dateThreshold } }).lean().exec();
  const declines = [];
  users.forEach((user) => { declines.push(usersTransactions[user.id]); });

  return declines;
};

