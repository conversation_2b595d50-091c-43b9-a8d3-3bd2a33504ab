const Player = require('../../model/audb/Player');
const _getAll = require('./getAll');
const getNextModelId = require('../../helpers/getNextModelId');

module.exports = async (playerId, data) => {
  try {
    data.playerid = playerId || await getNextModelId(Player);
    await Player.findOneAndUpdate(
      { playerid: playerId },
      { $set: data },
      { new: true, upsert: true },
    ).exec();
    const result = await _getAll();

    return result;
  } catch (error) {
    throw new Error(error);
  }
};
