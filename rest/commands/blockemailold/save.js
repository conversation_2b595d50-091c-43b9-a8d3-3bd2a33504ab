const BlockedEmail = require('../../model/audb/BlockedEmail');
const _getAll = require('./getAll');

module.exports = async (data) => {
  try {
    const isArray = data => Array.isArray(data);
    let blockedEmails = [];

    if (isArray(data)) blockedEmails = data;
    else blockedEmails.push(data);

    for (let i = 0; i < blockedEmails.length; ++i) {
      const blockedEmail = blockedEmails[i];

      if (!blockedEmail.emaildomain) continue;

      delete blockedEmail._id;
      delete blockedEmail.editing;
      delete blockedEmail.created;
      delete blockedEmail.updated;
      blockedEmail.emaildomain = blockedEmail.emaildomain.toLowerCase();
      await BlockedEmail.findOneAndUpdate(
        { emaildomain: blockedEmail.emaildomain },
        { $set: blockedEmail },
        { upsert: true, new: true },
      ).exec();
    }

    return await _getAll();
  } catch (error) {
    throw new Error(error);
  }
};
