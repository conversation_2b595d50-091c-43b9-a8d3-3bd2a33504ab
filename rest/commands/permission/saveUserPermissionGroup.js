const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');
const User = require('../../model/audb/User');

module.exports = async ({ user, data }) => {
  if (!data.name) throw new Error('Permission group name is required');

  let userPermissionGroup;

  if (data._id) userPermissionGroup = await UserPermissionGroup.findOne({ _id: data._id }).exec();
  else userPermissionGroup = new UserPermissionGroup();

  userPermissionGroup.name = data.name;
  userPermissionGroup.description = data.description || '';
  userPermissionGroup.isUserDefaultGroup = data.isUserDefaultGroup;
  userPermissionGroup.isUserGroup = data.isUserGroup;

  let currentDefaultPermissionGroup;

  if (data.isUserDefaultGroup) {
    currentDefaultPermissionGroup = await UserPermissionGroup.findOne({ isUserDefaultGroup: true }).lean().cache(3600, 'userPermissionGroup_default');

    // unset default group
    if (currentDefaultPermissionGroup && (!data._id || currentDefaultPermissionGroup._id.toString() !== data._id.toString())) {
      await UserPermissionGroup.updateMany({ isUserDefaultGroup: true }, { $set: { isUserDefaultGroup: false } }, { upsert: false }).exec();
    }
  }

  userPermissionGroup.modifiedByUid = user.id;
  userPermissionGroup.modifiedByName = user.name;

  await userPermissionGroup.save();

  if (data.isUserDefaultGroup
    && (!currentDefaultPermissionGroup || userPermissionGroup._id.toString() !== currentDefaultPermissionGroup._id.toString())) {
    // update users default permission group
    if (currentDefaultPermissionGroup && data._id && currentDefaultPermissionGroup._id.toString() !== data._id.toString()) {
      await User.updateMany(
        { permissionGroups: currentDefaultPermissionGroup._id.toString() },
        {
          $pull: { permissionGroups: currentDefaultPermissionGroup._id.toString() },
          $push: { permissionGroups: userPermissionGroup._id.toString() },
        },
        { upsert: false },
      ).exec();
    }

    // set default permission group for the users without permission groups
    await User.updateMany(
      { $or: [
        { permissionGroups: { $exists: false } },
        { permissionGroups: [] },
        { permissionGroups: null },
      ] },
      { $set: { permissionGroups: [userPermissionGroup._id.toString()] } },
      { upsert: false },
    ).exec();
  }

  return {
    error: 0,
    userPermissionGroup,
  };
};
