const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');

/**
 * Get all permission groups for users
 * */
module.exports = async () => {
  const userPermissionGroup = await UserPermissionGroup.aggregate([
    {
      $lookup: {
        from: 'paymentGroupRule',
        localField: '_id',
        foreignField: 'userPermissionGroups',
        as: 'paymentGroupRules',
      },
    },
  ]).exec()
    .then(results => results.map((permissionGroup) => {
      const groupsSet = new Set();
      const typesSet = new Set();
      const accountsSet = new Set();
      permissionGroup.paymentGroupRulesNames = [];

      permissionGroup.paymentGroupRules.forEach((groupRule) => {
        permissionGroup.paymentGroupRulesNames.push(groupRule.name);

        if (groupRule.paymentGroups && groupRule.paymentGroups.length) {
          groupRule.paymentGroups.forEach(id => groupsSet.add(id));
        }
        if (groupRule.paymentTypes && groupRule.paymentTypes.length) {
          groupRule.paymentTypes.forEach(id => typesSet.add(id));
        }
        if (groupRule.paymentAccounts && groupRule.paymentAccounts.length) {
          groupRule.paymentAccounts.forEach(id => accountsSet.add(id));
        }
      });

      permissionGroup.payments = {
        groups: Array.from(groupsSet),
        types: Array.from(typesSet),
        accounts: Array.from(accountsSet),
      };

      delete permissionGroup.paymentGroupRules;

      return permissionGroup;
    }));

  return {
    error: 0,
    list: userPermissionGroup,
  };
};
