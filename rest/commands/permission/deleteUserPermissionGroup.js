const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');
const User = require('../../model/audb/User');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('User permission group ID is required');

  const userPermissionGroup = await UserPermissionGroup.findOne({ _id }).exec();

  if (!userPermissionGroup) throw new Error('User permission group has been deleted');
  if (userPermissionGroup.isUserDefaultGroup) throw new Error('Remove default user permission group is not allowed');

  await userPermissionGroup.remove();
  // remove group from the user permission groups for all users
  await User.updateMany({ permissionGroups: _id }, { $pull: { permissionGroups: _id } }, { upsert: false }).exec();
  const defaultUserPermissionGroup = await UserPermissionGroup.findOne({ isUserDefaultGroup: true }).exec();

  // set default permission group for the users without permission groups
  if (defaultUserPermissionGroup) {
    await User.updateMany(
      { $or: [
        { permissionGroups: { $exists: false } },
        { permissionGroups: [] },
        { permissionGroups: null },
      ] },
      { $set: { permissionGroups: [defaultUserPermissionGroup._id] } },
      { upsert: false },
    ).exec();
  }

  return {
    error: 0,
    success: true,
  };
};
