/**
 * Calculate discount amount in USD
 *
 * @param {number} price - base package price
 * @param {object} discount - discount for current type
 *
 * @return {number} discount price in USD
 * */
module.exports = (price, discount) => {
  if (discount) {
    if (discount.type === 'percentage') return Math.round(price * (discount.value / 100) * 100) / 100;

    return discount.value;
  }

  return 0;
};
