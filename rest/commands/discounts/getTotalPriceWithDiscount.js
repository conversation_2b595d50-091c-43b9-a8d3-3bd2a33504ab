/**
 * Calculate total price with discount in USD
 *
 * @param {number} price - base package price
 * @param {object} discount - discount for current type
 *
 * @return {number} discountedPrice
 * */
module.exports = (price, discount) => {
  let discountedPrice = price;

  if (discount) {
    if (discount.type === 'percentage') {
      discountedPrice = Math.round(price * (1 - discount.value / 100) * 100) / 100;
    } else {
      discountedPrice -= discount.value;
    }
  }

  return discountedPrice;
};
