const Discount = require('../../model/audb/Discount');
const { Package } = require('../../model/audb/Package');

module.exports = async ({ user, data }) => {
  if (!data.packageId) throw new Error('Package ID is required');
  if (!data.type) throw new Error('Type is required');
  if (!data.paymentTypeId) throw new Error('Payment type ID is required');
  if (data.value && data.value < 0) throw new Error('Discount value should be positive value');
  if (data.type === 'percentage' && data.value > 100) throw new Error('Discount percentage value should be less or equal 100');

  const discountPackage = await Package.findOne({ id: data.packageId }).lean().cache(3600);

  if (data.type === 'price' && data.value && data.value > discountPackage.price) throw new Error('Discount value should be less or equal package price');

  let discount;

  if (data._id) discount = await Discount.findOne({ _id: data._id }).exec();
  else discount = new Discount();

  discount.value = data.value || 0;
  discount.packageId = data.packageId;
  discount.type = data.type;
  discount.paymentTypeId = data.paymentTypeId;
  discount.modifiedByUid = user.id;
  discount.modifiedByName = user.name;
  await discount.save();

  return {
    error: 0,
    discount,
  };
};
