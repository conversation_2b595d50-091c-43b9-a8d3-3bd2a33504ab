/**
 * get discount config for current payment type
 *
 * @param {string} pptype - payment type
 * @param {array} discounts - list of all discounts
 *
 * @return {object} discount
 * */
module.exports = (pptype, discounts) => {
  const filteredDiscounts = discounts.filter(discount => discount.paymentType.name === pptype);

  if (filteredDiscounts.length) return filteredDiscounts[0];

  return null;
};
