const HeaderCacheSettings = require('../../model/audb/HeaderCacheSettings');
const getNextModelId = require('../../helpers/getNextModelId');
const _getAll = require('./getAll');

module.exports = async (data) => {
  try {
    delete data._id;
    delete data.editing;
    data.id = data.id ? data.id : await getNextModelId(HeaderCacheSettings);
    data.id = parseInt(data.id);

    if (data.paras) {
      const split = data.paras.split('&');
      const rps = [];
      for (let i = 0; i < split.length; ++i) {
        const splitItem = split[i].split('=');
        rps.push({ [splitItem[0]]: splitItem[1] });
      }
      data.rp = rps;
    } else {
      data.rp = [];
    }

    await HeaderCacheSettings.findOneAndUpdate(
      { id: data.id },
      { $set: data },
      { upsert: true, new: true },
    ).exec();

    return await _getAll();
  } catch (error) {
    throw new Error(error.message);
  }
};
