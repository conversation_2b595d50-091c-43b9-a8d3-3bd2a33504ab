const HeaderCacheSettings = require('../../model/audb/HeaderCacheSettings');
const getNextModelId = require('../../helpers/getNextModelId');
const _getAll = require('./getAll');
const { IGNORE_ARRAY } = require('../../constants/headerCacheSettings');

module.exports = async (codename) => {
  try {
    if (codename !== '') {
      if (IGNORE_ARRAY.indexOf(codename) === -1) {
        const id = getNextModelId(HeaderCacheSettings);
        await HeaderCacheSettings.findOneAndUpdate(
          { codename },
          { $set: { codename, id } },
          { upsert: true },
        ).exec();
      }
    }

    return await _getAll();
  } catch (error) {
    throw new Error(error.message);
  }
};
