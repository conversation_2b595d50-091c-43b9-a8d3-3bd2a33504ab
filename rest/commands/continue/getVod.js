const _ = require('lodash');
const Vod = require('@s1/vod-models/Vod');
const UserVodPosition = require('../../model/audb/UserVodPosition');
const VodSettings = require('../../model/audb/VodSettings');

module.exports = async ({ uid, key, locale, isMovie = true, page, pageSize = 10 }) => {
  const vodSettings = await VodSettings.findOne({ data: 'vodurl' }).exec();
  const positions = await UserVodPosition.find({ uid, position: { $gte: 180 } })
    .sort('-updated')
    .exec();
  const vodPositions = {};
  const vodIds = positions.map((position) => {
    vodPositions[position.vod] = position.position;

    return position.vod;
  });
  const cateFilter = isMovie
    ? { $or: [{ episodeno: { $eq: 0 } }, { episodeno: { $eq: null } }] } : { episodeno: { $gt: 0 } };
  const options = {
    $and: [
      { id: { $in: vodIds } },
      cateFilter,
    ],
  };

  if (key) options.$or = [{ name: { $regex: key } }, { description: { $regex: key } }];

  const vods = vodIds.length
    ? await Vod.find(options)
      .skip((page - 1) * pageSize)
      .limit(Number(pageSize))
      .exec()
      .then(vods => (_.map(vods, (vod) => {
        const formattedVod = vod.format({ vodSettings, locale, isMovie });
        formattedVod.position = vodPositions[vod.id];

        return formattedVod;
      }))) : [];

  return vods;
};
