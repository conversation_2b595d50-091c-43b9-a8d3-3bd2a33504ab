const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const Schedule = require('../../model/audb/Schedule');
const Channel = require('../../model/audb/Channel');
const UserSchedulePosition = require('../../model/audb/UserSchedulePosition');

module.exports = async ({ user, ISP, countryCode, stateCode, locale }) => {
  const streamingServers = await user.getStreamingServers(
    ISP,
    stateCode,
    countryCode,
    STREAM_TYPE_VOD,
  );
  const positions = await UserSchedulePosition
    .find({ uid: user.id, position: { $gte: 180 } })
    .sort('-updated')
    .exec();

  const schedulePositions = {};
  const options = { $or: [] };
  positions.forEach(({ channel, rdatetime, position }) => {
    schedulePositions[`${channel}_${rdatetime}`] = position;
    options.$or.push({ channel, rdatetime });
  });
  const schedules = positions.length ? await Schedule.find(options).exec() : [];

  return Promise.all(schedules.map(async (schedule) => {
    schedule.position = schedulePositions[`${schedule.channel}_${schedule.rdatetime}`];

    if (!schedule.Channel) schedule.Channel = await Channel.findOne({ id: schedule.channel }).exec();

    return Schedule.format(schedule, {
      locale,
      thumbnailOptions: {
        domain: streamingServers.mainServer.sip,
      },
    });
  }));
};
