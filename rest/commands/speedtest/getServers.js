const StreamingServer = require('../../model/audb/class/StreamingServer');

const LATENCY = '/speedtest/latency.txt';
const BIGFILE = '/speedtest/test100.bin';
const SMALLFILE = '/speedtest/test50.bin';

module.exports = async ({ user, ISP, countryCode, stateCode }) => {
  const { mainServer, secondaryServers } = await user.getStreamingServers(ISP, countryCode, stateCode, 1);

  return [mainServer, ...secondaryServers]
    .map(server => server.toObject())
    .map(({ sparent, sip, ...server }) => {
      const {
        useTraffic,
        usedTraffic,
        trafficProvider,
        trafficLimit,
        trafficLogin,
        trafficPassword,
        trafficAccountHash,
        trafficApiKey,
        trafficAccountNumber,
        ...rest
      } = server;

      return {
        ...rest,
        sip,
        latency: `https://${sip}${LATENCY}${StreamingServer.signHighwind(LATENCY)}`,
        bigfile: `https://${sip}${BIGFILE}${StreamingServer.signHighwind(BIGFILE)}`,
        smallfile: `https://${sip}${SMALLFILE}${StreamingServer.signHighwind(SMALLFILE)}`,
      };
    });
};

