const mongoose = require('mongoose');
const moment = require('moment');
const logger = require('@s1/log');
const isLocal = require('is-local-ip');
const { InvoiceNotFoundError, PaymentUserNotFoundError, PaymentNoPackageError, PaymentAmountLowError, PaymentFailedError } = require('@s1/api-errors');
const Invoice = require('../../model/audb/Invoice');
const PaymentLog = require('../../model/audb/PaymentLog');
const PaymentActionsLog = require('../../model/audb/PaymentActionsLog');
const UserConfig = require('../../model/audb/UserConfig');
const PaymentShortUrl = require('../../model/audb/PaymentShortUrl');
const UserLogLogin = require('../../model/audb/UserLogLogin');
// const DeclinedTransaction = require('../../model/audb/DeclinedTransaction');
const sendEmailInvoice = require('./sendEmailInvoice');
const i18n = require('../../helpers/geti18n');
const getDiscountsByPackageId = require('../discounts/getByPackageId');
const getDiscountByPaymentType = require('../discounts/getDiscountByPaymentType');
const getDiscountAmountUSD = require('../discounts/getDiscountAmountUSD');
const redis = require('../../service/redisClient');
const { getUserLocation } = require('../../service/maxmind');
const { paymentStatusTypes } = require('../../constants/paymentLog');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getAllAccounts = require('../payment/getAllAccounts');
const checkPaymentTypeLimits = require('../payment/checkPaymentTypeLimits');
const config = require('../../../config');
const { paymentTypes } = require('../../constants/paymentType');

const log = logger.create(__filename);

module.exports = async ({
  tkey, currency, amount, additionalFee, stripeId, pptype, txid = '', firstname = '', lastname = '', email = '', card = '', query, body, paymentAccountId, locale,
}) => {
  const cacheKey = `blockPaymentUpdate_key_${tkey}`;

  let failedPaymentLogAlreadyExists = false;

  // check if exist payment for current transaction
  if (txid) {
    const paymentLogAlreadyPaid = await PaymentLog.findOne({ txid, upback: true, pptype, amount: { $gt: 0 } }).exec();

    if (paymentLogAlreadyPaid) {
      await redis.del(cacheKey);

      return { error: 0, result: paymentLogAlreadyPaid._doc };
    }

    failedPaymentLogAlreadyExists = await PaymentLog.findOne({ txid, upback: false }).exec();
  }

  const now = moment().unix();
  const invoice = await Invoice.findOne({ tkey }).populate('user').exec();

  if (!invoice) {
    await redis.del(cacheKey);

    throw new InvoiceNotFoundError(locale);
  }

  const { user } = invoice;

  if (!user) {
    await redis.del(cacheKey);

    throw new PaymentUserNotFoundError(locale);
  }

  const { paymentServiceKeyId } = body;
  const { addedMethod = 'automatic', addedByUid } = body;

  // check if payment serviceKeyId is in US stripe keys
  // we need to update key for US stripe payment only for the next payment with this key
  if (paymentServiceKeyId && (Invoice.getStripeUSServiceKeyIds()).includes(paymentServiceKeyId)) {
    // set default US stripe account for user
    await UserConfig.updateOne(user.id, { stripeServiceKeyId: paymentServiceKeyId });
  }

  let merchantApiResponse = body.merchantApiResponse || {};
  try {
    if (!(merchantApiResponse instanceof Object)) merchantApiResponse = JSON.parse(body.merchantApiResponse || '{}');
  } catch (e) {
    log.error(`Pay update::cannot parse merchantApiResponse, user: '${user.id}', invoice number: '${tkey}', billingAddress: ${body.merchantApiResponse}`);
  }

  const paymentLog = new PaymentLog({
    currency,
    amount,
    additionalFee,
    pptype,
    addedMethod,
    addedByUid,
    txid,
    firstname,
    lastname,
    email,
    card,
    merchantApiResponse,
    request: query,
    post: body,
    logid: mongoose.Types.ObjectId(),
    created: now,
    package: invoice.pack,
    upback: false,
    uid: user.id,
    stripeId,
    paymentAccountId,
    paymentServiceKeyId,
    tkey,
    locale,
  });

  if (body.hasOwnProperty('userIp') && body.userIp) paymentLog.userIp = body.userIp;
  else if (addedMethod !== 'manual') {
    // get user last IP from the payment action logs or last login
    const paymentActionsLog = await PaymentActionsLog.findOne({ uid: user.id, userIp: { $exists: true } }).sort({ _id: -1 }).exec();

    if (paymentActionsLog && paymentActionsLog.userIp && !isLocal(paymentActionsLog.userIp)) paymentLog.userIp = paymentActionsLog.userIp;

    const userLogLogin = await UserLogLogin.findOne({ uid: user.id }).sort({ _id: -1 }).exec();

    if (userLogLogin && userLogLogin.ip && !isLocal(userLogLogin.ip)) paymentLog.userIp = userLogLogin.ip;
  }
  if (paymentLog._doc.hasOwnProperty('userIp') && paymentLog.userIp && !isLocal(paymentLog.userIp)) {
    const { countryCode, stateCode } = getUserLocation(paymentLog.userIp);

    paymentLog.userIpInfo = { countryCode, stateCode };
  }
  if (body.hasOwnProperty('btServerId') && body.btServerId) paymentLog.btServerId = body.btServerId;

  try {
    if (!failedPaymentLogAlreadyExists) await paymentLog.save();
  } catch (e) {
    const errorMessage = e.stack || (e.message && e.message.toString());
    log.error(`Pay update::cannot save initial paymentLog, user: '${user.id}', invoice number: '${tkey}', error: ${errorMessage}`);
  }

  if (amount > 0) checkPaymentTypeLimits(pptype);
  if (body.expectedAmount) paymentLog.expectedAmount = body.expectedAmount;
  if (body.paidAmount) paymentLog.paidAmount = body.paidAmount;

  let invoiceProcessError = false;
  const paymentInfo = await invoice.getPaymentInfo(true, locale);

  let account;
  let descriptor = body.descriptor || null;

  if (!descriptor || [paymentTypes.paybis].includes(pptype)) {
    const accountId = paymentAccountId || body.accountId || null;

    try {
      const accountsResult = await getAllAccounts();
      const accounts = accountsResult.list;

      if (accountId) {
        account = accounts.find(account => account.id === accountId);
        descriptor = account.descriptor || '';
      } else if (paymentInfo && paymentInfo.Package) {
        const packageId = paymentInfo.Package.id;
        const filteredAccounts = filterPaymentAccountsByPackage(accounts, packageId);
        account = filteredAccounts.find(account => account.PaymentType.name === pptype);
        descriptor = account.descriptor || '';
      }
    } catch (e) {
      console.error(`Cannot get accounts for tkey: ${tkey}, userId: ${user.id}, error:`, e);
    }
  }
  if (paymentInfo && paymentInfo.Package) {
    if (user.isNgateUser) {
      const updateResult = await invoice.processNgate({ paymentInfo, tkey, amount, pptype, now, locale });
      paymentLog.upback = updateResult.upback;
      paymentLog.status = updateResult.status;
    } else {
      const packageDiscountsReponse = await getDiscountsByPackageId(paymentInfo.Package.id);
      let discountConfig = null;

      if (packageDiscountsReponse && packageDiscountsReponse.discounts) {
        discountConfig = getDiscountByPaymentType(pptype, packageDiscountsReponse.discounts);

        if (discountConfig) {
          const packagePrice = paymentInfo.price;
          const discountAmountUSD = getDiscountAmountUSD(packagePrice, discountConfig);
          paymentLog.discountUSD = discountAmountUSD;
          paymentLog.discountAmount = discountConfig.value;
          paymentLog.discountType = discountConfig.type;

          if (addedMethod === 'manual') {
            amount -= discountAmountUSD;
            paymentLog.amount = amount;
          }
        }
      }

      try {
        const updateResult = await invoice.process({ paymentInfo, tkey, amount, pptype, discountConfig, account, now, locale });
        paymentLog.upback = updateResult.upback;
        paymentLog.status = updateResult.status;

        if (updateResult.upback && updateResult.hasOwnProperty('addedDays')) paymentLog.addedDays = updateResult.addedDays;
      } catch (e) {
        const errorMessage = e.stack || (e.message && e.message.toString());
        log.error(`Can not process invoice user id: '${user.id}' invoice number: '${tkey}', error: ${errorMessage}`);
        paymentLog.upback = false;
        invoiceProcessError = true;
      }
    }
  } else {
    log.error(`Payment info not found for user id: '${user.id}' invoice number: '${tkey}'`);
    paymentLog.upback = false;
  }
  if (body.billingAddress) {
    try {
      let billingAddress = body.billingAddress || {};

      if (!(billingAddress instanceof Object)) billingAddress = JSON.parse(body.billingAddress || '{}');

      await user.addBillingAddress(billingAddress);
    } catch (e) {
      const errorMessage = e.stack || (e.message && e.message.toString());
      log.error(`Pay update::cannot parse billing address, user: '${user.id}', invoice number: '${tkey}', billingAddress: ${body.billingAddress}, error: ${errorMessage}`);
    }
  }
  if (paymentLog.isModified() && (!failedPaymentLogAlreadyExists || paymentLog.upback)) await paymentLog.save();

  // Disabled, seems do not used now
  // const { stripeCustomerId, retryable = 0 } = body;
  //
  // if ((amount < 0) && stripeCustomerId && retryable && stripeId === 2) {
  //   await DeclinedTransaction.remember(tkey, stripeCustomerId, now, paymentLog.card, user);
  // }

  const additionalPaymentData = { descriptor_name: descriptor || '' };

  // nowpayments - its a crypto type, should show custom formatted amount field in the template
  if (pptype === 'nowpayments' && addedMethod !== 'manual') {
    additionalPaymentData.crypto_amount = `${body.paidAmount.cryptoAmount} ${body.paidAmount.cryptoCurrency} - $${Math.round(body.amount * 100) / 100}`;
  }

  // ToDo 1) make queue table with emails, we should avoid dependencies between sending emails and updating packages
  // ToDo 2) we have different errors while processing payment: network connection, DB issue, payment issues in billnet side, etc

  if (!user.isNgateUser) {
    // so we need to have different email templates for such errors,
    // we should send invoiceProcessError to the sendEmailInvoice for send another email template
    try {
      // send email only once for the same transaction and payment status
      // once for fail or success pay
      // eslint-disable-next-line max-len
      if (!failedPaymentLogAlreadyExists || paymentLog.upback) await sendEmailInvoice({ paymentLog, user, additionalPaymentData, locale });
    } catch (e) {
      const errorMessage = e.stack || (e.message && e.message.toString());
      log.error(`Can not send email user id: '${user.id}' invoice number: '${tkey}', error: ${errorMessage}`);
    }
  }

  await redis.del(cacheKey);

  if (!paymentInfo || !paymentInfo.Package) throw new PaymentNoPackageError(locale);
  if (paymentLog.status === paymentStatusTypes.fail || invoiceProcessError) throw new PaymentFailedError(locale);
  if (!paymentLog.upback) {
    const error = new PaymentAmountLowError(locale);
    const paymentInfo = await paymentLog.getPaymentInfo(locale);
    const packagePrice = paymentInfo.packageprice.slice(1);

    i18n.setLocale(locale);

    return {
      error: `${error.code}`,
      msg: i18n.__('Package asked %s and send %s', packagePrice, paymentLog.amount),
      result: paymentLog._doc,
    };
  }

  PaymentShortUrl.deleteMany({ userId: user.id }).exec();

  return { error: 0, result: paymentLog._doc };
};
