const UserConfig = require('../../model/audb/UserConfig');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const PaymentLog = require('../../model/audb/PaymentLog');

async function updateUserConfig(userId, bypassedPaymentTypes) {
  let userConfig = await UserConfig.findOne({ uid: userId }).exec();

  if (!userConfig) userConfig = new UserConfig({ uid: userId, bypassedPaymentTypes });
  else {
    const bypassedPaymentTypesSet = new Set([...userConfig.bypassedPaymentTypes, ...bypassedPaymentTypes]);
    userConfig.bypassedPaymentTypes = Array.from(bypassedPaymentTypesSet);
  }

  await userConfig.save();
}

const runUpdateUserConfig = async (userId, bypassedPaymentTypes) => {
  try {
    await updateUserConfig(userId, bypassedPaymentTypes);
  } catch (e) {
    console.log(e);
  }
};

const getUserIdsByComments = async (comments) => {
  const parsedComments = comments.trim().split(',');
  const commentsMatch = { $match: { $or: [] } };

  parsedComments.forEach((comment) => {
    commentsMatch.$match.$or.push({ comment: new RegExp(comment.trim(), 'gi') });
  });

  // search users with admin comments
  const userIdsResult = await UserAdminComment.aggregate([
    commentsMatch,
    {
      $group: { _id: '$pptype', userIds: { $addToSet: '$uid' } },
    },
    {
      $project: { userIds: true },
    },
  ]).exec();

  const usersIds = [];

  if (userIdsResult && userIdsResult.length) {
    userIdsResult.forEach(result => usersIds.push(...result.userIds));
  }

  return usersIds;
};

const getUserIdsByPaidTypes = async (paidTypes) => {
  const match = {
    pptype: { $in: paidTypes },
    amount: { $gt: 0 },
    upback: true,
  };

  const paymentLogsResult = await PaymentLog.aggregate([
    {
      $match: match,
    },
    {
      $group: {
        _id: '$pptype',
        userIds: { $addToSet: '$uid' },
      },
    },
    {
      $project: { userIds: 1 },
    },
  ]);

  const usersIds = [];

  if (paymentLogsResult && paymentLogsResult.length) {
    paymentLogsResult.forEach(result => usersIds.push(...result.userIds));
  }

  return usersIds;
};

const setBypassedTypes = async (data) => {
  const { skipBlacklisted, comments, paidTypes, setTypes } = data;

  let blacklistedUserIds = [];
  let usersIds = [];

  // should not to add blacklisted users to bypass
  if (skipBlacklisted) {
    const paymentBlacklistedUsersResult = await PaymentBlacklist.find({ uid: { $exists: true } }).lean();
    blacklistedUserIds = paymentBlacklistedUsersResult.map(user => user.uid);
  }
  // no need to wait responses
  if (comments) {
    const userIdsByComments = await getUserIdsByComments(comments, setTypes, skipBlacklisted, blacklistedUserIds);

    if (userIdsByComments.length) usersIds.push(...userIdsByComments);
  }
  if (paidTypes && paidTypes.length) {
    const userIdsByPaidTypes = await getUserIdsByPaidTypes(paidTypes, setTypes, skipBlacklisted, blacklistedUserIds);

    if (userIdsByPaidTypes.length) usersIds.push(...userIdsByPaidTypes);
  }
  if (skipBlacklisted && blacklistedUserIds.length) {
    usersIds = usersIds.filter(userId => !blacklistedUserIds.includes(userId));
  }

  let promises = [];

  for (let i = 0; i < usersIds.length; ++i) {
    promises.push(runUpdateUserConfig(usersIds[i], setTypes));

    if (promises.length % 50 === 0) {
      await Promise.all(promises);
      promises = [];
    }
  }

  await Promise.all(promises);
};

module.exports = async (body) => {
  const { setTypes } = body;

  if (!setTypes || !setTypes.length) throw new Error('setTypes is required');

  // no need to wait responses
  setBypassedTypes(body);

  return {
    error: 0,
    success: true,
  };
};
