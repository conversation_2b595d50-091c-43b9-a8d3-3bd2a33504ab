const moment = require('moment');
const PaymentLog = require('../../model/audb/PaymentLog');
const User = require('../../model/audb/User');
const formatPaymentLog = require('./helpers/formatPaymentLog');
const { paymentStatusTypes } = require('../../constants/paymentLog');
const { paymentTypes } = require('../../constants/paymentType');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const getUserWatchingScore = require('../history/getUserWatchingScore');

module.exports = async (data) => {
  const page = data.page || 0;
  const limit = data.limit || 50;
  const skip = page * limit;
  // for logs query
  const match = {};
  const and = [];
  // for User query
  const userFilter = {};
  const userAnd = [];

  // fill filters
  if (data.userId) and.push({ uid: { $in: [parseInt(data.userId), data.userId] } });
  if (data.userIp) and.push({ userIp: data.userIp });
  if (data.email) {
    // lets search user IDs in the User DB instead of in the logs
    const encryptedEmail = User.encryptEmail(data.email);
    userAnd.push({ $or: [{ em: encryptedEmail }] });
  }
  if (data.name) {
    // lets search user IDs in the User DB instead of in the logs
    userAnd.push({ $or: [{ name: new RegExp(data.name, 'i') }] });
  }
  if (data.isBlacklisted === 0 || data.isBlacklisted === 1) {
    const paymentBlacklistedModels = await PaymentBlacklist.find({ uid: { $exists: true } }, { _id: 0, uid: 1 }).lean();
    const uids = paymentBlacklistedModels.map((model) => model.uid);

    if (data.isBlacklisted === 1) userAnd.push({ id: { $in: uids } });
    else if (data.isBlacklisted === 0) userAnd.push({ id: { $nin: uids } });
  }
  if (data.packageId) and.push({ package: data.packageId });
  if (data.paymentType) {
    switch (data.paymentType) {
      case paymentTypes.stripe:
        and.push({ $or: [{ pptype: { $in: [paymentTypes.stripe, 'stripe'] } }] });
        break;
      case paymentTypes.braintree:
        and.push({ $or: [{ pptype: { $in: [paymentTypes.braintree, 'braintree'] } }] });
        break;
      default:
        and.push({ pptype: data.paymentType });
        break;
    }
  }
  if (data.status) {
    switch (data.status) {
      case paymentStatusTypes.success:
        and.push({
          $or: [{ status: { $in: [paymentStatusTypes.success, paymentStatusTypes.overpaid, paymentStatusTypes.partialPaid] } }, { upback: true }],
        });
        break;
      case paymentStatusTypes.fail:
        and.push({ $or: [{ status: data.status }, { upback: false }] });
        break;
      default:
        and.push({ status: data.status });
        break;
    }
  }
  if (data.transactionId) and.push({ txid: data.transactionId });
  if (data.tkey) and.push({ tkey: { $in: [parseInt(data.tkey), data.tkey] } });
  if (data.dateFrom) and.push({ created: { $gte: moment(data.dateFrom).startOf('day').unix() } });
  if (data.dateTo) and.push({ created: { $lte: moment(data.dateTo).endOf('day').unix() } });
  if (data.ipCountry) {
    and.push({ userIpInfo: { $exists: true } });
    and.push({ 'userIpInfo.countryCode': new RegExp(data.ipCountry.toLowerCase(), 'i') });

    if (data.ipState) {
      and.push({ 'userIpInfo.stateCode': new RegExp(data.ipState.toLowerCase(), 'i') });
    }
  }
  if (userAnd.length) userFilter.$and = userAnd;

  let userIds = [];
  let userIdsForExpireFilter = [];

  // lets search user IDs in the User DB instead of in the logs
  if (userAnd.length) {
    const userIdsResult = await User.find(userFilter, { id: 1 }).lean();
    userIds = userIdsResult.map((user) => user.id);
  }
  if (data.userExpires) {
    const now = moment().unix();
    // lets search user expires in the User DB instead of in the logs
    switch (data.userExpires) {
      case 'expired':
        // eslint-disable-next-line no-case-declarations
        const userExpiredIdsResult = await User.find({ expires: { $lt: now } }, { id: 1 }).lean();
        userIdsForExpireFilter = userExpiredIdsResult.map((user) => user.id);
        break;
      case 'active':
        // eslint-disable-next-line no-case-declarations
        const userActiveIdsResult = await User.find({ expires: { $gt: now } }, { id: 1 }).lean();
        userIdsForExpireFilter = userActiveIdsResult.map((user) => user.id);
        break;
      default:
        break;
    }

    if (userIdsForExpireFilter.length) {
      and.push({ uid: { $in: userIdsForExpireFilter } });
    }
  }
  // search by email in the logs or found users IDs by email
  if (data.email) {
    const or = [{ email: data.email }, { 'post.email': data.email }];
    and.push({ $or: or });
  }
  // search by names in the logs or found users IDs by name
  if (data.name) {
    const or = [
      { firstname: new RegExp(data.name, 'i') },
      { lastname: new RegExp(data.name, 'i') },
      { 'post.firstname': new RegExp(data.name, 'i') },
      { 'post.lastname': new RegExp(data.name, 'i') },
    ];
    and.push({ $or: or });
  }
  if (userIds.length) {
    and.push({ uid: { $in: userIds } });
  }
  if (and.length) match.$and = and;

  const aggregation = [
    { $match: match },
    { $sort: { created: -1 } },
    { $skip: skip },
    { $limit: limit },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    {
      $lookup: {
        from: 'paymentAccount',
        localField: 'paymentAccountId',
        foreignField: 'id',
        as: 'PaymentAccount',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    { $unwind: { path: '$PaymentAccount', preserveNullAndEmptyArrays: true } },
  ];
  const aggregationCount = [{ $match: match }, { $sort: { created: -1 } }, { $count: 'count' }];

  const paymentLogs = await PaymentLog.aggregate(aggregation).exec();
  const countResult = await PaymentLog.aggregate(aggregationCount).exec();

  const updatedList = await Promise.all(paymentLogs.map(async (payment) => formatPaymentLog(payment)));
  const updatedListWithFormatedUserAndPaymentAccount = await Promise.all(
    updatedList.map(async (log) => {
      if (log.User) {
        const { em, name, expires } = log.User;
        const decryptedEmail = User.decryptEmail(em);
        const expiresDate = moment(expires * 1000).format('DD/MM/YYYY HH:mm:ss');
        const isBlacklisted = await isBlacklistedUser({ user: log.User });

        log.paidTypes = await getUserPaidTypes(log.uid);
        log.watchingScore = await getUserWatchingScore(log.uid);
        log.User = {
          email: decryptedEmail,
          name,
          expires: expiresDate,
          isBlacklisted,
        };
      }
      if (log.PaymentAccount) {
        log.PaymentAccount = {
          name: log.PaymentAccount.name,
          subType: log.PaymentAccount.subType,
        };
      }

      return log;
    }),
  );
  const total = countResult.length ? countResult[0].count : 0;
  const pages = Math.ceil(total / limit);

  return { list: updatedListWithFormatedUserAndPaymentAccount, total, pages, page };
};
