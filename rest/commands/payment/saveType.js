const PaymentType = require('../../model/audb/PaymentType');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ user, data }) => {
  if (!data.name) throw new Error('Payment type name is required');
  if (!data.paymentGroupId) throw new Error('Payment group ID is required');

  let paymentType;

  if (data._id) paymentType = await PaymentType.findOne({ _id: data._id }).exec();
  else paymentType = new PaymentType();

  Object.entries(data).forEach(([key, value]) => {
    paymentType[key] = value;
  });

  paymentType.modifiedByUid = user.id;
  paymentType.modifiedByName = user.name;

  await removeRedisCacheByKeys(['paymentAccount', 'paymentTypes']);
  await paymentType.save();

  return {
    error: 0,
    paymentType,
  };
};
