const PaymentType = require('../../model/audb/PaymentType');

module.exports = async () => {
  const paymentTypes = await PaymentType.find().populate('PaymentGroup').sort({ paymentGroupId: 1 }).lean()
    .cache(3600, 'paymentTypes')
    .then(paymentTypes => paymentTypes.sort((a, b) => {
      // sort by enabled on top
      if (a.enabled === b.enabled) {
        // sort by group
        if (a.paymentGroupId < b.paymentGroupId) { return -1; }
        if (a.paymentGroupId > b.paymentGroupId) { return 1; }
        // sort by type name
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }

        return 0;
      }

      return a.enabled ? -1 : 1;
    }));

  return {
    error: 0,
    list: paymentTypes,
  };
};
