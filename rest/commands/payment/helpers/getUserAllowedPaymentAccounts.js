const canPayWithGroupRule = require('../helpers/rules/canPayWithGroupRule');
const getAllAccounts = require('../getAllAccounts');
const checkRuleMembershipToAccount = require('./checkRuleMembershipToAccount');
const filterPaymentAccountsByPackage = require('./filterPaymentAccountsByPackage');

/**
 * Method filter accounts by rules with calculated basic rules
 *
 * @param {array} accounts
 * @param {array} rules
 * @param {object} canPayWithBasicRules
 *
 * @return {Array} - list of allowed accounts by rules
 * */
const filterAccountsByRules = (accounts, rules, canPayWithBasicRules) => {
  const filteredAccounts = [];

  for (const account of accounts) {
    const matchedRules = rules.filter(rule => checkRuleMembershipToAccount(rule, account));
    // set there by default allow true or false;
    // currently do not allow if there are not any rules
    let accountAllowed = matchedRules.length > 0;

    for (const rule of matchedRules) {
      const isUserCanPayWithRule = canPayWithGroupRule(rule, canPayWithBasicRules);

      // if some rule not allowed no need to check other
      if (!isUserCanPayWithRule) {
        accountAllowed = false;
        break;
      }
    }

    if (accountAllowed) filteredAccounts.push(account);
  }

  return filteredAccounts;
};

/**
 * Method check can pay with preset or with all accounts
 *
 * @param {array} rules
 * @param {object} canPayWithBasicRules
 * @param {array} accounts - accounts for check, if empty will get and check all accounts
 * @param {Array} bypassedAccounts - not required, user bypassed accounts based on userConfig bypassed types and accounts
 * @param {Array} packageId - not required, filter payment accounts by packageId
 *
 * @return {Promise} - list of allowed accounts
 * */
module.exports = async (rules = [], canPayWithBasicRules = {}, accounts = [], bypassedAccounts = [], packageId = null) => {
  let allAccounts = accounts;

  // need to check only if null, else if length = 0 user has no allowed accounts
  if (!allAccounts) {
    const allAccountsResult = await getAllAccounts();
    allAccounts = allAccountsResult.list || [];
  }

  const accountsForCurrentPackage = packageId ? filterPaymentAccountsByPackage(allAccounts, packageId) : allAccounts;
  // filter disabled accounts
  const enabledAccounts = accountsForCurrentPackage.filter(account => account.enabled && account.PaymentType.enabled);

  const permissionGroupsSet = new Set();
  rules.forEach(rule => (rule.userPermissionGroups || []).forEach(group => permissionGroupsSet.add(group)));

  const allFilteredAccountsObject = {};
  const permissionGroups = Array.from(permissionGroupsSet);

  // check allowed payment accounts by each permission group rules
  permissionGroups.forEach((permissionGroup) => {
    const filteredRules = rules.filter(rule => (rule.userPermissionGroups || []).includes(permissionGroup));
    const filteredAccounts = filterAccountsByRules(enabledAccounts, filteredRules, canPayWithBasicRules);

    // merge user bypassed accounts
    if (bypassedAccounts && bypassedAccounts.length) {
      const accountIds = filteredAccounts.map(account => account._id.toString());
      bypassedAccounts.forEach((account) => {
        if (!accountIds.includes(account._id.toString())) filteredAccounts.push(account);
      });
    }

    filteredAccounts.forEach((account) => {
      allFilteredAccountsObject[account._id] = account;
    });
  });

  return Object.values(allFilteredAccountsObject);
};
