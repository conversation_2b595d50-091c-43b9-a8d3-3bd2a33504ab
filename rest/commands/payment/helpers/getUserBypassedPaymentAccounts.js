const UserConfig = require('../../../model/audb/UserConfig');
const getAllPaymentTypeAccounts = require('../../payment/getAllPaymentTypeAccounts');
const isBlacklistedUser = require('./rules/isBlacklistedUser');

const hasUserBypassedTypes = userConfig => userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes && userConfig.bypassedPaymentTypes.length;
const hasUserBypassedAccounts = userConfig => userConfig.hasOwnProperty('bypassedPaymentAccounts') && userConfig.bypassedPaymentAccounts && userConfig.bypassedPaymentAccounts.length;

/**
 * @description Method get list of user bypassed accounts from user configs
 *
 * @param {User} user - user model
 * @param {object} config - not required, user config - user for check preset bypassed accounts
 *
 * @return {Promise} - list of accounts
 * */
module.exports = async (user, config = null) => {
  const result = [];

  if (!user) return result;
  // blacklisted user should not bypass any accounts or types
  if (!user.skipBlacklistPayment && await isBlacklistedUser({ user })) return result;

  const userConfig = config || await UserConfig.findOne({ uid: user.id }).lean();

  if (!userConfig) return result;
  if (hasUserBypassedTypes(userConfig) || hasUserBypassedAccounts(userConfig)) {
    const paymentAccountsResult = await getAllPaymentTypeAccounts();

    if (paymentAccountsResult && paymentAccountsResult.hasOwnProperty('list') && paymentAccountsResult.list) {
      const paymentAccounts = paymentAccountsResult.list;
      const hasBypassedTypes = hasUserBypassedTypes(userConfig);
      const hasBypassedAccounts = hasUserBypassedAccounts(userConfig);

      // get all accounts from the user bypassed types and accounts
      Object.entries(paymentAccounts).forEach(([, type]) => {
        if (type.enabled && type.accounts && type.accounts.length) {
          type.accounts.forEach((account) => {
            if (account.enabled
              && ((hasBypassedTypes && userConfig.bypassedPaymentTypes.includes(type._id.toString()))
                || (hasBypassedAccounts && userConfig.bypassedPaymentAccounts.includes(account._id.toString())))) result.push(account);
          });
        }
      });
    }
  }

  return result;
};
