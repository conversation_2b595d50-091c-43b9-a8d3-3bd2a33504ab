/**
 * Method checks user config for bypassed payment types and accounts, both should be active
 *
 * @param {object} userConfig
 * @param {string} userConfig
 * @param {array} paymentAccounts
 *
 * @return {boolean}
 * */
module.exports = async (userConfig, packageId, paymentAccounts) => {
  let hasOneBypassedActiveAccount = false;

  Object.entries(paymentAccounts)
    .filter(([, type]) => !!type.enabled)
    .forEach(([, type]) => {
      // checks if user has bypassed payment type and active account for this type and current package
      if (userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes
        && userConfig.bypassedPaymentTypes.length && userConfig.bypassedPaymentTypes.includes(type._id.toString())) {
        type.accounts.forEach((account) => {
          if (account.paymentGroup === 'Nft' && account.enabled && account.hasOwnProperty('packageId')
            && account.packageId === packageId) hasOneBypassedActiveAccount = true;
        });
      }
      // checks if user has bypassed active account for current package
      if (userConfig.hasOwnProperty('bypassedPaymentAccounts') && userConfig.bypassedPaymentAccounts
        && userConfig.bypassedPaymentAccounts.length && type.accounts && type.accounts.length) {
        type.accounts.forEach((account) => {
          if (account.paymentGroup === 'Nft' && account.enabled && account.PaymentType.enabled && userConfig.bypassedPaymentAccounts.includes(account._id.toString())
            && account.hasOwnProperty('packageId') && account.packageId === packageId) hasOneBypassedActiveAccount = true;
        });
      }
    });

  if (hasOneBypassedActiveAccount) return true;

  return false;
};
