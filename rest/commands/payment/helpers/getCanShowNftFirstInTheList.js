const getPaymentGeneralConfig = require('../getPaymentGeneralConfig');
const PaymentLog = require('../../../model/audb/PaymentLog');

/**
 * @description Method check can show NFT first in the list
 *
 * @param {User} user - user model
 *
 * @return {boolean}
 * */
module.exports = async (user) => {
  const paymentGeneralConfigResponse = await getPaymentGeneralConfig();

  if (!paymentGeneralConfigResponse || !paymentGeneralConfigResponse.generalConfig) return false;

  const paymentGeneralConfig = paymentGeneralConfigResponse.generalConfig;

  if (paymentGeneralConfig && paymentGeneralConfig.billingShowNftFirstIfPaidWith
    && Object.keys(paymentGeneralConfig.billingShowNftFirstIfPaidWith).length) {
    const paymentTypes = Object.keys(paymentGeneralConfig.billingShowNftFirstIfPaidWith);
    const paymentLogs = await PaymentLog.find({
      uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $in: paymentTypes },
    }).cache(300).lean();

    if (paymentLogs.length) return true;
  }

  return false;
};
