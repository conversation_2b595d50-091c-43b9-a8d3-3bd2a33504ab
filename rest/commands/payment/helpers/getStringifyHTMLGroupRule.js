/**
 * Recursive function to get single rule or group rule value
 *
 * @param {object} rule
 * @param {object} basicRules - all basic rules
 * @param {string} stringRule
 */
function evaluateFunction(ruleConfig, basicRules, stringRule) {
  if (ruleConfig.type === 'rule') {
    const basicRuleId = ruleConfig.basicRuleId;
    const basicRuleValue = ruleConfig.basicRuleValue;
    stringRule += "<div class='group_rule-basic_rule'>";

    if (!basicRuleValue) stringRule += 'NOT ';

    stringRule += `${basicRules[basicRuleId].name}`;
    stringRule += '</div>';

    return stringRule;
  }

  const groupRules = ruleConfig.rules;

  stringRule += "(<div class='group_rule-group'>";
  stringRule = evaluateFunction(groupRules[0], basicRules, stringRule);
  let prevOperator = groupRules[0].groupRuleOperator;

  for (let i = 1; i < groupRules.length; i++) {
    const nextRule = groupRules[i];

    if (prevOperator === 'OR') {
      stringRule += "<div class='group_rule-or_operator'>OR</div>";
      stringRule = evaluateFunction(nextRule, basicRules, stringRule);
    } else if (prevOperator === 'AND') {
      stringRule += "<div class='group_rule-and_operator'>AND</div>";
      stringRule = evaluateFunction(nextRule, basicRules, stringRule);
    }

    prevOperator = nextRule.groupRuleOperator;
  }

  stringRule += '</div>)';

  return stringRule;
}

/**
 * Method return stringify payment group rule
 *
 * @param {object} rule - group rule
 * @param {object} basicRules - basic rules object
 * @param {boolean} addBypassedRule
 *
 * @return {string} - string rule
 * */
module.exports = (rule, basicRules = {}, addBypassedRule = false) => {
  let stringRule = evaluateFunction(rule, basicRules, '');

  if (stringRule && stringRule.length > 1) stringRule = stringRule.slice(1, -1);

  stringRule = `<div class='group_rule-basic_rule'>Is enabled rule</div><div class='group_rule-or_operator'>AND</div>(<div class='group_rule-group'>${stringRule}</div>)`;

  if (addBypassedRule && (
    (rule.paymentAccounts && rule.paymentAccounts.length)
    || (rule.paymentTypes && rule.paymentTypes.length)
    || (rule.paymentGroups && rule.paymentGroups.length))
  ) {
    stringRule = `<div class='group_rule-basic_rule'>Has user (not blacklisted) bypassed payment account/type</div><div class='group_rule-or_operator'>OR</div>(<div class='group_rule-group'>${stringRule}</div>)`;
  }

  return stringRule;
};
