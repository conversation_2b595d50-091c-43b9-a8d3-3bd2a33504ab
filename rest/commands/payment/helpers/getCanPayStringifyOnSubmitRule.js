const { getAllowIpCountryString } = require('./onSubmitRule/allowIpCountry');
const { getDenyIpCountryString } = require('./onSubmitRule/denyIpCountry');
const { getAllowCardCountryString } = require('./onSubmitRule/allowCardCountry');
const { getDenyCardCountryString } = require('./onSubmitRule/denyCardCountry');
const { getAllowCardBrandString } = require('./onSubmitRule/allowCardBrand');
const { getDenyCardBrandString } = require('./onSubmitRule/denyCardBrand');
const { getAllowCardBankString } = require('./onSubmitRule/allowCardBank');
const { getDenyCardBankString } = require('./onSubmitRule/denyCardBank');
const { getAllowCardTypeString } = require('./onSubmitRule/allowCardType');
const { getDenyCardTypeString } = require('./onSubmitRule/denyCardType');
const { getAllowUserAddressCountryString } = require('./onSubmitRule/allowUserAddressCountry');
const { getDenyUserAddressCountryString } = require('./onSubmitRule/denyUserAddressCountry');

/**
 * Method check can pay on submit rule and return stringify failed cases response
 *
 * @param {object} rule
 * @param {object} userData - user IP country and state, card info, user address country
 * @param {object} pack - user package
 * @param {Array} accounts
 *
 * @return {string} - string cannot pay rule
 * */
module.exports = (rule, userData, pack, accounts = []) => {
  const {
    ipCountry,
    ipState,
    addressCountry,
    skipAddressCountry,
    cardCountry,
    cardBankName,
    cardType,
    cardBrand,
  } = userData;

  if (!rule.packageIds.includes(pack.id)) return `Current package ${pack.epricestr} is not allowed`;

  let hasAllowedAccounts = false;
  accounts.forEach((account) => {
    if (rule.paymentAccounts.includes(account._id.toString())) hasAllowedAccounts = true;
    if (rule.paymentTypes.includes(account.PaymentType._id.toString())) hasAllowedAccounts = true;
    if (rule.paymentGroups.includes(account.PaymentGroup._id.toString())) hasAllowedAccounts = true;
  });

  if (!hasAllowedAccounts) return 'Current rule payment accounts do not match with any allowed accounts by basic rules';

  /** Check user IP country */
  const getAllowIpCountryResult = getAllowIpCountryString(rule, ipCountry, ipState);
  const getDenyIpCountryResult = getDenyIpCountryString(rule, ipCountry, ipState);
  /** Check user card country */
  const getAllowCardCountryResult = getAllowCardCountryString(rule, cardCountry);
  const getDenyCardCountryResult = getDenyCardCountryString(rule, cardCountry);
  /** Check user card brand */
  const getAllowCardBrandResult = getAllowCardBrandString(rule, cardBrand);
  const getDenyCardBrandResult = getDenyCardBrandString(rule, cardBrand);
  /** Check user card bank name */
  const getAllowCardBankResult = getAllowCardBankString(rule, cardBankName);
  const getDenyCardBankResult = getDenyCardBankString(rule, cardBankName);
  /** Check user card type */
  const getAllowCardTypeResult = getAllowCardTypeString(rule, cardType);
  const getDenyCardTypeResult = getDenyCardTypeString(rule, cardType);
  /** Check user address country */
  let getAllowUserAddressCountryResult = false;
  let getDenyUserAddressCountryResult = false;

  if (!skipAddressCountry) {
    getAllowUserAddressCountryResult = getAllowUserAddressCountryString(rule, addressCountry);
    getDenyUserAddressCountryResult = getDenyUserAddressCountryString(rule, addressCountry);
  }

  const failedChecks = [];

  if (getAllowIpCountryResult) failedChecks.push(getAllowIpCountryResult);
  if (getDenyIpCountryResult) failedChecks.push(getDenyIpCountryResult);
  if (getAllowCardCountryResult) failedChecks.push(getAllowCardCountryResult);
  if (getDenyCardCountryResult) failedChecks.push(getDenyCardCountryResult);
  if (getAllowCardBrandResult) failedChecks.push(getAllowCardBrandResult);
  if (getDenyCardBrandResult) failedChecks.push(getDenyCardBrandResult);
  if (getAllowCardBankResult) failedChecks.push(getAllowCardBankResult);
  if (getDenyCardBankResult) failedChecks.push(getDenyCardBankResult);
  if (getAllowCardTypeResult) failedChecks.push(getAllowCardTypeResult);
  if (getDenyCardTypeResult) failedChecks.push(getDenyCardTypeResult);
  if (!skipAddressCountry && getAllowUserAddressCountryResult) failedChecks.push(getAllowUserAddressCountryResult);
  if (!skipAddressCountry && getDenyUserAddressCountryResult) failedChecks.push(getDenyUserAddressCountryResult);

  const stringCannotPayRules = failedChecks.join(', ');

  return stringCannotPayRules;
};
