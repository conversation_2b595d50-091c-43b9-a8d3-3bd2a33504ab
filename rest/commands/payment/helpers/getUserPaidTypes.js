const PaymentLog = require('../../../model/audb/PaymentLog');
const { Package } = require('../../../model/audb/Package');

/**
 * Get user paid types names
 *
 * @param {number} userId - user ID
 * */
module.exports = async (userId) => {
  const allPackages = await Package.find({}, { _id: 0, id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });

  const result = await PaymentLog.aggregate([
    { $match: {
      uid: userId,
      amount: { $gt: 0 },
      pptype: { $ne: 'manual' },
    } },
    {
      $group: {
        _id: {
          pptype: '$pptype',
          package: '$package',
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: '$_id.pptype',
        packages: {
          $push: {
            packageId: '$_id.package',
            count: '$count',
          },
        },
        totalCount: { $sum: '$count' },
      },
    },
  ]).cache(300);
  const paidTypes = result.map(log => ({
    type: log._id,
    packages: log.packages.map((packInfo) => {
      if (packagesObject.hasOwnProperty(packInfo.packageId)) {
        return Object.assign({ count: packInfo.count }, packagesObject[packInfo.packageId]);
      }

      return null;
    }).filter(pack => !!pack),
    totalCount: log.totalCount,
  }));

  return paidTypes;
};
