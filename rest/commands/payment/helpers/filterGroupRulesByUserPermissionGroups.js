const UserPermissionGroup = require('../../../model/audb/UserPermissionGroup');

/**
 * @description filter payment account by package ID
 *
 * @param {Object} user - user model
 * @param {number} packageId - user model
 *
 * @return {Array} - list of filtered accounts
 * */
module.exports = async (user, paymentGroupRules) => {
  if (!user) return [];
  if (!paymentGroupRules) return [];

  const defaultPermissionGroup = await UserPermissionGroup.findOne({ isUserDefaultGroup: true })
    .lean()
    .cache(3600, 'userPermissionGroup_default');
  const userPermissionGroups = user.permissionGroups || [defaultPermissionGroup._id];

  const filteredPaymentGroupRules = paymentGroupRules.filter((rule) => {
    if (!rule.hasOwnProperty('userPermissionGroups') || !rule.userPermissionGroups || !rule.userPermissionGroups.length) {
      if (userPermissionGroups.includes(defaultPermissionGroup._id)) return true;
    } else {
      for (let i = 0; i < rule.userPermissionGroups.length; ++i) {
        if (userPermissionGroups.includes(rule.userPermissionGroups[i])) return true;
      }
    }

    return false;
  });

  return filteredPaymentGroupRules;
};
