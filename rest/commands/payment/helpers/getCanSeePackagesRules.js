const isOldUser = require('./rules/isOldUser');
const hasPayments = require('./rules/hasPayments');
const hasOneAndThreeMonthPaymentLogs = require('./rules/hasOneAndThreeMonthPaymentLogs');
const hasWatchingActivities = require('./rules/hasWatchingActivities');
const isBlacklistedUser = require('./rules/isBlacklistedUser');

/**
 * @description Method get can see package rules
 *
 * @param {User} user - user model
 *
 * @return {object}
 * */
module.exports = async (user) => {
  const shouldIncludePaymentLogs = true;
  const isUserBlacklisted = user && user.skipBlacklistPayment ? false : await isBlacklistedUser({ user });
  const hasUserPaymentLogs = await hasPayments({ user, minLogs: 2 });
  const hasUserWatchingActivities = user && user.skipPaymentWatchActivities ? true : await hasWatchingActivities({
    user, calculationDays: 60, minLogs: 30, maxLogsPerDays: 5,
  });
  const hasOldUserWatchingActivities = user && user.skipPaymentWatchActivities ? true : await hasWatchingActivities({
    user, calculationDays: 60, minLogs: 30, maxLogsPerDays: 5,
  });
  const hasUserThreePaymentLogs = await hasPayments({ user, minLogs: 3 });
  const hasUserOneAndThreeMonthPaymentLogs = await hasOneAndThreeMonthPaymentLogs({ user });
  const isUserOldUser = isOldUser({ user });

  return {
    isUserBlacklisted,
    shouldIncludePaymentLogs,
    hasUserPaymentLogs,
    hasUserWatchingActivities,
    isUserOldUser,
    hasOldUserWatchingActivities,
    hasUserThreePaymentLogs,
    hasUserOneAndThreeMonthPaymentLogs,
  };
};
