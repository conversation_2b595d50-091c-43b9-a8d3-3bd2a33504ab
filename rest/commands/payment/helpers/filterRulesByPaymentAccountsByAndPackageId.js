const filterPaymentAccountsByPackage = require('./filterPaymentAccountsByPackage');
const filterRulesByPackage = require('./filterRulesByPackage');
const checkRuleMembershipToAccount = require('./checkRuleMembershipToAccount');

/**
 * @description filter payment rules by accounts and package ID
 *
 * @param {Array} rules - user model
 * @param {Array} accounts - user model
 * @param {number} packageId - user model
 *
 * @return {Array} - list of filtered rules
 * */
module.exports = (rules, accounts, packageId) => {
  if (!accounts || !rules) return [];

  const filteredAccounts = filterPaymentAccountsByPackage(accounts, packageId);
  const rulesForCurrentPackage = filterRulesByPackage(rules, packageId);
  const filteredRules = {};

  filteredAccounts.forEach((account) => {
    const matchedRules = rulesForCurrentPackage.filter(rule => rule.enabled && checkRuleMembershipToAccount(rule, account));

    matchedRules.forEach((rule) => {
      filteredRules[rule._id.toString()] = rule;
    });
  });

  return Object.values(filteredRules);
};
