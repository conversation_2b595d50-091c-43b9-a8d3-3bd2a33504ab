const getAllTypes = require('../getAllTypes');
const PaymentLog = require('../../../model/audb/PaymentLog');

/**
 * Method get total paid amount by each enabled payment type for the whole time with KYC config
 *
 * @param {object} user
 *
 * @return {object}
 * */
module.exports = async (user) => {
  const allTypesResult = await getAllTypes();
  const allTypes = allTypesResult.list || [];
  const cryptoWithCardTypes = allTypes.filter(type => type.PaymentGroup.name === 'Crypto with card' && type.enabled);
  const typeNames = cryptoWithCardTypes.map(type => type.name);
  const paidLogs = await PaymentLog.aggregate([
    { $match: { uid: user.id, pptype: { $in: typeNames }, upback: true, amount: { $gt: 0 } } },
    { $group: { _id: '$pptype', total: { $sum: '$amount' } } },
  ]);
  const typesWithKyc = [];
  // prepare list of types with not empty KYC configs and update with user total paid amount by type
  cryptoWithCardTypes.forEach((type) => {
    if (type.maxKycLimit) {
      const typeKyc = {
        maxKycLimit: type.maxKycLimit,
        kycAction: type.kycAction,
        name: type.name,
        totalPaid: 0,
      };
      for (let i = 0; i < paidLogs.length; ++i) {
        if (paidLogs[i]._id === type.name) {
          typeKyc.totalPaid = paidLogs[i].total;
          break;
        }
      }
      typesWithKyc.push(typeKyc);
    }
  });

  return typesWithKyc;
};
