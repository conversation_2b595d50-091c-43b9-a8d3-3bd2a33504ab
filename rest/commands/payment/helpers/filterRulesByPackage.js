/**
 * @description filter payment rules by package ID
 *
 * @param {Array} rules - user model
 * @param {number} packageId - user model
 *
 * @return {Array} - list of filtered rules
 * */
module.exports = (rules, packageId) => {
  if (!rules) return [];
  if (!packageId) return rules;

  // group rules has no packages, no need to filter them
  const result = rules.filter(rule => (!rule.hasOwnProperty('packageIds') || (rule.packageIds && rule.packageIds.length && rule.packageIds.includes(packageId))));

  return result;
};
