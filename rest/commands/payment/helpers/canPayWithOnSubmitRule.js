const { isAllowIpCountry } = require('./onSubmitRule/allowIpCountry');
const { isDenyIpCountry } = require('./onSubmitRule/denyIpCountry');
const { isAllowCardCountry } = require('./onSubmitRule/allowCardCountry');
const { isDenyCardCountry } = require('./onSubmitRule/denyCardCountry');
const { isAllowCardBrand } = require('./onSubmitRule/allowCardBrand');
const { isDenyCardBrand } = require('./onSubmitRule/denyCardBrand');
const { isAllowCardBank } = require('./onSubmitRule/allowCardBank');
const { isDenyCardBank } = require('./onSubmitRule/denyCardBank');
const { isAllowCardType } = require('./onSubmitRule/allowCardType');
const { isDenyCardType } = require('./onSubmitRule/denyCardType');
const { isAllowUserAddressCountry } = require('./onSubmitRule/allowUserAddressCountry');
const { isDenyUserAddressCountry } = require('./onSubmitRule/denyUserAddressCountry');

/**
 * Recursive function to get single rule or group rule value
 *
 * @param {object} rule
 * @param {object} userData
 * @param {boolean} userData.skipAddressCountry - used 'true' value for initial check cap pay with card types, for the rest checks - 'false'
 */
module.exports = async (user, rule, userData) => {
  /** Check user IP country */
  const isAllowedIpCountry = await isAllowIpCountry(user, rule, userData);

  if (!isAllowedIpCountry) return false;

  const isDeniedIpCountry = await isDenyIpCountry(user, rule, userData);

  if (isDeniedIpCountry) return false;

  /** Check user card country */
  const isAllowedCardCountry = await isAllowCardCountry(user, rule, userData);

  if (!isAllowedCardCountry) return false;

  const isDeniedCardCountry = await isDenyCardCountry(user, rule, userData);

  if (isDeniedCardCountry) return false;

  /** Check user card brand */
  const isAllowedCardBrand = await isAllowCardBrand(user, rule, userData);

  if (!isAllowedCardBrand) return false;

  const isDeniedCardBrand = await isDenyCardBrand(user, rule, userData);

  if (isDeniedCardBrand) return false;

  /** Check user card bank name */
  const isAllowedCardBank = await isAllowCardBank(user, rule, userData);

  if (!isAllowedCardBank) return false;

  const isDeniedCardBank = await isDenyCardBank(user, rule, userData);

  if (isDeniedCardBank) return false;

  /** Check user card type */
  const isAllowedCardType = await isAllowCardType(user, rule, userData);

  if (!isAllowedCardType) return false;

  const isDeniedCardType = await isDenyCardType(user, rule, userData);

  if (isDeniedCardType) return false;

  /** Check user address country */
  const isAllowedUserAddressCountry = await isAllowUserAddressCountry(user, rule, userData);

  if (!isAllowedUserAddressCountry) return false;

  const isDeniedUserAddressCountry = await isDenyUserAddressCountry(user, rule, userData);

  if (isDeniedUserAddressCountry) return false;

  return true;
};
