const getUserAllowedPaymentAccountsOnSubmit = require('./getUserAllowedPaymentAccountsOnSubmit');

/**
 * @param {User} user - user model
 * @param {object} paymentInfo - package info
 * @param {Array} rules - payment rules list
 * @param {object} userData - user data for check
 * @param {Array} accounts - list of accounts to check, is not required
 * @param {Array} bypassedAccounts - not required, user bypassed accounts based on userConfig bypassed types and accounts
 *
 * @return {Promise} - list of allowed payment types
 * */
module.exports = async (user, paymentInfo, rules = [], userData = {}, accounts = [], bypassedAccounts = []) => {
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccountsOnSubmit(user, paymentInfo, rules, userData, accounts, bypassedAccounts);
  const typesObject = {};
  const userAllowedPaymentTypes = [];

  userAllowedPaymentAccounts.forEach((account) => { typesObject[account.PaymentType._id.toString()] = account.PaymentType; });
  Object.entries(typesObject).forEach(([, value]) => userAllowedPaymentTypes.push(value));

  return userAllowedPaymentTypes;
};
