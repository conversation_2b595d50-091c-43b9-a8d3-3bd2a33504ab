const getAllBasicRules = require('./../getAllBasicRules');
const getRuleFunctionByName = require('./rules/getRuleFunctionByName');

const checkRule = async ({ user, paymentInfo, tkey, fromPage, rule, coin, canPayBasicRules }) => {
  const ruleId = rule._id.toString();
  const methodName = rule.methodName;
  const func = getRuleFunctionByName(methodName);

  if (func) {
    try {
      const funcResponse = await func({ user, paymentInfo, tkey, fromPage, rule, coin });

      canPayBasicRules[ruleId] = funcResponse;
    } catch (e) {
      canPayBasicRules[ruleId] = false;
    }
  } else {
    canPayBasicRules[ruleId] = false;
  }
};

module.exports = async ({ user, paymentInfo, fromPage = '', tkey = null, coin = null }) => {
  const basicRulesResponse = await getAllBasicRules();
  const basicRules = basicRulesResponse.list;
  const canPayBasicRules = {};
  const promises = [];

  basicRules.forEach(rule => promises.push(checkRule({ user, paymentInfo, tkey, fromPage, rule, coin, canPayBasicRules })));

  await Promise.all(promises);

  return canPayBasicRules;
};
