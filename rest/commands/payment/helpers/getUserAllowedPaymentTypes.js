const getUserAllowedPaymentAccounts = require('./getUserAllowedPaymentAccounts');

/**
 * Method check can pay payment types by group rules
 *
 * @param {Array} rules - group rules list
 * @param {object} canPayWithBasicRules - basic rules object
 * @param {Array} paymentAccounts - not required, we can parse types from already checked accounts
 * @param {Array} bypassedAccounts - not required, user bypassed accounts based on userConfig bypassed types and accounts
 * @param {Array} packageId - not required, filter payment accounts by packageId
 * @param {Array} allAccounts - not required, accounts for check
 * */
module.exports = async (
  rules = [], canPayWithBasicRules = {}, paymentAccounts = null, bypassedAccounts = [], packageId = null, allAccounts = null,
) => {
  let userAllowedPaymentAccounts = [];

  if (paymentAccounts) {
    userAllowedPaymentAccounts = bypassedAccounts && bypassedAccounts.length ? paymentAccounts.concat(bypassedAccounts) : paymentAccounts;
  } else {
    userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(rules, canPayWithBasicRules, allAccounts, bypassedAccounts, packageId);
  }

  const typesObject = {};
  const userAllowedPaymentTypes = [];

  userAllowedPaymentAccounts.forEach((account) => {
    typesObject[account.PaymentType._id.toString()] = account.PaymentType;
    typesObject[account.PaymentType._id.toString()].PaymentGroup = account.PaymentGroup;
  });
  Object.entries(typesObject).forEach(([, value]) => userAllowedPaymentTypes.push(value));

  return userAllowedPaymentTypes;
};
