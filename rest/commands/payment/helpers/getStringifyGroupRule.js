/**
 * Recursive function to get single rule or group rule value
 *
 * @param {object} rule
 * @param {object} basicRules - all basic rules
 * @param {string} stringRule
 */
function evaluateFunction(ruleConfig, basicRules, stringRule) {
  if (ruleConfig.type === 'rule') {
    const basicRuleId = ruleConfig.basicRuleId;
    const basicRuleValue = ruleConfig.basicRuleValue;

    if (!basicRuleValue) stringRule += 'NOT ';

    stringRule += `${basicRules[basicRuleId].name}`;

    return stringRule;
  }

  const groupRules = ruleConfig.rules;

  stringRule += '(';
  stringRule = evaluateFunction(groupRules[0], basicRules, stringRule);
  let prevOperator = groupRules[0].groupRuleOperator;

  for (let i = 1; i < groupRules.length; i++) {
    const nextRule = groupRules[i];

    if (prevOperator === 'OR') {
      stringRule += ' OR ';
      stringRule = evaluateFunction(nextRule, basicRules, stringRule);
    } else if (prevOperator === 'AND') {
      stringRule += ' AND ';
      stringRule = evaluateFunction(nextRule, basicRules, stringRule);
    }

    prevOperator = nextRule.groupRuleOperator;
  }

  stringRule += ')';

  return stringRule;
}

/**
 * Method return stringify payment group rule
 *
 * @param {object} rule - group rule
 * @param {object} basicRules - basic rules object
 * @param {boolean} addBypassedRule
 *
 * @return {string} - string rule
 * */
module.exports = (rule, basicRules = {}, addBypassedRule = false) => {
  let stringRule = evaluateFunction(rule, basicRules, '');

  if (stringRule && stringRule.length > 1) stringRule = stringRule.slice(1, -1);

  stringRule = `Is enabled rule AND (${stringRule})`;

  if (addBypassedRule && (
    (rule.paymentAccounts && rule.paymentAccounts.length)
    || (rule.paymentTypes && rule.paymentTypes.length)
    || (rule.paymentGroups && rule.paymentGroups.length))
  ) {
    stringRule = `Has user (not blacklisted) bypassed payment account/type OR (${stringRule})`;
  }

  return stringRule;
};
