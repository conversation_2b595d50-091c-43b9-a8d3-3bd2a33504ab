const getUserAllowedPaymentAccountsOnSubmit = require('./getUserAllowedPaymentAccountsOnSubmit');

/**
 * @param {User} user - user model
 * @param {object} paymentInfo - package info
 * @param {Array} rules - payment rules list
 * @param {object} userData - user data for check
 * @param {Array} accounts - list of accounts to check, is not required
 * @param {Array} bypassedAccounts - not required, user bypassed accounts based on userConfig bypassed types and accounts
 *
 * @return {object} - list of allowed accounts grouped by type
 * */
module.exports = async (user, paymentInfo, rules = [], userData = {}, accounts = null, bypassedAccounts = []) => {
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccountsOnSubmit(user, paymentInfo, rules, userData, accounts, bypassedAccounts);
  const typesObject = {};

  userAllowedPaymentAccounts.forEach((account) => {
    if (!typesObject.hasOwnProperty(account.PaymentType.name)) typesObject[account.PaymentType.name] = [];

    typesObject[account.PaymentType.name].push(account);
  });

  return typesObject;
};
