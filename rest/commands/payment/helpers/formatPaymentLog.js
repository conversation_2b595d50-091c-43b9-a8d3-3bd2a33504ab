const moment = require('moment');
const isLocal = require('is-local-ip');
const mongoose = require('mongoose');
const { getUserLocation } = require('../../../service/maxmind');
const proxydetect = require('../../../service/proxydetect');
const getProxyRule = require('../../../commands/proxy/getProxyRule');
const { paymentTypes } = require('../../../constants/paymentType');
const { paymentStatusTypes } = require('../../../constants/paymentLog');
const User = require('../../../model/audb/User');

const getUserLoginIpRegion = (userIp) => {
  let region = '';

  if (userIp) {
    const userLocation = getUserLocation(userIp);
    region = userLocation.countryCode || '';

    if (region && userLocation && userLocation.stateCode) region = `${region}-${userLocation.stateCode}`;
  }

  return region;
};

const formatSTCard = (payment) => {
  if (payment.hasOwnProperty('_doc')) {
    payment._doc.pptype = paymentTypes.stripe;
  }
  // we need remove all links showing stripe or mentioned about it
  if (payment.merchantApiResponse && typeof payment.merchantApiResponse === 'object') {
    Object.entries(payment.merchantApiResponse).forEach(([key]) => {
      if (key !== 'body' && payment.merchantApiResponse[key] && typeof payment.merchantApiResponse[key] === 'string' && payment.merchantApiResponse[key].toLowerCase().indexOf('stripe') >= 0) {
        delete payment.merchantApiResponse[key];
      }
      if (payment.merchantApiResponse[key] && typeof payment.merchantApiResponse[key] === 'object') {
        Object.entries(payment.merchantApiResponse[key]).forEach(([key2]) => {
          if (payment.merchantApiResponse[key][key2] && typeof payment.merchantApiResponse[key][key2] === 'string' && payment.merchantApiResponse[key][key2].toLowerCase().indexOf('stripe') >= 0) {
            delete payment.merchantApiResponse[key][key2];
          }
        });
      }
    });
  }

  return payment;
};

const formatNftgateEpik = (payment) => {
  if (payment.hasOwnProperty('_doc')) {
    payment._doc.pptype = paymentTypes.nftgateEpik;
  }
  // we need remove all links showing stripe or mentioned about it
  if (payment.merchantApiResponse && typeof payment.merchantApiResponse === 'object') {
    Object.entries(payment.merchantApiResponse).forEach(([key]) => {
      if (key !== 'body' && payment.merchantApiResponse[key] && typeof payment.merchantApiResponse[key] === 'string' && payment.merchantApiResponse[key].toLowerCase().indexOf('stripe') >= 0) {
        delete payment.merchantApiResponse[key];
      }
      if (payment.merchantApiResponse[key] && typeof payment.merchantApiResponse[key] === 'object') {
        Object.entries(payment.merchantApiResponse[key]).forEach(([key2]) => {
          if (payment.merchantApiResponse[key][key2] && typeof payment.merchantApiResponse[key][key2] === 'string' && payment.merchantApiResponse[key][key2].toLowerCase().indexOf('stripe') >= 0) {
            delete payment.merchantApiResponse[key][key2];
          }
        });
      }
    });
  }

  return payment;
};

const formatNftpay = (payment) => {
  if (payment.status === paymentStatusTypes.fail) {
    if (payment.merchantApiResponse && payment.merchantApiResponse.paymentDetails
      && payment.merchantApiResponse.paymentDetails.last_payment_error
      && payment.merchantApiResponse.paymentDetails.last_payment_error.message) {
      payment.error = payment.merchantApiResponse.paymentDetails.last_payment_error.message;
    }
  }

  return payment;
};
const formatBTCard = (payment) => {
  if (payment.post && payment.post.hasOwnProperty('autoCharge')) {
    payment.autoCharge = payment.post.autoCharge;
  } else {
    payment.autoCharge = false;
  }
  if (payment.status === paymentStatusTypes.fail) {
    if (payment.merchantApiResponse && payment.merchantApiResponse.message) {
      payment.error = payment.merchantApiResponse.message;
    }
  }

  payment.descriptor = payment.post.descriptor || '';

  return payment;
};

module.exports = async (payment) => {
  payment = payment instanceof mongoose.Model ? payment.toObject() : payment;
  let descriptor = '';
  let nowpaymentsAccountName = '';

  if (payment.post) {
    if (payment.post.hasOwnProperty('descriptor')) descriptor = payment.post.descriptor;
    if (payment.post.hasOwnProperty('nowpaymentsAccountName')) nowpaymentsAccountName = payment.post.nowpaymentsAccountName;
  }

  switch (payment.pptype) {
    case paymentTypes.stripe:
      payment = formatSTCard(payment);
      break;
    case paymentTypes.braintree:
      payment = formatBTCard(payment);
      break;
    case paymentTypes.nftpay:
      payment = formatNftpay(payment);
      break;
    case paymentTypes.nftgateEpik:
      payment = formatNftgateEpik(payment);
      break;
    default:
      break;
  }

  const createdDate = moment(payment.created * 1000).format('DD/MM/YYYY HH:mm:ss');
  const region = getUserLoginIpRegion(payment.userIp);

  if (payment.addedByUid) {
    const supportUser = await User.findOne({ id: payment.addedByUid }, { _id: 0, id: 1, em: 1, name: 1 }).lean();

    if (supportUser) {
      payment.addedBy = supportUser;
      payment.addedBy.email = User.decryptEmail(supportUser.em);
      delete payment.addedBy.em;
    }
  }
  if (payment.hasOwnProperty('LoginIp')) delete payment.LoginIp;
  if (payment.userIp) {
    const userIP = payment.userIp;
    const userLocation = getUserLocation(userIP);
    const userISP = userLocation.ISP;
    const proxy = {
      vpn: false,
      rule: 'none',
    };

    if (!isLocal(userIP)) {
      const proxyResult = await proxydetect.check(userIP);
      proxy.vpn = proxyResult.result;
      proxy.rule = await getProxyRule({ userIP, userISP, userLocation });
    }

    return {
      ...payment,
      descriptor,
      nowpaymentsAccountName,
      created: createdDate,
      request: '',
      post: '',
      userLocation,
      proxy,
      region,
    };
  }

  return {
    ...payment,
    descriptor,
    nowpaymentsAccountName,
    created: createdDate,
    request: '',
    post: '',
    region,
  };
};
