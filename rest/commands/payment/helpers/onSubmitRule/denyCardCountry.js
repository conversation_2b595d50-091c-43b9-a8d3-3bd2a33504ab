const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny card country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyCardCountry = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardCountry,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canDenyCardCountries && rule.deniedCardCountries) {
    if (!cardCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card country is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedCardCountries = parseStringToArrayBySeparators(rule.deniedCardCountries, [',', '|']);

    if (deniedCardCountries.includes(cardCountry)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card country is denied to pay: ${cardCountry}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny card country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardCountry
 * */
const getDenyCardCountryString = (rule, cardCountry) => {
  if (rule.canDenyCardCountries && rule.deniedCardCountries) {
    if (!cardCountry) {
      return 'Card country required';
    }

    const deniedCardCountries = parseStringToArrayBySeparators(rule.deniedCardCountries, [',', '|']);

    if (deniedCardCountries.includes(cardCountry)) {
      return `User card country: ${cardCountry} denied`;
    }
  }

  return '';
};

module.exports.isDenyCardCountry = isDenyCardCountry;

module.exports.getDenyCardCountryString = getDenyCardCountryString;
