const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny card bank by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyCardBank = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardBankName,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canDenyCardBankName && rule.deniedCardBankName) {
    if (!cardBankName) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card bank name is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedCardBankName = parseStringToArrayBySeparators(rule.deniedCardBankName, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardBankName.includes(cardBankName.toLowerCase())) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card bank name is denied to pay: ${cardBankName}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny card bank by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardBankName
 * */
const getDenyCardBankString = (rule, cardBankName) => {
  if (rule.canDenyCardBankName && rule.deniedCardBankName) {
    if (!cardBankName) {
      return 'Card bank required';
    }

    const deniedCardBankName = parseStringToArrayBySeparators(rule.deniedCardBankName, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardBankName.includes(cardBankName.toLowerCase())) {
      return `User card bank: ${cardBankName} denied, rule: ${rule.name}`;
    }
  }

  return '';
};

module.exports.isDenyCardBank = isDenyCardBank;

module.exports.getDenyCardBankString = getDenyCardBankString;
