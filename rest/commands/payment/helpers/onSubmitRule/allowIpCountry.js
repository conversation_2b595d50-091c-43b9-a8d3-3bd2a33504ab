const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is allow IP country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isAllowIpCountry = async (user, rule, userData) => {
  const {
    tkey,
    ipCountry,
    ipState,
    userIp,
    skipSaveLogs = false,
  } = userData;
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({}, rest);

  if (rule.canAllowIpCountries && rule.allowedIpCountries) {
    if (!ipCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user IP country is not provided, IP: ${userIp}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }

    const allowedIpCountries = parseStringToArrayBySeparators(rule.allowedIpCountries, [',', '|']);
    const countryStateKey = ipState ? `${ipCountry}_${ipState}` : ipCountry;

    if (!allowedIpCountries.includes(ipCountry) && !allowedIpCountries.includes(countryStateKey)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user IP country is not allowed to pay: ${countryStateKey}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }
  }

  return true;
};

/**
 * Method check is allow IP country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} ipCountry
 * @param {string} ipState - not require
 * */
const getAllowIpCountryString = (rule, ipCountry, ipState) => {
  if (rule.canAllowIpCountries && rule.allowedIpCountries) {
    if (!ipCountry) {
      return 'IP country required';
    }

    const allowedIpCountries = parseStringToArrayBySeparators(rule.allowedIpCountries, [',', '|']);
    const countryStateKey = ipState ? `${ipCountry}_${ipState}` : ipCountry;

    if (!allowedIpCountries.includes(ipCountry) && !allowedIpCountries.includes(countryStateKey)) {
      return `User IP country: ${ipCountry} is not allowed`;
    }
  }

  return '';
};

module.exports.isAllowIpCountry = isAllowIpCountry;

module.exports.getAllowIpCountryString = getAllowIpCountryString;
