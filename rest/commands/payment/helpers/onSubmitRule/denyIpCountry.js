const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny IP country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyIpCountry = async (user, rule, userData) => {
  const {
    tkey,
    ipCountry,
    ipState,
    userIp,
    skipSaveLogs = false,
  } = userData;
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({}, rest);

  if (rule.canDenyIpCountries && rule.deniedIpCountries) {
    if (!ipCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user IP country is not provided, IP: ${userIp}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedIpCountries = parseStringToArrayBySeparators(rule.deniedIpCountries, [',', '|']);
    const countryStateKey = ipState ? `${ipCountry}_${ipState}` : ipCountry;

    if (deniedIpCountries.includes(ipCountry) || deniedIpCountries.includes(countryStateKey)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user IP country is denied to pay: ${countryStateKey}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny IP country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} ipCountry
 * @param {string} ipState - not require
 * */
const getDenyIpCountryString = (rule, ipCountry, ipState) => {
  if (rule.canDenyIpCountries && rule.deniedIpCountries) {
    if (!ipCountry) {
      return 'IP country required';
    }

    const deniedIpCountries = parseStringToArrayBySeparators(rule.deniedIpCountries, [',', '|']);
    const countryStateKey = ipState ? `${ipCountry}_${ipState}` : ipCountry;

    if (deniedIpCountries.includes(ipCountry) || deniedIpCountries.includes(countryStateKey)) {
      return `User IP country: ${ipCountry} denied`;
    }
  }

  return '';
};

module.exports.isDenyIpCountry = isDenyIpCountry;

module.exports.getDenyIpCountryString = getDenyIpCountryString;
