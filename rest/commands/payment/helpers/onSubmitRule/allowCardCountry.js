const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is allow card country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isAllowCardCountry = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardCountry,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canAllowCardCountries && rule.allowedCardCountries) {
    if (!cardCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card country is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }

    const allowedCardCountries = parseStringToArrayBySeparators(rule.allowedCardCountries, [',', '|']);

    if (!allowedCardCountries.includes(cardCountry)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card country is not allowed to pay: ${cardCountry}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }
  }

  return true;
};

/**
 * Method check is allow card country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardCountry
 * */
const getAllowCardCountryString = (rule, cardCountry) => {
  if (rule.canAllowCardCountries && rule.allowedCardCountries) {
    if (!cardCountry) {
      return 'Card country required';
    }

    const allowedCardCountries = parseStringToArrayBySeparators(rule.allowedCardCountries, [',', '|']);

    if (!allowedCardCountries.includes(cardCountry)) {
      return `User card country: ${cardCountry} is not allowed`;
    }
  }

  return '';
};

module.exports.isAllowCardCountry = isAllowCardCountry;

module.exports.getAllowCardCountryString = getAllowCardCountryString;
