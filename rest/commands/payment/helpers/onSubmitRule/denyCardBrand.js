const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny card brand by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyCardBrand = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardBrand,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canDenyCardBrands && rule.deniedCardBrands) {
    if (!cardBrand) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card brand is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedCardBrands = parseStringToArrayBySeparators(rule.deniedCardBrands, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardBrands.includes(cardBrand.toLowerCase())) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card brand is denied to pay: ${cardBrand}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny card brand by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardBrand
 * */
const getDenyCardBrandString = (rule, cardBrand) => {
  if (rule.canDenyCardBrands && rule.deniedCardBrands) {
    if (!cardBrand) {
      return 'Card brand required';
    }

    const deniedCardBrands = parseStringToArrayBySeparators(rule.deniedCardBrands, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardBrands.includes(cardBrand.toLowerCase())) {
      return `User card brand: ${cardBrand} denied`;
    }
  }

  return '';
};

module.exports.isDenyCardBrand = isDenyCardBrand;

module.exports.getDenyCardBrandString = getDenyCardBrandString;
