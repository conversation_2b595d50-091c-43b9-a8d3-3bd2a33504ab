const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny card type by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyCardType = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardType,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canDenyCardTypes && rule.deniedCardTypes) {
    if (!cardType) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card type is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedCardTypes = parseStringToArrayBySeparators(rule.deniedCardTypes, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardTypes.includes(cardType.toLowerCase())) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card type is denied to pay: ${cardType}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny card type by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardType
 * */
const getDenyCardTypeString = (rule, cardType) => {
  if (rule.canDenyCardTypes && rule.deniedCardTypes) {
    if (!cardType) {
      return 'Card type required';
    }

    const deniedCardTypes = parseStringToArrayBySeparators(rule.deniedCardTypes, [',', '|']).map(value => value.toLowerCase());

    if (deniedCardTypes.includes(cardType.toLowerCase())) {
      return `User card type: ${cardType} denied`;
    }
  }

  return '';
};

module.exports.isDenyCardType = isDenyCardType;

module.exports.getDenyCardTypeString = getDenyCardTypeString;
