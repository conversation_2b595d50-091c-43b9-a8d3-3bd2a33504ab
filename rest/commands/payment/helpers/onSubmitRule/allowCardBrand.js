const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is allow card brand by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isAllowCardBrand = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardBrand,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canAllowCardBrands && rule.allowedCardBrands) {
    if (!cardBrand) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card brand is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }

    const allowedCardBrands = parseStringToArrayBySeparators(rule.allowedCardBrands, [',', '|']).map(value => value.toLowerCase());

    if (!allowedCardBrands.includes(cardBrand.toLowerCase())) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card brand is not allowed to pay: ${cardBrand}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }
  }

  return true;
};

/**
 * Method check is allow card brand by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardBrand
 * */
const getAllowCardBrandString = (rule, cardBrand) => {
  if (rule.canAllowCardBrands && rule.allowedCardBrands) {
    if (!cardBrand) {
      return 'Card brand required';
    }

    const allowedCardBrands = parseStringToArrayBySeparators(rule.allowedCardBrands, [',', '|']).map(value => value.toLowerCase());

    if (!allowedCardBrands.includes(cardBrand.toLowerCase())) {
      return `User card brand: ${cardBrand} is not allowed`;
    }
  }

  return '';
};

module.exports.isAllowCardBrand = isAllowCardBrand;

module.exports.getAllowCardBrandString = getAllowCardBrandString;
