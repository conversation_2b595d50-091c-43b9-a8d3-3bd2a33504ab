const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is deny user address country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isDenyUserAddressCountry = async (user, rule, userData) => {
  const {
    tkey,
    addressCountry,
    skipAddressCountry,
    userIp,
    skipSaveLogs = false,
  } = userData;
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({}, rest);

  if (!skipAddressCountry && rule.canDenyUserAddressCountries && rule.deniedUserAddressCountries) {
    if (!addressCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user address country is not provided, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }

    const deniedUserAddressCountries = parseStringToArrayBySeparators(rule.deniedUserAddressCountries, [',', '|']);

    if (deniedUserAddressCountries.includes(addressCountry)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user address country is denied to pay: ${addressCountry}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return true;
    }
  }

  return false;
};

/**
 * Method check is deny user address country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} addressCountry
 * */
const getDenyUserAddressCountryString = (rule, addressCountry) => {
  if (rule.canDenyUserAddressCountries && rule.deniedUserAddressCountries) {
    if (!addressCountry) {
      return 'Address country required';
    }

    const deniedUserAddressCountries = parseStringToArrayBySeparators(rule.deniedUserAddressCountries, [',', '|']);

    if (deniedUserAddressCountries.includes(addressCountry)) {
      return `User address country: ${addressCountry} denied`;
    }
  }

  return '';
};

module.exports.isDenyUserAddressCountry = isDenyUserAddressCountry;

module.exports.getDenyUserAddressCountryString = getDenyUserAddressCountryString;
