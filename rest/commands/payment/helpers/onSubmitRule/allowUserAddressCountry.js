const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is allow user address country by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isAllowUserAddressCountry = async (user, rule, userData) => {
  const {
    tkey,
    addressCountry,
    skipAddressCountry,
    userIp,
    skipSaveLogs = false,
  } = userData;
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({}, rest);

  if (!skipAddressCountry && rule.canAllowUserAddressCountries && rule.allowedUserAddressCountries) {
    if (!addressCountry) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user address country is not provided, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }

    const allowedUserAddressCountries = parseStringToArrayBySeparators(rule.allowedUserAddressCountries, [',', '|']);

    if (!allowedUserAddressCountries.includes(addressCountry)) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user address country name is not allowed to pay: ${addressCountry}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }
  }

  return true;
};

/**
 * Method check is allow user address country by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} addressCountry
 * */
const getAllowUserAddressCountryString = (rule, addressCountry) => {
  if (rule.canAllowUserAddressCountries && rule.allowedUserAddressCountries) {
    if (!addressCountry) {
      return 'Address country required';
    }

    const allowedUserAddressCountries = parseStringToArrayBySeparators(rule.allowedUserAddressCountries, [',', '|']);

    if (!allowedUserAddressCountries.includes(addressCountry)) {
      return `User address country: ${addressCountry} is not allowed`;
    }
  }

  return '';
};

module.exports.isAllowUserAddressCountry = isAllowUserAddressCountry;

module.exports.getAllowUserAddressCountryString = getAllowUserAddressCountryString;
