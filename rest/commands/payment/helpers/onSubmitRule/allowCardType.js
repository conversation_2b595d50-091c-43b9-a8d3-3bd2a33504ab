const parseStringToArrayBySeparators = require('../../../../helpers/parseStringToArrayBySeparators');
const addPaymentActionsLog = require('../../addActionsLog');

/* NOTES: Both methods should be similar! */

/**
 * Method check is allow card type by rule
 *
 * @param {object|User} user
 * @param {object} rule
 * @param {object} userData - user data for check
 *
 * @return {Boolean}
 * */
const isAllowCardType = async (user, rule, userData) => {
  const {
    tkey,
    number,
    cardType,
    userIp,
    skipSaveLogs = false,
  } = userData;

  const parsedNumber = number ? number.replace(/\s/g, '') : '';
  const first6 = parsedNumber ? parsedNumber.substring(0, 6) : '';
  const last4 = parsedNumber ? parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length) : '';
  const cardHidden = number ? `${first6}******${last4}` : '';
  const { number: _, ...rest } = userData;
  const userDataForLog = Object.assign({ cardHidden }, rest);

  if (rule.canAllowCardTypes && rule.allowedCardTypes) {
    if (!cardType) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card type is not provided, card ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }

    const allowedCardTypes = parseStringToArrayBySeparators(rule.allowedCardTypes, [',', '|']).map(value => value.toLowerCase());

    if (!allowedCardTypes.includes(cardType.toLowerCase())) {
      if (!skipSaveLogs) {
        const message = `Check can pay on submit: user card type is not allowed to pay: ${cardType}, card: ${cardHidden}, tkey: ${tkey}, rule: ${rule.name}`;
        await addPaymentActionsLog({ tkey, user, message, data: userDataForLog, userIp });
      }

      return false;
    }
  }

  return true;
};

/**
 * Method check is allow card type by rule and return error message or empty string
 *
 * @param {object} rule
 * @param {string} cardType
 * */
const getAllowCardTypeString = (rule, cardType) => {
  if (rule.canAllowCardTypes && rule.allowedCardTypes) {
    if (!cardType) {
      return 'Card type required';
    }

    const allowedCardTypes = parseStringToArrayBySeparators(rule.allowedCardTypes, [',', '|']).map(value => value.toLowerCase());

    if (!allowedCardTypes.includes(cardType.toLowerCase())) {
      return `User card type: ${cardType} is not allowed`;
    }
  }

  return '';
};

module.exports.isAllowCardType = isAllowCardType;

module.exports.getAllowCardTypeString = getAllowCardTypeString;
