const canPayWithGroupRule = require('../helpers/rules/canPayWithGroupRule');

/**
 * Method check can pay payment types by group rules
 *
 * @param {Array} rules - group rules list
 * @param {object} canPayWithBasicRules - basic rules object
 *
 * @return {object} - pair rule._id: boolean
 * */
module.exports = async (rules = [], canPayWithBasicRules = {}) => {
  const canPayGroupRules = {};
  const promises = [];

  rules.forEach(rule => promises.push((async () => {
    const isUserCanPayWithRule = canPayWithGroupRule(rule, canPayWithBasicRules);
    canPayGroupRules[rule._id.toString()] = isUserCanPayWithRule;
  })()));

  await Promise.all(promises);

  return canPayGroupRules;
};
