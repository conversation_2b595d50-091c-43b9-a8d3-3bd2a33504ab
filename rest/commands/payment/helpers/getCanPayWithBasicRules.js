const getAllBasicRules = require('./../getAllBasicRules');
const getRuleFunctionByName = require('./rules/getRuleFunctionByName');

const checkRule = async (user, paymentInfo, tkey, fromPage, rule, canPayBasicRules) => {
  const methodName = rule.methodName;
  const func = getRuleFunctionByName(methodName);

  if (func) {
    const funcResponse = await func({ user, paymentInfo, tkey, fromPage, rule });
    console.log(funcResponse);

    canPayBasicRules[methodName] = funcResponse;
  } else {
    canPayBasicRules[methodName] = false;
  }
};

// eslint-disable-next-line no-unused-vars
module.exports = async ({ user, paymentInfo, fromPage = '', tkey = null }) => {
  const basicRulesResponse = await getAllBasicRules();
  const basicRules = basicRulesResponse.list;
  const canPayBasicRules = {};
  const promises = [];

  basicRules.forEach(rule => promises.push(checkRule(user, paymentInfo, tkey, fromPage, rule, canPayBasicRules)));

  await Promise.all(promises);

  return canPayBasicRules;
};
