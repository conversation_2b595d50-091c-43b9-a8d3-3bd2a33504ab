const PaymentLog = require('../../../../model/audb/PaymentLog');
const getPackageIds = require('../../../package/helper/getPackageIds');

/**
 * @description Method check has user 1 and 3 month package payment logs
 *
 * @param {object} options
 * @param {User} options.user - user model
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async ({ user }) => {
  if (!user) return false;

  // we need to exclude upgrade packages, full pack is the same as normal+upgrade, so no need to check upgrade packages
  const longTermPackageIds = await getPackageIds({ includeUpgrade: false, includeTrial: false, longTermsOnly: true });
  const allPackageIds = await getPackageIds({ includeUpgrade: false, includeTrial: false });

  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, package: { $in: allPackageIds },
  }).cache(300).lean().exec();

  if (paymentLogs.length >= 2) {
    for (let i = 0; i < paymentLogs.length; ++i) {
      if (longTermPackageIds.includes(paymentLogs[i].package)) {
        return true;
      }
    }
  }

  return false;
};
