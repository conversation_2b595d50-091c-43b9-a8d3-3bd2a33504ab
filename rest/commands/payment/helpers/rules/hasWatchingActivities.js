const moment = require('moment');
const UserLogVod = require('../../../../model/audb/UserLogVod');
const UserLogLive = require('../../../../model/audb/UserLogLive');
const UserLogRecord = require('../../../../model/audb/UserLogRecord');
const UserDailyStatistics = require('../../../../model/audb/UserDailyStatistics');

const MAX_LOGS_PER_DAY = 5;
const MIN_LAST_LOGS = 15;
const TOTAL_CALCULATED_DAYS = 7;

const getAggregationStats = (userId, calculationDays, endTime) => {
  let dateFrom;
  let dateTo;
  const startOfToday = moment().startOf('day').unix();

  if (endTime && endTime < startOfToday) {
    const date = moment(endTime * 1000).endOf('day');
    dateTo = date.unix();
    dateFrom = date.add(`-${calculationDays}`, 'days').unix();
  } else {
    dateFrom = moment().add(`-${calculationDays}`, 'days').startOf('day').unix();
    dateTo = moment().subtract(1, 'day').endOf('day').unix();
  }

  const aggregation = [
    { $match: {
      $and: [
        { userId },
        { date: { $gte: dateFrom } },
        { date: { $lte: dateTo } },
      ],
    } },
  ];

  return aggregation;
};

const getAggregationTodayLogs = (fieldName, userId) => {
  const dateTo = moment().unix();
  const dateFrom = moment().startOf('day').unix();

  return [
    { $match: {
      $and: [
        { uid: userId },
        { playtime: { $gte: dateFrom } },
        { playtime: { $lte: dateTo } },
      ],
    } },
    {
      $addFields: {
        sourceDate: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: {
              $add: [
                new Date(0),
                { $multiply: [1000, '$playtime'] },
              ],
            },
          },
        },
      },
    },
    // Count all occurrences
    { $group: {
      _id: {
        sourceId: `$${fieldName}`,
        time: '$sourceDate',
      },
      count: { $sum: 1 },
    } },
    // Sum all occurrences and count distinct
    { $group: {
      _id: {
        date: '$_id.time',
      },
      totalCount: { $sum: '$count' },
      distinctCount: { $sum: 1 },
    } },
  ];
};

const addCountsByDay = (watchesByDate, watchLogs) => {
  watchLogs.forEach((watchLog) => {
    if (watchesByDate.hasOwnProperty(watchLog._id.date)) {
      watchesByDate[watchLog._id.date] += watchLog.distinctCount;
    } else {
      watchesByDate[watchLog._id.date] = watchLog.distinctCount;
    }
  });

  return watchesByDate;
};

/**
 * @description Method calculate last user activities for last XX days with max per days and minimum count
 *
 * @param options
 * @param {User} options.user - user model
 * @param {object} options.rule
 * @param {number} options.rule.calculationDays - last XX days for calculate logs
 * @param {number} options.rule.minLogs - minimum required logs count
 * @param {number} options.rule.maxLogsPerDays - maximum logs per day
 * @param {number} options.rule.endTime - not require, calculation logs before this date (included endTime), unit time
 *
 * @return {boolean|Promise} true|false
 *
 * @example
 * logic:
 * 15 watching activities (in last 7 days )  will open the 3 months package..
 * but calculate the activites based on 5 max a day calculate activity, example:
 * if i watched today 15 times some vod,live,records and yesterday i didnt watch at all
 * and other days i didn't watch as well then its not "15 times" because i limit per day as calculation = 5 max
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user) return false;
  if (user.skipPaymentWatchActivities) return true;

  const defaultOptions = {
    calculationDays: TOTAL_CALCULATED_DAYS,
    minLogs: MIN_LAST_LOGS,
    maxLogsPerDays: MAX_LOGS_PER_DAY,
  };
  const { calculationDays, minLogs, maxLogsPerDays, endTime = null } = Object.assign({}, defaultOptions, rule);
  let activities = 0;

  const startOfToday = moment().startOf('day').unix();

  if (!endTime || endTime > startOfToday) {
    const vodLogs = await UserLogVod.aggregate(getAggregationTodayLogs('vodid', user.id, calculationDays, endTime)).cache(300).exec();
    const liveLogs = await UserLogLive.aggregate(getAggregationTodayLogs('channel', user.id, calculationDays, endTime)).cache(300).exec();
    const recordLogs = await UserLogRecord.aggregate(getAggregationTodayLogs('show._id', user.id, calculationDays, endTime)).cache(300).exec();

    let watchesByDate = {};
    watchesByDate = addCountsByDay(watchesByDate, vodLogs);
    watchesByDate = addCountsByDay(watchesByDate, liveLogs);
    watchesByDate = addCountsByDay(watchesByDate, recordLogs);

    for (const [, value] of Object.entries(watchesByDate)) {
      if (value > maxLogsPerDays) activities += maxLogsPerDays;
      else activities += value;
    }
  }
  if (calculationDays > 1 || endTime && endTime < startOfToday) {
    const stats = await UserDailyStatistics.aggregate(getAggregationStats(user.id, calculationDays, endTime)).cache(300).exec();

    for (const stat of stats) {
      const totalUniqueSources = (stat.uniqueChannelsCount || 0) + (stat.uniqueRecordsCount || 0) + (stat.uniqueVodsCount || 0);

      if (totalUniqueSources > maxLogsPerDays) activities += maxLogsPerDays;
      else activities += totalUniqueSources;
    }
  }

  return activities >= minLogs;
};
