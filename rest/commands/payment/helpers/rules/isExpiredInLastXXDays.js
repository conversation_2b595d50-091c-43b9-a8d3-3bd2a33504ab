const moment = require('moment');

/**
 * @description Check if user active or expired in last XX days
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {object} options.rule - payment basic rule
 * @param {Array} options.rule.days - last XX days
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user || !rule || !rule.days) return false;

  const { days } = Object.assign({}, rule);

  const now = moment().subtract(days, 'days').unix();

  return user.expires >= now;
};
