const UserConfig = require('../../../../model/audb/UserConfig');
const getAllTypes = require('../../../../commands/payment/getAllTypes');
const getAllPaymentTypeAccounts = require('../../../../commands/payment/getAllPaymentTypeAccounts');
const isBlacklistedUser = require('../rules/isBlacklistedUser');

const hasUserBypassedTypes = userConfig => userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes && userConfig.bypassedPaymentTypes.length;
const hasUserBypassedAccounts = userConfig => userConfig.hasOwnProperty('bypassedPaymentAccounts') && userConfig.bypassedPaymentAccounts && userConfig.bypassedPaymentAccounts.length;

/**
 * @description Method get list of user bypassed payment types name
 *
 * @param {object} options
 * @param {User} options.user - user model
 *
 * @return {array<string>}
 * */
module.exports = async (options = {}) => {
  const { user } = options;

  if (!user) return [];
  // blacklisted user should not bypass any rules
  if (!user.skipBlacklistPayment && await isBlacklistedUser({ user })) return [];

  const userConfig = await UserConfig.findOne({ uid: user.id }).lean();
  const bypassedPaymentTypeNamesSet = new Set([]);

  if (!userConfig) return [];
  if (hasUserBypassedTypes(userConfig) || hasUserBypassedAccounts(userConfig)) {
    const paymentAccountsResult = await getAllPaymentTypeAccounts();
    const paymentTypesResult = await getAllTypes();

    if (paymentTypesResult && paymentTypesResult.hasOwnProperty('list') && paymentTypesResult.list
      && paymentAccountsResult && paymentAccountsResult.hasOwnProperty('list') && paymentAccountsResult.list) {
      const paymentTypes = paymentTypesResult.list;
      const paymentAccounts = paymentAccountsResult.list;

      if (hasUserBypassedTypes(userConfig)) {
        userConfig.bypassedPaymentTypes.forEach((typeId) => {
          paymentTypes.forEach((type) => {
            if (type._id.toString() === typeId && type.enabled) bypassedPaymentTypeNamesSet.add(type.name);
          });
        });
      }
      if (hasUserBypassedAccounts(userConfig)) {
        userConfig.bypassedPaymentAccounts.forEach((accountId) => {
          Object.entries(paymentAccounts).forEach(([typeName, type]) => {
            if (type.enabled && type.accounts && type.accounts.length) {
              type.accounts.forEach((account) => {
                if (account._id.toString() === accountId && account.enabled) bypassedPaymentTypeNamesSet.add(typeName);
              });
            }
          });
        });
      }
    }
  }

  return Array.from(bypassedPaymentTypeNamesSet);
};
