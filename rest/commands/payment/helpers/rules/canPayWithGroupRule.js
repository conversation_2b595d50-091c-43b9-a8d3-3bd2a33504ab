/**
 * Recursive function to get single rule or group rule value
 *
 * @param {object} rule
 * @param {object} basicRulesResults
 */
function evaluateFunction(ruleConfig, ruleData) {
  if (ruleConfig.type === 'rule') {
    const basicRuleId = ruleConfig.basicRuleId;
    const basicRuleValue = ruleConfig.basicRuleValue;
    //  Get basic rule calculation result value by basic rule ID
    const actualRuleValue = ruleData[basicRuleId];

    return basicRuleValue === actualRuleValue;
  }
  // else initial rule or included rules

  const groupRules = ruleConfig.rules;

  // calculate first rule value
  let result = evaluateFunction(groupRules[0], ruleData);
  let prevOperator = groupRules[0].groupRuleOperator;

  for (let i = 1; i < groupRules.length; i++) {
    const nextRule = groupRules[i];

    if (prevOperator === 'OR') {
      // if previous AND group is true, no need to calculate next expressions
      if (result) return result;

      // lets start from next OR group
      result = evaluateFunction(nextRule, ruleData);
    } else if (prevOperator === 'AND') {
      const nextRuleValue = evaluateFunction(nextRule, ruleData);

      result = result && nextRuleValue;
    }

    prevOperator = nextRule.groupRuleOperator;
  }

  return result;
}

/**
 * @param {object} rule - group rule
 * @param {object} basicRulesResults - can pay with basic rules object
 *
 * @return {boolean}
 * */
module.exports = (rule, basicRulesResults) => {
  if (!rule) return false;

  const result = evaluateFunction(rule, basicRulesResults);

  return result;
};
