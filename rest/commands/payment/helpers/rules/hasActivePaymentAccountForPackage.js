const getAllPaymentTypeAccounts = require('../../../../commands/payment/getAllPaymentTypeAccounts');

/**
 * @description Method checks has active payment account and payment type for the package
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {string} options.paymentType - payment type name
 * @param {number} options.packageId - package ID
 *
 * @return {boolean} true|false
 * */
module.exports = async (options = {}) => {
  const { paymentType, packageId, user } = options;

  if (!user || !paymentType || !packageId) return false;

  const paymentAccountsResult = await getAllPaymentTypeAccounts();

  // package account and payment type both should be active
  if (paymentAccountsResult && paymentAccountsResult.hasOwnProperty('list') && paymentAccountsResult.list) {
    const paymentAccounts = paymentAccountsResult.list;
    const paymentTypeActiveAccounts = paymentAccounts.hasOwnProperty(paymentType)
      && paymentAccounts[paymentType].accounts && paymentAccounts[paymentType].accounts.length
      ? paymentAccounts[paymentType].accounts.filter(account => !!account.enabled && !!account.PaymentType.enabled
        && (account.packageId === packageId || account.packageIds.includes(packageId))) : [];

    if (paymentTypeActiveAccounts.length) return true;
  }

  return false;
};
