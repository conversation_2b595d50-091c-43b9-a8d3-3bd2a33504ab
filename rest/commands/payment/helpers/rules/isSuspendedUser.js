const mongoose = require('mongoose');
const Suspended = require('../../../../model/audb/Suspended');
const config = require('../../../../../config');

/**
 * @description Method check is user suspended
 *
 * @param {object} options
 * @param {object|model} options.user - user object or model
 *
 * @return {boolean} true|false
 * */
module.exports = async ({ user }) => {
  if (!user) return false;

  const data = user instanceof mongoose.Model ? user.toObject() : user;

  if (data && data.hasOwnProperty('id') && data.id) {
    const suspendedUser = await Suspended.findOne({ uid: data.id })
      .lean().cache(config.suspend.cache, `suspended_id_${data.id}`);

    if (suspendedUser) return true;
  }

  return false;
};
