const PaymentLog = require('../../../../model/audb/PaymentLog');
const getAllTypes = require('../../../payment/getAllTypes');

/**
 * @description Check if user has card payments only
 *
 * @param {object} options
 * @param {User} options.user - user model
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async ({ user }) => {
  if (!user) return false;

  const allTypesResult = await getAllTypes();
  const allTypes = allTypesResult.list;
  const cardPaymentTypes = [];
  allTypes.forEach((type) => {
    if (type.hasOwnProperty('PaymentGroup') && type.PaymentGroup && (type.PaymentGroup.name === 'Card')) cardPaymentTypes.push(type.name);
  });

  // add previous used payment types
  cardPaymentTypes.push(...['stripe', 'braintree', 'manual', 'paypal', 'paypal_card']);
  // add additional types to the card type
  cardPaymentTypes.push(...['sendwyre-card', 'tazapay', 'sendwyre', 'applepay']);

  const paymentNonCardLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $nin: cardPaymentTypes },
  }).cache(300).lean();
  const paymentCardLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $in: cardPaymentTypes },
  }).limit(1).cache(300).lean();

  if (!paymentCardLogs.length || paymentNonCardLogs.length) return false;

  return true;
};
