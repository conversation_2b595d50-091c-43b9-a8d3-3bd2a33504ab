const PaymentLog = require('../../../../model/audb/PaymentLog');
const getPackageIds = require('../../../package/helper/getPackageIds');

/**
 * @description Check if user has no payments
 *
 * @param options
 * @param {User} options.user - user model
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user } = options;

  if (!user) return false;

  const allPackageIds = await getPackageIds({ includeTrial: false });
  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, package: { $in: allPackageIds },
  }).cache(300).lean();

  if (!paymentLogs.length) return true;

  return false;
};
