const moment = require('moment');

/**
 * @description Method check is user old by preset date
 *
 * @param {object} options
 * @param {User} options.user - user model
 *
 * @return {boolean} true|false
 * */
module.exports = (options = {}) => {
  const { user, rule } = options;

  if (!user) return false;

  // From Feb 01 2022 all new registered users will not be able to pay with credit card,
  // instead use moonpay, crypto, etc only
  // const agedUsersEndDate = moment(rule.date).unix();
  const agedUsersEndDate = moment('01-02-2022', 'DD-MM-YYYY').unix();

  if (user.regtime <= agedUsersEndDate) return true;

  return false;
};
