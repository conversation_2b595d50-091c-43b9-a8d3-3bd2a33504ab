/**
 * @description Method is user paying with a crypto coin
 *
 * @param {object} options
 * @param {string} options.coin - crypto coin name
 * @param {string} options.rule.coin
 *
 * @return {boolean} true|false
 * */
module.exports = async (options = {}) => {
  const { coin = null, rule } = options;

  if (!coin || !rule.coin) return false;
  if (coin.toLowerCase() === rule.coin.toLowerCase()) return true;

  return false;
};
