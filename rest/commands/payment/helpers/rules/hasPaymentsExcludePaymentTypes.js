const PaymentLog = require('../../../../model/audb/PaymentLog');
const getPackageIds = require('../../../package/helper/getPackageIds');

/**
 * @description Check if user has at least 'minLogs' payments exclude payment types
 *
 * @param options
 * @param {User} options.user - user model
 * @param {object} options.rule
 * @param {number} options.rule.minLogs - minimum required amount of payments
 * @param {Array} options.rule.paymentTypes - list of exclude payment types to check
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user) return false;

  const defaultOptions = {
    minLogs: 1,
  };
  const { minLogs, paymentTypes } = Object.assign({}, defaultOptions, rule);
  const allPackageIds = await getPackageIds({ includeTrial: false });
  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, package: { $in: allPackageIds }, pptype: { $nin: paymentTypes },
  }).cache(300).lean();

  if (paymentLogs.length && paymentLogs.length >= minLogs) return true;

  return false;
};
