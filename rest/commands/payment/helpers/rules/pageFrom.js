/**
 * @description Check if user comes from the pageFrom page
 *
 * @param options
 * @param {User} options.user - user model
 * @param {object} options.rule
 * @param {string} options.rule.pageFrom
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule, fromPage = '' } = options;

  if (!user) return false;

  const defaultOptions = {
    pageFrom: '',
  };
  const { pageFrom: rulePageFrom } = Object.assign({}, defaultOptions, rule);

  if ((!fromPage && !rulePageFrom) || fromPage === rulePageFrom) return true;

  return false;
};
