const PaymentLog = require('../../../../model/audb/PaymentLog');

/**
 * @description Check if user paid XX payments with payment types
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {object} options.rule - payment basic rule
 * @param {Array} options.rule.paymentTypes - list of payment type names
 * @param {number} options.rule.minLogs - minimum required amount of payments
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user || !rule || !rule.hasOwnProperty('paymentTypes') || !rule.paymentTypes) return false;

  const defaultOptions = {
    minLogs: 1,
  };
  const { minLogs } = Object.assign({}, defaultOptions, rule);
  const { paymentTypes } = Object.assign({}, rule);

  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $in: paymentTypes },
  }).cache(300).lean();

  if (paymentLogs.length && paymentLogs.length >= minLogs) return true;

  return false;
};
