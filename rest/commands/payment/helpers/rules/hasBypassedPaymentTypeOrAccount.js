const UserConfig = require('../../../../model/audb/UserConfig');
const getAllTypes = require('../../../../commands/payment/getAllTypes');
const getAllPaymentTypeAccounts = require('../../../../commands/payment/getAllPaymentTypeAccounts');
const isBlacklistedUser = require('../rules/isBlacklistedUser');

const hasUserBypassedTypes = userConfig => userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes && userConfig.bypassedPaymentTypes.length;
const hasUserBypassedAccounts = userConfig => userConfig.hasOwnProperty('bypassedPaymentAccounts') && userConfig.bypassedPaymentAccounts && userConfig.bypassedPaymentAccounts.length;

/**
 * @description Method checks has user bypassed payment types or accounts
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {string} options.paymentType - payment type name
 * @param {number} options.packageId - package ID
 *
 * @return {boolean} true|false
 * */
module.exports = async (options = {}) => {
  const { user, packageId, paymentType } = options;

  if (!user || !packageId || !paymentType) return false;
  // blacklisted user should not bypass any rules
  if (!user.skipBlacklistPayment && await isBlacklistedUser({ user })) return [];

  const userConfig = await UserConfig.findOne({ uid: user.id }).lean();

  if (!userConfig) return false;
  // allow to pay users with bypassed type or account if they are active
  if (hasUserBypassedTypes(userConfig) || hasUserBypassedAccounts(userConfig)) {
    const paymentAccountsResult = await getAllPaymentTypeAccounts();
    const paymentTypesResult = await getAllTypes();

    if (paymentTypesResult && paymentTypesResult.hasOwnProperty('list') && paymentTypesResult.list
      && paymentAccountsResult && paymentAccountsResult.hasOwnProperty('list') && paymentAccountsResult.list) {
      const paymentAccounts = paymentAccountsResult.list;
      const paymentTypeActiveAccounts = paymentAccounts.hasOwnProperty(paymentType)
        && paymentAccounts[paymentType].accounts && paymentAccounts[paymentType].accounts.length
        ? paymentAccounts[paymentType].accounts.filter(account => !!account.enabled
          && (account.packageId === packageId || account.packageIds.includes(packageId))) : [];

      // if has no active accounts should not bypass rule
      if (!paymentTypeActiveAccounts.length) return false;

      let paymentTypeObject = null;
      paymentTypesResult.list.forEach((type) => {
        if (type.name === paymentType) paymentTypeObject = type;
      });

      // if has no active payment type should not bypass rule
      if (!paymentTypeObject || !paymentTypeObject.enabled) return false;
      // check if user has bypassed current paymentType
      if (hasUserBypassedTypes(userConfig) && userConfig.bypassedPaymentTypes.includes(paymentTypeObject._id.toString())) {
        return true;
      }
      // check if user has bypassed account for current paymentType
      if (hasUserBypassedAccounts(userConfig)) {
        let hasBypassedPaymentTypeAccount = false;
        paymentTypeActiveAccounts.forEach((account) => {
          if (userConfig.bypassedPaymentAccounts.includes(account._id.toString())) hasBypassedPaymentTypeAccount = true;
        });

        if (hasBypassedPaymentTypeAccount) return true;
      }
    }
  }

  return false;
};
