const mongoose = require('mongoose');
const PaymentBlacklist = require('../../../../model/audb/PaymentBlacklist');
const config = require('../../../../../config');

const checkBlacklistedUserDetails = async (data) => {
  // do not check empty details
  if (!data || (!data.hasOwnProperty('firstname') && !data.hasOwnProperty('lastname')
    && !data.hasOwnProperty('address') && !data.hasOwnProperty('country')
    && !data.hasOwnProperty('state') && !data.hasOwnProperty('city')
    && !data.hasOwnProperty('zip'))) return false;

  const blacklistedUserRules = await PaymentBlacklist.find(
    { $and: [
      { ip: { $exists: false } },
      { uid: { $exists: false } },
    ] },
    { firstname: 1, lastname: 1, address: 1, country: 1, state: 1, city: 1, zip: 1 },
  ).lean().cache(config.paymentBlacklist.cache, 'paymentBlacklist_billingDetails');

  for (let i = 0; i < blacklistedUserRules.length; ++i) {
    const fraudRule = blacklistedUserRules[i];
    delete fraudRule._id;
    let match = true;

    for (const key of Object.keys(fraudRule)) {
      if (!data.hasOwnProperty(key) || !data[key] || !data[key].toLowerCase().match(fraudRule[key].toLowerCase())) {
        match = false;
        continue;
      }
    }

    if (match) return {
      rule: JSON.stringify(fraudRule),
      data: JSON.stringify(data),
    };
  }

  return false;
};

/**
 * @description Method check is user blacklisted
 *
 * @param {object} options
 * @param {object|model} options.user - user object or model
 * @param {boolean} options.checkUserIdOnly
 *
 * @return {Promise<boolean>} true|false
 * */
module.exports = async ({ user, checkUserIdOnly = false }) => {
  if (!user) return false;

  const data = user instanceof mongoose.Model ? user.toObject() : user;

  if (data && data.hasOwnProperty('id') && data.id) {
    const blacklistedUserId = await PaymentBlacklist.findOne({ uid: data.id })
      .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_id_${data.id}`);

    if (blacklistedUserId) return true;
  }
  if (checkUserIdOnly) return false;
  if (data && data.hasOwnProperty('ip') && data.ip) {
    const blacklistedUserIp = await PaymentBlacklist.findOne({ ip: data.ip })
      .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_ip_${data.ip}`);

    if (blacklistedUserIp) return true;
  }

  const isBlacklistedUserData = await checkBlacklistedUserDetails(data);

  if (isBlacklistedUserData) return isBlacklistedUserData;
  if (data.hasOwnProperty('billingAddresses') && data.billingAddresses) {
    for (const key of Object.keys(data.billingAddresses)) {
      const isBlacklistedBillingAddress = await checkBlacklistedUserDetails(data.billingAddresses[key]);

      if (isBlacklistedBillingAddress) return isBlacklistedBillingAddress;
    }
  }

  return false;
};
