/**
 * Check if rules includes active accounts
 *
 * @param {object} rule
 * @param {Array<string>} activeAccountsIds - general active accounts IDs list
 */
function checkRuleHasActiveAccounts(rule, activeAccountsIds) {
  if (!rule || !rule.hasOwnProperty('paymentAccounts') || !rule.paymentAccounts || !rule.paymentAccounts.length) return false;

  for (const accId of rule.paymentAccounts) {
    if (activeAccountsIds.includes(accId)) return true;
  }

  return false;
}

/**
 * Check if rules includes active types
 *
 * @param {object} rule
 * @param {Array<string>} allActiveTypesIds - general active types IDs list
 */
function checkRuleHasActiveTypes(rule, allActiveTypesIds) {
  if (!rule || !rule.hasOwnProperty('paymentTypes') || !rule.paymentTypes || !rule.paymentTypes.length) return false;

  for (const typeId of rule.paymentTypes) {
    if (allActiveTypesIds.includes(typeId)) return true;
  }

  return false;
}

/**
 * Check if rules includes active groups
 *
 * @param {object} rule
 * @param {Array<string>} allActiveGroupsIds - general active groups IDs list
 */
function checkRuleHasActiveGroups(rule, allActiveGroupsIds) {
  if (!rule || !rule.hasOwnProperty('paymentGroups') || !rule.paymentGroups || !rule.paymentGroups.length) return false;

  for (const groupId of rule.paymentGroups) {
    if (allActiveGroupsIds.includes(groupId)) return true;
  }

  return false;
}

/**
 * @param {object} rule - group rule
 * @param {Array} allAccounts - general payment accounts includes payment types and groups objects
 *
 * @return {boolean}
 * */
module.exports = (rule, allAccounts) => {
  // both account and his payment type should be enabled
  const allActiveAccounts = allAccounts.filter(account => account.enabled && account.hasOwnProperty('PaymentType') && account.PaymentType && account.PaymentType.enabled && account.hasOwnProperty('PaymentGroup') && account.PaymentGroup);
  const allActiveAccountsIds = allActiveAccounts.map(account => account._id.toString());
  let result = checkRuleHasActiveAccounts(rule, allActiveAccountsIds);

  if (result) return true;

  const allActiveTypesIds = Array.from(new Set(allActiveAccounts.map(account => account.PaymentType._id.toString())));
  result = checkRuleHasActiveTypes(rule, allActiveTypesIds);

  if (result) return true;

  const allActiveGroupsIds = Array.from(new Set(allActiveAccounts.map(account => account.PaymentGroup._id.toString())));
  result = checkRuleHasActiveGroups(rule, allActiveGroupsIds);

  if (result) return true;

  return false;
};

