const PaymetLog = require('../../../../model/audb/PaymentLog');

/**
 * @description Check if user failed pay with payment types
 *
 * @param {object} options
 * options.rule.paymentTypes
 * @param {User} options.user - user model
 * @param {object} options.rule - payment basic rule
 * @param {Array} options.rule.paymentTypes - list of payment type names
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule, tkey } = options;

  if (!user || !rule || !rule.hasOwnProperty('paymentTypes') || !rule.paymentTypes || !tkey) return false;

  const { paymentTypes } = Object.assign({}, rule);

  const paymentLogs = await PaymetLog.find({
    uid: user.id, tkey, amount: { $lt: 0 }, upback: false, pptype: { $in: paymentTypes },
  }).cache(30).lean();

  if (paymentLogs.length) return true;

  return false;
};
