const mongoose = require('mongoose');
const Stealer = require('../../../../model/audb/Stealer');
const config = require('../../../../../config');

const checkStealerUserDetails = async (data) => {
  // do not check empty details
  if (!data || (!data.hasOwnProperty('firstname') && !data.hasOwnProperty('lastname')
    && !data.hasOwnProperty('address') && !data.hasOwnProperty('country')
    && !data.hasOwnProperty('state') && !data.hasOwnProperty('city')
    && !data.hasOwnProperty('zip'))) return false;

  const stealerUserRules = await Stealer.find(
    { $and: [
      { ip: { $exists: false } },
      { uid: { $exists: false } },
    ] },
    { firstname: 1, lastname: 1, address: 1, country: 1, state: 1, city: 1, zip: 1 },
  ).lean().cache(config.stealerList.cache, 'stealer_billingDetails');

  for (let i = 0; i < stealerUserRules.length; ++i) {
    const fraudRule = stealerUserRules[i];
    delete fraudRule._id;
    let match = true;

    for (const key of Object.keys(fraudRule)) {
      if (!data.hasOwnProperty(key) || !data[key] || !data[key].toLowerCase().match(fraudRule[key].toLowerCase())) {
        match = false;
        continue;
      }
    }

    if (match) return {
      rule: JSON.stringify(fraudRule),
      data: JSON.stringify(data),
    };
  }

  return false;
};

/**
 * @description Method check is user stealer
 *
 * @param {object} options
 * @param {object|model} options.user - user object or model
 * @param {boolean} options.checkUserIdOnly
 *
 * @return {Promise<boolean>} true|false
 * */
module.exports = async ({ user, checkUserIdOnly = false }) => {
  if (!user) return false;

  const data = user instanceof mongoose.Model ? user.toObject() : user;

  if (data && data.hasOwnProperty('id') && data.id) {
    const stealerUserId = await Stealer.findOne({ uid: data.id })
      .lean().cache(config.stealerList.cache, `stealer_id_${data.id}`);

    if (stealerUserId) return true;
  }
  if (checkUserIdOnly) return false;
  if (data && data.hasOwnProperty('ip') && data.ip) {
    const stealerUserIp = await Stealer.findOne({ ip: data.ip })
      .lean().cache(config.stealerList.cache, `stealer_ip_${data.ip}`);

    if (stealerUserIp) return true;
  }

  const isStealerUserData = await checkStealerUserDetails(data);

  if (isStealerUserData) return isStealerUserData;
  if (data.hasOwnProperty('billingAddresses') && data.billingAddresses) {
    for (const key of Object.keys(data.billingAddresses)) {
      const isStealerBillingAddress = await checkStealerUserDetails(data.billingAddresses[key]);

      if (isStealerBillingAddress) return isStealerBillingAddress;
    }
  }

  return false;
};
