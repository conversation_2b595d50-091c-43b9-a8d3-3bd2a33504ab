/**
 * @description Method check can user bypass blacklisted payment rules
 *
 * @param {object} options
 * @param {object} options.paymentInfo - package info
 * @param {object} options.rule - payment rule
 * @param {number} options.rule.packageDays
 *
 * @return {boolean} true|false
 * */
module.exports = async (options = {}) => {
  const { paymentInfo, rule } = options;

  if (paymentInfo && rule && rule.hasOwnProperty('packageDays') && paymentInfo.days === rule.packageDays) return true;

  return false;
};
