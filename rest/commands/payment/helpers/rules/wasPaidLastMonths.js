const moment = require('moment');
const PaymentLog = require('../../../../model/audb/PaymentLog');
const getPackageIds = require('../../../package/helper/getPackageIds');

const LAST_MONTH_PERIOD = 6;

/**
 * @description Method check if user paid last XX months
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {object} options.rule - payment basic rule
 * @param {number} options.rule.lastMonthsPeriod - last months period for payment logs
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user) return false;

  const defaultOptions = { lastMonthsPeriod: LAST_MONTH_PERIOD };
  const { lastMonthsPeriod } = Object.assign({}, defaultOptions, rule);
  const allPackageIds = await getPackageIds({ includeTrial: false });
  const lastPaymentLog = await PaymentLog.findOne({
    uid: user.id, amount: { $gt: 0 }, upback: true, package: { $in: allPackageIds },
  }).sort({ created: -1 }).cache(300).exec();
  const xxMonthAgoDate = moment().add(-lastMonthsPeriod, 'months').unix();

  return lastPaymentLog && lastPaymentLog.created >= xxMonthAgoDate;
};
