const fs = require('fs');

/**
 * @description Method get rule function by name
 *
 * @param {string} ruleName - basic rule name
 *
 * @return {boolean} true|false
 * */
module.exports = (methodName) => {
  const path = `${__dirname}/${methodName}.js`;

  try {
    const exists = fs.existsSync(path);

    if (exists) {
      // eslint-disable-next-line import/no-dynamic-require
      const methodFunction = require(`./${methodName}`);

      return methodFunction;
    }
  } catch (err) {
    console.error(err);
  }

  return false;
};
