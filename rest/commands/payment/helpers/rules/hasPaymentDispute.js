const PaymentLog = require('../../../../model/audb/PaymentLog');

/**
 * @description Check if user has payment disputes
 *
 * @param {object} options
 * @param {User} options.user - user model
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user } = options;

  if (!user) return false;

  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, isRefunded: true,
  }).cache(300).lean();

  if (paymentLogs.length) return true;

  return false;
};
