const PaymentLog = require('../../../../model/audb/PaymentLog');

/**
 * @description Check if user paid with payment types
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {object} options.rule - payment basic rule
 * @param {Array} options.rule.paymentTypes - list of payment type names
 *
 * @return {boolean|Promise} true|false
 * */
module.exports = async (options = {}) => {
  const { user, rule } = options;

  if (!user || !rule || !rule.hasOwnProperty('paymentTypes') || !rule.paymentTypes) return false;

  const { paymentTypes } = Object.assign({}, rule);

  const paymentLogs = await PaymentLog.find({
    uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $in: paymentTypes },
  }).cache(300).lean();

  if (paymentLogs.length) return true;

  return false;
};
