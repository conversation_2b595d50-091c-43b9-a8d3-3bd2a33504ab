/**
 * Recursive function to get single rule or group rule value
 *
 * @param {object} rule
 * @param {object} basicRules - all basic rules
 * @param {object} ruleData - can pay rules object
 * @param {string} stringRule
 */
function evaluateFunction(ruleConfig, basicRules, ruleData, stringRule) {
  if (ruleConfig.type === 'rule') {
    const basicRuleId = ruleConfig.basicRuleId;
    const basicRuleValue = ruleConfig.basicRuleValue;
    const actualRuleValue = ruleData[basicRuleId];

    if (!basicRuleValue) stringRule += 'NOT ';

    stringRule += `${basicRules[basicRuleId].name}(${basicRuleValue === actualRuleValue})`;

    return stringRule;
  }

  const groupRules = ruleConfig.rules;

  stringRule += '(';
  stringRule = evaluateFunction(groupRules[0], basicRules, ruleData, stringRule);
  let prevOperator = groupRules[0].groupRuleOperator;

  for (let i = 1; i < groupRules.length; i++) {
    const nextRule = groupRules[i];

    if (prevOperator === 'OR') {
      stringRule += ' OR ';
      stringRule = evaluateFunction(nextRule, basicRules, ruleData, stringRule);
    } else if (prevOperator === 'AND') {
      stringRule += ' AND ';
      stringRule = evaluateFunction(nextRule, basicRules, ruleData, stringRule);
    }

    prevOperator = nextRule.groupRuleOperator;
  }

  stringRule += ')';

  return stringRule;
}

/**
 * Method return stringify payment group rule
 *
 * @param {object} rule - group rule
 * @param {object} basicRules - basic rules object
 * @param {object} canPayWithBasicRules - can pay with basic rules object
 * @param {boolean} addBypassedRule
 * @param {boolean} hasBypassedAccount
 *
 * @return {string} - string rule
 * */
module.exports = (rule, basicRules = {}, canPayWithBasicRules = {}, addBypassedRule = false, hasBypassedAccount = false) => {
  let stringRule = evaluateFunction(rule, basicRules, canPayWithBasicRules, '');

  if (stringRule && stringRule.length > 1) stringRule = stringRule.slice(1, -1);
  if (addBypassedRule && (
    (rule.paymentAccounts && rule.paymentAccounts.length)
    || (rule.paymentTypes && rule.paymentTypes.length)
    || (rule.paymentGroups && rule.paymentGroups.length))
  ) stringRule = `Has user (not blacklisted) bypassed payment account/type(${hasBypassedAccount}) OR (${stringRule})`;

  return stringRule;
};
