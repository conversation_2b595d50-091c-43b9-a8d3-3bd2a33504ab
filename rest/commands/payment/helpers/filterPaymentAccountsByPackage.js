/**
 * @description filter payment account by package ID
 *
 * @param {Array} accounts - accounts
 * @param {number} packageId - package ID
 *
 * @return {Array} - list of filtered accounts
 * */
module.exports = (accounts, packageId) => {
  if (!accounts) return [];
  if (!packageId) return accounts;

  const result = accounts.filter(account => (account.packageIds && account.packageIds.length && account.packageIds.includes(packageId))
    || (account.hasOwnProperty('packageId') && account.packageId && account.packageId === packageId));

  return result;
};
