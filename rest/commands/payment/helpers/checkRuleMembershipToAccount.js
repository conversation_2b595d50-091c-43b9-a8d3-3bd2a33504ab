/**
 * Method check if account belongs to the rule by payment account, type or group
 *
 * @param {object} rule
 * @param {object} account
 *
 * @return {boolean}
 * */
module.exports = (rule, account) => {
  if (!rule.enabled) return false;
  if (rule.paymentGroups && account.paymentGroupId && rule.paymentGroups.includes(account.paymentGroupId.toString())) return true;
  if (rule.paymentTypes && account.paymentTypeId && rule.paymentTypes.includes(account.paymentTypeId.toString())) return true;
  if (rule.paymentAccounts && account._id && rule.paymentAccounts.includes(account._id.toString())) return true;

  return false;
};
