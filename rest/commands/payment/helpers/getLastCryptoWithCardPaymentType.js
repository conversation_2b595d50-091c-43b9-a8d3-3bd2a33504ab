const PaymentLog = require('../../../model/audb/PaymentLog');
const getAllTypes = require('../../../commands/payment/getAllTypes');

module.exports = async (user) => {
  if (!user) return false;

  const allTypesResult = await getAllTypes();
  const allTypes = allTypesResult.list;
  const paidTypesForCryptoWithCardSection = [];
  allTypes.forEach((type) => {
    if (type.PaymentGroup && (type.PaymentGroup.name === 'Nft' || type.PaymentGroup.name === 'Crypto with card')) paidTypesForCryptoWithCardSection.push(type.name);
  });

  const lastCryptoWithCardPayment = await PaymentLog.findOne({
    uid: user.id,
    pptype: { $in: paidTypesForCryptoWithCardSection },
    amount: { $gt: 0 },
  }).sort({ created: -1 }).exec();

  return lastCryptoWithCardPayment ? lastCryptoWithCardPayment.pptype : false;
};
