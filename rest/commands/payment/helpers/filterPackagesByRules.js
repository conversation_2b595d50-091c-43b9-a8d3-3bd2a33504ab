const getCanPayWithBasicRulesIds = require('../helpers/getCanPayWithBasicRulesIds');
const getGroupRuleById = require('../getGroupRuleById');
const canPayWithGroupRule = require('../helpers/rules/canPayWithGroupRule');

/**
 * @description Method filtered packages by preset rules
 *
 * @param {User} user - user model
 * @param {array} packages - list of packages
 * @param {string} fromPage - where from user navigated, authCode, website, etc
 *
 * @return {array} packages - return list of filtered packages by rules
 * */
module.exports = async ({ user, packages, fromPage = null }) => {
  const filteredPackages = await Promise.all(
    packages.map(async (pack) => {
      if (!pack.enable || !pack.price || pack.price === 0) return false;
      if (fromPage === 'admin') return true;
      if (fromPage === 'adminGenerateInvoice') return pack;
      // do not show package if payment rule is not provided
      if (!pack.canSeeRuleId) return false;

      const packageCanSeeRule = await getGroupRuleById(pack.canSeeRuleId.toString());
      const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: pack, fromPage });
      const isUserCanPayWithRule = canPayWithGroupRule(packageCanSeeRule, canPayWithBasicRules);

      return isUserCanPayWithRule ? pack : null;
    }),
  );

  return filteredPackages.filter(pack => !!pack);
};
