const canPayWithOnSubmitRule = require('./canPayWithOnSubmitRule');
const getAllAccounts = require('../getAllAccounts');
const checkRuleMembershipToAccount = require('./checkRuleMembershipToAccount');
const filterRulesByPackage = require('./filterRulesByPackage');

// filter accounts by rules
const filterAccounts = async (user, accounts, rules, userData) => {
  const filteredAccounts = [];

  for (const account of accounts) {
    const matchedRules = rules.filter(rule => checkRuleMembershipToAccount(rule, account));
    // set there by default allow true or false;
    // currently do not allow if there are not any rules
    let accountAllowed = matchedRules.length > 0;

    for (const rule of matchedRules) {
      const isUserCanPayWithRule = await canPayWithOnSubmitRule(user, rule, userData);

      // if some rule not allowed no need to check other
      if (!isUserCanPayWithRule) {
        accountAllowed = false;
        break;
      }
    }

    if (accountAllowed) filteredAccounts.push(account);
  }

  return filteredAccounts;
};

/**
 * @param {User} user - user model
 * @param {object} paymentInfo - package info
 * @param {Array} rules - payment rules list
 * @param {object} userData - user data for check
 * @param {Array} accounts - list of accounts to check, is not required
 * @param {Array} bypassedAccounts - not required, user bypassed accounts based on userConfig bypassed types and accounts
 *
 * @return {Array} - list of allowed accounts
 * */
module.exports = async (user, paymentInfo, rules = [], userData = {}, accounts = null, bypassedAccounts = []) => {
  let allAccounts = accounts;

  // need to check only if null, else if length = 0 user has no allowed accounts
  if (!allAccounts) {
    const allAccountsResult = await getAllAccounts();
    allAccounts = allAccountsResult.list || [];
  }

  // filter disabled accounts
  const enabledAccounts = allAccounts.filter(account => account.enabled && account.PaymentType.enabled);
  const currentPackageRules = filterRulesByPackage(rules, paymentInfo.id);
  const filteredAccounts = await filterAccounts(user, enabledAccounts, currentPackageRules, userData);

  // disabled bypassed accounts, submit rules are more strict an we should not bypass them
  // merge user bypassed accounts
  // if (bypassedAccounts && bypassedAccounts.length) {
  //   const accountIds = filteredAccounts.map(account => account._id.toString());
  //   bypassedAccounts.forEach((account) => {
  //     if (!accountIds.includes(account._id.toString())) filteredAccounts.push(account);
  //   });
  // }

  return filteredAccounts;
};
