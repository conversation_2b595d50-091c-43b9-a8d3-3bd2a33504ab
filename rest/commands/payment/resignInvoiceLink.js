const url = require('url');
const getSignedHmac = require('../../helpers/getSignedHmac');
const shortenPaymentUrl = require('../../commands/payment/shortenPaymentUrl');

module.exports = async (data) => {
  if (!data || !data.url) throw new Error('url is required');

  const { newParams = {}, userId, tkey, sid, shorten = false } = data;
  const parsedUrl = url.parse(data.url);
  const { protocol, host, query, pathname } = parsedUrl;
  const params = new URLSearchParams(query);
  const queryParams = {};
  for (const [name, value] of params) {
    queryParams[name] = value;
  }

  if (sid) {
    queryParams.sid = sid;
  }

  // replace url params with new params
  Object.entries(newParams).forEach(([key, value]) => { queryParams[key] = value; });

  const signString = `${queryParams.tkey}|${queryParams.uid}|${queryParams.sid}|${queryParams.sc}|${queryParams.ac}`;
  queryParams.misc = getSignedHmac({ str: signString });
  const queryParamsString = Object.keys(queryParams).map(key => `${key}=${queryParams[key]}`).join('&');

  const finalUrl = `${protocol}//${host}${pathname}?${queryParamsString}`;

  if (shorten && userId && tkey) {
    const shortenResult = await shortenPaymentUrl(userId, tkey, finalUrl, queryParams.sid);

    if (shortenResult && shortenResult.url) return {
      error: 0,
      url: shortenResult.url,
    };
  }

  return {
    error: 0,
    url: finalUrl,
  };
};
