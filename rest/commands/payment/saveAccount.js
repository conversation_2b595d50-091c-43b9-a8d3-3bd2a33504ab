const PaymentAccountClass = require('../../model/audb/class/PaymentAccount');
const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const { LastIdModel } = require('../../model/audb/LastId');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ user, data }) => {
  if (!data.name) throw new Error('Payment account name is required');
  if (!data.paymentTypeId) throw new Error('Payment type ID is required');
  if (!data.paymentType) throw new Error('Payment type is required');

  let paymentAccount;
  const Model = PaymentAccountClass.getAccountModelByPaymentType(data.paymentType);

  if (data._id) paymentAccount = await Model.findOne({ _id: data._id }).exec();
  else paymentAccount = new Model();
  if (!paymentAccount.id) paymentAccount.id = await LastIdModel.getNextId(PaymentAccount);

  Object.entries(data).forEach(([key, value]) => {
    paymentAccount[key] = value;
  });

  paymentAccount.modifiedByUid = user.id;
  paymentAccount.modifiedByName = user.name;

  await removeRedisCacheByKeys(['paymentAccount']);
  await paymentAccount.save();

  return {
    error: 0,
    paymentAccount: { ...paymentAccount._doc, paymentType: data.paymentType },
  };
};
