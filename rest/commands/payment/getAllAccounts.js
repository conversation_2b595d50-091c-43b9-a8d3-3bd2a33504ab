const _ = require('lodash');
const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const PaymentGroup = require('../../model/audb/PaymentGroup');

module.exports = async () => {
  const paymentGroups = await PaymentGroup.find().lean().cache(3600, 'paymentGroup');
  const paymentGroupsObject = {};
  paymentGroups.forEach((group) => {
    paymentGroupsObject[group._id] = group;
  });
  const paymentAccounts = await PaymentAccount.find()
    .populate('PaymentType').lean().cache(20 * 60, 'paymentAccount')
    .then(paymentAccounts => paymentAccounts.sort((a, b) => {
      // sort by enabled on top
      if (a.enabled === b.enabled) {
        // sort by group
        if (a.PaymentType.paymentGroupId < b.PaymentType.paymentGroupId) { return -1; }
        if (a.PaymentType.paymentGroupId > b.PaymentType.paymentGroupId) { return 1; }
        // sort by type
        if (a.paymentTypeId < b.paymentTypeId) { return -1; }
        if (a.paymentTypeId > b.paymentTypeId) { return 1; }
        // sort by account name
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }

        return 0;
      }

      return a.enabled ? -1 : 1;
    }))
    .then(paymentAccounts => (_.map(paymentAccounts, (paymentAccount) => {
      paymentAccount.paymentType = paymentAccount.PaymentType.name || 'Unknown';
      paymentAccount.paymentGroupId = paymentAccount.PaymentType.paymentGroupId.toString();
      paymentAccount.PaymentGroup = paymentGroupsObject[paymentAccount.PaymentType.paymentGroupId.toString()];
      paymentAccount.paymentGroup = paymentAccount.PaymentGroup.name || 'Unknown';

      return paymentAccount;
    })));

  return {
    error: 0,
    list: paymentAccounts,
  };
};
