const PaymentCoinConfig = require('../../model/audb/PaymentCoinConfig');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('Payment coin config ID is required');

  const paymentCoinConfig = await PaymentCoinConfig.findOne({ _id }).exec();

  if (!paymentCoinConfig) throw new Error('Payment coin config has been deleted');

  await paymentCoinConfig.remove();

  return {
    error: 0,
    success: true,
  };
};
