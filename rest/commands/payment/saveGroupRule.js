const PaymentGroupRule = require('../../model/audb/PaymentGroupRule');

const checkRules = (data) => {
  for (let i = 0; i < data.rules.length; ++i) {
    const rule = data.rules[i];

    if (rule.type === 'rule' && !rule.basicRuleId) throw new Error('Payment group rule: basic rule is required');
    if (rule.type === 'rule' && typeof rule.basicRuleValue !== 'boolean') throw new Error('Payment group rule: basic rule value is required');
    if (!rule.groupRuleOperator || !['AND', 'OR'].includes(rule.groupRuleOperator)) throw new Error('Payment group rule: group rule operator is required');
    if (rule.type === 'group') {
      if (rule.rules && rule.rules.length) {
        checkRules(rule);
      }
      if (!rule.rules || !rule.rules.length) {
        data.rules.splice(i, 1);
        --i;
      }
    }
  }
};

module.exports = async ({ user, data }) => {
  // switch enable/disable only for the rule
  if (!(data.hasOwnProperty('enableChanged') && data._id)) {
    if (!data.name) throw new Error('Payment group rule name is required');
    if (!data.rules || !data.rules.length) throw new Error('Payment group rules list is required');
    if (!data.userPermissionGroups || !data.userPermissionGroups.length) throw new Error('User permissions group is required');

    checkRules(data);
  }

  let groupRule;

  if (data._id) groupRule = await PaymentGroupRule.findOne({ _id: data._id }).exec();
  else groupRule = new PaymentGroupRule();

  Object.entries(data).forEach(([key, value]) => {
    groupRule[key] = value;
  });

  groupRule.modifiedByUid = user.id;
  groupRule.modifiedByName = user.name;

  await groupRule.save();

  return {
    error: 0,
    groupRule: { ...groupRule._doc },
  };
};
