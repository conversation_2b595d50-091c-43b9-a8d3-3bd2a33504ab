const getCanPayStringifyGroupRule = require('../payment/helpers/getCanPayStringifyGroupRule');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const filterRulesByPaymentAccountsByAndPackageId = require('../payment/helpers/filterRulesByPaymentAccountsByAndPackageId');
const getCanPayStringifyOnSubmitRule = require("../payment/helpers/getCanPayStringifyOnSubmitRule");
const canPayWithOnSubmitRule = require("../payment/helpers/canPayWithOnSubmitRule");

/**
 * Method check can pay with group rules by accounts and package and return cannot pay stringify group rules
 *
 * @param {object} user
 * @param {object} dataForCheck
 * @param {Array} allOnSubmitRules
 * @param {Array} accounts - accounts for check
 * @param {object} packageInfo - current package
 *
 * @return {Promise} - [{name, canPay, rule}]
 * */
module.exports = async (user, dataForCheck, allOnSubmitRules, accounts, packageInfo) => {
  const filteredOnSubmitRules = filterRulesByPaymentAccountsByAndPackageId(allOnSubmitRules, accounts, packageInfo.id);
  const canPayStringifySubmitRules = [];
  const promises = [];
  filteredOnSubmitRules.forEach(rule => promises.push((async () => {
    const isUserCanPayWithRuleStringify = getCanPayStringifyOnSubmitRule(rule, dataForCheck, packageInfo, accounts);
    const canPay = await canPayWithOnSubmitRule(user, rule, dataForCheck);
    canPayStringifySubmitRules.push({
      name: rule.name,
      canPay,
      rule: isUserCanPayWithRuleStringify,
    });
  })()));
  await Promise.all(promises);
  const cannotPayOnSubmitRules = canPayStringifySubmitRules.filter(rule => !rule.canPay);

  return cannotPayOnSubmitRules;
};
