/* eslint-disable camelcase */
const moment = require('moment-timezone');
const Binlist = require('../../model/audb/Binlist');
const flp = require('../../service/fraudLab');

const getPromisifiedBinData = async params => new Promise((resolve, reject) => {
  flp.validate(params, (err, data) => {
    if (err) reject();

    resolve(data);
  });
});

const isFreshBinData = binDate => moment().subtract(3, 'months').unix() < binDate;

const getBinInfoFromDb = async (bin) => {
  const binInfo = await Binlist.findOne({ bin }).cache(600).exec();

  let data = {};

  if (binInfo) {
    if (['', '-', 'NA'].includes(binInfo.card_issuing_country)) {
      data = {
        error: 1,
        result: false,
        message: 'Country not found',
      };
    } else {
      data = binInfo.toJSON();
    }

    return { status: isFreshBinData(binInfo.updated) ? 'actual' : 'outdated', data };
  }

  return { status: 'none', data: null };
};
const handleFreshBinData = (freshBinData, binDataStatus) => {
  if (binDataStatus === 'outdated') {
    const now = moment().unix();

    return Binlist.findOneAndUpdate({ bin: freshBinData.bin },
      {
        ...freshBinData,
        updated: now,
      }, {
        new: true,
      }).exec();
  }

  const newBin = new Binlist(freshBinData);

  return newBin.save();
};

const getBinInfoFromExternalProvider = async (bin, binDataStatus, user, userIP) => {
  const params = {
    ip: userIP,
    currency: 'USD',
    number: bin,
    email: user.email,
    user_phone: `${user.phonearea}${user.phone}`,
  };
  const fraudPromiseResult = await getPromisifiedBinData(params);

  const {
    card_brand,
    card_type,
    card_subtype,
    card_issuing_bank,
    card_issuing_country,
  } = fraudPromiseResult;

  const freshBinData = {
    bin,
    card_brand,
    card_type,
    card_subtype,
    card_issuing_bank,
    card_issuing_country,
    fullProviderResponse: fraudPromiseResult };

  const savedBin = await handleFreshBinData(freshBinData, binDataStatus);

  if (['', '-', 'NA'].includes(card_issuing_country)) {
    return {
      error: 1,
      result: false,
      message: 'Country not found',
    };
  }

  return savedBin.toJSON();
};

/**
 * Get binlist info from DB - otherwise from external provider
 *
 * @param bin
 * @param user
 * */
module.exports = async ({ bin, user, userIP }) => {
  if (!bin || !userIP) throw new Error('bin is required');

  const parsedBin = bin.replace(/\s/g, '');
  const first6DigBin = parsedBin.substring(0, 6);

  const dbResult = await getBinInfoFromDb(first6DigBin);

  return dbResult.status === 'actual'
    ? dbResult.data
    : getBinInfoFromExternalProvider(first6DigBin, dbResult.status, user, userIP);
};
