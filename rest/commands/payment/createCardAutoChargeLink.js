const log = require('@s1/log').create(__filename);
const Session = require('../../model/audb/Session');
const Invoice = require('../../model/audb/Invoice');

module.exports = async (data) => {
  if (!data.userId) throw new Error('User ID is required');
  if (!data.type) throw new Error('Type is required');
  if (!data.tkey) throw new Error('tkey is required');
  if (!data.cardInfoEncrypted) throw new Error('Card info encrypted is required');

  const userLastSession = await Session.findOne({ user_id: data.userId }).sort({ _id: -1 }).lean();

  if (!userLastSession) {
    log.error(`Cannot get user last session for createAutoChargeUrl for user: ${data.userId}, tkey: ${data.tkey}`);

    return false;
  }

  const sid = userLastSession.__sid;
  const invoice = await Invoice.findOne({ tkey: data.tkey }).exec();

  if (!invoice) throw new Error('Invoice not found');

  let url = await invoice.getUrl(sid, null, 1, 1);
  url = `${url}&data=${data.cardInfoEncrypted}`;

  return {
    error: 0,
    result: { url },
  };
};
