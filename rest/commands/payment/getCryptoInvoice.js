const CryptoPaymentInfo = require('../../model/audb/CryptoPaymentInfo');

/**
 * Get crypto invoice info
 *
 * @param data
 * */
module.exports = async (data) => {
  if (!data.invoiceId) throw new Error('Invoice ID data is required');
  if (!data.walletAddress) throw new Error('Wallet address data is required');

  const { invoiceId, walletAddress } = data;
  const cryptoPaymentInfo = await CryptoPaymentInfo.findOne({ invoiceId, walletAddress }).lean();

  if (!cryptoPaymentInfo) throw new Error('Invoice info not found');

  return {
    success: true,
    result: cryptoPaymentInfo,
    error: 0,
  };
};
