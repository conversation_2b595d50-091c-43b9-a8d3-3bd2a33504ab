const logger = require('@s1/log');
const { InvoiceNotFoundError } = require('@s1/api-errors');
const { ErrorWhileCancelling } = require('../../errors');
const User = require('../../model/audb/User');
const PaymentLog = require('../../model/audb/PaymentLog');

const log = logger.create(__filename);

const canWeCancel = (paymentLog) => paymentLog.txid && !paymentLog.isRefunded && !paymentLog.isCancelled && paymentLog.amount > 0;

module.exports = async ({ tkey, body, locale }) => {
  const paymentLog = await PaymentLog.findOne({ tkey })
    .populate(User.foreignPopulateOptions('User'))
    .populate({ path: 'Package', select: 'epricestr' })
    .exec();

  if (!paymentLog) throw new InvoiceNotFoundError(locale);
  if (!canWeCancel(paymentLog)) {
    const message = paymentLog.isCancelled ? 'Invoice already cancelled' : 'Invoice cannot be cancelled';

    return {
      success: false,
      error: message,
    };
  }

  try {
    await paymentLog.cancel(body.reason, body.merchantApiResponse);
  } catch (e) {
    log.error(e.stack || (e.message && e.message.toString()));

    throw new ErrorWhileCancelling('Error while cancelling');
  }

  return { success: true };
};
