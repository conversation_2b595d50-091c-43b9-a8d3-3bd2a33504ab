const PaymentBasicRuleClass = require('../../model/audb/class/PaymentBasicRule');

module.exports = async ({ user, data }) => {
  if (!data.name) throw new Error('Payment basic rule name is required');

  let basicRule;
  const Model = PaymentBasicRuleClass.getBasicRuleModelByMethodName(data.methodName);

  if (data._id) basicRule = await Model.findOne({ _id: data._id }).exec();
  else basicRule = new Model();

  Object.entries(data).forEach(([key, value]) => {
    basicRule[key] = value;
  });

  basicRule.modifiedByUid = user.id;
  basicRule.modifiedByName = user.name;

  await basicRule.save();

  return {
    error: 0,
    basicRule: { ...basicRule._doc },
  };
};
