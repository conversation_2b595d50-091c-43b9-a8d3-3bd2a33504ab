const { WrongParamsError } = require('@s1/api-errors');
const UserCard = require('../../model/audb/UserCard');
const md5 = require('../../helpers/md5');
const { encrypt } = require('../../helpers/security');
const config = require('../../../config');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const isBlacklistedUser = require('../../commands/payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../../commands/payment/helpers/rules/isStealerUser');
const User = require('../../model/audb/User');
const getWhitelistBlacklistCardNumbers = require('../paymentBlacklist/getWhitelistCardNumbers');
const getWhitelistStealerCardNumbers = require('../stealer/getWhitelistCardNumbers');

module.exports = async ({ user, number, owner, type, brand, country, bank }) => {
  if (!user || !number) throw new WrongParamsError();

  const parsedNumber = number.replace(/\s/g, '');

  const encryptedCardNumber = md5(encrypt(parsedNumber.toString(), `${parsedNumber}${config.cardDataSecuritySalt}`));
  let userCardModel = await UserCard.findOne({ uid: user.id, number: encryptedCardNumber })
    .lean().cache(config.paymentBlacklist.cache, `userCard_uid_${user.id}_cardNumber_${encryptedCardNumber}`);

  // save detected new user card number
  if (!userCardModel) {
    const data = {
      uid: user.id,
      number: encryptedCardNumber,
      first6: parsedNumber.substring(0, 6),
      last4: parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length),
      owner: owner ? encrypt(owner, config.cardDataSecuritySalt) : '',
      type,
      brand,
      country,
      bank,
    };

    const [isCardUserBlacklisted, isCardUserStealer, whitelistedBlacklistCards, whitelistedStealerCards] = await Promise.all([
      isBlacklistedUser({ user }),
      isStealerUser({ user }),
      getWhitelistBlacklistCardNumbers(),
      getWhitelistStealerCardNumbers(),
    ]);

    if (isCardUserBlacklisted && !whitelistedBlacklistCards.includes(encryptedCardNumber)) {
      const userEmail = await User.decryptEmailWithRedis(user.em);
      data.isBlacklisted = true;
      data.blacklistDescription = `Automatically added card numbers for blacklisted user: ${user.id} ${userEmail}`;
    }
    if (isCardUserStealer && !whitelistedStealerCards.includes(encryptedCardNumber)) {
      const userEmail = await User.decryptEmailWithRedis(user.em);
      data.isStealer = true;
      data.stealerDescription = `Automatically detected card numbers for stealer user: ${user.id} ${userEmail}`;
    }

    userCardModel = new UserCard(data);
    await userCardModel.save();
    await removeRedisCacheByKeys([`userCard_uid_${user.id}`]);
  } else if (userCardModel && !userCardModel.owner && owner) {
    await UserCard.findOneAndUpdate(
      { uid: user.id, number: encryptedCardNumber },
      { $set: { owner: encrypt(owner, config.cardDataSecuritySalt) } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${user.id}`]);
  }
};
