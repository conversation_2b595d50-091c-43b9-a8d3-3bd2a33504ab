const { WrongParamsError } = require('@s1/api-errors');
const MtpelerinAuthData = require('../../model/audb/MtpelerinAuthData');

module.exports = async ({ tkey, sid, user, body }) => {
  if (!tkey || !user || !body) throw new WrongParamsError();

  const data = { ...body, user_id: user.id };

  const result = await MtpelerinAuthData.findOneAndUpdate(
    { tkey, sid },
    { $set: data },
    { upsert: true, new: true },
  ).exec();

  return {
    error: 0,
    success: !!result,
  };
};
