const log = require('@s1/log').create(__filename);
const PaymentShortUrl = require('../../model/audb/PaymentShortUrl');
const Session = require('../../model/audb/Session');
const getPaymentGeneralConfig = require('./getPaymentGeneralConfig');

module.exports = async (userId, tkey, url, sid = null) => {
  let _sid;

  if (!sid) {
    const userLastSession = await Session.findOne({ user_id: userId }).sort({ _id: -1 }).lean();

    if (!userLastSession) {
      log.error(`Cannot get user last session for paymentShortUrl for user: ${userId}, tkey: ${tkey}, url: ${url}`);

      return false;
    }

    _sid = userLastSession.__sid;
  }

  const paymentShortUrl = new PaymentShortUrl({ sid: sid || _sid, userId, tkey, url });
  paymentShortUrl.createdForTtlIndex = new Date();

  const saveResult = await paymentShortUrl.save();

  if (!saveResult) {
    log.error(`Cannot save paymentShortUrl for user: ${userId}, tkey: ${tkey}, url: ${url}`);

    return false;
  }

  const { generalConfig } = await getPaymentGeneralConfig();
  const shortUrl = `${generalConfig.billingBaseUrl}pay/${saveResult._id.toString()}`;

  return { url: shortUrl };
};
