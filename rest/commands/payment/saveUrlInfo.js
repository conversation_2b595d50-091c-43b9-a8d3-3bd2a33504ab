const PaymentUrlInfo = require('../../model/audb/PaymentUrlInfo');

module.exports = async (data) => {
  if (!data.url) throw new Error('Url is required');
  if (!data.userId) throw new Error('User ID is required');

  const urlInfo = new PaymentUrlInfo();
  urlInfo.userId = data.userId;
  urlInfo.url = data.url;
  urlInfo.createdForTtlIndex = new Date();
  await urlInfo.save();

  return {
    error: 0,
    result: { ...urlInfo._doc },
  };
};
