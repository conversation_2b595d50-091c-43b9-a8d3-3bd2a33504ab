const { getUserLocation } = require('../../service/maxmind');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getCanPayStringifyOnSubmitRule = require('../payment/helpers/getCanPayStringifyOnSubmitRule');
const getAllPaymentOnSubmitPayRules = require('../payment/getAllPaymentOnSubmitPayRules');
const getUserAllowedPaymentAccountsOnSubmit = require('../payment/helpers/getUserAllowedPaymentAccountsOnSubmit');
const isBlacklistedUserMethod = require('../payment/helpers/rules/isBlacklistedUser');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const Binlist = require('../../model/audb/Binlist');
const User = require('../../model/audb/User');
const { Package } = require('../../model/audb/Package');
const { PaymentBasicRule } = require('../../model/audb/PaymentBasicRule');
const { paymentBasicRuleTypes } = require('../../constants/paymentBasicRuleConfigs');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');
const getBinFieldValue = require('../../helpers/getBinFieldValue');

module.exports = async ({ data }) => {
  const { userId, canPayWithBasicRules, onSubmitRulesData, userConfig } = data;
  const packageId = parseInt(data.packageId);
  const { ipData = {}, cardData = {}, billingAddress = {}, checkBillingAddressForBlacklist = false } = onSubmitRulesData;
  const { userIp, ipCountry, ipState } = ipData;
  const { number, cardCountry, cardBankName, cardType, cardBrand } = cardData;

  if (!userId) throw new Error('userId is required');
  if (!packageId) throw new Error('packageId is required');
  if (!userIp && !ipCountry) throw new Error('IP info is required');

  const user = await User.findOne({ id: userId }).exec();

  if (!user) throw new Error('user not found');

  const pack = await Package.findOne({ id: packageId }).exec();

  if (!pack) throw new Error('package not found');

  const { list: allGroupRules } = await getAllGroupRules();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);
  let isCurrentUserBlacklisted = false;
  const isBlacklistedRule = await PaymentBasicRule.findOne({ methodName: paymentBasicRuleTypes.isBlacklistedUser }).lean().cache(60);

  if (checkBillingAddressForBlacklist) {
    // check and override basic rule if billing address is backlisted
    const isBlacklisted = await isBlacklistedUserMethod({ user: { billingAddresses: [billingAddress] } });

    if (isBlacklisted) {
      if (isBlacklistedRule) {
        canPayWithBasicRules[isBlacklistedRule._id.toString()] = true;
      }
    }
  }
  if (isBlacklistedRule) isCurrentUserBlacklisted = canPayWithBasicRules[isBlacklistedRule._id.toString()];

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user, userConfig);
  const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, packageId);

  // filter accounts and types by group rules
  // eslint-disable-next-line max-len
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, null, userBypassedPaymentAccountsForCurrentPackage, packageId);

  const dataForCheck = {
    skipSaveLogs: true,
  };

  if (userIp) {
    const { countryCode, stateCode } = getUserLocation(userIp);

    if (countryCode) dataForCheck.ipCountry = countryCode;
    if (stateCode) dataForCheck.ipState = stateCode;
  } else if (ipCountry) {
    dataForCheck.ipCountry = ipCountry;

    if (ipState) dataForCheck.ipState = ipState;
  }
  if (number) {
    const parsedBin = number.replace(/\s/g, '');
    const first6DigBin = parsedBin.substring(0, 6);
    const binInfo = await Binlist.findOne({ bin: first6DigBin }).cache(600).exec();

    if (!binInfo) throw new Error('card info not found');

    dataForCheck.cardCountry = getBinFieldValue(binInfo.card_issuing_country);
    dataForCheck.cardBankName = getBinFieldValue(binInfo.card_issuing_bank);
    dataForCheck.cardType = getBinFieldValue(binInfo.card_type);
    dataForCheck.cardBrand = getBinFieldValue(binInfo.card_brand);
  } else {
    dataForCheck.cardCountry = cardCountry || '';
    dataForCheck.cardBankName = cardBankName || '';
    dataForCheck.cardType = cardType || '';
    dataForCheck.cardBrand = cardBrand || '';
  }
  if (billingAddress) {
    dataForCheck.addressCountry = billingAddress.country || '';
  }

  // filter accounts and types by on submit rules
  const { list: allOnSubmitRules } = await getAllPaymentOnSubmitPayRules();
  const enabledOnSubmitRules = allOnSubmitRules.filter(rule => rule.enabled);
  // eslint-disable-next-line max-len
  const allowedPaymentAccounts = await getUserAllowedPaymentAccountsOnSubmit(user, pack, enabledOnSubmitRules, dataForCheck, userAllowedPaymentAccounts, userBypassedPaymentAccountsForCurrentPackage);
  const typesObject = {};
  const allowedPaymentTypes = [];

  allowedPaymentAccounts.forEach((account) => {
    typesObject[account.PaymentType._id.toString()] = account.PaymentType;
    typesObject[account.PaymentType._id.toString()].PaymentGroup = account.PaymentGroup;
  });
  Object.entries(typesObject).forEach(([, value]) => allowedPaymentTypes.push(value));

  const canPayStringifyRules = {};
  const promises = [];
  enabledOnSubmitRules.forEach(rule => promises.push((async () => {
    const isUserCanPayWithRule = getCanPayStringifyOnSubmitRule(rule, dataForCheck, pack, userAllowedPaymentAccounts);
    canPayStringifyRules[rule._id.toString()] = isUserCanPayWithRule;
  })()));
  await Promise.all(promises);

  return {
    error: 0,
    isBlacklistedUser: isCurrentUserBlacklisted,
    allowedPaymentAccounts,
    allowedPaymentTypes,
    canPayStringifyRules,
  };
};
