const PaymentGeneralConfig = require('../../model/audb/PaymentGeneralConfig');

module.exports = async ({ user, data }) => {
  let paymentGeneralConfig;

  if (data._id) paymentGeneralConfig = await PaymentGeneralConfig.findOne({ _id: data._id }).exec();
  else paymentGeneralConfig = new PaymentGeneralConfig();
  // check properly json value
  if (data.billingCryptoCardSortOrder) {
    try {
      JSON.parse(data.billingCryptoCardSortOrder);
    } catch (e) {
      throw new Error('Wrong crypto card sort order config');
    }
  }

  Object.entries(data).forEach(([key, value]) => {
    paymentGeneralConfig[key] = value;
  });

  if (data.billingShowNftFirstIfPaidWith && Object.keys(data.billingShowNftFirstIfPaidWith).length) {
    Object.entries(data.billingShowNftFirstIfPaidWith).forEach(([key, value]) => {
      if (!value) delete paymentGeneralConfig.billingShowNftFirstIfPaidWith[key];
    });
  }
  if (data.defaultNftPaymentTypes && Object.keys(data.defaultNftPaymentTypes).length) {
    Object.entries(data.defaultNftPaymentTypes).forEach(([key, value]) => {
      if (!value) delete paymentGeneralConfig.defaultNftPaymentTypes[key];
    });
  }
  if (data.nftgateLinksNftPaymentTypes && Object.keys(data.nftgateLinksNftPaymentTypes).length) {
    Object.entries(data.nftgateLinksNftPaymentTypes).forEach(([key, value]) => {
      if (!value) delete paymentGeneralConfig.nftgateLinksNftPaymentTypes[key];
    });
  }

  paymentGeneralConfig.modifiedByUid = user.id;
  paymentGeneralConfig.modifiedByName = user.name;

  await paymentGeneralConfig.save();

  return {
    error: 0,
    generalConfig: { ...paymentGeneralConfig._doc },
  };
};
