const { UserSuspendedError } = require('@s1/api-errors');
const Invoice = require('../../model/audb/Invoice');
const isValidCardNumber = require('../../helpers/isValidCardNumber');
const addPaymentActionsLog = require('../payment/addActionsLog');
const getAllAccounts = require('../payment/getAllAccounts');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getAllPaymentOnSubmitPayRules = require('../payment/getAllPaymentOnSubmitPayRules');
const getUserAllowedPaymentTypesOnSubmit = require('../payment/helpers/getUserAllowedPaymentTypesOnSubmit');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');
const getCannotPayGroupRulesStringifyByAccountAndPackage = require('./getCannotPayGroupRulesStringifyByAccountAndPackage');
const getCannotPayOnSubmitRulesStringifyByAccountAndPackage = require('./getCannotPayOnSubmitRulesStringifyByAccountAndPackage');
const isSuspendedUser = require('../../commands/payment/helpers/rules/isSuspendedUser');

module.exports = async ({ user, body }) => {
  if (!user) throw new Error('User required');
  if (!body) throw new Error('body required');

  const { tkey, userIp, fromPage = '' } = body;
  const { number, getTypeNames = false, ...userData } = body;

  if (!number || !tkey) return { error: 0, canPay: false };

  const typeNamesLabel = getTypeNames ? ' payments' : '';
  const baseMessageLabel = `Check can pay with card${typeNamesLabel}:`;
  const parsedNumber = number.replace(/\s/g, '');
  const isNumberValid = isValidCardNumber(parsedNumber);
  const first6 = parsedNumber.substring(0, 6);
  const last4 = parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length);
  userData.cardHidden = `${first6}******${last4}`;

  if (!isNumberValid) {
    const message = `${baseMessageLabel} wrong card number: ${parsedNumber}`;
    await addPaymentActionsLog({ tkey, user, message, data: userData, userIp });

    return { error: 0, canPay: false };
  }

  const invoice = await Invoice.findOne({ tkey }).exec();

  if (!invoice) {
    const message = `${baseMessageLabel} user invoice not found, tkey: ${tkey}`;
    await addPaymentActionsLog({ tkey, user, message, data: userData, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }
  // do not show invoices for another users
  if (invoice.id !== user.id) {
    const message = `${baseMessageLabel} user tried to check invoice for another user, other user ID: ${invoice.id}, invoiceId: ${tkey}`;
    await addPaymentActionsLog({ tkey, user, message, data: userData, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }

  const paymentInfo = await invoice.getPaymentInfo(false, 'en');

  if (!paymentInfo || Object.keys(paymentInfo).length === 0) {
    const message = `${baseMessageLabel} paymentInfo for invoice not found`;
    await addPaymentActionsLog({ tkey, user, message, data: userData, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }

  const isUserSuspended = await isSuspendedUser({ user });

  if (isUserSuspended) {
    const logMessage = `${baseMessageLabel} user suspended`;
    addPaymentActionsLog({ tkey, user, message: logMessage, data: {}, userIp });

    throw new UserSuspendedError('en');
  }

  const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo, fromPage, tkey });
  const { list: allBasicRules } = await getAllBasicRules();
  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allAccounts } = await getAllAccounts();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);
  const userBypassedCardPaymentAccountsForCurrentPackage = userBypassedPaymentAccountsForCurrentPackage.filter(account => account.PaymentGroup.name === 'Card');

  // check only allowed card accounts
  const cardAccounts = allAccounts.filter(account => account.PaymentGroup.name === 'Card');
  const cardAccountsForCurrentPackage = filterPaymentAccountsByPackage(cardAccounts, paymentInfo.id);
  // filter disabled accounts
  const enabledСardAccountsForCurrentPackage = cardAccountsForCurrentPackage.filter(account => account.enabled && account.PaymentType.enabled);

  if (!enabledСardAccountsForCurrentPackage.length) {
    const groupMessage = `${baseMessageLabel} user has not active payment accounts or types for the current package: ${paymentInfo.epricestr}`;
    await addPaymentActionsLog({ tkey, user, message: groupMessage, data: {}, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }

  // eslint-disable-next-line max-len
  const userAllowedPaymentCardAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, enabledСardAccountsForCurrentPackage, userBypassedCardPaymentAccountsForCurrentPackage, paymentInfo.id);
  // check all group rules for can pay and save user log for not allowed rules
  // eslint-disable-next-line max-len
  const cannotPayGroupRules = await getCannotPayGroupRulesStringifyByAccountAndPackage(userPermissionsGroupRules, allBasicRules, canPayWithBasicRules, enabledСardAccountsForCurrentPackage, paymentInfo);

  // save user log if user cannot pay some card group rule
  if (cannotPayGroupRules.length) {
    const groupMessage = `${baseMessageLabel} user didn’t pass some group rules, package: ${paymentInfo.epricestr}`;
    await addPaymentActionsLog({ tkey, user, message: groupMessage, data: { cannotPayGroupRules }, userIp });
  }
  if (userAllowedPaymentCardAccounts && userAllowedPaymentCardAccounts.length) {
    const { list: allOnSubmitRules } = await getAllPaymentOnSubmitPayRules();
    const enabledOnSubmitRules = allOnSubmitRules.filter(rule => rule.enabled);
    // eslint-disable-next-line max-len
    const allowedPaymentTypes = await getUserAllowedPaymentTypesOnSubmit(user, paymentInfo, enabledOnSubmitRules, body, userAllowedPaymentCardAccounts, userBypassedCardPaymentAccountsForCurrentPackage);
    // check all group rules for can pay and save user log for not allowed rules
    // eslint-disable-next-line max-len
    const cannotPayOnSubmitRules = await getCannotPayOnSubmitRulesStringifyByAccountAndPackage(user, body, enabledOnSubmitRules, userAllowedPaymentCardAccounts, paymentInfo);

    // save user log if user cannot pay some card on submit rule
    if (cannotPayOnSubmitRules.length) {
      const onSubmitMessage = `${baseMessageLabel} user didn’t pass some on submit rules, package: ${paymentInfo.epricestr}`;
      await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { userData, cannotPayOnSubmitRules }, userIp });
    }
    if (allowedPaymentTypes.length) {
      const cardTypesNames = allowedPaymentTypes.map(type => type.name);
      const onSubmitMessage = `${baseMessageLabel} user can pay with card types: ${cardTypesNames.join(',')}, package: ${paymentInfo.epricestr}`;
      await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { userData }, userIp });

      return {
        error: 0,
        canPay: getTypeNames ? cardTypesNames : true,
      };
    }
  }

  return {
    error: 0,
    canPay: false,
  };
};
