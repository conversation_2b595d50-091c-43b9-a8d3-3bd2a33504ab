const PaymentUrlInfo = require('../../model/audb/PaymentUrlInfo');

module.exports = async (_id) => {
  if (_id) {
    const urlInfo = await PaymentUrlInfo.findOne({ _id }).exec();

    if (urlInfo) {
      urlInfo.createdForTtlIndex = new Date();
      await urlInfo.save();

      return {
        error: 0,
        result: { ...urlInfo._doc },
      };
    }
  }

  return {
    error: 0,
    result: null,
  };
};
