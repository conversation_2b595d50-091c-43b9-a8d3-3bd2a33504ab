const moment = require('moment');
const { paymentTypes } = require('../../constants/paymentType');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const getAllTypes = require('../../commands/payment/getAllTypes');

const CONFIGS = {
  label: 'Payment types:',
  childrens: [
    {
      name: 'CARDS_GROUP',
      label: 'Cards:',
      childrens: [
        {
          name: 'EASYHOST_BT',
          label: 'Easyhost (BT)',
          btServerId: ['J5NcRfUjXnZr4u7x', 'tukan'], // prod and dev keys
          pptype: [paymentTypes.braintree],
        },
        {
          name: 'VOCALHOST_BT',
          label: 'Vocalhost (BT)',
          btServerId: ['VmYq3t6w9z5C4FhJ', 'vaverka'], // prod and dev keys
          pptype: [paymentTypes.braintree],
        },
        {
          name: 'VOCALHOST_ST',
          label: 'Vocalhost (ST)',
          stripeId: [1, 6],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'DUBAIHOST',
          label: 'Dubaihost (ST)',
          stripeId: [2],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'VOXILITY',
          label: 'Voxihost (ST)',
          stripeId: [3],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'VOCALNET_SG',
          label: 'VocalnetSG (ST)',
          stripeId: [4],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'EASYHOST',
          label: 'Easyhost (ST)',
          stripeId: [7, 13],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'FIBERHOST',
          label: 'Fiberhost (ST)',
          stripeId: [9, 10],
          pptype: [paymentTypes.stripe],
        },
        {
          name: 'SENDWYRE_CARDS',
          label: 'Wyre cards',
          stripeId: [],
          pptype: [paymentTypes.sendwyreCard],
        },
        {
          name: 'MTPELERIN',
          label: 'Mtpelerin',
          stripeId: [],
          pptype: [paymentTypes.mtpelerin],
        },
        {
          name: 'TAZAPAY',
          label: 'Tazapay',
          stripeId: [],
          pptype: [paymentTypes.tazapay, paymentTypes.tazapayCard],
        },
        {
          name: 'CROSSMINT_NFT_CARD',
          label: 'Crossmint NFT card',
          stripeId: [],
          pptype: [paymentTypes.crossmintNftCard],
        },
      ],
    },
    {
      name: 'CARDS_CRYPTO_GROUP',
      label: 'Cards through crypto:',
      childrens: [
        {
          name: 'MOONPAY',
          label: 'Moonpay',
          stripeId: [],
          pptype: [paymentTypes.moonpay],
        },
        {
          name: 'SENDWYRE_CARDS',
          label: 'Wyre cards',
          stripeId: [],
          pptype: [paymentTypes.sendwyre],
        },
        {
          name: 'SENDWYRE_APPLEPAY',
          label: 'Wyre applepay',
          stripeId: [],
          pptype: [paymentTypes.applepay],
        },
        {
          name: 'B2PAY',
          label: 'B2pay',
          stripeId: [],
          pptype: [paymentTypes.b2pay],
        },
        {
          name: 'MERCURYO',
          label: 'Mercuryo (trezor)',
          stripeId: [],
          pptype: [paymentTypes.trezor],
        },
        {
          name: 'SIMPLEX',
          label: 'Simplex',
          stripeId: [],
          pptype: [paymentTypes.simplex],
        },
        {
          name: 'SWITCHERE',
          label: 'Switchere',
          stripeId: [],
          pptype: [paymentTypes.switchere],
        },
        {
          name: 'WERT',
          label: 'Wert',
          stripeId: [],
          pptype: [paymentTypes.wert],
        },
        // {
        //   name: 'CROSSMINT',
        //   label: 'Crossmint',
        //   stripeId: [],
        //   pptype: [paymentTypes.crossmint],
        // },
        {
          name: 'ALCHEMYPAY',
          label: 'Alchemy pay',
          stripeId: [],
          pptype: [paymentTypes.alchemypay],
        },
        {
          name: 'FATPAY',
          label: 'Fatpay',
          stripeId: [],
          pptype: [paymentTypes.fatpay],
        },
        {
          name: 'PAYBIS',
          label: 'Paybis',
          stripeId: [],
          pptype: [paymentTypes.paybis],
        },
        {
          name: 'UTORG',
          label: 'UTORG',
          stripeId: [],
          pptype: [paymentTypes.UTORG],
        },
        {
          name: 'CRYPTOCOM',
          label: 'Crypto.com',
          stripeId: [],
          pptype: [paymentTypes.cryptoCom],
        },
        {
          name: 'MERCURYO',
          label: 'Mercuryo',
          stripeId: [],
          pptype: [paymentTypes.mercuryo],
        },
        {
          name: 'TRANSAK',
          label: 'Transak',
          stripeId: [],
          pptype: [paymentTypes.transak],
        },
        {
          name: 'TOPPER',
          label: 'Topper',
          stripeId: [],
          pptype: [paymentTypes.topper],
        },
        {
          name: 'UNLIMIT_CRYPTO',
          label: 'Unlimit Crypto',
          stripeId: [],
          pptype: [paymentTypes.unlimitCrypto],
        },
        {
          name: 'SARDINE',
          label: 'Sardine',
          stripeId: [],
          pptype: [paymentTypes.sardine],
        },
        {
          name: 'LINK_BY_STRIPE',
          label: 'Link by Stripe',
          stripeId: [],
          pptype: [paymentTypes.linkByStripe],
        },
        {
          name: 'RAMP_NETWORK',
          label: 'Ramp Network',
          stripeId: [],
          pptype: [paymentTypes.rampNetwork],
        },
      ],
    },
    {
      name: 'CRYPTO_GROUP',
      label: 'Crypto:',
      childrens: [
        {
          name: 'COINREMITTER',
          label: 'Coinremitter',
          stripeId: [],
          pptype: [paymentTypes.crypto],
        },
        {
          name: 'NOWPAYMENTS',
          label: 'Nowpayments',
          stripeId: [],
          pptype: [paymentTypes.nowpayments],
        },
      ],
    },
    {
      name: 'NFT_GROUP',
      label: 'NFT:',
      childrens: [
        {
          name: 'MOONGATE',
          label: 'Moongate',
          stripeId: [],
          pptype: [paymentTypes.moongate],
        },
        {
          name: 'NFTPAY',
          label: 'Nftpay',
          stripeId: [],
          pptype: [paymentTypes.nftpay],
        },
        {
          name: 'PAPER',
          label: 'Paper',
          stripeId: [],
          pptype: [paymentTypes.paper],
        },
        {
          name: 'PAPER CRYPTO WITH CARD',
          label: 'Paper crypto with card',
          stripeId: [],
          pptype: [paymentTypes.paperCryptoCard],
        },
        {
          name: 'RAMPER',
          label: 'Ramper',
          stripeId: [],
          pptype: [paymentTypes.ramper],
        },
        {
          name: 'BITSTORE',
          label: 'Bit Store',
          stripeId: [],
          pptype: [paymentTypes.bitstore],
        },
        {
          name: 'WINTER',
          label: 'Winter',
          stripeId: [],
          pptype: [paymentTypes.winter],
        },
        {
          name: 'CROSSMINT',
          label: 'Crossmint',
          stripeId: [],
          pptype: [paymentTypes.crossmint],
        },
        {
          name: 'CROSSMINT BONFIRE',
          label: 'Crossmint Bonfire',
          stripeId: [],
          pptype: [paymentTypes.crossmintBonfire],
        },
        {
          name: 'THIRDWEB',
          label: 'Thirdweb',
          stripeId: [],
          pptype: [paymentTypes.thirdweb],
        },
        {
          name: 'NFTGATE EPIK',
          label: 'Nftgate epik',
          stripeId: [],
          pptype: [paymentTypes.nftgateEpik],
        },
        {
          name: 'NFTGATE PAYPAL',
          label: 'Nftgate paypal',
          stripeId: [],
          pptype: [paymentTypes.nftgatePaypal],
        },
        {
          name: 'TRANSAK NFT',
          label: 'Transak',
          stripeId: [],
          pptype: [paymentTypes.transakNft],
        },
        {
          name: 'WYNPAY',
          label: 'Wynpay',
          stripeId: [],
          pptype: [paymentTypes.wynpay],
        },
        {
          name: 'COMETH',
          label: 'Cometh',
          stripeId: [],
          pptype: [paymentTypes.cometh],
        },
      ],
    },
    // Note: total group calculated manually at the end, do not uncomment it
    // {
    //   name: 'TOTAL_GROUP',
    //   label: 'Total:',
    //   childrens: [
    //     {
    //       name: 'TOTAL',
    //       label: 'Total',
    //       stripeId: [],
    //       pptype: [],
    //     },
    //   ],
    // },
  ],
};

const getAllPackages = async () => {
  const packages = await Package.find({ price: { $gt: 0 } }, { id: 1, epricestr: 1, price: 1 }).lean();

  return packages;
};

const getPackagesObject = (value) => {
  const packages = {};
  value.forEach((pack) => {
    packages[pack.id] = pack;
  });

  return packages;
};

const getFilter = (params) => {
  const filter = {
    $and: [
      { amount: { $gt: 0 } },
      {
        $or: [{ isRefunded: false }, { isRefunded: { $exists: false } }],
      },
      {
        $or: [{ isCancelled: false }, { isCancelled: { $exists: false } }],
      },
      { created: { $gte: params.startTime } },
      { created: { $lte: params.endTime } },
      { pptype: { $in: params.pptypes } },
    ],
  };

  return filter;
};

const getAllStats = async (params) => {
  const filter = getFilter(params);
  const payments = await PaymentLog.aggregate([
    { $match: filter },
    {
      $project: { pptype: 1, package: 1, amount: 1, btServerId: 1 },
    },
    {
      $group: {
        _id: {
          pptype: '$pptype',
          packageId: '$package',
          btServerId: '$btServerId',
        },
        count: { $sum: 1 },
        paidAmount: { $sum: '$amount' },
      },
    },
  ]).exec();

  return payments;
};

const formatStats = (payments, configs, packages) => {
  const statsByType = {};
  payments.forEach((payment) => {
    const pptype = payment._id.hasOwnProperty('btServerId') ? `${payment._id.pptype}_${payment._id.btServerId}` : payment._id.pptype;

    if (!statsByType.hasOwnProperty(pptype)) statsByType[pptype] = { childrens: {}, total: 0, paidAmount: 0 };

    statsByType[pptype].childrens[payment._id.packageId] = {
      count: payment.count,
      paidAmount: Math.round(payment.paidAmount * 100) / 100,
    };
    statsByType[pptype].total += payment.count;
    statsByType[pptype].paidAmount += Math.round(payment.paidAmount * 100) / 100;
  });

  const stats = {
    label: 'Payment types:',
    childrens: [],
  };

  const totalGroup = {
    name: 'TOTAL_GROUP',
    label: 'Total:',
    childrens: [
      {
        name: 'TOTAL',
        label: 'Total',
        packages: [],
        transactions: 0,
        paidAmount: 0,
      },
    ],
  };
  const nftSalesGroup = {
    name: 'NFT_SALES_GROUP',
    label: 'NFT sales:',
    childrens: [
      {
        name: 'NFT_SALES',
        label: 'NFT sales',
        packages: [],
        transactions: 0,
        paidAmount: 0,
      },
    ],
  };
  const nftSalesTypes = [
    paymentTypes.ramper,
    paymentTypes.paper,
    paymentTypes.nftpay,
    paymentTypes.crossmintNftCard,
    paymentTypes.crossmint,
    paymentTypes.paperCryptoCard,
    paymentTypes.crossmintBonfire,
    paymentTypes.thirdweb,
    paymentTypes.moongate,
    paymentTypes.transakNft,
    paymentTypes.wynpay,
    paymentTypes.cometh,
  ];
  const totalGroupPackages = {};

  configs.childrens.forEach((groupChildren) => {
    const formatedGroupChildren = { label: groupChildren.label, childrens: [], transactions: 0, paidAmount: 0 };

    groupChildren.childrens.forEach((typeChildren) => {
      if (typeChildren.hidden) return;

      const formatedTypeChildren = { label: typeChildren.label, packages: [], transactions: 0, paidAmount: 0 };
      const pptypes = typeChildren.pptype;
      let hasStats = false;

      // collect counts from each types
      pptypes.forEach((type) => {
        if (type.hidden) return;

        const ppSubTypes = [];

        if (typeChildren.hasOwnProperty('btServerId') && typeChildren.btServerId.length) {
          typeChildren.btServerId.forEach((servId) => {
            ppSubTypes.push(`${type}_${servId}`);
          });
        } else {
          ppSubTypes.push(type);
        }

        ppSubTypes.forEach((pptype) => {
          if (!statsByType.hasOwnProperty(pptype)) return;

          hasStats = true;
          const typeStats = statsByType[pptype];
          formatedTypeChildren.transactions += typeStats.total;
          formatedGroupChildren.transactions += typeStats.total;
          formatedTypeChildren.paidAmount += Math.round(typeStats.paidAmount * 100) / 100;
          formatedGroupChildren.paidAmount += Math.round(typeStats.paidAmount * 100) / 100;

          Object.entries(typeStats.childrens).forEach(([packId, data]) => {
            // update total group
            if (!totalGroupPackages.hasOwnProperty(packId)) {
              totalGroupPackages[packId] = Object.assign({}, packages[packId]);
              totalGroupPackages[packId].transactions = data.count;
              totalGroupPackages[packId].paidAmount = Math.round(data.paidAmount * 100) / 100;
            } else {
              totalGroupPackages[packId].transactions += data.count;
              totalGroupPackages[packId].paidAmount += Math.round(data.paidAmount * 100) / 100;
            }

            totalGroup.childrens[0].transactions += data.count;
            totalGroup.childrens[0].paidAmount += Math.round(data.paidAmount * 100) / 100;

            if (nftSalesTypes.includes(pptype)) {
              nftSalesGroup.childrens[0].transactions += data.count;
              nftSalesGroup.childrens[0].paidAmount += Math.round(data.paidAmount * 100) / 100;
            }

            let existPackage = false;
            // update each package from all types
            formatedTypeChildren.packages.forEach((pack) => {
              if (pack.id === packId) {
                existPackage = true;
                pack.transactions += data.count;
                pack.paidAmount += Math.round(data.paidAmount * 100) / 100;
              }
            });

            if (!existPackage) {
              const formatedPack = Object.assign({}, packages[packId]);
              formatedPack.transactions = data.count;
              formatedPack.paidAmount = Math.round(data.paidAmount * 100) / 100;
              formatedTypeChildren.packages.push(formatedPack);
            }
          });

          formatedGroupChildren.childrens.push(formatedTypeChildren);
        });
      });

      if (!hasStats) formatedGroupChildren.childrens.push(formatedTypeChildren);
    });

    stats.childrens.push(formatedGroupChildren);
  });
  Object.entries(totalGroupPackages).forEach(([, pack]) => totalGroup.childrens[0].packages.push(pack));
  stats.childrens.push(totalGroup);
  stats.childrens.push(nftSalesGroup);

  return stats;
};

module.exports = async (data) => {
  const now = moment().unix();
  const params = {
    startTime: data.startTime || moment().add(-1, 'month').unix(),
    endTime: data.endTime || now,
  };
  const packages = await getAllPackages();
  const { list: types } = await getAllTypes();
  params.packages = getPackagesObject(packages);
  const typesObj = {};
  types.forEach((type) => {
    typesObj[type.name] = type;
  });

  const pptypes = [];
  CONFIGS.childrens.forEach((group) => {
    group.hidden = true;

    if (group.childrens && group.childrens.length)
      group.childrens.forEach((type) => {
        type.hidden = true;

        if (type.pptype && type.pptype.length) {
          type.pptype.forEach((pptype) => {
            if (typesObj.hasOwnProperty(pptype) && (!typesObj[pptype].hasOwnProperty('hidden') || !typesObj[pptype].hidden)) {
              pptypes.push(pptype);
              type.hidden = false;
              group.hidden = false;
            }
          });
        }
      });
  });
  params.pptypes = pptypes;

  const payments = await getAllStats(params);
  const statistics = formatStats(payments, CONFIGS, params.packages);

  return {
    error: 0,
    statistics,
  };
};
