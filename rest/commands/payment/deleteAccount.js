const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const PaymentGroupRule = require('../../model/audb/PaymentGroupRule');
const PaymentOnSubmitRule = require('../../model/audb/PaymentOnSubmitRule');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('Payment account ID is required');

  const paymentAccount = await PaymentAccount.findOne({ _id }).exec();

  if (!paymentAccount) throw new Error('Payment account has been deleted');

  await paymentAccount.remove();
  // remove account from the related models
  await PaymentGroupRule.updateMany({ paymentAccounts: _id }, { $pull: { paymentAccounts: _id } }, { upsert: false }).exec();
  await PaymentOnSubmitRule.updateMany({ paymentAccounts: _id }, { $pull: { paymentAccounts: _id } }, { upsert: false }).exec();

  return {
    error: 0,
    success: true,
  };
};
