const CryptoPaymentInfo = require('../../model/audb/CryptoPaymentInfo');

module.exports = async (data) => {
  if (!data) throw new Error('Payment data is required');

  const сryptoPaymentInfo = new CryptoPaymentInfo();

  Object.entries(data).forEach(([key, value]) => {
    сryptoPaymentInfo[key] = value;
  });

  сryptoPaymentInfo.createdForTtlIndex = new Date();

  try {
    await сryptoPaymentInfo.save();
  } catch (e) {
    console.log('Cannot save сrypto payment info, error:', e);

    return {
      success: false,
      error: 'Cannot save сrypto payment info',
    };
  }

  return {
    success: true,
    error: 0,
  };
};
