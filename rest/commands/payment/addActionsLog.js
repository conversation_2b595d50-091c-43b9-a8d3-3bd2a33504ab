const PaymentActionsLog = require('../../model/audb/PaymentActionsLog');
const Invoice = require('../../model/audb/Invoice');

module.exports = async ({ tkey, user, message, data, userIp }) => {
  if (!tkey) throw new Error('tkey is required');
  if (!message) throw new Error('message is required');

  let userId;

  if (user) {
    userId = user.id;
  } else {
    const invoice = await Invoice.findOne({ tkey }).lean();

    if (invoice) userId = invoice.id;
  }

  const log = new PaymentActionsLog();
  log.tkey = tkey;
  log.uid = userId;
  log.message = message;

  // parse json.stringified value to the object
  try {
    if (typeof data === 'string') {
      log.data = JSON.parse(data);
    } else {
      log.data = data;
    }
  } catch (e) {
    log.data = data;
  }

  log.userIp = userIp && userIp !== 'undefined' ? userIp : null;
  log.createdForTtlIndex = new Date();

  await log.save();

  return {
    error: 0,
    log,
  };
};
