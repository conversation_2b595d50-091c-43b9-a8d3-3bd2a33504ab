const PaymentGroupRule = require('../../model/audb/PaymentGroupRule');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('Payment group rule ID is required');

  const paymentGroupRule = await PaymentGroupRule.findOne({ _id }).exec();

  if (!paymentGroupRule) throw new Error('Payment group rule has been deleted');

  await paymentGroupRule.remove();

  return {
    error: 0,
    success: true,
  };
};
