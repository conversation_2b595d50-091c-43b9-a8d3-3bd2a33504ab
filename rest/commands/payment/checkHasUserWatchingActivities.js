const moment = require('moment');
const hasWatchingActivities = require('./helpers/rules/hasWatchingActivities');

module.exports = async (data) => {
  const now = new Date();
  const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
  const { minLogs = 30, calculationDays = 90, maxLogsPerDays = 5, endTime = endOfDay, userId } = data;
  const timeTo = moment(endTime).unix();

  if (!userId) throw new Error('userId required');

  const watchingResult = await hasWatchingActivities({
    user: {
      id: userId,
      skipPaymentWatchActivities: false,
    },
    rule: {
      calculationDays,
      minLogs,
      maxLogsPerDays,
      endTime: timeTo,
    },
  });
  const message = `The user has ${watchingResult ? '' : 'no '}enough watching activities`;

  return {
    result: {
      hasWatchingActivities: watchingResult,
      message,
    },
  };
};
