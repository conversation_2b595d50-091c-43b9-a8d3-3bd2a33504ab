const PaymentOnSubmitRule = require('../../model/audb/PaymentOnSubmitRule');

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('Payment on submit pay rule ID is required');

  const paymentOnSubmitRule = await PaymentOnSubmitRule.findOne({ _id }).exec();

  if (!paymentOnSubmitRule) throw new Error('Payment on submit pay rule has been deleted');

  await paymentOnSubmitRule.remove();

  return {
    error: 0,
    success: true,
  };
};
