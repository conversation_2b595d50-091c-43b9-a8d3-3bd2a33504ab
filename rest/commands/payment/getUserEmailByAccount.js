const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const User = require('../../model/audb/User');
const generateUserNftEmail = require('../../helpers/generateUserNftEmail');

module.exports = async (accountId, userId) => {
  if (!accountId) throw new Error('accountId required');
  if (!userId) throw new Error('userId required');

  const paymentAccount = await PaymentAccount.findOne({ _id: accountId }).populate('PaymentType').lean().cache(60);

  if (!paymentAccount) throw new Error('account not found');

  const user = await User.findOne({ id: userId }).exec();

  if (!user) throw new Error('user not found');

  const userEmail = user.email;
  const paymentTypeName = paymentAccount.PaymentType.name;
  let email = userEmail;

  if (paymentAccount.hasOwnProperty('emailType') && paymentAccount.emailType) {
    switch (paymentAccount.emailType) {
      case 'randomOnce':
        if (!user[`${paymentTypeName}Em`]) {
          // based on user email name we add 2 chars or numbers with user email domain
          user[`${paymentTypeName}Em`] = generateUserNftEmail(userEmail);
          await user.save();
        }

        email = await User.decryptEmailWithRedis(user[`${paymentTypeName}Em`]);
        break;
      case 'randomEachOrder':
        // based on user email name we add 2 chars or numbers with user email domain
        // eslint-disable-next-line no-case-declarations
        const em = generateUserNftEmail(userEmail);
        email = await User.decryptEmailWithRedis(em);
        break;
      default:
        break;
    }
  }

  return {
    error: 0,
    email,
  };
};
