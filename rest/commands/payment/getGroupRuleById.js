const PaymentGroupRule = require('../../model/audb/PaymentGroupRule');

module.exports = async (_id) => {
  if (!_id) return null;

  const paymentGroupRule = await PaymentGroupRule.findOne({ _id })
    .populate({ path: 'PaymentGroups' })
    .populate({ path: 'PaymentTypes' })
    .populate({ path: 'PaymentAccounts' })
    .lean()
    .cache(60 * 60, `paymentGroupRule_${_id.toString()}`);

  return paymentGroupRule;
};
