const PaymentOnSubmitRule = require('../../model/audb/PaymentOnSubmitRule');
const parseStringToArrayBySeparators = require('../../helpers/parseStringToArrayBySeparators');

/**
 * Method compare 2 string values separated by separators: [',', '|']
 *
 * @param {string} stringValue1
 * @param {string} stringValue2
 *
 * @returns {array|boolean} return list of duplicates or false
 * */
const checkDuplicates = (stringValue1, stringValue2) => {
  if (!stringValue1 || !stringValue2) return false;

  const array1 = parseStringToArrayBySeparators(stringValue1, [',', '|']);
  const array2 = parseStringToArrayBySeparators(stringValue2, [',', '|']);
  const duplicates = [];

  array1.forEach((val1) => {
    if (array2.includes(val1)) duplicates.push(val1);
  });

  return duplicates.length ? duplicates : false;
};

module.exports = async ({ user, data }) => {
  // switch enable/disable only for the rule
  if (!(data.hasOwnProperty('enableChanged') && data._id)) {
    if (!data.name) throw new Error('Payment on submit pay rule name is required');
  }

  let paymentOnSubmitRule;

  if (data._id) paymentOnSubmitRule = await PaymentOnSubmitRule.findOne({ _id: data._id }).exec();
  else paymentOnSubmitRule = new PaymentOnSubmitRule();

  Object.entries(data).forEach(([key, value]) => {
    paymentOnSubmitRule[key] = value;
  });

  // switch enable/disable only for the rule
  if (!(data.hasOwnProperty('enableChanged') && data._id)) {
    if (paymentOnSubmitRule.canAllowIpCountries && paymentOnSubmitRule.allowedIpCountries
      && paymentOnSubmitRule.canDenyIpCountries && paymentOnSubmitRule.deniedIpCountries) {
      const duplicatesResult = checkDuplicates(paymentOnSubmitRule.allowedIpCountries, paymentOnSubmitRule.deniedIpCountries);

      if (duplicatesResult) throw new Error(`Duplicated IP countries: ${duplicatesResult.join(', ')}`);
    }
    if (paymentOnSubmitRule.canAllowCardCountries && paymentOnSubmitRule.allowedCardCountries
      && paymentOnSubmitRule.canDenyCardCountries && paymentOnSubmitRule.deniedIpCountries) {
      const duplicatesResult = checkDuplicates(paymentOnSubmitRule.allowedCardCountries, paymentOnSubmitRule.deniedIpCountries);

      if (duplicatesResult) throw new Error(`Duplicated card countries: ${duplicatesResult.join(', ')}`);
    }
    if (paymentOnSubmitRule.canAllowCardBrands && paymentOnSubmitRule.allowedCardBrands
      && paymentOnSubmitRule.canDenyCardBrands && paymentOnSubmitRule.deniedCardBrands) {
      const duplicatesResult = checkDuplicates(paymentOnSubmitRule.allowedCardBrands, paymentOnSubmitRule.deniedCardBrands);

      if (duplicatesResult) throw new Error(`Duplicated card brands: ${duplicatesResult.join(', ')}`);
    }
    if (paymentOnSubmitRule.canAllowUserAddressCountries && paymentOnSubmitRule.allowedUserAddressCountries
      && paymentOnSubmitRule.canDenyUserAddressCountries && paymentOnSubmitRule.deniedUserAddressCountries) {
      const duplicatesResult = checkDuplicates(paymentOnSubmitRule.allowedUserAddressCountries, paymentOnSubmitRule.deniedUserAddressCountries);

      if (duplicatesResult) throw new Error(`Duplicated user address countries: ${duplicatesResult.join(', ')}`);
    }
  }

  paymentOnSubmitRule.modifiedByUid = user.id;
  paymentOnSubmitRule.modifiedByName = user.name;

  await paymentOnSubmitRule.save();

  return {
    error: 0,
    onSubmitPayRule: { ...paymentOnSubmitRule._doc },
  };
};
