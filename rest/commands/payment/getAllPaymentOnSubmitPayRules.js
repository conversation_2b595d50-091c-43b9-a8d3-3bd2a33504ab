const PaymentOnSubmitRule = require('../../model/audb/PaymentOnSubmitRule');

module.exports = async () => {
  const paymentOnSubmitRules = await PaymentOnSubmitRule.find()
    .sort({ enabled: -1 })
    .populate({ path: 'PaymentGroups' })
    .populate({ path: 'PaymentTypes' })
    .populate({ path: 'PaymentAccounts' })
    .lean()
    .cache(20 * 60, 'paymentOnSubmitRule');

  return {
    error: 0,
    list: paymentOnSubmitRules,
  };
};
