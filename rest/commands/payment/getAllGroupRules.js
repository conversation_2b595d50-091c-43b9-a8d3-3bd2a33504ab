const _ = require('lodash');
const PaymentGroupRule = require('../../model/audb/PaymentGroupRule');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getStringifyGroupRule = require('../payment/helpers/getStringifyGroupRule');
const getStringifyHTMLGroupRule = require('../payment/helpers/getStringifyHTMLGroupRule');

module.exports = async () => {
  const { list: allBasicRules } = await getAllBasicRules();
  const allBasicRulesObject = {};
  allBasicRules.forEach((rule) => { allBasicRulesObject[rule._id.toString()] = rule; });

  const paymentGroupRules = await PaymentGroupRule.find()
    .sort({ enabled: -1 })
    .populate({ path: 'UserPermissionGroups' })
    .populate({ path: 'PaymentGroups' })
    .populate({ path: 'PaymentTypes' })
    .populate({ path: 'PaymentAccounts' })
    .lean()
    .cache(60 * 60, 'paymentGroupRule')
    .then(rules => (_.map(rules, (rule) => {
      rule.stringRule = getStringifyGroupRule(rule, allBasicRulesObject, true);
      rule.stringRuleHTML = getStringifyHTMLGroupRule(rule, allBasicRulesObject, true);

      return rule;
    })));

  return {
    error: 0,
    list: paymentGroupRules,
  };
};
