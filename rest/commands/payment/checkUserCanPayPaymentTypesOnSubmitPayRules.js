const Invoice = require('../../model/audb/Invoice');
const { getUserLocation } = require('../../service/maxmind');
const addPaymentActionsLog = require('../payment/addActionsLog');
const getAllAccounts = require('../payment/getAllAccounts');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getAllPaymentOnSubmitPayRules = require('../payment/getAllPaymentOnSubmitPayRules');
const getUserAllowedPaymentTypesOnSubmit = require('../payment/helpers/getUserAllowedPaymentTypesOnSubmit');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getCannotPayGroupRulesStringifyByAccountAndPackage = require('./getCannotPayGroupRulesStringifyByAccountAndPackage');
const getCannotPayOnSubmitRulesStringifyByAccountAndPackage = require('./getCannotPayOnSubmitRulesStringifyByAccountAndPackage');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');

/**
 * Get user can pay payment methods
 *
 * @param {object} option
 * @param {object|User} option.user - current user
 * @param {object} body - data for check
 * @param {string} userIp - user IP, not required
 * @param {boolean} saveLogs - should save logs, not required
 * */
module.exports = async ({ user, body, userIp, saveLogs = true }) => {
  if (!user) throw new Error('User required');
  if (!body) throw new Error('body required');

  const { tkey, fromPage, requestFrom = null, types } = body;
  const dataForCheck = Object.assign({}, body);
  const baseMessageLabel = `Check can pay payments: ${types && types.length ? types.join(',') : 'All'},${requestFrom ? ` request from: ${requestFrom},` : ''}`;

  if (!tkey) return { error: 0, canPay: false };
  if (userIp) {
    const { countryCode, stateCode } = getUserLocation(userIp);

    if (countryCode) dataForCheck.ipCountry = countryCode;
    if (stateCode) dataForCheck.ipState = stateCode;
  }

  const invoice = await Invoice.findOne({ tkey }).exec();

  if (!invoice) {
    if (saveLogs) {
      const message = `${baseMessageLabel} user invoice not found, invoiceId: ${tkey}`;
      await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });
    }

    return {
      error: 0,
      canPay: false,
    };
  }
  // do not show invoices for another users
  if (invoice.id !== user.id) {
    if (saveLogs) {
      const message = `${baseMessageLabel} user tried to check invoice for another user, other user ID: ${invoice.id}, invoiceId: ${tkey}`;
      await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });
    }

    return {
      error: 0,
      canPay: false,
    };
  }

  const paymentInfo = await invoice.getPaymentInfo(false, 'en');

  if (!paymentInfo || Object.keys(paymentInfo).length === 0) {
    if (saveLogs) {
      const message = `${baseMessageLabel} paymentInfo for invoice not found, invoiceId: ${tkey}`;
      await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });
    }

    return {
      error: 0,
      canPay: false,
    };
  }

  const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo, fromPage, tkey });
  const { list: allBasicRules } = await getAllBasicRules();
  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allAccounts } = await getAllAccounts();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  let userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);
  // filter bypassed account by checked types
  userBypassedPaymentAccountsForCurrentPackage = types && types.length
    ? userBypassedPaymentAccountsForCurrentPackage.filter(account => types.includes(account.PaymentType.name))
    : userBypassedPaymentAccountsForCurrentPackage;
  const filteredAccounts = types && types.length ? allAccounts.filter(account => types.includes(account.PaymentType.name)) : allAccounts;
  // eslint-disable-next-line max-len
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, filteredAccounts, userBypassedPaymentAccountsForCurrentPackage, paymentInfo.id);

  // check all group rules for can pay and save user log for not allowed rules
  // eslint-disable-next-line max-len
  const cannotPayGroupRules = await getCannotPayGroupRulesStringifyByAccountAndPackage(userPermissionsGroupRules, allBasicRules, canPayWithBasicRules, filteredAccounts, paymentInfo);

  // save user log if user cannot pay some group rule
  if (cannotPayGroupRules.length && saveLogs) {
    const groupMessage = `${baseMessageLabel} user didn’t pass some group rules, package: ${paymentInfo.epricestr}`;
    await addPaymentActionsLog({ tkey, user, message: groupMessage, data: { cannotPayGroupRules }, userIp });
  }
  if (userAllowedPaymentAccounts && userAllowedPaymentAccounts.length) {
    const { list: allOnSubmitRules } = await getAllPaymentOnSubmitPayRules();
    const enabledOnSubmitRules = allOnSubmitRules.filter(rule => rule.enabled);
    // eslint-disable-next-line max-len
    const allowedPaymentTypes = await getUserAllowedPaymentTypesOnSubmit(user, paymentInfo, enabledOnSubmitRules, dataForCheck, userAllowedPaymentAccounts, userBypassedPaymentAccountsForCurrentPackage);
    // check all group rules for can pay and save user log for not allowed rules
    // eslint-disable-next-line max-len
    const cannotPayOnSubmitRules = await getCannotPayOnSubmitRulesStringifyByAccountAndPackage(user, body, enabledOnSubmitRules, userAllowedPaymentAccounts, paymentInfo);

    // save user log if user cannot pay some on submit rule
    if (cannotPayOnSubmitRules.length && saveLogs) {
      const onSubmitMessage = `${baseMessageLabel} user didn’t pass some on submit rules, package: ${paymentInfo.epricestr}`;
      await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { dataForCheck, cannotPayOnSubmitRules }, userIp });
    }
    if (allowedPaymentTypes.length) {
      const allowedPaymentTypeNames = allowedPaymentTypes.map(type => type.name);

      if (saveLogs) {
        const onSubmitMessage = `${baseMessageLabel} user can pay with types: ${allowedPaymentTypeNames.join(',')}, package: ${paymentInfo.epricestr}`;
        await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { dataForCheck }, userIp });
      }

      return {
        error: 0,
        canPayTypes: allowedPaymentTypeNames,
      };
    }
  }

  return {
    error: 0,
    canPay: false,
  };
};
