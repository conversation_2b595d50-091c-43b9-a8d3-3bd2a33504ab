const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const User = require('../../model/audb/User');
const Invoice = require('../../model/audb/Invoice');
const { Package } = require('../../model/audb/Package');

module.exports = async ({ userId, packageId, fromPage = '' }) => {
  if (!userId) throw new Error('user required');
  if (!packageId) throw new Error('packageId required');

  const user = await User.findOne({ id: userId }).exec();

  if (!user) throw new Error(`user not found, userID: ${userId}`);

  const invoices = await Invoice.find({ id: userId }).exec();
  const invoice = invoices.find(invoice => invoice.packageId === packageId);
  const { tkey = null } = invoice || {};
  const packageInfo = await Package.findOne({ id: packageId }).exec();

  if (!packageInfo) throw new Error(`package not found, packageId: ${packageId}`);

  const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: packageInfo, fromPage, tkey, });

  return {
    error: 0,
    canPayWithBasicRules,
  };
};
