const getAllTypes = require('./getAllTypes');
const getAllAccounts = require('./getAllAccounts');

module.exports = async () => {
  const allTypes = await getAllTypes();
  const allAccounts = await getAllAccounts();

  const types = {};
  allTypes.list.forEach((type) => {
    types[type.name] = {
      ...type,
      accounts: [],
    };
  });
  allAccounts.list.forEach((account) => {
    types[account.paymentType].accounts.push(account);
  });

  // check each payment type should have at least one enabled account
  Object.entries(types).forEach(([key]) => {
    let hasEnabledAccounts = false;
    types[key].accounts.forEach((account) => {
      if (account.enabled) hasEnabledAccounts = true;
    });

    // should be enabled payment type and at least one account
    types[key].enabled = types[key].enabled && hasEnabledAccounts;
  });

  return {
    error: 0,
    list: types,
  };
};
