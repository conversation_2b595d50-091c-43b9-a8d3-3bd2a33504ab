const log = require('@s1/log').create(__filename);
const { decrypt } = require('../../helpers/security');
const config = require('../../../config');

module.exports = async (data) => {
  if (!data.cardInfo) throw new Error('Card info is required');

  try {
    const cardInfo = decrypt(data.cardInfo, config.cardDataSecuritySalt);

    return {
      error: 0,
      result: cardInfo,
    };
  } catch (e) {
    log.error('Cannot decrypt card info', e);

    throw new Error('Cannot decrypt card info');
  }
};
