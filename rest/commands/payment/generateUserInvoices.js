const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const Session = require('../../model/audb/Session');

const getLastUserSid = async (userId) => {
  const session = await Session.findOne({ user_id: userId, __sid: { $ne: 'undefined' } }).sort({ _id: -1 }).lean();

  if (session) return session.__sid;

  return null;
};

module.exports = async (userId, fromPage = '') => {
  if (!userId) throw new ApiError(903, 'userId is required');

  const user = await User.findOne({ id: userId }).exec();

  if (!user) throw new ApiError(903, 'User not found');

  const sid = await getLastUserSid(userId);

  if (!sid && !['adminGenerateInvoice', 'adminPanel'].includes(fromPage)) throw new ApiError(903, 'User session not found');

  const invoices = await user.getInvoices({ fromPage: 'adminGenerateInvoice', sid, coupon: null, locale: 'en' });
  const filteredInvoices = invoices.filter(invoice => !!invoice.tokey);
  const formattedInvoices = filteredInvoices.map(invoice => ({
    tkey: invoice.tokey,
    packageName: invoice.pricestr,
    price: invoice.price,
    packageId: invoice.id,
    days: invoice.days,
  }));
  const sortedPackages = formattedInvoices.sort((a, b) => {
    // sort by package days
    if (a.days < b.days) { return -1; }
    if (a.days > b.days) { return 1; }
    // sort by price
    if (a.price < b.price) { return -1; }
    if (a.price > b.price) { return 1; }

    return 0;
  });

  return {
    error: 0,
    result: sortedPackages,
  };
};
