const PaymentCoinConfig = require('../../model/audb/PaymentCoinConfig');

const convertSchemaToFrontend = (value) => {
  const frontendSchema = {};

  Object.keys(value).forEach((field) => {
    if (!value[field].hasOwnProperty('type')) {
      frontendSchema[field] = {
        type: 'object',
        fields: convertSchemaToFrontend(value[field]),
      };
    } else {
      frontendSchema[field] = { type: 'string' };
    }
  });

  return frontendSchema;
};

module.exports = async () => {
  const paymentCoinsConfigs = await PaymentCoinConfig.find()
    .lean()
    .cache(24 * 60 * 60, 'paymentCoinConfig');

  const fields = convertSchemaToFrontend(PaymentCoinConfig.schema.obj.coins);

  const activeCoins = {};

  paymentCoinsConfigs.forEach((coin) => {
    activeCoins[coin.name] = {};

    if (coin.coins) {
      Object.entries(coin.coins).forEach(([key, value]) => {
        if (typeof value === 'object') {
          if (value.coin || value.changellyCoin || value.letsexchangeCoin) {
            activeCoins[coin.name][key] = {};
            Object.entries(value).forEach(([key2, value2]) => {
              activeCoins[coin.name][key][key2] = value2;
            });
          }
        } else if (value) {
          activeCoins[coin.name][key] = value;
        }
      });

      activeCoins[coin.name].name = coin.name;
    }
    // remove empty coin config
    if (!Object.keys(activeCoins[coin.name]).length) delete activeCoins[coin.name];
  });

  return {
    error: 0,
    coinsConfigs: paymentCoinsConfigs,
    activeCoins,
    fields,
  };
};
