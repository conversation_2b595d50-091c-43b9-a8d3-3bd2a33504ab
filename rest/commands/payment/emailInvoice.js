const { PaymentUserNotFoundError } = require('@s1/api-errors');
const PaymentLog = require('../../model/audb/PaymentLog');
const User = require('../../model/audb/User');
const sendEmailInvoice = require('./sendEmailInvoice');
const isMongooseObjectID = require('../../helpers/isMongooseObjectID');

module.exports = async ({ invoiceID, locale }) => {
  let paymentLog = {};

  if (isMongooseObjectID(invoiceID)) {
    paymentLog = await PaymentLog.findOne({ _id: invoiceID }).exec();
  }

  const user = await User.findOne({ id: paymentLog.uid });

  if (!user) {
    throw new PaymentUserNotFoundError(locale);
  }

  const response = await sendEmailInvoice({ paymentLog, user, locale });

  return response;
};
