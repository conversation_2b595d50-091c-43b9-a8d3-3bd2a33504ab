const { PaymentBasicRule } = require('../../model/audb/PaymentBasicRule');
const getAllGroupRules = require('../payment/getAllGroupRules');

/**
 * @param {object} rule
 * @param {string} basicRuleId
 * */
const checkRuleIncludeBasicRule = (rule, basicRuleId) => {
  if (rule.type === 'rule') {
    return rule.basicRuleId === basicRuleId;
  }

  const groupRules = rule.rules;

  for (let i = 0; i < groupRules.length; i++) {
    const nextRule = groupRules[i];

    const result = checkRuleIncludeBasicRule(nextRule, basicRuleId);

    if (result) return true;
  }

  return false;
};

module.exports = async ({ _id }) => {
  if (!_id) throw new Error('Payment basic rule ID is required');

  const paymentAccount = await PaymentBasicRule.findOne({ _id }).exec();

  if (!paymentAccount) throw new Error('Payment basic rule has been deleted');

  const { list: allGroupRules } = await getAllGroupRules();

  for (const rule of allGroupRules) {
    const isBasicRuleExists = checkRuleIncludeBasicRule(rule, _id);

    if (isBasicRuleExists) throw new Error(`Payment group rule '${rule.name}' include current basic rule, please remove basic first from this group rule!`);
  }

  await paymentAccount.remove();

  return {
    error: 0,
    success: true,
  };
};
