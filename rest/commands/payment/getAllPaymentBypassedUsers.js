const _ = require('lodash');
const UserConfig = require('../../model/audb/UserConfig');
const User = require('../../model/audb/User');

module.exports = async (body) => {
  const { sort = { uid: 1 }, limit = 50, offset = 0, showUsersWithBypassesPaymentTypes, showUsersWithBypassesPaymentAccounts } = body;

  const paymentTypesFilter = {
    $and: [
      { bypassedPaymentTypes: { $exists: true, $ne: [] } },
      { bypassedPaymentTypes: { $exists: true, $ne: '' } },
      { bypassedPaymentTypes: { $exists: true, $ne: null } },
    ],
  };
  const paymentAccountsFilter = {
    $and: [
      { bypassedPaymentAccounts: { $exists: true, $ne: [] } },
      { bypassedPaymentAccounts: { $exists: true, $ne: '' } },
      { bypassedPaymentAccounts: { $exists: true, $ne: null } },
    ],
  };
  const filterStatement = { $or: [] };

  if (showUsersWithBypassesPaymentTypes) filterStatement.$or.push(paymentTypesFilter);
  if (showUsersWithBypassesPaymentAccounts) filterStatement.$or.push(paymentAccountsFilter);

  const userConfigs = await UserConfig.aggregate([
    {
      $match: filterStatement,
    },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { uid: 1, em: '$User.em', name: '$User.name', bypassedPaymentAccounts: 1, bypassedPaymentTypes: 1 },
    },
  ]).sort(sort).skip(offset).limit(limit)
    .exec()
    .then(items => Promise.all(items.map(async (item) => {
      item.email = item.em ? await User.decryptEmailWithRedis(item.em) : '';
      delete item.em;

      return item;
    })));

  const total = await UserConfig
    .countDocuments(filterStatement)
    .exec();

  return {
    error: 0,
    list: userConfigs,
    total,
  };
};
