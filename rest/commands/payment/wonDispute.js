const { ChargeNotProvidedError, ChargeNotFoundError } = require('@s1/api-errors');
const PaymentLog = require('../../model/audb/PaymentLog');
const unblockUserCommand = require('../user/unblock');

module.exports = async ({ charge, locale }) => {
  if (!charge) throw new ChargeNotProvidedError(locale);

  const [paymentLog] = await Promise.all([
    PaymentLog.findOne({ txid: charge }).exec(),
  ]);

  if (!paymentLog || Object.keys(paymentLog).length === 0) throw new ChargeNotFoundError(locale);
  if (paymentLog.hasOwnProperty('alreadyWonDispute') && paymentLog.alreadyWonDispute
    || paymentLog._doc.hasOwnProperty('alreadyWonDispute') && paymentLog._doc.alreadyWonDispute) return 'Already won dispute';

  const tkey = paymentLog.tkey;
  const uid = paymentLog.uid;
  const reason = 'Dispute won';
  const comment = `Dispute won about tkey ${tkey} (${charge}), so we unsuspended him immediately and notified with email`;
  const blockUserResult = await unblockUserCommand(uid, reason, comment, paymentLog, locale);

  if (blockUserResult) {
    paymentLog.alreadyWonDispute = true;
    await paymentLog.save();
  }

  return blockUserResult;
};
