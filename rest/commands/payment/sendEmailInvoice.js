const formData = require('form-data');
const Mailgun = require('mailgun.js');
const moment = require('moment');
const config = require('../../../config');
const { MailTemplatePostmark, MailTemplateGeneralNft } = require('../../model/audb/MailTemplate');
const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const PaymentGroup = require('../../model/audb/PaymentGroup');
const User = require('../../model/audb/User');
const { encrypt } = require('../../helpers/security');

const setValuesIntoHtml = (html, htmlParams) => {
  let parsedHtml = html;

  // eslint-disable-next-line no-return-assign
  Object.entries(htmlParams).forEach(([key, value]) => parsedHtml = parsedHtml.replace(new RegExp(`{{${key}}}`, 'g'), value));

  return parsedHtml;
};

const sendNftEmail = async (emailAddress, paymentLog, paymentInfo, additionalPaymentData, account) => {
  const { emailTemplateSuccess: emailTemplate, emailApiKey, emailDomain, emailFrom } = account;

  // send only success email
  if (!paymentLog.upback || !emailTemplate || !emailApiKey || !emailDomain || !emailFrom) return false;
  if (!emailAddress) {
    console.log('Email is not provided, paymentLog:', paymentLog);

    return false;
  }

  const mailTemplateModel = await MailTemplateGeneralNft.findOne().byTag(emailTemplate).exec();

  if (!mailTemplateModel) return false;

  const DOMAIN = emailDomain;
  const API_KEY = emailApiKey;
  const mailgun = new Mailgun(formData);
  const client = mailgun.client({ username: 'api', key: API_KEY, url: 'https://api.eu.mailgun.net' });
  const htmlParams = {};

  // eslint-disable-next-line no-return-assign
  Object.entries({ ...paymentInfo, ...additionalPaymentData }).forEach(([key]) => htmlParams[key] = paymentInfo[key] || additionalPaymentData[key] || '');

  const { card } = { ...paymentInfo, ...additionalPaymentData, ...paymentLog.post };
  const cardBrand = paymentLog.post.cardBrand || 'Card';
  const cardLast4 = card || 'XXXX';
  const cardInfo = `${cardBrand} - ${cardLast4}`;

  htmlParams.cardInfo = cardInfo;
  htmlParams.transactionId = encrypt(paymentLog.tkey, config.cardDataSecuritySalt);
  htmlParams.date = moment.unix(paymentLog.created || new Date()).locale('en').format('ddd MMM D YYYY');
  htmlParams.amount = paymentLog.amount;
  htmlParams.nftPrice = paymentLog.post.nftPrice;

  const messageParams = {
    from: emailFrom,
    to: emailAddress,
    subject: mailTemplateModel.title,
    html: setValuesIntoHtml(mailTemplateModel.contents, htmlParams),
  };

  const response = await client.messages.create(DOMAIN, messageParams)
    .then(res => res && res.status === 200)
    .catch((err) => {
      console.error('Cannot send nft email with mailgun, error:', err);

      return false;
    });

  return !!response;
};

module.exports = async ({ paymentLog, user, additionalPaymentData = {}, locale }) => {
  const emailAddress = config.email.noReply;
  const paymentInfo = await paymentLog.getPaymentInfo(locale);
  let emailTemplate = 'success_payment';
  let account;
  const sendEmailTo = paymentLog.post && paymentLog.post.sendEmailTo ? paymentLog.post.sendEmailTo : null;

  if (paymentLog.paymentAccountId) {
    account = await PaymentAccount.findOne({ id: paymentLog.paymentAccountId }).populate('PaymentType').lean();

    if (account) {
      account.PaymentGroup = await PaymentGroup.findOne({ _id: account.PaymentType.paymentGroupId }).lean();
    }
  }
  // send success custom emails for the Nft payment types
  if (paymentLog.upback && account && account.PaymentGroup && account.PaymentGroup.name === 'Nft') {
    await sendNftEmail(sendEmailTo || paymentLog.email, paymentLog, paymentInfo, additionalPaymentData, account);
  }
  if (paymentLog.isRefunded) {
    emailTemplate = 'refund_payment';
  } else if (paymentLog.upback) {
    let mailTemplateModel;

    // check if exist mail template for payment type
    if (locale && locale !== 'he') mailTemplateModel = await MailTemplatePostmark.findOne().byTag(`success_payment_${paymentLog.pptype}_${locale}`).exec();
    if (!mailTemplateModel) mailTemplateModel = await MailTemplatePostmark.findOne().byTag(`success_payment_${paymentLog.pptype}`).exec();
    if (mailTemplateModel) emailTemplate = `success_payment_${paymentLog.pptype}`;
  } else {
    emailTemplate = 'fail_payment';
  }
  if (account && account.PaymentGroup && account.PaymentGroup.name === 'Nft') {
    if (paymentLog.upback) {
      // for NFT types we need to send our email with some delay
      setTimeout(async () => {
        if (sendEmailTo) {
          await User.mail(
            `${emailTemplate}_nft`, emailAddress, sendEmailTo, {}, { ...paymentInfo, ...additionalPaymentData }, locale,
          );
        } else {
          await user.mail(
            `${emailTemplate}_nft`, emailAddress, {}, { ...paymentInfo, ...additionalPaymentData }, locale,
          );
        }
      }, 60 * 1000 * (5 + Math.random() * 5));
    }

    return true;
  }

  let response;

  if (sendEmailTo) {
    response = await User.mail(
      emailTemplate, emailAddress, sendEmailTo, {}, { ...paymentInfo, ...additionalPaymentData }, locale,
    );
  } else {
    response = await user.mail(
      emailTemplate, emailAddress, {}, { ...paymentInfo, ...additionalPaymentData }, locale,
    ) || {};
  }

  return response;
};
