const { WrongParamsError, ApiError } = require('@s1/api-errors');
const moment = require('moment');
const checkUserCanPayPaymentAccountsOnSubmitPayRules = require('../payment/checkUserCanPayPaymentAccountsOnSubmitPayRules');
const { paymentTypes } = require('../../constants/paymentType');
const PaymentLog = require('../../model/audb/PaymentLog');
const { paymentStatusTypes } = require('../../constants/paymentLog');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const Invoice = require('../../model/audb/Invoice');

const getBraintreeLoadBalancerAccounts = (accounts, cardCountry) => accounts.filter(acc => acc.hasOwnProperty('cardCountriesForLoadBalancer')
  && acc.cardCountriesForLoadBalancer.split(',').includes(cardCountry) && acc.hasOwnProperty('maxPaidTotalAmountForLoadBalancer')
  && acc.maxPaidTotalAmountForLoadBalancer && acc.hasOwnProperty('percentageForLoadBalancer'));

const checkCanBalanceWithAccount = async (account) => {
  const { _id, braintreeServerId, maxPaidTotalAmountForLoadBalancer } = account;

  if (!braintreeServerId || !maxPaidTotalAmountForLoadBalancer) return {
    _id,
    canBalance: false,
  };

  const startOfMonth = moment().startOf('month').unix();
  const endOfDayMonth = moment().endOf('month').unix();

  const totalPaidResult = await PaymentLog.aggregate([
    {
      $match: {
        $and: [
          { btServerId: braintreeServerId },
          { amount: { $gt: 0 } },
          { upback: true },
          { status: { $in: [paymentStatusTypes.success, paymentStatusTypes.overpaid, paymentStatusTypes.partialPaid] } },
          { created: { $gte: startOfMonth } },
          { created: { $lte: endOfDayMonth } },
        ],
      },
    },
    {
      $group: {
        _id: '$braintreeServerId',
        totalAmount: { $sum: '$amount' },
      },
    },
  ]).exec();

  return {
    _id,
    canBalance: (!totalPaidResult.length && maxPaidTotalAmountForLoadBalancer && maxPaidTotalAmountForLoadBalancer > 0)
      || (totalPaidResult.length && maxPaidTotalAmountForLoadBalancer && maxPaidTotalAmountForLoadBalancer > 0
        && totalPaidResult[0].totalAmount < maxPaidTotalAmountForLoadBalancer),
  };
};

module.exports = async ({ data, user }) => {
  if (!user) throw new WrongParamsError();

  const { tkey, userIp, fromPage, number, addressCountry, cardType, cardCountry, cardBrand, cardBankName, billingAddress } = data;

  if (!userIp || !user || !cardCountry) throw new WrongParamsError();

  const canPayData = {
    tkey,
    number,
    userIp,
    addressCountry,
    types: [paymentTypes.braintree],
    fromPage,
    requestFrom: 'Billnet getBraintreeHash',
    owner: `${billingAddress.firstname} ${billingAddress.lastname}`,
    cardType,
    cardCountry,
    cardBrand,
    cardBankName,
    billingAddress,
    skipAddressCountry: false,
  };

  const canPayResult = await checkUserCanPayPaymentAccountsOnSubmitPayRules({ user, body: canPayData, userIp });

  if (!canPayResult || !canPayResult.result || !canPayResult.result.hasOwnProperty(paymentTypes.braintree)
    || !canPayResult.result[paymentTypes.braintree].length) {
    throw new ApiError(502, 'User has no allowed accounts');
  }

  const paymentAccounts = canPayResult.result[paymentTypes.braintree];

  const invoice = await Invoice.findOne({ tkey }).exec();

  if (!invoice) throw new ApiError(502, 'Invoice not found');

  const packageId = invoice.pack;
  const braintreeCurrentPackageAccounts = filterPaymentAccountsByPackage(paymentAccounts, packageId);

  if (!braintreeCurrentPackageAccounts.length) throw new ApiError(502, 'Account not found, check that default bt account exists for the current package');

  const braintreeActiveAccounts = braintreeCurrentPackageAccounts.filter(account => account.enabled);
  const braintreeMainAccount = braintreeActiveAccounts.find(acc => acc.isDefaultBraintreeAccount);
  const braintreeAllCountriesAccount = braintreeActiveAccounts.find(acc => acc.allow3mPackageForCardCountries === 'ALL');
  const braintreeCountryAccount = braintreeActiveAccounts.find(acc => acc.allow3mPackageForCardCountries.split(',').includes(cardCountry));
  const braintreeLoadBalancerAccounts = getBraintreeLoadBalancerAccounts(braintreeActiveAccounts, cardCountry);

  let accountsForBalance = [];

  if (braintreeLoadBalancerAccounts.length) {
    const promises = [];
    // load and filter BT account do not reach limits
    braintreeLoadBalancerAccounts.forEach(account => promises.push(checkCanBalanceWithAccount(account)));
    const results = await Promise.all(promises);
    const canBalanceAccountsIds = results.filter(result => result.canBalance).map(result => result._id);
    accountsForBalance = braintreeLoadBalancerAccounts.filter(account => canBalanceAccountsIds.includes(account._id));

    if (braintreeAllCountriesAccount && accountsForBalance.length && !canBalanceAccountsIds.includes(braintreeAllCountriesAccount._id)) {
      accountsForBalance.push(braintreeAllCountriesAccount);
    }
    if (accountsForBalance.length) {
      // get random percentage between 0 and 100 rounded to 2 decimal
      const randomValue = Math.round((Math.random() * 100) * 100) / 100;
      let currentPercentage = 0;

      // get account by percentage
      for (let i = 0; i < accountsForBalance.length; ++i) {
        const account = accountsForBalance[i];
        currentPercentage += account.percentageForLoadBalancer;

        // get account from the percentage range on last(default) account
        if (randomValue <= currentPercentage || i === accountsForBalance.length - 1) return {
          error: 0,
          hash: account.braintreeServerId,
        };
      }
    }
  }

  // if no balance accounts or all reached limits use default logic
  const braintreeAccountForPay = braintreeCountryAccount || braintreeAllCountriesAccount || braintreeMainAccount;

  if (braintreeAccountForPay) return {
    error: 0,
    hash: braintreeAccountForPay.braintreeServerId,
  };

  throw new ApiError(502, 'Account not found, check that default bt account exists');
};
