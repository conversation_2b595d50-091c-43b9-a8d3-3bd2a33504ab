const getAllGroupRules = require('../payment/getAllGroupRules');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getUserAllowedPaymentTypes = require('../payment/helpers/getUserAllowedPaymentTypes');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getCanPayWithGroupRules = require('../payment/helpers/getCanPayWithGroupRules');
const getCanPayStringifyGroupRule = require('../payment/helpers/getCanPayStringifyGroupRule');
const getCanPayStringifyHTMLGroupRule = require('../payment/helpers/getCanPayStringifyHTMLGroupRule');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const hasRuleActiveAccounts = require('../payment/helpers/rules/hasRuleActiveAccounts');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');
const User = require('../../model/audb/User');

module.exports = async ({ data }) => {
  const { canPayWithBasicRules, userId, userConfig } = data;
  const packageId = parseInt(data.packageId);
  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allBasicRules } = await getAllBasicRules();
  const user = await User.findOne({ id: userId }).exec();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user, userConfig);
  const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, packageId);

  // eslint-disable-next-line max-len
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, null, userBypassedPaymentAccountsForCurrentPackage, packageId);
  // eslint-disable-next-line max-len
  const userAllowedPaymentTypes = await getUserAllowedPaymentTypes(userPermissionsGroupRules, canPayWithBasicRules, userAllowedPaymentAccounts, userBypassedPaymentAccountsForCurrentPackage);
  const canPayWithGroupRules = await getCanPayWithGroupRules(userPermissionsGroupRules, canPayWithBasicRules);
  const allBasicRulesObject = {};
  allBasicRules.forEach((rule) => { allBasicRulesObject[rule._id.toString()] = rule; });

  const canPayStringifyRules = {};
  const canPayStringifyRulesHTML = {};
  const promises = [];
  userPermissionsGroupRules.forEach(rule => promises.push((async () => {
    const hasActiveAccounts = hasRuleActiveAccounts(rule, userBypassedPaymentAccountsForCurrentPackage);
    const isUserCanPayWithRule = getCanPayStringifyGroupRule(rule, allBasicRulesObject, canPayWithBasicRules, true, hasActiveAccounts);
    // eslint-disable-next-line max-len
    const isUserCanPayWithRuleHTML = getCanPayStringifyHTMLGroupRule(rule, allBasicRulesObject, canPayWithBasicRules, true, hasActiveAccounts);
    canPayStringifyRules[rule._id.toString()] = isUserCanPayWithRule;
    canPayStringifyRulesHTML[rule._id.toString()] = isUserCanPayWithRuleHTML;
  })()));
  await Promise.all(promises);

  return {
    error: 0,
    canPayWithGroupRules,
    userAllowedPaymentAccounts,
    userAllowedPaymentTypes,
    canPayStringifyRules,
    canPayStringifyRulesHTML,
  };
};
