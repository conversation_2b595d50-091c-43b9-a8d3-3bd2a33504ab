const { PaymentAccount } = require('../../model/audb/PaymentAccount');
const PaymentType = require('../../model/audb/PaymentType');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const sendTelegramMessage = require('../../helpers/sendTelegramMessage');

module.exports = async (type) => {
  const paymentType = await PaymentType.findOne({ name: type }).lean();

  if (paymentType && paymentType.enabledDailyPaidAmountChecker && paymentType.dailyPaidAmountLimit && paymentType.dailyPaidAmountLimit > 0) {
    const now = new Date();
    const midnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0));
    const timestamp = Math.round(midnightUTC.getTime() / 1000);

    const results = await PaymentLog.aggregate([
      { $match: {
          pptype: type,
          amount: { $gt: 0 },
          created: { $gte: timestamp }
      } },
      {
        $group: {
          _id: '$pptype',
          totalPaid: { $sum: '$amount' },
        },
      },
    ]);

    if (results && results.length) {
      const { totalPaid } = results[0];
      let accountsForDisable = [];
      const paymentAccounts = await PaymentAccount.find({ paymentTypeId: paymentType._id, enabled: true }).lean();

      if (totalPaid >= paymentType.dailyPaidAmountLimit) {
        accountsForDisable = paymentAccounts;
      } else {
        const allPackages = await Package.find({}, { _id: 0, id: 1, price: 1 }).lean();
        const packagesObject = {};
        allPackages.forEach((pack) => {
          packagesObject[pack.id] = pack;
        });

        for (let i = 0; i < paymentAccounts.length; ++i) {
          const account = paymentAccounts[i];

          if (account.packageId && packagesObject.hasOwnProperty(account.packageId)) {
            const accountPackage = packagesObject[account.packageId];

            if (totalPaid + accountPackage.price > paymentType.dailyPaidAmountLimit) accountsForDisable.push(account);
          }
        }
      }

      if (accountsForDisable.length) {
        const paymentAccountsIds = accountsForDisable.map(account => account.id);
        await PaymentAccount.updateMany(
          { id: { $in: paymentAccountsIds } },
          { $set: { enabled: false } },
          { upsert: false },
        ).exec();

        const accountNames = accountsForDisable.map(account => account.name);
        const message = `Disabled ${type} payment accounts by reaching daily paid limits: ${accountNames.join(', ')}`;
        try {
          await sendTelegramMessage(message);
        } catch (e) {
          console.log(`Cannot send Telegram message on disable ${type} accounts by reach payment type daily paid limits, error:`, e);
        }
      }
    }
  }
};
