const moment = require('moment');
const PaymentLog = require('../../model/audb/PaymentLog');

/**
 * Check if he has last 2 payments via the card ('stripe', 'ST-card', 'BT-card', 'CR-card', 'moonpay' or 'b2pay') and is our client at least 1 month
 * Old user has >= 2 payments and registered 1 month (31 days) ago
 *
 * @param user
 * */
module.exports = async ({ user }) => {
  const paymentLog = await PaymentLog.find({ uid: user.id, amount: { $gt: 0 }, upback: true, pptype: { $in: ['stripe', 'ST-card', 'BT-card', 'moonpay', 'b2pay', 'CR-card'] } }).countDocuments();

  const now = moment().unix();
  const registerDate = Math.floor(user.regtime) || moment().add(-60, 'days').unix();
  const registeredDays = (now - registerDate) / (24 * 3600);

  return { isNew: paymentLog < 2 || registeredDays < 31 };
};
