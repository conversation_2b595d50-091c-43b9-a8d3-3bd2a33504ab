const { ChargeNotProvidedError, ChargeNotFoundError } = require('@s1/api-errors');
const PaymentLog = require('../../model/audb/PaymentLog');
const blockUserCommand = require('../user/block');
const User = require('../../model/audb/User');
const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');

module.exports = async ({ charge, locale }) => {
  if (!charge) throw new ChargeNotProvidedError(locale);

  const [paymentLog] = await Promise.all([
    PaymentLog.findOne({ txid: charge }).exec(),
  ]);

  if (!paymentLog || Object.keys(paymentLog).length === 0) throw new ChargeNotFoundError(locale);
  if (paymentLog.hasOwnProperty('alreadyDisputed') && paymentLog.alreadyDisputed
    || paymentLog._doc.hasOwnProperty('alreadyDisputed') && paymentLog._doc.alreadyDisputed) return 'Already disputed';

  const tkey = paymentLog.tkey;
  const uid = paymentLog.uid;
  const reason = 'Dispute started';
  const comment = `Dispute started about tkey ${tkey} (${charge}), so we suspended him immediately and notified with email`;
  const blockUserResult = await blockUserCommand(uid, reason, comment, paymentLog, locale);

  if (blockUserResult) {
    paymentLog.alreadyDisputed = true;
    await paymentLog.save();

    const disputedLogs = await PaymentLog.find({
      uid, amount: { $gt: 0 }, upback: true, isRefunded: true,
    }).lean();

    if (disputedLogs.length > 1) {
      const permissionGroup = await UserPermissionGroup.findOne({ isUserGroup: true, name: 'Disputed users' }).lean();

      if (permissionGroup) {
        const user = await User.findOne({ id: uid }).exec();
        user.permissionGroups = [permissionGroup._id.toString()];

        await user.save();
      }
    }
  }

  return blockUserResult;
};
