const Invoice = require('../../model/audb/Invoice');
const { getUserLocation } = require('../../service/maxmind');
const addPaymentActionsLog = require('../payment/addActionsLog');
const getAllAccounts = require('../payment/getAllAccounts');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getAllPaymentOnSubmitPayRules = require('../payment/getAllPaymentOnSubmitPayRules');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getUserAllowedPaymentAccountsByTypeOnSubmit = require('../payment/helpers/getUserAllowedPaymentAccountsByTypeOnSubmit');
const getCannotPayGroupRulesStringifyByAccountAndPackage = require('./getCannotPayGroupRulesStringifyByAccountAndPackage');
const getCannotPayOnSubmitRulesStringifyByAccountAndPackage = require('./getCannotPayOnSubmitRulesStringifyByAccountAndPackage');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');

/**
 * Get user can pay payment accounts
 *
 * @param {object} option
 * @param {object|User} option.user - current user
 * @param {object} body - data for check
 * @param {string} userIp - user IP, not required
 *
 * @return {object} - allowed accounts by type
 * */
module.exports = async ({ user, body, userIp }) => {
  if (!user) throw new Error('User required');
  if (!body) throw new Error('body required');

  const { tkey, fromPage, requestFrom = null, types, coin = null } = body;
  const dataForCheck = Object.assign({}, body);
  const baseMessageLabel = `Check can pay accounts for payments: ${types && types.length ? types.join(',') : 'All'},${requestFrom ? ` request from: ${requestFrom},` : ''}`;

  if (!tkey) return { error: 0, canPay: false };
  if (userIp) {
    const { countryCode, stateCode } = getUserLocation(userIp);

    if (countryCode) dataForCheck.ipCountry = countryCode;
    if (stateCode) dataForCheck.ipState = stateCode;
  }

  const invoice = await Invoice.findOne({ tkey }).exec();

  if (!invoice) {
    const message = `${baseMessageLabel} user invoice not found, invoiceId: ${tkey}`;
    await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }
  // do not show invoices for another users
  if (invoice.id !== user.id) {
    const message = `${baseMessageLabel} user tried to check invoice for another user, other user ID: ${invoice.id}, invoiceId: ${tkey}`;
    await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }

  const paymentInfo = await invoice.getPaymentInfo(false, 'en');

  if (!paymentInfo || Object.keys(paymentInfo).length === 0) {
    const message = `${baseMessageLabel} paymentInfo for invoice not found, invoiceId: ${tkey}`;
    await addPaymentActionsLog({ tkey, user, message, data: dataForCheck, userIp });

    return {
      error: 0,
      canPay: false,
    };
  }

  const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo, fromPage, tkey, coin });
  const { list: allBasicRules } = await getAllBasicRules();
  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allAccounts } = await getAllAccounts();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  let userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);

  if (types && types.length) {
    // eslint-disable-next-line max-len
    userBypassedPaymentAccountsForCurrentPackage = userBypassedPaymentAccountsForCurrentPackage.filter(account => types.includes(account.PaymentType.name));
  }

  const filteredAccounts = types && types.length ? allAccounts.filter(account => types.includes(account.PaymentType.name)) : allAccounts;
  // eslint-disable-next-line max-len
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, filteredAccounts, userBypassedPaymentAccountsForCurrentPackage, paymentInfo.id);

  // check all group rules for can pay and save user log for not allowed rules
  // eslint-disable-next-line max-len
  const cannotPayGroupRules = await getCannotPayGroupRulesStringifyByAccountAndPackage(userPermissionsGroupRules, allBasicRules, canPayWithBasicRules, filteredAccounts, paymentInfo);

  // save user log if user cannot pay some group rule
  if (cannotPayGroupRules.length) {
    const groupMessage = `${baseMessageLabel} user didn’t pass some group rules, package: ${paymentInfo.epricestr}`;
    await addPaymentActionsLog({ tkey, user, message: groupMessage, data: { cannotPayGroupRules }, userIp });
  }
  if (userAllowedPaymentAccounts && userAllowedPaymentAccounts.length) {
    const { list: allOnSubmitRules } = await getAllPaymentOnSubmitPayRules();
    const enabledOnSubmitRules = allOnSubmitRules.filter(rule => rule.enabled);
    // eslint-disable-next-line max-len
    const allowedPaymentAccountsByType = await getUserAllowedPaymentAccountsByTypeOnSubmit(user, paymentInfo, enabledOnSubmitRules, dataForCheck, userAllowedPaymentAccounts, userBypassedPaymentAccountsForCurrentPackage);

    // check all group rules for can pay and save user log for not allowed rules
    // eslint-disable-next-line max-len
    const cannotPayOnSubmitRules = await getCannotPayOnSubmitRulesStringifyByAccountAndPackage(user, dataForCheck, enabledOnSubmitRules, userAllowedPaymentAccounts, paymentInfo);

    // save user log if user cannot pay some on submit rule
    if (cannotPayOnSubmitRules.length) {
      const onSubmitMessage = `${baseMessageLabel} user didn’t pass some on submit rules, package: ${paymentInfo.epricestr}`;
      await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { dataForCheck, cannotPayOnSubmitRules }, userIp });
    }
    if (Object.keys(allowedPaymentAccountsByType).length) {
      const allowedPaymentTypeNames = Object.keys(allowedPaymentAccountsByType);
      const onSubmitMessage = `${baseMessageLabel} user can pay with types: ${allowedPaymentTypeNames.join(',')}, package: ${paymentInfo.epricestr}`;
      await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: { dataForCheck }, userIp });

      return {
        error: 0,
        result: allowedPaymentAccountsByType,
      };
    }
  }

  return {
    error: 0,
    result: {},
  };
};
