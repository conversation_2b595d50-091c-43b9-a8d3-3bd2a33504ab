const { PaymentUserNotFoundError, InvoiceNotFound, MissingCommonParamsError } = require('@s1/api-errors');
const axios = require('axios');
const PaymentLog = require('../../model/audb/PaymentLog');
const getPaymentGeneralConfig = require('./getPaymentGeneralConfig');
const User = require('../../model/audb/User');
const sendEmailInvoice = require('./sendEmailInvoice');
const isMongooseObjectID = require('../../helpers/isMongooseObjectID');
const getSignedHmac = require('../../helpers/getSignedHmac');

const canWeDoRefund = paymentLog => paymentLog.amount > 0 && paymentLog.txid && !paymentLog.isRefunded;

const sendBillnetRefundStripe = async (chargeId, paymentServiceKeyId) => {
  const { generalConfig } = await getPaymentGeneralConfig();
  const instance = await axios.create({
    baseURL: generalConfig.billingBaseUrl,
    headers: {
      Accept: 'application/json',
    },
  });
  const response = await instance.post(`refunds.php?keyid=${paymentServiceKeyId}`, { chargeId });

  return response.status === 200 && response.data ? response.data : { success: false, error: response.statusText };
};

const sendBillnetRefundBraintree = async (chargeId, btServerId, additionalUrlParams) => {
  const { generalConfig } = await getPaymentGeneralConfig();
  const instance = await axios.create({
    baseURL: generalConfig.billingBaseUrl,
    headers: {
      Accept: 'application/json',
    },
  });
  const response = await instance.post(`refund-bt.php?hash=${btServerId}${additionalUrlParams}`, { chargeId });

  return response.status === 200 && response.data ? response.data : { success: false, error: response.statusText };
};

const refundStripe = async ({ chargeId, paymentServiceKeyId }) => {
  try {
    const refundResult = await sendBillnetRefundStripe(chargeId, paymentServiceKeyId);

    if (refundResult && refundResult.success) return refundResult;

    return { success: false, error: refundResult.error };
  } catch (e) {
    if (e.type) return { success: false, error: e.message };

    throw new Error(e);
  }
};

const refundBraintree = async ({ tkey, userId, chargeId, btServerId }) => {
  try {
    const signString = `${tkey}|${userId}|0|0|0`;
    const misc = getSignedHmac({ str: signString });

    const additionalUrlParams = `&misc=${misc}&tkey=${tkey}&uid=${userId}&sid=0&sc=0&ac=0`;
    const refundResult = await sendBillnetRefundBraintree(chargeId, btServerId, additionalUrlParams);

    if (refundResult && refundResult.success) return refundResult;

    return { success: false, error: refundResult.error };
  } catch (e) {
    if (e.type) return { success: false, error: e.message };

    throw new Error(e);
  }
};

const refundCrypto = async ({ chargeId, paymentServiceKeyId }) => ({ success: false, error: 'Refund is not supported for crypto currencies' });

module.exports = async ({ invoiceId, reason, locale }) => {
  if (!invoiceId) throw new MissingCommonParamsError('Missing required param invoiceId');

  let paymentLog;

  if (isMongooseObjectID(invoiceId)) {
    paymentLog = await PaymentLog.findOne({ _id: invoiceId }).exec();
  }
  if (!paymentLog) throw new InvoiceNotFound(locale);
  if (canWeDoRefund(paymentLog)) {
    const chargeId = paymentLog.txid;

    let response;
    const { pptype, paymentServiceKeyId, btServerId, tkey, uid: userId } = paymentLog;

    switch (pptype) {
      case 'crypto':
        response = await refundCrypto({ chargeId, paymentServiceKeyId, locale });
        break;
      case 'BT-card':
        response = await refundBraintree({ tkey, userId, chargeId, btServerId, locale });
        break;
      default:
        response = await refundStripe({ chargeId, paymentServiceKeyId, locale });
    }

    if (response && response.success) {
      paymentLog.isRefunded = true;
      paymentLog.status = 'Refunded';
      paymentLog.refundReason = reason;
      await paymentLog.save();

      const user = await User.findOne({ id: paymentLog.uid });

      if (!user) throw new PaymentUserNotFoundError(locale);

      await sendEmailInvoice({ paymentLog, user, locale });
    }

    return response;
  }
  if (paymentLog.isRefunded) return ({ success: false, error: 'Invoice already refunded' });

  return ({ success: false, error: 'Refunds is not possible on this invoice' });
};
