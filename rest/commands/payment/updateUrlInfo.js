const PaymentUrlInfo = require('../../model/audb/PaymentUrlInfo');

module.exports = async (data) => {
  if (!data.id) throw new Error('ID is required');
  if (!data.attempt) throw new Error('attempt is required');

  const urlInfo = await PaymentUrlInfo.findOne({ _id: data.id }).exec();

  if (!urlInfo) throw new Error('url info not found');

  urlInfo.attempt = data.attempt;
  // update TTL time
  urlInfo.createdForTtlIndex = new Date();

  await urlInfo.save();

  return {
    error: 0,
    result: { ...urlInfo._doc },
  };
};
