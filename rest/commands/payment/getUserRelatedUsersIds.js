const UserRelations = require('../../model/audb/UserRelations');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');

module.exports = async (userId, adminsAndSupportsIDs = []) => {
  if (!adminsAndSupportsIDs || !adminsAndSupportsIDs.length) {
    adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
  }

  // search relations in current user models
  const relatedInCurrentUserModels = await UserRelations.find({ userId, relatedUserId: { $nin: adminsAndSupportsIDs } }).lean().cache(3600, `userRelations_id_${userId}`);
  const relatedInCurrentUserIds = relatedInCurrentUserModels.map(model => model.relatedUserId);
  // search relations in other users models
  const relatedInOtherUsersModels = await UserRelations.find({ $and: [
    { relatedUserId: userId },
    { userId: { $nin: relatedInCurrentUserIds } },
    { userId: { $nin: adminsAndSupportsIDs } },
  ] }).lean().cache(120);
  const relatedInOtherUsersIds = relatedInOtherUsersModels.map(model => model.userId);
  const relatedUsers = relatedInCurrentUserIds.concat(relatedInOtherUsersIds);
  relatedUsers.push(userId);

  return relatedUsers;
};
