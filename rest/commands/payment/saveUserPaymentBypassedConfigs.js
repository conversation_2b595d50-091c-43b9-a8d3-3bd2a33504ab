const UserConfig = require('../../model/audb/UserConfig');
const User = require('../../model/audb/User');

module.exports = async (body) => {
  const { uid, bypassedPaymentTypes = [], bypassedPaymentAccounts = [] } = body;

  if (!uid) throw new Error('User ID is required');

  const user = await User.findOne({ id: uid }).exec();

  if (!user) throw new Error('User not found');

  let userConfig = await UserConfig.findOne({ uid }).exec();

  if (!userConfig) userConfig = new UserConfig({ uid });

  userConfig.bypassedPaymentTypes = bypassedPaymentTypes;
  userConfig.bypassedPaymentAccounts = bypassedPaymentAccounts;

  await userConfig.save();

  return {
    error: 0,
    success: true,
  };
};
