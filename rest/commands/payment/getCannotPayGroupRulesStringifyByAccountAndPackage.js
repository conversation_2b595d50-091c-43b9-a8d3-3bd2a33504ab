const getCanPayStringifyGroupRule = require('../payment/helpers/getCanPayStringifyGroupRule');
const getCanPayStringifyHTMLGroupRule = require('../payment/helpers/getCanPayStringifyHTMLGroupRule');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const filterRulesByPaymentAccountsByAndPackageId = require('../payment/helpers/filterRulesByPaymentAccountsByAndPackageId');

/**
 * Method check can pay with group rules by accounts and package and return cannot pay stringify group rules
 *
 * @param {Array} allGroupRules
 * @param {Array} allBasicRules
 * @param {object} canPayWithBasicRules
 * @param {Array} accounts - accounts for check
 * @param {object} packageInfo - current package
 *
 * @return {Promise} - [{name, canPay, rule}]
 * */
module.exports = async (allGroupRules, allBasicRules, canPayWithBasicRules, accounts, packageInfo) => {
  const filteredGroupRules = filterRulesByPaymentAccountsByAndPackageId(allGroupRules, accounts, packageInfo.id);
  const allBasicRulesObject = {};
  allBasicRules.forEach((rule) => { allBasicRulesObject[rule._id] = rule; });
  const canPayStringifyGroupRules = [];
  const promises = [];
  filteredGroupRules.forEach(rule => promises.push((async () => {
    const isUserCanPayWithRuleStringify = getCanPayStringifyGroupRule(rule, allBasicRulesObject, canPayWithBasicRules);
    const isUserCanPayWithRuleStringifyHTML = getCanPayStringifyHTMLGroupRule(rule, allBasicRulesObject, canPayWithBasicRules);
    const canPay = canPayWithGroupRule(rule, canPayWithBasicRules);
    canPayStringifyGroupRules.push({
      name: rule.name,
      canPay,
      rule: isUserCanPayWithRuleStringify,
      ruleHTML: isUserCanPayWithRuleStringifyHTML,
    });
  })()));
  await Promise.all(promises);
  const cannotPayGroupRules = canPayStringifyGroupRules.filter(rule => !rule.canPay);

  return cannotPayGroupRules;
};
