const log = require('@s1/log').create(__filename);
const { encrypt } = require('../../helpers/security');
const config = require('../../../config');

module.exports = async (data) => {
  if (!data.cardInfo) throw new Error('Card info is required');

  try {
    const cardInfo = encrypt(data.cardInfo, config.cardDataSecuritySalt);

    return {
      error: 0,
      result: cardInfo,
    };
  } catch (e) {
    log.error('Cannot encrypt cardInfo', e);

    throw new Error('Cannot encrypt card info');
  }
};
