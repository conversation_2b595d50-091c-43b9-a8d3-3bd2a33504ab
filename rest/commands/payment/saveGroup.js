const PaymentGroup = require('../../model/audb/PaymentGroup');

module.exports = async ({ user, data }) => {
  if (!data.name) throw new Error('Payment group name is required');

  let paymentGroup;

  if (data._id) paymentGroup = await PaymentGroup.findOne({ _id: data._id }).exec();
  else paymentGroup = new PaymentGroup();

  paymentGroup.name = data.name;
  paymentGroup.description = data.description || '';
  paymentGroup.modifiedByUid = user.id;
  paymentGroup.modifiedByName = user.name;

  await paymentGroup.save();

  return {
    error: 0,
    paymentGroup,
  };
};
