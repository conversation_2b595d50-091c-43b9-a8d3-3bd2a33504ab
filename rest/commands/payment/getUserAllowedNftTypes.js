const UserConfig = require('../../model/audb/UserConfig');
const { Package } = require('../../model/audb/Package');
const getAllPaymentTypeAccounts = require('../../commands/payment/getAllPaymentTypeAccounts');
const isBlacklistedUser = require('../../commands/payment/helpers/rules/isBlacklistedUser');

/**
 * Method checks user config for bypassed payment types and accounts, both should be active
 * and return list of bypassed payment types
 *
 * @param {object} userConfig
 * @param {string} userConfig
 * @param {array} paymentAccounts
 *
 * @return {array<string>} - list of bypasses payment type names
 * */
const getAllowedTypes = (userConfig, packageId, paymentAccounts) => {
  const allowedTypes = new Set();

  Object.entries(paymentAccounts)
    .filter(([, type]) => !!type.enabled)
    .forEach(([, type]) => {
      // checks if user has bypassed payment type and active account for this type and current package
      if (userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes
        && userConfig.bypassedPaymentTypes.length && userConfig.bypassedPaymentTypes.includes(type._id.toString())) {
        type.accounts.forEach((account) => {
          if (account.paymentGroup === 'Nft' && account.enabled && account.hasOwnProperty('packageId')
            && account.packageId === parseInt(packageId)) allowedTypes.add(account.PaymentType.name);
        });
      }
      // checks if user has bypassed active account for current package
      if (userConfig.hasOwnProperty('bypassedPaymentAccounts') && userConfig.bypassedPaymentAccounts
        && userConfig.bypassedPaymentAccounts.length && type.accounts && type.accounts.length) {
        type.accounts.forEach((account) => {
          if (account.paymentGroup === 'Nft' && account.enabled && account.PaymentType.enabled && userConfig.bypassedPaymentAccounts.includes(account._id.toString())
            && account.hasOwnProperty('packageId') && account.packageId === parseInt(packageId)) allowedTypes.add(account.PaymentType.name);
        });
      }
    });

  return Array.from(allowedTypes);
};

module.exports = async (user, packageId) => {
  if (!user) throw new Error('User not found');
  if (!packageId) throw new Error('packageId is required');
  // do not allow nft types for the blacklisted users
  if (!user.skipBlacklistPayment && await isBlacklistedUser({ user })) return {
    error: 0,
    allowedTypes: [],
  };

  const userPackage = await Package.findOne({ id: packageId }).lean();

  if (!userPackage) throw new Error('Package not found');

  const userConfig = await UserConfig.findOne({ uid: user.id }).lean();
  let allowedTypes = [];

  // allow to pay users with bypassed accounts for current package only
  if (userConfig && (
    (userConfig.hasOwnProperty('bypassedPaymentTypes') && userConfig.bypassedPaymentTypes && userConfig.bypassedPaymentTypes.length)
    || (userConfig.hasOwnProperty('bypassedPaymentAccounts' && userConfig.bypassedPaymentAccounts && userConfig.bypassedPaymentAccounts.length))
  )) {
    // load list of payment accounts grouped by payment type
    const paymentAccountsResult = await getAllPaymentTypeAccounts();

    if (paymentAccountsResult && paymentAccountsResult.hasOwnProperty('list') && paymentAccountsResult.list) {
      allowedTypes = getAllowedTypes(userConfig, packageId, paymentAccountsResult.list);
    }
  }

  return {
    error: 0,
    allowedTypes,
  };
};
