const PaymentCoinConfig = require('../../model/audb/PaymentCoinConfig');
const getPaymentCoinsConfigs = require('./getPaymentCoinsConfigs');

module.exports = async ({ user, data }) => {
  let paymentCoinConfig;

  if (data._id) paymentCoinConfig = await PaymentCoinConfig.findOne({ _id: data._id }).exec();
  else paymentCoinConfig = new PaymentCoinConfig();

  Object.entries(data).forEach(([key, value]) => {
    paymentCoinConfig[key] = value;
  });

  paymentCoinConfig.modifiedByUid = user.id;
  paymentCoinConfig.modifiedByName = user.name;

  await paymentCoinConfig.save();

  const results = await getPaymentCoinsConfigs();

  return results;
};
