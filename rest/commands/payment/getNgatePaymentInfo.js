const { Package } = require('../../model/audb/Package');
const { PaymentNoPackageError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const config = require('../../../config');

module.exports = async ({ amount, email }) => {
  if (!amount) throw new Error('amount is required');

  const allPackages = await Package.find().cache(600).lean();
  const pack = allPackages.find(_pack => Math.round(_pack.price) === Math.round(amount));

  if (!pack) throw new PaymentNoPackageError('en');

  let currentUser;
  let isNgateGuest = false;

  if (email) {
    const encryptedEmail = User.encryptEmail(email);
    currentUser = await User.findOne({ em: encryptedEmail, isNgateUser: 1 }).exec();
  }
  if (!currentUser) {
    const encryptedEmail = User.encryptEmail(config.ngate.generalUserEmail);
    currentUser = await User.findOne({ em: encryptedEmail, isNgateUser: 1 }).exec();
    isNgateGuest = true;
  }
  if (!currentUser) throw new Error('User not found');

  const invoices = await currentUser.getInvoices({ fromPage: 'adminGenerateInvoice', coupon: null, locale: 'en' });
  const invoice = invoices.find(invoice => invoice.id === pack.id);

  if (!invoice) throw new Error('Cannot create invoice');

  const result = {
    tkey: invoice.tokey,
    userId: currentUser.id,
    isNgateGuest,
  };

  return {
    error: 0,
    result,
  };
};
