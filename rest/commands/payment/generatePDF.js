const i18n = require('../../helpers/geti18n');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');
const PaymentLog = require('../../model/audb/PaymentLog');
const PDFInvoice = require('../../helpers/PDFInvoice');

module.exports = async ({ user, tkey }) => {
  const [paymentLog, plans] = await Promise.all([
    PaymentLog.findOne({ tkey }).lean().exec(),
    PackageToFeatures.find()
      .lean()
      .populate('packages')
      .cache(600)
      .exec(),
  ]);

  if (!paymentLog || paymentLog.uid !== user.id) throw new Error(i18n.__('Invoice generating error'));

  const plan = plans.find(plan => plan.packages.some(pckg => pckg.id === paymentLog.package)) || {};
  const pckg = (plan.packages || []).find(pckg => pckg.id === paymentLog.package) || {};
  const { epricestr } = pckg;

  return PDFInvoice.generate({ ...paymentLog, epricestr });
};
