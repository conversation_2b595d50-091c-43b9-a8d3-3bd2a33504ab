const moment = require('moment');
const PaymentLog = require('../../model/audb/PaymentLog');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');
const PackageFeature = require('../../model/audb/PackageFeature');
const getLocalizedEntityField = require('../../helpers/getLocalizedEntityField');

module.exports = async ({ uid, locale }) => {
  const [logs, plans, features] = await Promise.all([
    // get successfully paid logs only for displaying on users UI
    PaymentLog.find({ uid, amount: { $gt: 0 }, upback: true }).select('-_id').sort({ created: -1 }).limit(100)
      .lean()
      .exec(),
    PackageToFeatures.find().lean().populate('packages').cache(600)
      .exec(),
    PackageFeature.find().select('-_id').sort({ sor: 1 })
      .cache(600)
      .exec()
      .then(packageFeatures => Promise.all(packageFeatures.map(
        packageFeature => packageFeature.format({ locale }),
      ))),
  ]);
  let plan = {};

  return logs.reduce((data, log) => {
    const year = moment.unix(log.created).format('YYYY');
    data[year] = data[year] || [];

    if (log.pptype === 'stripe' || log.pptype === 'ST-card' || log.pptype === 'BT-card' || log.pptype === 'CR-card') log.pptype = 'card/debit card';

    delete log.logid;
    log.paymentid = log.tkey;
    plan = plans.find(plan => plan.packages.some(pckg => pckg.id === log.package)) || plan;
    const pckg = (plan.packages || []).find(pckg => pckg.id === log.package) || {};
    const { price = null, length = null } = pckg;
    const { extra = null } = plan;
    Object.assign(log, {
      name: getLocalizedEntityField(plan, 'name', locale),
      extra,
      description: getLocalizedEntityField(plan, 'description', locale),
      pricestr: getLocalizedEntityField(pckg, 'name', locale),
      epricestr: pckg.epricestr,
      price,
      length,
      features: features.filter(feature => (plan.features || []).includes(feature.id)),
    });
    data[year].push(log);

    return data;
  }, {});
};
