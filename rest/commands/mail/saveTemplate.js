const { MailTemplate, MailTemplatePostmark, MailTemplateGeneralNft } = require('../../model/audb/MailTemplate');
const { LastIdModel } = require('../../model/audb/LastId');

module.exports = async ({ data, provider, locale }) => {
  let mailTemplate;
  let Model;
  const { tagname } = data;
  switch (provider) {
    case 'postmark':
      Model = MailTemplatePostmark;
      break;
    case 'general':
      Model = MailTemplateGeneralNft;
      break;
    default:
      Model = MailTemplate;
      break;
  }

  if (locale && locale !== 'he') mailTemplate = await Model.findOne().byTag(`${tagname}_${locale}`).exec();
  if (!mailTemplate) mailTemplate = await Model.findOne().byTag(tagname).exec();
  if (!mailTemplate) mailTemplate = new Model();

  // no need to update mongo _id
  delete data._id;
  // eslint-disable-next-line no-return-assign
  Object.entries(data).forEach(([key, value]) => mailTemplate[key] = value);

  try {
    switch (provider) {
      case 'postmark':
        if (!mailTemplate.postmarkid) {
          mailTemplate.postmarkid = await mailTemplate.createRemoteTemplate();

          if (!mailTemplate.postmarkid) throw new Error('Cannot create MailTemplate');
        }

        break;
      default:
        break;
    }

    if (!mailTemplate.id) mailTemplate.id = await LastIdModel.getNextId(MailTemplate);

    await mailTemplate.save();
  } catch (e) {
    throw new Error('Cannot save MailTemplate');
  }

  return mailTemplate;
};
