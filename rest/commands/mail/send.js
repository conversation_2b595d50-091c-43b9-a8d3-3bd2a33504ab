const {
  MailTemplateNotFoundError,
} = require('@s1/api-errors');
const { MailTemplatePostmark } = require('../../model/audb/MailTemplate');

module.exports = async ({
  tag,
  from,
  to,
  data,
  locale,
  replyTo,
}) => {
  let mailTemplate;

  if (locale && locale !== 'he') mailTemplate = await MailTemplatePostmark.findOne().byTag(`${tag}_${locale}`).exec();
  if (!mailTemplate) mailTemplate = await MailTemplatePostmark.findOne().byTag(tag).exec();
  if (!mailTemplate) throw new MailTemplateNotFoundError();

  const response = await mailTemplate.send(data, from, to, null, replyTo);

  return response.Message === 'OK';
};
