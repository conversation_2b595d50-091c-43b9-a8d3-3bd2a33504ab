const sendPostmark = require('./send');
const sendGeneralNft = require('./sendGeneralNft');
const config = require('../../../config');

module.exports = async ({ data, provider, locale }) => {
  const { tagname } = data;
  let sendEmailResult = null;

  const mailOptions = {
    tag: tagname,
    from: config.email.noReply,
    to: data.emailto,
    data,
    locale,
  };

  try {
    switch (provider) {
      case 'postmark':
        sendEmailResult = await sendPostmark(mailOptions);
        break;
      case 'general':
        mailOptions.paymentAccountId = data.paymentAccountId;
        sendEmailResult = await sendGeneralNft(mailOptions);
        break;
      default:
        break;
    }
  } catch (e) {
    throw new Error('Cannot send test template');
  }

  return sendEmailResult;
};
