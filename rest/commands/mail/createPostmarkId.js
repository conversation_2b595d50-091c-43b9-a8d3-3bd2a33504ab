const { MailTemplatePostmark } = require('../../model/audb/MailTemplate');

module.exports = async ({ data }) => {
  const mailTemplate = await MailTemplatePostmark.findOne({ id: data.id }).exec();
  let postmarkid = null;

  if (mailTemplate) {
    try {
      postmarkid = await mailTemplate.createRemoteTemplate();

      if (postmarkid) {
        mailTemplate.postmarkid = postmarkid;
        await mailTemplate.save();
      }
    } catch (e) {
      throw new Error('Cannot create postmark ID');
    }
  }

  return postmarkid;
};
