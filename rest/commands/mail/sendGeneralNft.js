const { MailTemplateNotFoundError } = require('@s1/api-errors');
const formData = require('form-data');
const Mailgun = require('mailgun.js');
const { MailTemplateGeneralNft } = require('../../model/audb/MailTemplate');
const { PaymentAccount } = require('../../model/audb/PaymentAccount');

module.exports = async ({
  tag,
  to,
  paymentAccountId,
}) => {
  if (!tag) throw new Error('tag is required');
  if (!paymentAccountId) throw new Error('paymentAccountId is required');

  const paymentAccount = await PaymentAccount.findOne({ id: paymentAccountId }).lean();

  if (!paymentAccount) throw new Error(`Payment account not found, paymentAccountId: ${paymentAccountId}`);

  const mailTemplate = await MailTemplateGeneralNft.findOne().byTag(tag).lean();

  if (!mailTemplate) throw new MailTemplateNotFoundError();

  const { emailApiKey, emailDomain, emailFrom } = paymentAccount;

  if (!emailApiKey || !emailDomain || !emailFrom) return false;

  const DOMAIN = emailDomain;
  const API_KEY = emailApiKey;
  const mailgun = new Mailgun(formData);
  const client = mailgun.client({ username: 'api', key: API_KEY, url: 'https://api.eu.mailgun.net' });
  const htmlParams = {
    name: 'TestUserName',
    tkey: **********,
    email: '<EMAIL>',
  };

  let html = mailTemplate.contents;
  // eslint-disable-next-line no-return-assign
  Object.entries(htmlParams).forEach(([key, value]) => html = html.replace(`{{${key}}}`, value));

  const messageParams = {
    from: emailFrom,
    to,
    subject: mailTemplate.title,
    html,
  };

  const response = await client.messages.create(DOMAIN, messageParams)
    .then(res => res && res.status === 200)
    .catch((err) => {
      console.error('Cannot send nft email with mailgun, error:', err);

      return false;
    });

  return !!response;
};
