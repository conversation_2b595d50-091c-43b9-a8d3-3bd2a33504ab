const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const moment = require('moment');
const UserLogVod = require('../../model/audb/UserLogVod');
const VodSettings = require('../../model/audb/VodSettings');
const sUserVodPosition = require('../../model/subscription/sUserVodPosition');
const sUserFavoriteVod = require('../../model/subscription/sUserFavoriteVod');
const sUserFavoriteTvShow = require('../../model/subscription/sUserFavoriteTvShow');
const categoriesCache = require('../../service/cache/categories');
const { fillSubscriptions } = require('../../service/subscribeForData');
const sendTelegramMessage = require('../../helpers/sendTelegramMessage');
const config = require('../../../config');
const writePlayVodLog = require('./writePlayVodLog');
const _getRecord = require('../record/get');
const Channel = require('../../model/audb/Channel');
const increaseRedisViews = require('../../helpers/increaseRedisViews');

const processRecord = async ({ vodId, user, locale, userAllIPs, sessionID, countryCode, ISP, stateCode, req }) => {
  const [categoryId, channelId, startTime] = String(vodId).split('_');
  const channel = await Channel.findOne({ id: channelId }).exec();
  const category = await Category.findOne({ id: categoryId }).exec();
  const streamingServers = await user.getStreamingServers(ISP, countryCode, stateCode, 2);
  let fullPath = null;

  if (category) {
    fullPath = await category.getFullPath().then((fullPath) =>
      Promise.all(
        fullPath.map(async (category) => {
          category.showpic = await category.resolvePicture(categoriesCache, {
            domain: streamingServers.mainServer.sip,
          });

          return category;
        }),
      ),
    );
  }

  const result = await _getRecord({
    channel,
    startTime,
    user,
    userIP: req.userIP,
    userAllIPs,
    ISP,
    countryCode,
    stateCode,
    locale,
    sid: sessionID,
    req,
    fromVod: true,
  });

  const fileData = result.playlist[0];
  const fulllinks = result.fulllinks.map((links) => [links[0].playurl, links[0].vtturl]);
  const vodinfo = {
    id: vodId,
    episodeno: 0,
    genre: result.show.genre,
    stars: result.show.star,
    startotal: result.show.startotal,
    approve: true,
    cateid: categoryId,
    created: result.show.rdatetime,
    updated: result.show.rdatetime,
    description: result.show.description,
    length: result.show.lengthtime,
    duration: result.show.lengthtime,
    name: result.show.name,
    picture: [
      {
        big: result.show.showpic,
        small: result.show.showpic,
        smallwhite: result.show.showpic,
      },
    ],
    year: result.show.year,
    isAlreadySeen: result.show.isAlreadySeen,
    position: result.show.position,
    isinfav: result.show.isinfav,
    showpic: result.show.showpic,
    views: result.show.views,
  };
  const pathinfo = fullPath ? fullPath.map((category) => category.format({ locale })) : null;

  return {
    fulllinks,
    istvshow: 0,
    vodinfo,
    pathinfo,
    results: [fileData],
    playlist: [fileData],
  };
};

module.exports = async ({ user, vodId, locale, userAllIPs, sessionID, countryCode, ISP, stateCode, req }) => {
  if (String(vodId).split('_').length > 2) {
    return processRecord({ vodId, user, locale, userAllIPs, sessionID, countryCode, ISP, stateCode, req });
  }

  let startTime;
  let timeLog;

  if (config.isSlowLog) {
    startTime = Date.now();
    timeLog = {
      api: 'loadvod',
      request: {
        userId: user ? user.id : null,
        vodId,
        locale,
        userAllIPs,
        sessionID,
        countryCode,
        ISP,
        stateCode,
      },
      startTime: moment().format('YYYY/MM/DD, h:mm:ss.SSS'),
    };
  }

  const [vod, streamingServers] = await Promise.all([
    Vod.findOne({ id: vodId }).populate('category').cache(120).exec(),
    user.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD),
  ]);

  if (config.isSlowLog) timeLog.loadVod = Date.now() - startTime;
  if (!vod) return null; // VodNotFoundException

  req.resolved = req.resolved || { vod };
  const vodSettings = await VodSettings.findOne({ data: 'vodurl' }).exec();
  let fullPath = null;

  if (vod.category) {
    fullPath = await vod.category.getFullPath().then((fullPath) =>
      Promise.all(
        fullPath.map(async (category) => {
          category.showpic = await category.resolvePicture(categoriesCache, {
            domain: streamingServers.mainServer.sip,
          });

          return category;
        }),
      ),
    );
  }
  if (config.isSlowLog) timeLog.resolvePicture = Date.now() - startTime;
  if (fullPath) fullPath.reverse();

  const tvShowCategory = fullPath ? Category.getTvShow(fullPath) : null;

  if (config.isSlowLog) timeLog.getTvShow = Date.now() - startTime;
  if (tvShowCategory) await increaseRedisViews('vodcate', tvShowCategory.id);

  const { isOldApp = false } = req;
  const fullLinks = Vod.getFullLinks(vod, streamingServers, user, userAllIPs, sessionID, req.clIpHeaders, isOldApp);
  const urlPrefix = streamingServers.mainServer.getUrlPrefix();
  const urlPath = fullLinks[0][0].replace(urlPrefix, '');

  if (config.isSlowLog) timeLog.getFullLinks = Date.now() - startTime;

  const fileData = {
    urlport: urlPrefix,
    file: urlPath,
    duration: 0,
    start: 0,
  };
  const baseCondition = { uid: user.id };
  // TODO remove fillSubscriptions, use aggregation instead on mongodb v3.6+, right now mongodb v3.4 does not support join by few fields
  const subUserVodPosition = await sUserVodPosition({ baseCondition });
  const sUserFavoriteSubscription = tvShowCategory ? sUserFavoriteTvShow : sUserFavoriteVod;
  sUserFavoriteSubscription({ baseCondition }).add(vod);
  subUserVodPosition.add(vod);
  const thumbnailOptions = { domain: streamingServers.mainServer.sip };
  const viewsToAdd = await increaseRedisViews('vodm', vod.id);
  vod.views += viewsToAdd;
  await vod.prepare({ thumbnailOptions });

  if (config.isSlowLog) timeLog.addSubscriptions = Date.now() - startTime;

  await fillSubscriptions();

  if (config.isSlowLog) timeLog.fillSubscriptions = Date.now() - startTime;

  // do not save the same channel log for the last 10 mins
  const checkLastXXmins = 10;
  const timeFrom = moment().subtract(checkLastXXmins, 'minutes').unix();
  const lastLogs = await UserLogVod.find({ uid: user.id, playtime: { $gte: timeFrom }, vodid: vodId }, { _id: 1 })
    .cache(checkLastXXmins)
    .lean();

  if (!lastLogs.length) {
    // do not need wait to write logs
    writePlayVodLog({ req, streamingServers, vodId, vod, user, tvShowCategory, vodSettings, fullPath, isOldApp });
  }

  const vodinfo = vod.format({ tvShowCategory, vodSettings, locale });
  const pathinfo = fullPath ? fullPath.map((category) => category.format({ locale })) : null;

  if (config.isSlowLog) timeLog.formatVodAndCategory = Date.now() - startTime;
  if (config.isSlowLog && Date.now() - startTime > config.slowLogTimeout) sendTelegramMessage(timeLog);

  let filteredLinks = Array.from(new Set(fullLinks));
  filteredLinks = [filteredLinks[0]].concat(filteredLinks);

  return {
    fulllinks: filteredLinks,
    istvshow: +!!tvShowCategory,
    vodinfo,
    pathinfo,
    results: [fileData],
    playlist: [fileData],
  };
};
