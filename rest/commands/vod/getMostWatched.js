const { mostWatchedMoviesProjection, STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const categoriesCache = require('../../service/cache/categories');
const sUserFavoriteVod = require('../../model/subscription/sUserFavoriteVod');
const sUserFavoriteTvShow = require('../../model/subscription/sUserFavoriteTvShow');
const sUserVodPosition = require('../../model/subscription/sUserVodPosition');

/**
* Return most watched tvShows (from vod Categoryes), most watched movies from Vod, and the best movies by rate
*
* @param {object} data - options object with current user, locale, etc...
* @param {User} data.user - Current user
* @param {string} data.ISP - User Internet service provider from GeoIp - Vodafone
* @param {string} data.countryCode - Country code from GeoIp - US
* @param {string} data.stateCode - State code from GeoIp - CA
* @param {string} data.locale - Current language - en
* @returns object with arrays like: { mostTvShows: [], mostMovies: [], mostRated: [] }
* */
module.exports = async ({ user, ISP, countryCode, stateCode, locale }) => {
  const [tvShowCategoriesIds, offlineCategoriesIds] = await categoriesCache
    .wrap('tvShowCategoriesIds:offlineCategoriesIds', '10m', () => Promise.all([
      Category.getIdsByCondition({ isepisode: TVSHOW_ISEPISODE }),
      Category.getIdsByCondition({ ifonline: 0 }),
    ]));
  const tvShowsQuery = Category.find({
    isepisode: TVSHOW_ISEPISODE,
    id: { $nin: offlineCategoriesIds },
  }).sort({ views: -1 })
    .limit(24);
  const moviesQuery = Vod.find({
    cateid: { $nin: tvShowCategoriesIds },
    linkto: { $exists: false },
  }, mostWatchedMoviesProjection)
    .whichOnline()
    .sort({ views: -1 })
    .limit(32);
  const ratedIdsQuery = Vod.aggregate([
    {
      $match: {
        $and: [
          { cateid: { $nin: tvShowCategoriesIds } },
          { cateid: { $nin: offlineCategoriesIds } },
        ],
      },
    },
    {
      $project: {
        id: 1,
        rateno: { $divide: ['$stars', '$startotal'] },
      },
    },
    {
      $sort: { rateno: -1 },
    },
    {
      $limit: 6,
    },
  ]);
  const [tvShows, movies, ratedIds, streamingServers] = await Promise.all([
    tvShowsQuery,
    moviesQuery,
    ratedIdsQuery,
    User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD, user ? user.config || {} : {}, user),
  ]);
  const baseCondition = user ? { uid: user.id } : {};
  const subUserFavoriteVod = sUserFavoriteVod({ baseCondition });
  const subUserFavoriteTvShow = sUserFavoriteTvShow({ baseCondition });
  const subUserVodPosition = await sUserVodPosition({ baseCondition });
  const thumbnailDomain = streamingServers.mainServer.sip;
  const tvShowsFormatting = Promise.all(tvShows.map(async (tvShow) => {
    const result = tvShow.format({ locale });

    result.showpic = await tvShow.resolvePicture(categoriesCache, { domain: thumbnailDomain });
    result.upper = await tvShow.getFullPath();
    result.upper = await Promise.all(result.upper.map(async (category) => {
      const result = category.format({ locale });
      result.showpic = await category.resolvePicture(categoriesCache, { domain: thumbnailDomain });

      return result;
    }));

    if (user) subUserFavoriteTvShow.add(result);

    return result;
  }));
  const moviesFormatting = async () => {
    const categoryIds = movies.map(movie => movie.cateid);
    const categories = await Category.find({ id: { $in: categoryIds } });

    return Promise.all(movies.map(async (movie) => {
      const result = movie.format({ locale });
      const category = categories.find(cat => cat.id === movie.cateid);

      result.showpic = await movie.resolvePicture({ domain: thumbnailDomain });
      result.upper = null;

      if (user) {
        subUserFavoriteVod.add(result);
        subUserVodPosition.add(result);
      }
      if (category) {
        result.upper = await category.getFullPath();
        result.upper = await Promise.all(result.upper.map(async (category) => {
          const result = category.format({ locale });

          result.showpic = await category.resolvePicture(categoriesCache, { domain: thumbnailDomain });

          return result;
        }));
      }

      return result;
    }));
  };
  const ratedFormatting = async () => {
    const ids = ratedIds.map(r => r.id);
    const vods = await Vod.find({ id: { $in: ids } });
    const catIds = vods.map(vod => vod.cateid);
    const categories = await Category.find({ id: { $in: catIds } });

    return Promise.all(vods.map(async (vod) => {
      const category = categories.find(cat => cat.id === vod.cateid);
      const result = vod.format({ locale });

      result.rateno = vod.rateno;
      result.showpic = await vod.resolvePicture({ category, domain: thumbnailDomain });
      result.upper = null;

      if (user) {
        subUserFavoriteVod.add(result);
        subUserVodPosition.add(result);
      }
      if (category) {
        result.upper = await category.getFullPath();
        result.upper = await Promise.all(result.upper.map(async (cat) => {
          const formattedCategory = cat.format({ locale });

          formattedCategory.showpic = await cat.resolvePicture(categoriesCache, { domain: thumbnailDomain });

          return formattedCategory;
        }));
      }

      return result;
    }));
  };
  const [mostTvShows, mostMovies, mostRated] = await Promise.all([
    tvShowsFormatting,
    moviesFormatting(),
    ratedFormatting(),
  ]);

  return { mostTvShows, mostMovies, mostRated };
};
