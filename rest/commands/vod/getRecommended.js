const moment = require('moment');
const { shuffle } = require('lodash');
const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const UserVodPosition = require('../../model/audb/UserVodPosition');
const { makeSubscription, fillSubscriptions } = require('../../service/subscribeForData');
const categoryCache = require('../../service/cache/categories');

module.exports = async ({
  uid,
  type,
  limit,
  period,
  periodLimit,
  page,
  pageSize,
  liteQuery,
  ISP,
  countryCode,
  stateCode,
  locale,
}) => {
  const results = [];
  const timestamp = moment().subtract(1, period).unix();
  let count;

  if (type === 'movies') {
    const subscriptionPosition = makeSubscription(UserVodPosition, {
      defaultValue: {
        position: 0,
      },
      extractKey({ vod }) {
        return vod;
      },
      getCondition({ id: vod }) {
        return { vod, uid };
      },
      assignData(vod, { position }) {
        vod.position = position;
      },
    });

    let movies = [];
    const query = {
      $or: [{ episodeno: { $eq: 0 } }, { episodeno: { $eq: null } }],
      linkto: { $exists: false },
      created: { $gte: timestamp },
    };
    await Vod.find(query)
      .whichOnline()
      .sort({ views: -1 })
      .limit(Number(pageSize || periodLimit))
      .skip((page - 1) * pageSize || 0)
      .cursor()
      .eachAsync((vod) => {
        vod.position = 0;
        movies.push(vod);
        subscriptionPosition.add(vod);
      });

    if (movies.length) await fillSubscriptions();
    // Disabled count, it is not correct calculating
    // if (page && pageSize && !liteQuery) count = await Vod.find(query).countDocuments();

    movies = movies.filter(movie => movie.position === 0);
    movies.forEach(movie => results.push(movie.format({ locale })));
  } else if (type === 'episodes') {
    const query = {
      isepisode: TVSHOW_ISEPISODE,
      ifonline: 1,
      created: { $gte: timestamp },
    };
    const streamingServers = await User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD, {}, { id: uid });
    await Category.find(query)
      .sort({ views: -1 })
      .limit(Number(pageSize || periodLimit))
      .skip((page - 1) * pageSize || 0)
      .cursor()
      .eachAsync(async (episode) => {
        const result = episode.format({ locale });

        if (result.showpic.includes('undefined')) result.showpic = await episode.resolvePicture(
          categoryCache,
          { domain: streamingServers.mainServer.sip },
        );

        results.push(result);
      });

    if (page && pageSize && !liteQuery) count = await Category.find(query).countDocuments();
  }

  await Promise.all(results);

  return {
    result: count >= 0 || liteQuery ? results : shuffle(results).slice(-limit),
    count,
  };
};
