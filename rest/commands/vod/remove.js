const { ApiError } = require('@s1/api-errors');
const Vod = require('@s1/vod-models/Vod');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserFavoriteVod = require('../../model/audb/UserFavoriteVod');

module.exports = async (vodid) => {
  if (!vodid) throw new ApiError(-1, 'Missed VOD id param');

  const vod = await Vod.findOne({ id: vodid });

  if (vod) {
    await vod.remove();
    await UserFavoriteVod.deleteMany({ chvod: vodid });
    await UserLogVod.deleteMany({ vodid });

    return {
      error: 0,
      result: 'Ok',
    };
  }

  throw new ApiError(-1, 'On remove VOD not found');
};
