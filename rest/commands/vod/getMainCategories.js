const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const categoryCache = require('../../service/cache/categories');

module.exports = async ({ locale, ISP, countryCode, stateCode }) => {
  const [categories, streamingServers] = await Promise.all([
    Category
      .find({ topath: '1,' })
      .whichOnline()
      .sort({ sorder: 1 })
      .exec()
      .then(Category.resolveLinks.bind(Category)),
    User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD),
  ]);
  const results = await Category.getOneLevel(
    { isepisode: 0 },
    categories,
    categoryCache,
    { domain: streamingServers.mainServer.sip },
  );

  return results.map(result => result.format({ isMain: true, isShow: true, level: 1, locale }));
};
