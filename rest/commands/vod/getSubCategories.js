const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const UserFavoriteTvShow = require('../../model/audb/UserFavoriteTvShow');
const categoryCache = require('../../service/cache/categories');
const seasonPropertiesSubscriptionConstructor = require('../../model/subscription/categorySeasonProperties');

const getLastEpisodeAndSeasonExtraProperties = async (category, categoryParents) => {
  const result = {
    lastSeasonDescription: '',
    lastSeasonEDescription: '',
    lastEpisodeYear: '',
  };

  if (category.category_type === 'season' || category.category_type === 'series') {
    let lastSeasonId = null;

    if (category.category_type === 'season' && categoryParents && categoryParents.length) {
      const seriesParent = categoryParents.find(p => p.category_type === 'series');
      lastSeasonId = seriesParent.lastSeasonId;
    } else {
      lastSeasonId = category.lastSeasonId;
    }
    if (lastSeasonId) {
      const lastSeason = await Category.findOne({ id: lastSeasonId })
        .whichOnline()
        .populate('Movies')
        .lean()
        .cache(300);

      if (lastSeason) {
        result.lastSeasonDescription = lastSeason.description;
        result.lastSeasonEDescription = lastSeason.edescription;

        if (lastSeason.Movies && lastSeason.Movies.length) {
          const lastEpisode = lastSeason.Movies.reduce((prev, current) => ((prev.episodeno > current.episodeno) ? prev : current));

          if (lastEpisode) {
            result.lastEpisodeYear = lastEpisode.year;
          }
        }
      }
    }
  }

  return result;
};

/**
 * Method return subcategories
 *
 * @param {User} user
 * @param {number} id - category ID
 * @param {number} hf - first letter index
 * @param {number} page
 * @param {number} pageSize
 * @param {string} locale
 * @param {number} mo - sort field param
 * @param {string} ISP - comes from user location loaded by user IP
 * @param {string} countryCode - comes from user location loaded by user IP
 * @param {string} stateCode - comes from user location loaded by user IP
 * @returns {Promise<{cate, upper, subetcs: {sons, hfs}}|{cate, upper, subetcs: {hfs, sons}}>}
 */
module.exports = async ({
  user, id, hf, page, locale, pageSize, mo, ISP, countryCode, stateCode,
}) => {
  const category = await Category.findOne({ id })
    .whichOnline()
    .exec()
    .then(category => category && category.resolveLink());

  if (!category) return {
    cate: { picture: null },
    upper: null,
    subetcs: {
      sons: null,
      hfs: null,
    },
  };

  const seasonPropertiesSubscription = seasonPropertiesSubscriptionConstructor();
  const currentPage = page ? parseInt(page) : false;
  // eslint-disable-next-line max-len
  // TODO instead of load all info into category.getSubCategories() need to load id and hf or ehf for current locale after filtering by pages need to load with whole info
  const [parents, allSubcategories, streamingServers] = await Promise.all([
    category.getParents(),
    category.getSubCategories(),
    User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD, user ? user.config : {}, user),
  ]);
  const lastEpisodeAndSeasonExtraProperties = await getLastEpisodeAndSeasonExtraProperties(category, parents);
  const thumbnailOptions = { domain: streamingServers.mainServer.sip };
  mo = hf ? '10' : (mo || category.mo);
  parents.push(category);

  await Promise.all(parents.map((parent, i) => parent.prepare(
    categoryCache,
    parents[i - 1] || { isepisode: 0 },
    { thumbnailOptions, locale },
  )));
  // TODO sort into DB query
  Category.sortCategories(mo, allSubcategories);
  const pageSubcategories = allSubcategories;
  // TODO load user favorites for page categories only instead of all user
  const favourites = user ? await UserFavoriteTvShow.find({ uid: user.id }).exec() : [];
  const hfs = Category.extractHfsByLocale(pageSubcategories, locale);
  const upperEpisode = parents.filter(({ isepisode }) => isepisode === TVSHOW_ISEPISODE)[0];
  const show = category.isepisode === TVSHOW_ISEPISODE ? category : (upperEpisode || false);
  let subcategories = hf ? Category.filterByHf(hf, hfs, pageSubcategories) : pageSubcategories;
  subcategories = currentPage
    ? subcategories.slice((currentPage - 1) * pageSize, currentPage * pageSize) : subcategories;

  await Promise.all(subcategories.map(subcategory => subcategory.prepare(
    categoryCache,
    category,
    { thumbnailOptions, seasonPropertiesSubscription, locale },
  )));

  subcategories = await Promise.all(subcategories.map(
    async (s) => {
      const subcategory = s.format({ favourites, category, isSon: true, locale, stringifyPicture: true });
      const lastEpisodeAndSeasonExtraProperties = await getLastEpisodeAndSeasonExtraProperties(subcategory, null);

      return { ...subcategory, ...lastEpisodeAndSeasonExtraProperties };
    },
  ));

  const results = {
    cate: category.format({ favourites, category, locale, stringifyPicture: true }),
    upper: parents.map(p => (p.format({ favourites, needbyseason: 0, category, locale, stringifyPicture: true }))),
    subetcs: {
      hfs,
      sons: subcategories,
    },
    ...lastEpisodeAndSeasonExtraProperties,
    ...(show ? { show: show.format({ favourites, isShow: true, category, locale }) } : {}),
  };

  if (category.allshowenable > 0) results.subetcs.sons = [
    category.format({ allShow: true, stringifyPicture: true, locale }),
    ...results.subetcs.sons,
  ];
  if ((category.isepisode !== TVSHOW_ISEPISODE) && upperEpisode) {
    results.cate.isepisode = TVSHOW_ISEPISODE;
    results.upper[results.upper.length - 1].isepisode = TVSHOW_ISEPISODE;
  }

  return results;
};
