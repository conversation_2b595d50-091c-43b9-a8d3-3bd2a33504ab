const moment = require('moment');
const last = require('lodash/last');
const isEmpty = require('lodash/isEmpty');
const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { TVSHOW_ISEPISODE, moviesByCategoryProjection } = require('@s1/api-constants').category;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const { CategoryNotFoundError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');
const categoryCache = require('../../service/cache/categories');
const sUserFavoriteVod = require('../../model/subscription/sUserFavoriteVod');
const sUserFavoriteTvShow = require('../../model/subscription/sUserFavoriteTvShow');
const sUserVodPosition = require('../../model/subscription/sUserVodPosition');
const sUserRatedVods = require('../../model/subscription/sUserRatedVods');
const sUserRatedCategory = require('../../model/subscription/sUserRatedCategory');
const { fillSubscriptions } = require('../../service/subscribeForData');

const SEPARATOR = '/';
const sortParams = {
  6: 'year',
  7: 'views',
  8: 'starsAveraged',
  9: 'id',
  10: 'hfall',
  20: 'episodeno',
};
const DEFAULT_GENRE = {
  en: 'Movies',
  he: 'סרטים',
};

const processRecords = async (
  topCategory,
  page,
  pageSize,
  locale,
  gs,
  sortNeeded,
  mo,
  ISP,
  countryCode,
  stateCode,
  baseResponse,
  search,
  isVdfPage,
) => {
  page = page || 1;
  const unixNow = moment().unix();
  const daysToSubtract = unixNow > moment(moment().format('YYYY-MM-DD 06:00')).unix() ? 13 : 14;
  const unixTwoWeeksAgo = moment(moment().subtract(daysToSubtract, 'days').format('YYYY-MM-DD 06:00')).unix();
  const query = {
    rdatetime: { $gte: unixTwoWeeksAgo, $lte: unixNow },
    // query (`rdatetime` + `lengthtime`) less than now
    $expr: { $lt: [{ $add: ['$rdatetime', '$lengthtime'] }, unixNow] },
  };
  let channels = {};
  const streamingServers = await User.getStreamingServers(ISP, countryCode, stateCode, 2);
  const thumbnailOptions = { domain: streamingServers.mainServer.sip };
  const sortOptions = { rdatetime: -1 };

  if (mo) sortOptions[sortParams[mo]] = sortNeeded ? 1 : -1;
  if (search) {
    query.$or = [{ name: { $regex: search, $options: 'i' } }, { name_en: { $regex: search, $options: 'i' } }];
  }
  if (topCategory.channelIds) {
    query.channel = { $in: topCategory.channelIds };
    channels = await Channel.find({ id: { $in: topCategory.channelIds } })
      .exec()
      .then((channels) => channels.reduce((acc, channel) => ({ ...acc, [channel.id]: channel }), {}));
  }
  if (topCategory.yearRange) {
    const [from, to] = topCategory.yearRange.split('-');
    query.year = { $gte: Number(from), $lte: Number(to) };
  }
  if (isVdfPage) {
    const [rawRecords, totalRecords] = await Promise.all([
      Schedule.aggregate([
        { $match: query },
        { $sort: { rdatetime: 1 } },
        {
          $group: {
            _id: { name: '$name', year: '$year' },
            doc: { $first: '$$ROOT' },
          },
        },
        {
          $replaceRoot: { newRoot: '$doc' },
        },
        { $sort: sortOptions },
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize },
      ]).exec(),
      // Count all documents matching the query and group by channel, name, year
      Schedule.aggregate([
        { $match: query },
        {
          $group: {
            _id: { name: '$name', year: '$year' },
            doc: { $first: '$$ROOT' },
          },
        },
        {
          $replaceRoot: { newRoot: '$doc' },
        },
        { $count: 'count' },
      ])
        .exec()
        .then((r) => (r && r[0] && r[0].count ? r[0].count : 0)),
    ]);

    const records = await rawRecords.map((r) => {
      r.showpic = Schedule.prototype.getThumbnailUrl.call(r, channels[r.channel], thumbnailOptions);

      return {
        _id: r._id,
        id: `${topCategory.id}_${r.channel}_${r.rdatetime}`,
        channel: r.channel,
        name: r.name,
        ename: r.name_en,
        genre: r.genre,
        egenre: r.genre_en,
        description: r.description,
        edescription: r.description_en,
        showpic: r.showpic,
        length: r.lengthtime,
        year: r.year,
        category_type: isVdfPage ? 'records' : undefined,
        ifonline: r.hasOwnProperty('ifonline') ? r.ifonline : 1,
        showCustomPicForRecords: r.showCustomPicForRecords,
        views: r.views || 0,
      };
    });

    return { result: records, vodmsPagination: { page, pageSize, count: totalRecords } };
  }

  const [rawRecords, totalRecords] = await Promise.all([
    Schedule.aggregate([
      { $match: { ...query } },
      { $sort: { rdatetime: 1 } },
      {
        $group: {
          _id: { name: '$name', year: '$year' },
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
      { $match: { $or: [{ ifonline: 1 }, { ifonline: { $exists: false } }] } },
      { $sort: sortOptions },
      { $skip: (page - 1) * pageSize },
      { $limit: pageSize },
    ]).exec(),
    // Count all documents matching the query and group by channel, name, year
    Schedule.aggregate([
      { $match: { ...query, $or: [{ ifonline: 1 }, { ifonline: { $exists: false } }] } },
      {
        $group: {
          _id: { name: '$name', year: '$year' },
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
      { $count: 'count' },
    ])
      .exec()
      .then((r) => (r && r[0] && r[0].count ? r[0].count : 0)),
  ]);

  const records = await rawRecords.map((r) => {
    r.showpic = Schedule.prototype.getThumbnailUrl.call(r, channels[r.channel], thumbnailOptions);

    return {
      _id: r._id,
      id: `${topCategory.id}_${r.channel}_${r.rdatetime}`,
      cateid: topCategory.id,
      channel: r.channel,
      name: locale === 'en' && r.name_en ? r.name_en : r.name,
      ename: r.name_en,
      description: locale === 'en' && r.description_en ? r.description_en : r.description,
      edescription: r.description_en,
      genre: (locale === 'en' && r.genre_en ? r.genre_en : r.genre) || DEFAULT_GENRE[locale || 'he'],
      egenre: r.genre_en || DEFAULT_GENRE.en,
      showpic: r.showpic,
      length: r.lengthtime,
      duration: r.lengthtime,
      year: r.year,
      views: r.views || 0,
    };
  });

  const vodmsPagination = { page, pageSize, count: totalRecords };

  return {
    result: {
      ...baseResponse,
      subshows: {
        hfs: [],
        vodms: records,
        totp: Math.ceil(totalRecords / pageSize),
        page: page.toString(),
        ...vodmsPagination,
      },
    },
    vodmsPagination,
  };
};

/**
 * Method return subcategories, parent category, related vods/shows
 *
 * @param {number} rawId - category id
 * @param {User} user
 * @param {number} hf - first letter index
 * @param {string} locale
 * @param {number} page
 * @param {boolean} sortNeeded
 * @param {number} gs - 0 search in current category, value 1 search in subcategories like for All Series or All Films
 * @param {number} pageSize
 * @param {number} mo - sort field param
 * @param {string} ISP - comes from user location loaded by user IP
 * @param {string} countryCode - comes from user location loaded by user IP
 * @param {string} stateCode - comes from user location loaded by user IP
 * @returns {Promise<{red, cate, actualid, blue, subshows, upper, vodmsPagination}}
 */
module.exports = async ({
  rawId,
  user,
  hf,
  locale,
  page,
  sortNeeded,
  gs,
  pageSize,
  mo,
  ISP,
  countryCode,
  stateCode,
  search = null,
  isVdfPage = false,
}) => {
  const idParts = rawId.includes(SEPARATOR) ? rawId.split(SEPARATOR) : null;
  const id = parseInt(idParts ? idParts[idParts.length - 2] : rawId, 10);

  if (!id) throw new CategoryNotFoundError();

  let topCategory = await Category.findOne({ id }).exec();

  if (!topCategory) {
    return {
      cate: user ? { isinfav: 0 } : null,
      upper: null,
      red: null,
      blue: null,
      subshows: {
        hfs: null,
        vodms: null,
        totp: null,
        page: null,
      },
      actualid: null,
    };
  }
  if (topCategory.linkto > 0) topCategory = await Category.findOne({ id: topCategory.linkto }).whichOnline().exec();

  const baseCondition = user ? { uid: user.id } : {};
  const subUserFavoriteVod = user ? sUserFavoriteVod({ baseCondition }) : null;
  const subUserFavoriteTvShow = user ? sUserFavoriteTvShow({ baseCondition }) : null;
  const subUserVodPosition = user ? await sUserVodPosition({ baseCondition }) : null;
  const subUserRatedVods = user ? sUserRatedVods({ baseCondition }) : null;
  const subUserRatedCategory = user ? sUserRatedCategory({ baseCondition }) : null;
  const [upperCategories, streamingServers] = await Promise.all([
    Category.find({ id: { $in: idParts || topCategory.ancestor } }).exec(),
    User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD, user ? user.config : {}, user),
  ]);
  upperCategories.push(topCategory);
  const upperShowCategory = upperCategories.find((category) => category.isepisode === TVSHOW_ISEPISODE);
  let realTop = topCategory;
  let checkSeason;
  let show;
  let maxEpisode = 0;

  if (topCategory.isepisode === TVSHOW_ISEPISODE) {
    checkSeason = { isparentseason: true };
    checkSeason.sons = await realTop.getSubCategories();
    show = realTop;

    if (checkSeason.sons[0]) realTop = checkSeason.sons[0];

    upperCategories.push(realTop);
  } else if (upperShowCategory) {
    checkSeason = { isoneseason: true };
    show = upperShowCategory;

    if (show.linkto > 0) show = await Category.findOne({ id: show.linkto }).whichOnline().exec();
    if (show) checkSeason.sons = await show.getSubCategories();
  }

  const upperCategoriesObj = await Promise.all(
    upperCategories.map(async (item, i) => {
      await item.prepare(categoryCache, upperCategories[i - 1] || { isepisode: 0 }, {
        thumbnailOptions: { domain: streamingServers.mainServer.sip },
      });
      const formattedItem = item.format({ locale });

      if (user) {
        subUserFavoriteTvShow.add(formattedItem);
        subUserRatedCategory.add(formattedItem);
      }

      return formattedItem;
    }),
  );
  const realTopObj = last(upperCategoriesObj);

  if (topCategory.category_type === 'records') {
    return processRecords(
      topCategory,
      page,
      pageSize,
      locale,
      gs,
      sortNeeded,
      mo,
      ISP,
      countryCode,
      stateCode,
      {
        cate: realTopObj,
        upper: upperCategoriesObj,
        subshows: {},
        actualid: realTopObj.id,
      },
      search,
      isVdfPage,
    );
  }
  if (realTopObj.id === 3 && gs) realTopObj.listx = 1;
  if (show) {
    if (user) subUserFavoriteTvShow.add(show);

    mo = 20;
    maxEpisode = await realTop.getMaxEpisodeNumber(gs, categoryCache);
  }
  if (!mo) mo = 12;

  const hfs = await realTop.getHfs(gs, categoryCache, locale);
  const hfChar = (hfs[hf - 1] || {}).hf;
  const vodsCount = await realTop.getVodsCount({ gs, hf: hfChar, locale }, categoryCache);
  const toTp = maxEpisode > 0 ? Math.ceil(maxEpisode / 10 / pageSize) : Math.ceil(vodsCount / pageSize);
  const upperCategoryWithGenre = [...upperCategories].reverse().find((e) => e.genre);
  const sortField = sortParams[mo];
  const primarySortOrder = sortNeeded ? 1 : -1;
  const sortOptions = sortField ? { [sortField]: primarySortOrder } : { id: primarySortOrder };
  const userPage = parseInt(page) || (maxEpisode > 0 ? toTp : 1);
  const currentPage = userPage > toTp || userPage <= 0 ? toTp || 1 : userPage || toTp;
  const resultVodsCondition = await realTop.getVodsCondition({ gs, hf: hfChar, locale }, categoryCache);
  const resultVodsQuery = Vod.find({}, moviesByCategoryProjection);
  const totalEpisodes = maxEpisode > 0 ? Math.ceil(maxEpisode / 10) : undefined;
  const episodePages = [];

  if (maxEpisode > 0) {
    let end = totalEpisodes;
    while (end > 0) {
      const rawStart = end - pageSize + 1 || 1;
      const start = rawStart > 0 ? rawStart : 1;
      episodePages.push({ e: end, s: start });
      end -= pageSize;
    }

    if (!gs) episodePages.reverse();
  }
  if (maxEpisode > 0) {
    const startEpisode = +episodePages[currentPage - 1].s === 1 ? 0 : +episodePages[currentPage - 1].s;
    const endEpisode = episodePages[currentPage - 1].e;
    resultVodsCondition.episodeno = {
      $gte: startEpisode * 10,
      $lte: endEpisode * 10,
    };
  } else resultVodsQuery.skip((currentPage - 1) * pageSize).limit(pageSize);

  const countVods = await Vod.find({}, moviesByCategoryProjection)
    .where(resultVodsCondition)
    .whichOnline()
    .sort(sortOptions)
    .populate('category')
    .countDocuments();

  await resultVodsQuery.where(resultVodsCondition).whichOnline().sort(sortOptions).populate('category');

  const aggregation = [
    // - added field where calculate average stars by method divide `stars / startotal`
    { $addFields: { starsAveraged: { $divide: ['$stars', '$startotal'] } } },
    { $match: resultVodsQuery._conditions },
    { $sort: resultVodsQuery.options.sort },
    { $project: moviesByCategoryProjection },
    {
      $lookup: {
        from: 'vodcate',
        localField: 'cateid',
        foreignField: 'id',
        as: 'category',
      },
    },
    { $unwind: { path: '$category' } },
  ];

  if (resultVodsQuery.options.skip) {
    aggregation.push({ $skip: resultVodsQuery.options.skip });
  }
  if (resultVodsQuery.options.limit) {
    aggregation.push({ $limit: resultVodsQuery.options.limit });
  }

  const vods = await Vod.aggregate(aggregation).exec();

  const resultVods = await Promise.all(
    vods.map(async (vod) => {
      const newVod = new Vod(vod);
      const genre = upperCategoryWithGenre ? upperCategoryWithGenre.genre : '';
      const stars = newVod.startotal > 0 ? vod.starsAveraged : 3;

      newVod.showpic = await newVod.resolvePicture({ domain: streamingServers.mainServer.sip });
      newVod.genre = newVod.genre || genre;
      newVod.stars = stars;

      const formattedVod = newVod.format({ locale });

      if (user) {
        subUserFavoriteVod.add(formattedVod);
        subUserVodPosition.add(formattedVod);
        subUserRatedVods.add(formattedVod);
      }

      return formattedVod;
    }),
  );

  if (user) {
    await fillSubscriptions();
    realTopObj.isAlreadyRated = realTopObj.isAlreadyRated || upperCategoriesObj.some((upper) => upper.isAlreadyRated);
  }

  const blueRed = Category.getRedBlue(realTopObj, upperCategories, gs);
  const results = {
    toTp,
    currentPage,
    pagesize: pageSize,
    cate: realTopObj,
    checkseason: checkSeason || {},
    upper: upperCategoriesObj,
    vodms: resultVods || [],
    bluered: blueRed,
    maxepi: maxEpisode,
    realtopid: realTopObj.id,
    show: show ? show.format({ locale }) : show,
    hfs,
  };
  results.checkseason.sons = results.checkseason.sons && results.checkseason.sons.map((category) => category.format({ locale }));

  if (totalEpisodes) {
    results.epage = totalEpisodes;
    results.epipages = episodePages;
  }

  const vodmsPagination = {
    page: currentPage,
    pageSize,
    count: countVods,
  };
  const subShows = {
    hfs: results.hfs,
    vodms: results.vodms,
    totp: results.toTp,
    page: results.currentPage.toString(),
    ...vodmsPagination,
  };

  if (results.epipages) {
    subShows.epage = results.epage;
    subShows.epipages = results.epipages;
  }

  const result = {
    cate: results.cate,
    upper: results.upper,
    subshows: subShows,
    red: (locale === 'en' ? results.bluered.ered : results.bluered.red) || null,
    blue: (locale === 'en' ? results.bluered.eblue : results.bluered.blue) || null,
    actualid: results.cate.id,
  };

  if (!isEmpty(results.show)) {
    result.checksea = results.checkseason;
    result.show = results.show;
  }

  return {
    result,
    vodmsPagination,
  };
};
