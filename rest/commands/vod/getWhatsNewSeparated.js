const first = require('lodash/first');
const log = require('@s1/log').create(__filename);
const { whatsNewProjection, STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const VodSettings = require('../../model/audb/VodSettings');
const categoriesCache = require('../../service/cache/categories');
const UserVodPosition = require('../../model/audb/UserVodPosition');
const settings = require('../../service/settings');
const AUserPosition = require('../../model/audb/abstract/AUserPosition');
const config = require('../../../config');

const imagesUriPath = `//${config.baseImagesPath}/images/b/`;
const WHATS_NEW_LIMIT_DEFAULT = 25;

const getLastEpisodesIds = async (serialCategories) => {
  const lastEpisodesQueries = serialCategories.map(serial => Vod.findOne({
    cateid: serial._id,
    episodeno: serial.maxEpisode,
  }).cache(600).exec());
  const lastEpisodes = await Promise.all(lastEpisodesQueries);

  return lastEpisodes
    .filter(episode => episode)
    .map(episode => episode.id);
};

const addFilteredVodsToTargetByCategoryWithLimit = (target, categoriesWithLimit = []) => sources => sources.forEach((source) => {
  const categoryLimitInfo = categoriesWithLimit.find(category => category.id === source._id);
  const isTvShows = !categoryLimitInfo.hasOwnProperty('countOfLastItemsInWhatsNew');

  // Now only movies category has setting for countOfLastItemsInWhatsNew also some movies somehow have episodeno property more then 0
  if (isTvShows) {
    target.episodes.push(source);
  } else {
    const numberOfMoviesToGetFromCategory = categoryLimitInfo.countOfLastItemsInWhatsNew || 1;
    target.movies.push(...source.ids.sort((a, b) => a - b).slice(-numberOfMoviesToGetFromCategory));
  }
});

const addLastVodsIdsToTarget = async (
  target,
  numberOfLastVodsToAdd,
  moviesCategoriesIds,
  key,
  categoriesWithLimit,
) => {
  await categoriesCache.wrap(`whatsNew:vodAggregation:${key}:${numberOfLastVodsToAdd}`, '10m',
    () => Vod.getLatest({
      exclude: [],
      neededCount: numberOfLastVodsToAdd,
      isTvShow: true,
      tvShowIds: moviesCategoriesIds,
      offlineIds: [],
      group: {
        _id: '$cateid',
        maxId: { $max: '$id' },
        maxEpisode: { $max: '$episodeno' },
        ids: { $push: '$id' },
      },
    }))
    .then(addFilteredVodsToTargetByCategoryWithLimit(target, categoriesWithLimit));
};

const getVodPosition = (vodId, vodPositions) => {
  if (!vodPositions || !vodPositions.length) return 0;

  const positionEntity = vodPositions.filter(position => position.vod === vodId);

  return positionEntity.length ? positionEntity[0].position : 0;
};

/**
 *
 * Get unique categories from raw vods array
 * @param {[object]} rawMoviesAndEpisodes
 * @returns
 */
const getUniqCategoryIds = rawMoviesAndEpisodes => Object.entries(rawMoviesAndEpisodes).reduce((accumulator, [key, value]) => {
  value.sort((a, b) => b.id - a.id);
  value.forEach((vod, vodIndex) => {
    const isEpisode = (key !== 'movies');
    accumulator.add(vod.cateid);

    if (isEpisode) {
      const withTheSameCategoryIndex = rawMoviesAndEpisodes[key].findIndex((e, i) => (e.cateid === vod.cateid) && (i > vodIndex));

      if (withTheSameCategoryIndex > -1) {
        const temp = rawMoviesAndEpisodes[key][withTheSameCategoryIndex];

        if (temp.episodeno > vod.episodeno) {
          rawMoviesAndEpisodes[key][withTheSameCategoryIndex] = vod;
          rawMoviesAndEpisodes[key][vodIndex] = temp;
        }
      }
    }
  });

  return accumulator;
}, new Set());

const addInfoToVods = (
  rawVods,
  categoriesMap,
  vodSettings,
  user,
  locale,
  userVodPositions,
  ALREADY_SEEN_OPTIONS,
) => rawVods.map((vod) => {
  const isEpisode = !!vod.episodeno;
  const episodeCategories = categoriesMap[vod.cateid];

  if (isEpisode) {
    if (!episodeCategories || !episodeCategories.length) return log.error(`empty path for category ${vod.cateid}`);

    const firstToCategory = episodeCategories[0];
    vod.pic = firstToCategory.showpic;
    vod.showpic = firstToCategory.showpic;
  }

  const result = vod.format({ vodSettings, locale, isMovie: !isEpisode });
  result.thumbnailUrl = result && result.picture && result.picture[0] && result.picture[0].big ? `${imagesUriPath}${result.picture[0].big}` : null;

  if (user) {
    const vodPosition = getVodPosition(vod.id, userVodPositions);

    if (vodPosition) {
      result.position = vodPosition;
      result.isAlreadySeen = AUserPosition.isAlreadySeen(result, vodPosition, ALREADY_SEEN_OPTIONS);
    }
    if (isEpisode) result.isinfav = vod.isinfav || +episodeCategories.some(category => category.cate_isinfav);
  }

  return result;
}).filter(vod => !!vod);

/**
 *
 * Get last uploaded VOD items separated by main categories
 * Task in Jira -  https://alina90871.atlassian.net/browse/BAC-66
 */
module.exports = async ({ user, locale, ISP, countryCode, stateCode, limit }) => {
  const categoriesIds = {};
  // Get MAIN categories (Movies, Series, Docu etc..)
  const mainCategories = await Category.find({ topath: '1,', ifonline: 1, enabledInWhatsNew: 1 }, { id: 1, ename: 1, name: 1, countOfLastItemsInWhatsNew: 1 }).sort({ sorder: 1 })
    .cache(600).lean();

  // Collect all categories IDs relative to each main category
  await Promise.all(mainCategories.map(async (mainCategory) => {
    // Get English category name in lower case
    const categoryName = mainCategory.ename.toLowerCase();

    // Collect all movies category IDs with parent_id from main category
    const moviesCategories = await Category.find(
      { parent_id: mainCategory.id, ifonline: 1, enabledInWhatsNew: 1, category_type: 'movies' },
      { id: 1, countOfLastItemsInWhatsNew: 1 },
    ).cache(600).lean();

    // Collect all subcategories IDs with parent_id from main category
    const subCategories = await Category.find(
      { parent_id: mainCategory.id, ifonline: 1, enabledInWhatsNew: 1 },
      { id: 1 },
    ).cache(600).lean();

    // For Episodes we collect all tv show categories where parent_id includes subcategories IDs
    const tvShowCategories = await Category.find(
      { parent_id: { $in: subCategories.map(subCategory => subCategory.id) }, ifonline: 1, enabledInWhatsNew: 1, isepisode: TVSHOW_ISEPISODE },
      { id: 1, lastSeasonId: 1 },
    ).cache(600).lean();

    // Collect all season categories where id includes tv show category lastSeasonId (get latest season what enabled in whats new)
    const seasonCategories = await Category.find(
      { id: { $in: tvShowCategories.map(tvShowCategory => tvShowCategory.lastSeasonId) }, ifonline: 1, enabledInWhatsNew: 1 },
      { id: 1 },
    ).cache(600).lean();

    // Collect all episodes and movies categories IDs by main category
    categoriesIds[categoryName] = [...seasonCategories.map(seasonCategory => ({ id: seasonCategory.id })), ...moviesCategories.map(
      movieCategory => ({
        id: movieCategory.id,
        countOfLastItemsInWhatsNew: movieCategory.countOfLastItemsInWhatsNew,
      }),
    )];
  }));

  const vodSettings = await VodSettings.findOne({ data: 'vodurl' }).cache(600).exec();
  const { alreadySeen: ALREADY_SEEN_OPTIONS } = await settings.get();
  const moviesAndEpisodesIds = {};

  // Get latest uploaded Movies and Episodes IDs with UI limit by categories IDs (default limit is 25 items per category)
  for await (const [key, value] of Object.entries(categoriesIds)) {
    const mainCategory = mainCategories.find(mainCategory => mainCategory.ename.toLowerCase() === key);
    const mainCategoryLimit = mainCategory.countOfLastItemsInWhatsNew || WHATS_NEW_LIMIT_DEFAULT;
    moviesAndEpisodesIds[key] = { episodes: [], movies: [] };

    const allCategoryIds = value.map(category => category.id);
    await addLastVodsIdsToTarget(
      moviesAndEpisodesIds[key],
      mainCategoryLimit,
      allCategoryIds,
      key,
      value,
    );
  }

  const rawMoviesAndEpisodes = {};

  // Get raw Episodes and Movies by IDs
  for await (const [key, value] of Object.entries(moviesAndEpisodesIds)) {
    rawMoviesAndEpisodes[key] = [];
    const mainCategory = mainCategories.find(mainCategory => mainCategory.ename.toLowerCase() === key);
    const mainCategoryLimit = mainCategory.countOfLastItemsInWhatsNew || WHATS_NEW_LIMIT_DEFAULT;
    const lastEpisodesIds = await getLastEpisodesIds(value.episodes);
    const allVodIds = lastEpisodesIds.concat(value.movies);

    let vodQuery = Vod.find({ id: { $in: allVodIds } }, whatsNewProjection).sort('-id');

    if (limit) {
      vodQuery = vodQuery.limit(Number(limit));
    } else {
      vodQuery = vodQuery.limit(Number(mainCategoryLimit));
    }

    rawMoviesAndEpisodes[key] = await vodQuery.cache(600).exec();
  }

  // Get all unique categories IDs by all vods result
  const uniqueCategoryIds = getUniqCategoryIds(rawMoviesAndEpisodes);
  const streamingServers = await User.getStreamingServers(
    ISP,
    countryCode,
    stateCode,
    STREAM_TYPE_VOD,
    user ? user.config : {},
    user,
  );
  const thumbnailDomain = streamingServers.mainServer.sip;
  const allVods = Object.values(rawMoviesAndEpisodes).reduce((acc, val) => [...acc, ...val], []);
  const variablePromises = [
    Category.find({ id: { $in: [...uniqueCategoryIds] } })
      .exec()
      .then(uniqueCategories => Category.getFullPathsForAll(uniqueCategories, async (item) => {
        item.showpic = await item.resolvePicture(categoriesCache, { domain: thumbnailDomain });
        item.cat_id = item.id;
      })),
    Promise.all(allVods.map(async (vod) => {
      const picture = await vod.resolvePicture({ domain: thumbnailDomain });
      vod.showpic = picture;
      vod.pic = picture;
    })),
  ];
  const [paths] = await Promise.all(variablePromises);
  const [pathsMap] = paths.reduce((acc, path) => {
    const [map] = acc;
    map[first(path).id] = path.map((category) => {
      const formatted = category.format({ locale });

      return formatted;
    });

    return acc;
  }, [{}]);
  let cates = {};
  cates = limit
    ? Object.entries(pathsMap).slice(0, Number(limit)).forEach((entry) => { cates[entry[0]] = entry[1]; })
    : pathsMap;

  let userVodPositions = [];

  if (user) {
    const allVodsIds = Object.values(moviesAndEpisodesIds).map(item => [...item.movies, ...item.episodes]).reduce((acc, val) => [...acc, ...val], []);
    userVodPositions = await UserVodPosition.find({
      uid: user.id,
      vod: { $in: allVodsIds },
    }).exec();
  }

  const newvodms = {};
  // Write category info and vods with info to the result object
  for (const mainCategory of mainCategories) {
    const categoryName = mainCategory.ename.toLowerCase();

    newvodms[categoryName] = {
      id: mainCategory.id,
      name: mainCategory.name,
      ename: mainCategory.ename,
      vods: addInfoToVods(rawMoviesAndEpisodes[categoryName], pathsMap, vodSettings, user, locale, userVodPositions, ALREADY_SEEN_OPTIONS),
    };
  }

  return {
    newvodms,
    cates,
  };
};
