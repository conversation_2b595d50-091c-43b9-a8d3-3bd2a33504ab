const { STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const Category = require('@s1/vod-models/Category');
const Vod = require('@s1/vod-models/Vod');
const VodSettings = require('../../model/audb/VodSettings');
const sUserVodPosition = require('../../model/subscription/sUserVodPosition');
const sUserFavoriteVod = require('../../model/subscription/sUserFavoriteVod');
const sUserFavoriteTvShow = require('../../model/subscription/sUserFavoriteTvShow');
const categoriesCache = require('../../service/cache/categories');
const { fillSubscriptions } = require('../../service/subscribeForData');
const writePlayVodLog = require('./writePlayVodLog');
const increaseRedisViews = require('../../helpers/increaseRedisViews');

module.exports = async ({ id, locale, user, userAllIPs, sessionID, countryCode, ISP, stateCode, req }) => {
  const [item, streamingServers] = await Promise.all([
    Vod.findOne({ id }).populate('category').exec(),
    user.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD),
  ]);

  if (!item) return null;

  req.resolved = req.resolved || { vod: item };
  const [fullPath, vodSettings] = await Promise.all([
    item.category.getFullPath().then((fullPath) =>
      Promise.all(
        fullPath.map(async (category) => {
          category.showpic = await category.resolvePicture(categoriesCache, {
            domain: streamingServers.mainServer.sip,
          });

          return category;
        }),
      ),
    ),
    VodSettings.findOne({ data: 'vodurl' }).exec(),
  ]);
  fullPath.reverse();
  const tvShowCategory = Category.getTvShow(fullPath);

  if (tvShowCategory) await increaseRedisViews('vodcate', tvShowCategory.id);

  const baseCondition = { uid: user.id };
  const subUserVodPosition = await sUserVodPosition({ baseCondition });
  const sUserFavoriteSubscription = tvShowCategory ? sUserFavoriteTvShow : sUserFavoriteVod;
  sUserFavoriteSubscription({ baseCondition }).add(item);
  subUserVodPosition.add(item);
  await Promise.all([item.prepare({ thumbnailOptions: { domain: streamingServers.mainServer.sip } }), fillSubscriptions()]);
  const { isOldApp = false } = req;
  const streams = item.getStreams(streamingServers, user, userAllIPs, sessionID, req.clIpHeaders, isOldApp);
  const prefix = streamingServers.mainServer.getUrlPrefix();
  const path = streams[0].replace(prefix, '');
  const url = { prefix, path };

  // no need to wait for save logs
  writePlayVodLog({ req, streamingServers, vodId: id, vod: item, user, tvShowCategory, vodSettings, fullPath, isOldApp });

  return {
    url,
    item: item.format({ tvShowCategory, vodSettings, locale }),
    streams,
    path: fullPath.map((category) => category.format({ locale })),
    isTvShow: !!tvShowCategory,
  };
};
