const first = require('lodash/first');
const log = require('@s1/log').create(__filename);
const { whatsNewProjection, STREAM_TYPE_VOD } = require('@s1/api-constants').vod;
const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const User = require('../../model/audb/User');
const VodSettings = require('../../model/audb/VodSettings');
const categoriesCache = require('../../service/cache/categories');
const UserVodPosition = require('../../model/audb/UserVodPosition');
const settings = require('../../service/settings');
const AUserPosition = require('../../model/audb/abstract/AUserPosition');
const config = require('../../../config');

const imagesUriPath = `//${config.baseImagesPath}/images/b/`;

const getLastEpisodesIds = async (serialCategories) => {
  const lastEpisodesQueries = serialCategories.map(serial => Vod.findOne({
    cateid: serial._id,
    episodeno: serial.maxEpisode,
  }).cache(600).exec());
  const lastEpisodes = await Promise.all(lastEpisodesQueries);

  return lastEpisodes
    .filter(episode => episode)
    .map(episode => episode.id);
};

function appendVodsAggregation(target, isTvShows) {
  return sources => sources.forEach((source) => {
    if (isTvShows) {
      target.push(source);
    } else {
      target.push(source.maxId);
    }
  });
}

const addLastVodsIdsToTarget = async (
  target,
  numberOfLastVodsToAdd,
  isTvShows,
  tvShowCategoriesIds,
  offlineCategoriesIds,
) => {
  const vodAggregationIndex = isTvShows ? 1 : 0;

  await categoriesCache.wrap(`whatsNew:vodAggregation:${vodAggregationIndex}:${numberOfLastVodsToAdd}`, '10m',
    () => Vod.getLatest({
      exclude: target,
      neededCount: numberOfLastVodsToAdd,
      isTvShow: isTvShows,
      tvShowIds: tvShowCategoriesIds,
      offlineIds: offlineCategoriesIds,
    }))
    .then(appendVodsAggregation(target, isTvShows));
};

const getVodPosition = (vodId, vodPositions) => {
  const positionEntity = vodPositions.filter(position => position.vod === vodId);

  return positionEntity.length ? positionEntity[0].position : 0;
};

const getTopVods = async () => Vod.find({
  istopnew: true,
  linkto: { $exists: false },
  cateid: { $exists: true },
}, {
  _id: 0,
  id: 1,
  cateid: 1,
}).whichOnline().cache(600).exec();

const getUniqCategoryIds = (movies, episodes) => [movies, episodes].reduce((accumulator, vods, index) => {
  vods.sort((a, b) => b.id - a.id);
  vods.forEach((vod, vodIndex) => {
    const isEpisode = (index === 1);
    accumulator.add(vod.cateid);

    if (isEpisode) {
      const withTheSameCategoryIndex = episodes.findIndex((e, i) => (e.cateid === vod.cateid) && (i > vodIndex));

      if (withTheSameCategoryIndex > -1) {
        const temp = episodes[withTheSameCategoryIndex];

        if (temp.episodeno > vod.episodeno) {
          episodes[withTheSameCategoryIndex] = vod;
          episodes[vodIndex] = temp;
        }
      }
    }
  });

  return accumulator;
}, new Set());

const addInfoToEpisodes = (
  rawEpisodes,
  categoriesMap,
  vodSettings,
  user,
  locale,
  userVodPositions,
  ALREADY_SEEN_OPTIONS,
) => rawEpisodes.map((episode) => {
  const episodeCategories = categoriesMap[episode.cateid];

  if (!episodeCategories || !episodeCategories.length) return log.error(`empty path for category ${episode.cateid}`);

  const firstToCategory = episodeCategories[0]; // Category.getTvShow(path);
  episode.pic = firstToCategory.showpic;
  episode.showpic = firstToCategory.showpic;
  const result = episode.format({ vodSettings, locale });

  result.thumbnailUrl = result && result.picture && result.picture[0] && result.picture[0].big ? `${imagesUriPath}${result.picture[0].big}` : null;

  if (user) {
    const vodPosition = getVodPosition(episode.id, userVodPositions);

    result.position = vodPosition;
    result.isAlreadySeen = AUserPosition.isAlreadySeen(result, vodPosition, ALREADY_SEEN_OPTIONS);
    result.isinfav = episode.isinfav || +episodeCategories.some(category => category.cate_isinfav);
  }

  return result;
}).filter(episode => !!episode);

const addInfoToMovies = (
  rawMoviesModels,
  vodSettings,
  user,
  locale,
  userVodPositions,
  ALREADY_SEEN_OPTIONS,
) => rawMoviesModels.map((movie) => {
  const result = movie.format({ vodSettings, locale, isMovie: true });

  result.thumbnailUrl = result && result.picture && result.picture[0] && result.picture[0].big ? `${imagesUriPath}${result.picture[0].big}` : null;

  if (user) {
    const vodPosition = getVodPosition(movie.id, userVodPositions);

    result.position = vodPosition;
    result.isAlreadySeen = AUserPosition.isAlreadySeen(movie, vodPosition, ALREADY_SEEN_OPTIONS);
  }

  return result;
});

module.exports = async ({ user, locale, ISP, countryCode, stateCode, limit }) => {
  const topNewVods = await getTopVods();

  const [tvShowCategoriesIds, offlineCategoriesIds, vodSettings] = await categoriesCache
    .wrap('tvShowCategoriesIds:offlineCategoriesIds', '10m', () => Promise.all([
      Category.getIdsByCondition({ isepisode: TVSHOW_ISEPISODE }),
      Category.getIdsByCondition({ ifonline: 0 }),
      VodSettings.findOne({ data: 'vodurl' }).exec(),
    ]));

  const movieAndEpisodeIds = {
    movieIds: [],
    tvShowEpisodeIds: [],
  };

  topNewVods.forEach((vod) => {
    if (tvShowCategoriesIds.includes(vod.cateid)) {
      movieAndEpisodeIds.tvShowEpisodeIds.push(vod.id);
    } else {
      movieAndEpisodeIds.movieIds.push(vod.id);
    }
  });

  const { alreadySeen: ALREADY_SEEN_OPTIONS, ui: UI_OPTIONS } = await settings.get();

  const numberOfLastMoviesToAdd = UI_OPTIONS.whatsNewLimit - movieAndEpisodeIds.movieIds.length;
  const numberOfLastTvShowsToAdd = UI_OPTIONS.whatsNewLimit - movieAndEpisodeIds.tvShowEpisodeIds.length;

  await addLastVodsIdsToTarget(
    movieAndEpisodeIds.movieIds,
    numberOfLastMoviesToAdd,
    false,
    tvShowCategoriesIds,
    offlineCategoriesIds,
  );
  await addLastVodsIdsToTarget(
    movieAndEpisodeIds.tvShowEpisodeIds,
    numberOfLastTvShowsToAdd,
    true,
    tvShowCategoriesIds,
    offlineCategoriesIds,
  );

  let moviesQuery = Vod.find({ id: { $in: movieAndEpisodeIds.movieIds } }, whatsNewProjection).sort('-id');

  if (limit) moviesQuery = moviesQuery.limit(Number(limit));

  // Here we get full models of last Items
  const rawMoviesModels = await moviesQuery.cache(600).exec();
  const lastEpisodesIds = await getLastEpisodesIds(movieAndEpisodeIds.tvShowEpisodeIds);
  const rawEpisodesModels = await Vod.find({ id: { $in: lastEpisodesIds } }, whatsNewProjection).sort('-id').cache(600).exec();

  const uniqueCategoryIds = getUniqCategoryIds(rawMoviesModels, rawEpisodesModels);
  const streamingServers = await User.getStreamingServers(
    ISP,
    countryCode,
    stateCode,
    STREAM_TYPE_VOD,
    user ? user.config : {},
    user,
  );
  const thumbnailDomain = streamingServers.mainServer.sip;
  const allVods = [...rawMoviesModels, ...rawEpisodesModels];
  const variablePromises = [
    Category.find({ id: { $in: [...uniqueCategoryIds] } })
      .cache(600)
      .exec()
      .then(uniqueCategories => Category.getFullPathsForAll(uniqueCategories, async (item) => {
        item.showpic = await item.resolvePicture(categoriesCache, { domain: thumbnailDomain });
        item.cat_id = item.id;
      })),
    Promise.all(allVods.map(async (vod) => {
      const picture = await vod.resolvePicture({ domain: thumbnailDomain });
      vod.showpic = picture;
      vod.pic = picture;
    })),
  ];
  const [paths] = await Promise.all(variablePromises);
  const [pathsMap] = paths.reduce((acc, path) => {
    const [map] = acc;
    map[first(path).id] = path.map((category) => {
      const formatted = category.format({ locale });

      return formatted;
    });

    return acc;
  }, [{}]);
  let cates = {};
  cates = limit
    ? Object.entries(pathsMap).slice(0, Number(limit)).forEach((entry) => { cates[entry[0]] = entry[1]; })
    : pathsMap;

  let userVodPositions = [];

  if (user) {
    userVodPositions = await UserVodPosition.find({
      uid: user.id,
      vod: { $in: movieAndEpisodeIds.movieIds.concat(movieAndEpisodeIds.tvShowEpisodeIds) },
    }).exec();
  }

  let _episodes = addInfoToEpisodes(
    rawEpisodesModels,
    pathsMap,
    vodSettings,
    user,
    locale,
    userVodPositions,
    ALREADY_SEEN_OPTIONS,
  );

  if (limit) _episodes = _episodes.slice(0, Number(limit));

  const _movies = addInfoToMovies(rawMoviesModels, vodSettings, user, locale, userVodPositions, ALREADY_SEEN_OPTIONS);

  return {
    newvodms: {
      episodes: _episodes,
      movies: _movies,
    },
    cates,
  };
};
