const Category = require('@s1/vod-models/Category');
const Vod = require('@s1/vod-models/Vod');

const subReg = category => new RegExp(`^${category.topath}${category.id},`);

const fixCatId = (upper) => {
  upper.map((item) => {
    if (!item.cat_id) {
      item.cat_id = item.id;
    }

    return item;
  });

  return upper;
};

const getLinkedTo = async (category) => {
  if (!category.linkto) return category;

  return Category
    .findOne({ id: category.linkto, ifonline: 1 })
    .exec();
};

const fullPath = async (id) => {
  let category = await Category
    .findOne({ id: +id })
    .exec();

  if (!category) return [];
  if (category.linkto) {
    category = getLinkedTo(category);
  }

  const upper = await Category
    .find({ id: { $in: category.ancestor } })
    .limit(1000)
    .exec();

  upper.push(category);

  let newUpper = [];
  try {
    upper.map((item) => {
      const upperItem = item.toObject();
      newUpper.push(upperItem);

      return item;
    });
  } catch (error) {
    console.log(`getRelativeVods helper ${error && error.message}`);
  }
  newUpper = fixCatId(newUpper);

  return newUpper;
};

const subIds = async (category) => {
  const childrenIdList = [];
  const childrens = await Category
    .find({ topath: subReg(category) }, { id: 1 })
    .exec();
  childrens.map(item => childrenIdList.push(item.id));

  return childrenIdList;
};

const getGoToAct = async (category) => {
  if (category.islistp > 0) return 2;

  const relativeRegex = new RegExp(`^${category.topath}$`);
  const nrChildren = new RegExp(`^${category.topath}\\d+,$`);

  const oneSon = await Category
    .findOne({ topath: relativeRegex })
    .exec();

  if (oneSon) {
    if (category.isepisode === 8) {
      const grandSon = await Category
        .findOne({ topath: nrChildren })
        .exec();

      return grandSon ? 2 : 1;
    }

    const parent = await Category
      .findOne({ id: category.parent_id })
      .exec();

    if (parent.isepisode === 8) return 1;
  }

  const vodmone = await Vod
    .findOne({ cateid: category.id })
    .exec();

  return vodmone ? 1 : 0;
};

const getPicture = async (category) => {
  if (category.linkto > 0 && !category.picture) {
    category = getLinkedTo(category);
  }
  if (category.picture && category.picture.length > 0) return `//images.peer5.net/images/b/${category.picture[0].big}`;

  const subCategory = await Category
    .findOne({ topath: subReg(category), picture: { $ne: [] }, ifonline: 1 }, { picture: 1 })
    .exec();

  if (subCategory && subCategory.picture) return `//images.peer5.net/images/b/${subCategory.picture[0].big}`;

  const ids = subIds(category);
  let vodMone;

  if (ids && ids.length > 0) {
    vodMone = await Vod
      .findOne({ cateid: { $in: ids }, picture: { $ne: [] }, ifonline: 1 }, { picture: 1 })
      .exec();

    if (vodMone) return `//images.peer5.net/images/b/${vodMone.picture[0].big}`;

    vodMone = await Vod
      .findOne({ cateid: { $in: ids }, vodlist: { $exists: true } }, { vodlist: 1 })
      .exec();
  }
  if (!vodMone) return 'noimage';

  const filename = vodMone.vodlist.replace(/\..*/, '.jpg');

  return `//images.peer5.net/thumbs/vod-temp/${filename}?second=300&width=384&height=216`;
};

module.exports = async (vodcate) => {
  let vodCategory = vodcate;

  if (typeof vodcate !== 'object') {
    vodCategory = await Category
      .findOne({ id: vodcate })
      .exec();
  }

  let relativeVodCat = await Category
    .find({ id: { $in: vodCategory.relative }, ifonline: 1 })
    .exec();

  const idRelativeList = [];
  relativeVodCat.map(item => idRelativeList.push(item.id));
  relativeVodCat = await Category
    .find({ parent_id: { $in: idRelativeList }, ifonline: 1 })
    .limit(10000)
    .exec();

  async function processArray(relativeVodCat) {
    const arrPromise = [];
    for (const value of relativeVodCat) {
      const promise = new Promise(async (res) => {
        const vodcate = value.toObject();
        const gotoact = await getGoToAct(value);
        const showpic = await getPicture(value);
        const path = `${value.topath.substring(2).replace(/,/g, '/')}${value.id}/`;
        const upper = await fullPath(value.id);
        res({ ...vodcate, gotoact, showpic, path, upper });
      });
      arrPromise.push(promise);
    }
    const vodcateList = await Promise.all(arrPromise);

    return vodcateList;
  }

  const result = await processArray(relativeVodCat);

  return result;
};
