const config = require('../../../config');
const Stealer = require('../../model/audb/Stealer');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const User = require('../../model/audb/User');
const getUserRelatedUsersIds = require('../../commands/payment/getUserRelatedUsersIds');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const { ignoredCardNumbersEncryptedList } = require('../../constants/ignoredCardNumbers');

const getUserFingerprintModels = async (userId) => {
  const userFingerprintModels = await UserFingerprintPayment.find({ uid: userId }).lean();

  return userFingerprintModels;
};

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

const getUserCards = async (userId) => {
  const userCardModels = await UserCard.find({ uid: userId }).lean();
  const cards = userCardModels.map(model => model.number);

  return cards;
};

const getUserIPs = async (userId) => {
  const userIPModels = await UserIp.find({ uid: userId }).lean();
  const userIPs = userIPModels.map(model => model.ip);

  return userIPs;
};

const filterUserIPsInStealerList = async (userIPs) => {
  if (!userIPs || !userIPs.length) return [];

  const stealerIPModels = await Stealer.find({ ip: { $exists: true } }).cache(config.stealerList.cache, 'stealer_ip').lean();
  const stealerIPs = stealerIPModels.map(model => model.ip);

  const userStealerIPs = userIPs.filter(ip => stealerIPs.includes(ip));

  return userStealerIPs;
};

const getUsersByFingerprints = async (fingerprints, adminsAndSupportsIDs) => {
  if (!fingerprints || !fingerprints.length) return [];

  const fingerprintModels = await UserFingerprintPayment.find({ fingerprint: { $in: fingerprints }, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = fingerprintModels.map(model => model.uid);

  return usersIds;
};

const getUsersByCards = async (cards, adminsAndSupportsIDs) => {
  if (!cards || !cards.length) return [];

  const cardModels = await UserCard.find({ number: { $in: cards }, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = cardModels.map(model => model.uid);

  return usersIds;
};

const getUsersByIPs = async (IPs, adminsAndSupportsIDs) => {
  if (!IPs || !IPs.length) return [];

  const userIPsModels = await UserIp.find({ ip: { $in: IPs }, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = userIPsModels.map(model => model.uid);

  return usersIds;
};

const searchRelatedUsers = async (userId, adminsAndSupportsIDs) => {
  const foundUsersSet = new Set();
  // get usersIds by stealer fingerprints, cards, IPs

  if (config.stealerList.fingerprints.enabled) {
    const [userFingerprintModels, whitelistedFingerprints, ignoredUseragents] = await Promise.all([
      getUserFingerprintModels(userId),
      getWhitelistedFingerprints(),
      getIgnoredUseragents()
    ]);

    const userStealerFingerprints = userFingerprintModels
      .filter(model => !whitelistedFingerprints.includes(model.fingerprint))
      .filter(model => !ignoredUseragents.includes(model.userAgent))
      .map(model => model.fingerprint);
    const stealerUsersIDsByFingerprints = await getUsersByFingerprints(userStealerFingerprints, adminsAndSupportsIDs);
    stealerUsersIDsByFingerprints.forEach(_userId => foundUsersSet.add(_userId));
  }

  const [userStealerCards, whitelistedCards] = await Promise.all([
    getUserCards(userId),
    getWhitelistCardNumbers(),
  ]);
  // do not filter userCardNumbers for local and dev tests
  let filteredCards = process.env.NODE_ENV === 'production' ? userStealerCards.filter(cardNumber => !ignoredCardNumbersEncryptedList.includes(cardNumber)) : userStealerCards;
  filteredCards = filteredCards.filter(cardNumber => !whitelistedCards.includes(cardNumber));
  const stealerUsersIDsByCards = await getUsersByCards(filteredCards, adminsAndSupportsIDs);
  stealerUsersIDsByCards.forEach(_userId => foundUsersSet.add(_userId));

  const userIPs = await getUserIPs(userId);
  const userStealerIPs = await filterUserIPsInStealerList(userIPs);
  const stealerUsersIDsByIPs = await getUsersByIPs(userStealerIPs, adminsAndSupportsIDs);
  stealerUsersIDsByIPs.forEach(_userId => foundUsersSet.add(_userId));

  const userRelatedUsersIDs = await getUserRelatedUsersIds(userId, adminsAndSupportsIDs);
  userRelatedUsersIDs.forEach(_userId => foundUsersSet.add(_userId));
  // load related users by registered keys
  try {
    const currentUser = await User.findOne({ id: userId }).lean();

    if (currentUser) {
      const users = await User.getRelatedUsersUsedTheSameDevice(currentUser);

      if (users && users.length) {
        users.forEach(user => foundUsersSet.add(user.id));
      }
    }
  } catch (e) {
    console.error(`Cannot get related users used the same device for the user ${userId}`, e);
  }

  const foundUsersIDs = Array.from(foundUsersSet);

  return foundUsersIDs;
};

const getRelatedUsersToCurrentUser = async (userId, allUsersIDs, adminsAndSupportsIDs) => {
  if (!adminsAndSupportsIDs || !adminsAndSupportsIDs.length) adminsAndSupportsIDs = await getAdminsAndSupportsIDs();

  const foundUsersIDs = await searchRelatedUsers(userId, adminsAndSupportsIDs);

  if (foundUsersIDs.length) {
    const filteredUsers = foundUsersIDs.filter(userId => !adminsAndSupportsIDs.includes(userId));

    const uniqueUsersIDs = [];
    filteredUsers.forEach((_userId) => {
      if (!allUsersIDs.includes(_userId)) {
        uniqueUsersIDs.push(_userId);
        allUsersIDs.push(_userId);
      }
    });

    if (uniqueUsersIDs.length) {
      for (let i = 0; i < uniqueUsersIDs.length; ++i) {
        const foundUsersByUser = await getRelatedUsersToCurrentUser(uniqueUsersIDs[i], allUsersIDs, adminsAndSupportsIDs);

        if (foundUsersByUser.length) {
          allUsersIDs.push(...foundUsersByUser);
          allUsersIDs = Array.from(new Set(allUsersIDs));
        }
      }
    }
  }

  return allUsersIDs;
};

/**
 *  @param {number} userId  - user ID
 * */
module.exports = async (userId) => {
  // initially put current user ID to the all users list
  const allUsersIDs = await getRelatedUsersToCurrentUser(userId, [userId]);

  return allUsersIDs || [userId];
};
