const moment = require('moment');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ignoredCardNumbersEncryptedList } = require('../../constants/ignoredCardNumbers');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

module.exports = async (userId, description = '') => {
  const now = moment().unix();
  const whitelistCardNumbers = await getWhitelistCardNumbers();
  // update card numbers not yet stealer for all users
  await UserCard.updateMany(
    { uid: userId,
      $or: [{ isStealer: { $exists: false } }, { isStealer: false }],
      number: { $nin: [...whitelistCardNumbers, ...ignoredCardNumbersEncryptedList] } },
    { $set: { isStealer: true, stealerDescription: description, updated: now } },
    { upsert: false },
  ).exec();
  // need to clean cache key for current user
  await removeRedisCacheByKeys([`userCard_uid_${userId}`]);
};
