const { ApiError } = require('@s1/api-errors');
const Stealer = require('../../model/audb/Stealer');
const StealerIpReview = require('../../model/audb/StealerIpReview');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserIp = require('../../model/audb/UserIp');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ipReviewStatuses } = require('../../constants/stealer');
const moveUserToDefaultPermissionGroup = require('./moveUserToDefaultPermissionGroup');

/**
 * Get all users IPS
 * */
const getAllUsersIPs = async (usersIds) => {
  const userIPModels = await UserIp.find({ uid: { $in: usersIds } }).lean();
  const usersIPs = userIPModels.map(model => model.ip);

  return usersIPs;
};

/**
 * Remove all ips from the stealer list and unblock from the IPs from review list.
 * Clean cache keys for stealer list ips and stealerIpReview.
 * */
const removeStealerIPs = async (ips) => {
  // remove all user IPs from the stealer list
  if (ips.length) {
    const dateNow = new Date();
    await Stealer.deleteMany({ ip: { $in: ips } }).exec();
    await StealerIpReview.deleteMany({ ip: { $in: ips }, status: ipReviewStatuses.new }).exec();
    // for removed stealer IPs we need to unlock them from the review list
    await StealerIpReview.updateMany(
      { ip: { $in: ips } },
      { $set: { status: ipReviewStatuses.unblock, createdForTtlIndex: dateNow } },
      { upsert: false },
    ).exec();

    const userIPsCacheKeys = [];
    ips.forEach((ip) => {
      userIPsCacheKeys.push(`stealer_ip_${ip}`);
      userIPsCacheKeys.push(`stealerIpReview_ip_${ip}`);
    });
    await removeRedisCacheByKeys(userIPsCacheKeys);
  }
};

/**
 * Add admin comment to the user on remove him from the stealer list
 * */
const addUserAdminCommentOnRemoveFromStealerList = async (userId, adminUser) => {
  const comment = `User removed from the stealer list by ${adminUser.name} (${adminUser.id})`;
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async ({ uids, adminUser }) => {
  if (!uids) throw new ApiError(903, 'uids are required');
  if (typeof uids === 'string' || typeof uids === 'number') uids= [uids];

  await Stealer.deleteMany({ uid: { $in: uids } }).exec();
  await UserFingerprintPayment.updateMany(
    { uid: { $in: uids }, isStealer: true },
    { $set: { isStealer: false, stealerDescription: '' } },
    { upsert: false },
  ).exec();
  await UserCard.updateMany(
    { uid: { $in: uids }, isStealer: true },
    { $set: { isStealer: false, stealerDescription: '' } },
    { upsert: false },
  ).exec();
  const allUsersIPs = await getAllUsersIPs(uids);
  await removeStealerIPs(allUsersIPs);

  for (let i = 0; i < uids.length; ++i) {
    await moveUserToDefaultPermissionGroup({ id: uids[i] });
    await addUserAdminCommentOnRemoveFromStealerList(uids[i], adminUser);
  }

  const redisKeys = ['stealer', 'userFingerprintPayment', 'userCard'];
  await removeRedisCacheByKeys(redisKeys);

  return {
    error: 0,
    success: true,
  };
};
