const { WrongParamsError, ApiError } = require('@s1/api-errors');
const StealerWhitelistCard = require('../../model/audb/StealerWhitelistCard');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.number) throw new ApiError(903, 'number is required');

  let stealerWhitelistCard = await StealerWhitelistCard.findOne({ number: data.number }).exec();

  if (!stealerWhitelistCard) {
    stealerWhitelistCard = new StealerWhitelistCard();
    stealerWhitelistCard.number = data.number;
    stealerWhitelistCard.first6 = data.first6;
    stealerWhitelistCard.last4 = data.last4;
    stealerWhitelistCard.type = data.type;
    stealerWhitelistCard.brand = data.brand;
    stealerWhitelistCard.country = data.country;
    stealerWhitelistCard.bank = data.bank;
    stealerWhitelistCard.addedByUid = admin.id;
    stealerWhitelistCard.addedByName = admin.name;

    await stealerWhitelistCard.save();
    await removeRedisCacheByKeys(['stealerWhitelistCard']);
  }

  await UserCard.updateMany({ number: data.number }, { $set: { isStealer: false, stealerDescription: '' } }, { upsert: false }).exec();
  await removeRedisCacheByKeys([`userCard_stealer_cardNumber_${data.number}`]);
  await removeRedisCacheByKeys([`userCard_uid_${data.number}`]);

  return {
    error: 0,
    stealerWhitelistCard,
  };
};
