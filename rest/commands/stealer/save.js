const { WrongParamsError, ApiError } = require('@s1/api-errors');
const Stealer = require('../../model/audb/Stealer');
const User = require('../../model/audb/User');
const { stealerTypes, stealerTypesList, creationMethods } = require('../../constants/stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const blockUsersByIp = require('./blockUsersByIp');
const addStealerUserAndRelatedUsers = require('../../commands/stealer/addStealerUserAndRelatedUsers');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const moveUserToStealerPermissionGroup = require('./moveUserToStealerPermissionGroup');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.type) throw new ApiError(903, 'Type is required');
  if (!stealerTypesList.includes(data.type)) throw new ApiError(903, 'Invalid stealer type');
  if (data.type === stealerTypes.ip) {
    const ipRegex = /\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){4}\b/;

    // eslint-disable-next-line no-prototype-builtins
    if (!data.hasOwnProperty('ip') || !data.ip.match(ipRegex)) throw new ApiError(903, 'Wrong IP address');
  }

  let stealerModel;

  if (data._id) {
    stealerModel = await Stealer.findOne({ _id: data._id }).exec();
    stealerModel.description = data.description || '';

    if (data.type === stealerTypes.uid) stealerModel.creationMethod = data.creationMethod || creationMethods.manual;
    if (admin && (!stealerModel.addedByUid || !stealerModel.addedByName)) {
      stealerModel.addedByUid = admin.id;
      stealerModel.addedByName = admin.name;
    }
  } else {
    stealerModel = new Stealer();
    stealerModel.creationMethod = creationMethods.manual;
    stealerModel.description = data.description;

    if (admin) {
      stealerModel.addedByUid = admin.id;
      stealerModel.addedByName = admin.name;
    }
  }

  switch (data.type) {
    case stealerTypes.ip:
      stealerModel.ip = data.ip;
      await removeRedisCacheByKeys([`stealer_ip_${data.ip}`]);
      break;
    case stealerTypes.uid:
      // eslint-disable-next-line no-case-declarations
      const userId = parseInt(data.uid);
      // eslint-disable-next-line no-case-declarations
      const currentUser = await User.findOne({ id: userId }).lean();

      // for admin or support do not need to check is stealer
      if (!currentUser || isAdminOrSupportUser(currentUser)) throw new ApiError(903, 'Cannot block admin or support user');

      stealerModel.uid = userId;
      await removeRedisCacheByKeys([`stealer_id_${userId}`]);
      break;
    default:
      if (admin) {
        stealerModel.modifiedByUid = admin.id;
        stealerModel.modifiedByName = admin.name;
      }

      for (const key of Object.keys(data)) {
        stealerModel[key] = data[key];
      }

      await removeRedisCacheByKeys(['stealer_billingDetails']);
      break;
  }

  await stealerModel.save();

  // add all user fingerprints to the stealer list
  if (data.type === stealerTypes.uid) {
    await moveUserToStealerPermissionGroup({ id: data.uid });
    await addStealerUserAndRelatedUsers(parseInt(data.uid), admin, data.description || '');
  } else if (data.type === stealerTypes.ip) {
    await blockUsersByIp(data.ip);
  }

  return {
    error: 0,
    stealerModel: { ...stealerModel.toObject(), type: data.type },
  };
};
