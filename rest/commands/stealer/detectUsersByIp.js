const moment = require('moment');
const log = require('@s1/log').create(__filename);
const Stealer = require('../../model/audb/Stealer');
const UserIp = require('../../model/audb/UserIp');
const { creationMethods } = require('../../constants/stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');

module.exports = async (ip) => {
  // find all users by IP
  const users = await UserIp.find({ ip }, { uid: 1 }).lean();

  if (!users.length) return;

  const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
  const usersIds = users.map(user => user.uid);
  const filteredUsersIds = usersIds.filter(userId => !adminsAndSupportsIDs.includes(userId));
  const stealerUidsModels = await Stealer.find({ uid: { $in: filteredUsersIds } }, { uid: 1 }).lean();
  const stealerUids = stealerUidsModels.map(user => user.uid);
  const uidsForInsert = [];
  const cacheKeysForRemove = [];
  const now = moment().unix();

  // insert only not stealer user ids
  filteredUsersIds.forEach((id) => {
    if (!stealerUids.includes(id)) {
      uidsForInsert.push({
        creationMethod: creationMethods.automatic,
        description: `Automatic detected by added IP: ${ip}`,
        uid: id,
        created: now,
        updated: now,
      });

      cacheKeysForRemove.push(`stealer_id_${id}`);
    }
  });

  if (uidsForInsert.length) {
    try {
      await Stealer.insertMany(uidsForInsert, { ordered: false });
    } catch (e) {
      // some users might be already added and we can get an error
      log.info(`Cannot add new stealer users, error: ${e.stack || e.message}`);
    }
    await removeRedisCacheByKeys(cacheKeysForRemove);
  }
};
