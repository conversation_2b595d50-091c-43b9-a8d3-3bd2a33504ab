const { WrongParamsError, ApiError } = require('@s1/api-errors');
const Stealer = require('../../model/audb/Stealer');
const StealerIpReview = require('../../model/audb/StealerIpReview');
const { ipReviewStatuses, ipReviewStatusesList, creationMethods, stealerTypes } = require('../../constants/stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const blockUsersByIp = require('./blockUsersByIp');

module.exports = async (data, user) => {
  if (!data || !data.id || !data.status) throw new WrongParamsError();
  if (!ipReviewStatusesList.includes(data.status)) throw new ApiError(903, 'Invalid status');

  const stealerIpReview = await StealerIpReview.findOne({ _id: data.id }).exec();

  if (!stealerIpReview) throw new ApiError(903, 'Stealer IP for review not found');
  if (user) {
    stealerIpReview.modifiedByUid = user.id;
    stealerIpReview.modifiedByName = user.name;
  }

  switch (data.status) {
    case ipReviewStatuses.block:
    case ipReviewStatuses.ignore:
      // do not remove ignored and blocked IPs for review
      if (stealerIpReview.createdForTtlIndex) delete stealerIpReview.createdForTtlIndex;

      break;
    default:
      stealerIpReview.createdForTtlIndex = new Date();
      break;
  }

  stealerIpReview.status = data.status;
  await stealerIpReview.save();

  let stealer;

  switch (data.status) {
    case ipReviewStatuses.block:
      stealer = new Stealer();
      stealer.ip = stealerIpReview.ip;
      stealer.description = stealerIpReview.description;
      stealer.creationMethod = creationMethods.automatic;

      if (user) {
        stealer.addedByUid = user.id;
        stealer.addedByName = user.name;
      }

      try {
        await removeRedisCacheByKeys(`stealer_ip_${stealerIpReview.ip}`);
        await stealer.save();
        await blockUsersByIp(stealerIpReview.ip);
      } catch (e) {
        // already blocked by other rules (from manual mode)
        // need to clear value, not need to update on admin UI
        stealer = null;
      }
      break;
    case ipReviewStatuses.unblock:
      stealer = await Stealer.findOne({ ip: stealerIpReview.ip }).exec();

      if (stealer) {
        await removeRedisCacheByKeys([`stealer_ip_${stealerIpReview.ip}`]);
        await stealer.remove();
      }

      break;
    default:
      break;
  }

  return {
    error: 0,
    stealerIpReview: { ...stealerIpReview.toObject() },
    stealer: stealer ? { ...stealer.toObject(), type: stealerTypes.ip } : null,
  };
};
