const { WrongParamsError, ApiError } = require('@s1/api-errors');
const StealerWhitelistCard = require('../../model/audb/StealerWhitelistCard');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const md5 = require('../../helpers/md5');
const { encrypt } = require('../../helpers/security');
const getBinFieldValue = require('../../helpers/getBinFieldValue');
const config = require('../../../config');
const Binlist = require('../../model/audb/Binlist');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.number) throw new ApiError(903, 'number is required');

  const parsedNumber = data.number.replace(/\s/g, '');

  const encryptedCardNumber = md5(encrypt(parsedNumber.toString(), `${parsedNumber}${config.cardDataSecuritySalt}`));

  let stealerWhitelistCard = await StealerWhitelistCard.findOne({ number: data.number }).exec();

  if (!stealerWhitelistCard) {
    const first6DigBin = parsedNumber.substring(0, 6);
    const binInfo = await Binlist.findOne({ bin: first6DigBin }).cache(600).exec() || {};

    stealerWhitelistCard = new StealerWhitelistCard();
    stealerWhitelistCard.number = encryptedCardNumber;
    stealerWhitelistCard.first6 = first6DigBin;
    stealerWhitelistCard.last4 = parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length);
    stealerWhitelistCard.type =  getBinFieldValue(binInfo.card_type);
    stealerWhitelistCard.brand = getBinFieldValue(binInfo.card_brand);
    stealerWhitelistCard.country = getBinFieldValue(binInfo.card_issuing_country);
    stealerWhitelistCard.bank = getBinFieldValue(binInfo.card_issuing_bank);
    stealerWhitelistCard.addedByUid = admin.id;
    stealerWhitelistCard.addedByName = admin.name;

    await stealerWhitelistCard.save();
    await removeRedisCacheByKeys(['stealerWhitelistCard']);
  }

  await UserCard.updateMany({ number: data.number }, { $set: { isStealer: false } }, { upsert: false }).exec();
  await removeRedisCacheByKeys([`userCard_stealer_cardNumber_${data.number}`]);
  await removeRedisCacheByKeys([`userCard_uid_${data.number}`]);

  return {
    error: 0,
    stealerWhitelistCard,
  };
};
