const getRelatedUsersIDsToCurrentStealerUser = require('./getRelatedUsersIDsToCurrentStealerUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const UserIp = require('../../model/audb/UserIp');
const StealerIpReview = require('../../model/audb/StealerIpReview');
const Stealer = require('../../model/audb/Stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ipReviewStatuses } = require('../../constants/stealer');
const moveUserToDefaultPermissionGroup = require('./moveUserToDefaultPermissionGroup');

/**
 * Remove all user fingerprints from the stealer list.
 * Clean cache keys for userFingerprintPayment_id and userFingerprintPayment_stealer_fingerprint.
 * */
const removeUserStealerFingerprints = async (userId) => {
  // remove all user fingerprints from the stealer list
  const userFingerprintsCacheKeys = [];
  const userFingerprintsUpdate = await UserFingerprintPayment.find({ uid: userId, isStealer: true }, { fingerprint: 1 }).lean();
  const userStealerFingerprints = [];
  userFingerprintsUpdate.forEach((model) => {
    userFingerprintsCacheKeys.push(`userFingerprintPayment_stealer_fingerprint_${model.fingerprint}`);
    userStealerFingerprints.push(model.fingerprint);
  });

  if (userStealerFingerprints.length) {
    // update fingerprints for all users
    await UserFingerprintPayment.updateMany(
      { fingerprint: { $in: userStealerFingerprints } },
      { $set: { isStealer: false, stealerDescription: '' } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userFingerprintPayment_id_${userId}`, ...userFingerprintsCacheKeys]);
  }
};

/**
 * Remove all user cards from the stealer list.
 * Clean cache keys for userCard_uid and userCard_stealer_cardNumber.
 * */
const removeUserStealerCards = async (userId) => {
  // remove all user card numbers from the stealer list
  const userCardsCacheKeys = [];
  const userCardsForUpdate = await UserCard.find({ uid: userId, isStealer: true }, { number: 1 }).lean();
  const userStealerCards = [];
  userCardsForUpdate.forEach((model) => {
    userCardsCacheKeys.push(`userCard_stealer_cardNumber_${model.number}`);
    userStealerCards.push(model.number);
  });

  if (userStealerCards.length) {
    // update cards for all users
    await UserCard.updateMany(
      { number: { $in: userStealerCards } },
      { $set: { isStealer: false, stealerDescription: '' } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Get all users IPS
 * */
const getAllUsersIPs = async (usersIds) => {
  const userIPModels = await UserIp.find({ uid: { $in: usersIds } }).lean();
  const usersIPs = userIPModels.map(model => model.ip);

  return usersIPs;
};

/**
 * Remove all ips from the stealer list and unblock from the IPs from review list.
 * Clean cache keys for stealer ips and stealerIpReview.
 * */
const removeStealerIPs = async (ips) => {
  // remove all user card numbers from the stealer list
  const userIPsCacheKeys = [];
  ips.forEach((ip) => {
    userIPsCacheKeys.push(`stealer_ip_${ip}`);
    userIPsCacheKeys.push(`stealerIpReview_ip_${ip}`);
  });

  if (ips.length) {
    const dateNow = new Date();
    await Stealer.deleteMany({ ip: { $in: ips } }).exec();
    // for removed stealer IPs we need to unlock them from the review list
    await StealerIpReview.updateMany(
      { ip: { $in: ips } },
      { $set: { status: ipReviewStatuses.unblock, createdForTtlIndex: dateNow } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys(userIPsCacheKeys);
  }
};

/**
 * Add admin comment to the user on remove him from the stealer list
 * */
const addUserAdminCommentOnRemoveFromStealerList = async (userId, adminUser) => {
  const comment = `User removed from the stealer list by ${adminUser.name} (${adminUser.id})`;
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async (currentUserId, adminUser) => {
  const usersIds = await getRelatedUsersIDsToCurrentStealerUser(currentUserId);
  const redisKeys = [`stealer_id_${currentUserId}`];

  for (let i = 0; i < usersIds.length; ++i) {
    const userId = usersIds[i];
    await removeUserStealerFingerprints(userId);
    await removeUserStealerCards(userId);
    await moveUserToDefaultPermissionGroup({ id: userId });
    await addUserAdminCommentOnRemoveFromStealerList(userId, adminUser);
    redisKeys.push(`stealer_id_${userId}`);
  }

  await removeRedisCacheByKeys(redisKeys);
  const allUsersIPs = await getAllUsersIPs(usersIds);
  await removeStealerIPs(allUsersIPs);
  await Stealer.deleteMany({ uid: { $in: usersIds } }).exec();
};
