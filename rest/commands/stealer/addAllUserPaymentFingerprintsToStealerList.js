const moment = require('moment');
const log = require('@s1/log').create(__filename);
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const Stealer = require('../../model/audb/Stealer');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const config = require('../../../config');
const User = require('../../model/audb/User');
const { creationMethods } = require('../../constants/stealer');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const moveUserToStealerPermissionGroup = require('./moveUserToStealerPermissionGroup');

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

/**
 * Method update not yet added stealer fingerprints for all users depending on the fingerprints list
 * */
module.exports = async (fingerprints, description = '') => {
  if (!config.stealerList.fingerprints.enabled) return;

  const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();

  const userFingerprintsModels = await UserFingerprintPayment.find(
    { fingerprint: { $in: fingerprints } },
    { uid: 1, fingerprint: 1, userAgent: 1 },
  ).lean();

  // filter ignored and whitelisted fingerprints
  const whitelistedFingerprints = await getWhitelistedFingerprints();
  const ignoredUseragents = await getIgnoredUseragents();
  const filteredFingerprints = userFingerprintsModels
    .filter(model => !whitelistedFingerprints.includes(model.fingerprint))
    .filter(model => !ignoredUseragents.includes(model.userAgent))
    .map(model => model.fingerprint);

  // search fingerprints not yet stealer for all users, exclude admins adn supports
  const notYetStealerUserFingerprintsUIDsModels = await UserFingerprintPayment.find(
    { fingerprint: { $in: filteredFingerprints }, $or: [{ isStealer: { $exists: false } }, { isStealer: false }], uid: { $nin: adminsAndSupportsIDs } },
    { uid: 1, fingerprint: 1, userAgent: 1 },
  ).lean();

  // search for fingerprints will be added to the stealer list to block their users too
  const notYetStealerUserFingerprintsUIDs = notYetStealerUserFingerprintsUIDsModels.map(model => model.uid);
  const usersUIDs = Array.from(new Set(notYetStealerUserFingerprintsUIDs));
  const now = moment().unix();
  // update fingerprints not yet stealer for all users, exclude admins adn supports
  await UserFingerprintPayment.updateMany(
    { fingerprint: { $in: filteredFingerprints }, $or: [{ isStealer: { $exists: false } }, { isStealer: false }], uid: { $nin: adminsAndSupportsIDs } },
    { $set: { isStealer: true, stealerDescription: description, updated: now } },
    { upsert: false },
  ).exec();
  // need to clean cache keys for blocked users only
  const userFingerprintPaymentKeys = [];
  // cache keys by each user
  usersUIDs.forEach(uid => userFingerprintPaymentKeys.push(`userFingerprintPayment_id_${uid}`));
  await removeRedisCacheByKeys(userFingerprintPaymentKeys);

  // add all users IDs for stealer fingerprints to the stealer list
  if (notYetStealerUserFingerprintsUIDsModels.length) {
    const stealerUids = [];

    for (let i = 0; i < usersUIDs.length; ++i) {
      const stealerUidModel = await Stealer.findOne({ uid: usersUIDs[i] })
        .lean().cache(config.stealerList.cache, `stealer_id_${usersUIDs[i]}`);

      if (stealerUidModel) stealerUids.push(stealerUidModel.uid);
    }

    const uidsForInsert = [];
    const cacheKeysForRemove = [];
    // insert only not exists stealer user ids
    for (let i = 0; i < notYetStealerUserFingerprintsUIDsModels.length; ++i) {
      const model = notYetStealerUserFingerprintsUIDsModels[i];

      if (!stealerUids.includes(model.uid)) {
        moveUserToStealerPermissionGroup({ id: model.uid });
        // create fingerprintsLabel like: iaepBRayYK3CcFtMT8O3 (userId-1 email-1, userId-2 email-2, userId-3 email-3)
        let fingerprintsLabel = model.fingerprint;
        // eslint-disable-next-line max-len
        const fingerprintUsersModels = await UserFingerprintPayment.aggregate([
          {
            $match: { isStealer: true, fingerprint: model.fingerprint },
          },
          {
            $lookup: {
              from: 'tuser',
              localField: 'uid',
              foreignField: 'id',
              as: 'User',
            },
          },
          { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
          {
            $project: { uid: 1, em: '$User.em' },
          },
        ]);

        if (fingerprintUsersModels.length) {
          fingerprintsLabel += ' (';

          for (let f = 0; f < fingerprintUsersModels.length; ++f) {
            if (f !== 0) fingerprintsLabel += ', ';

            fingerprintsLabel += fingerprintUsersModels[f].uid;
            const email = fingerprintUsersModels[f].em ? await User.decryptEmailWithRedis(fingerprintUsersModels[f].em) : '';
            fingerprintsLabel += email ? ` ${email}` : '';
          }

          fingerprintsLabel += ')';
        }

        uidsForInsert.push({
          uid: model.uid,
          description: `Detected by fingerprint: ${fingerprintsLabel} ${description ? ` <-- ${description}` : ''}`,
          creationMethod: creationMethods.automatic,
          created: now,
          updated: now,
        });

        cacheKeysForRemove.push(`stealer_id_${model.uid}`);
      }
    }

    if (uidsForInsert.length) {
      try {
        await Stealer.insertMany(uidsForInsert, { ordered: false });
      } catch (e) {
        // some users might be already added and we can get an error
        log.info(`Cannot add new stealer users, error: ${e.stack || e.message}`);
      }
      await removeRedisCacheByKeys(cacheKeysForRemove);
    }
  }
};
