const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const getRelatedUsersIDsToCurrentStealerUser = require('./getRelatedUsersIDsToCurrentStealerUser');
const isStealerUser = require('../../commands/payment/helpers/rules/isStealerUser');

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentStealerUser(userId);
  const users = await User.find({ id: { $in: usersIds } }).lean();
  const stealerUsersByBillingAddress = [];

  for (let i = 0; i < users.length; ++i) {
    const user = users[i];
    const [firstname, lastname] = user.name.split(' ');
    const userDataName = { firstname, lastname };
    const hasStealerName = await isStealerUser({ user: userDataName });

    if (hasStealerName) {
      stealerUsersByBillingAddress.push({
        id: user.id,
        name: user.name,
        email: await User.decryptEmailWithRedis(user.em),
        detectedByRule: `Stealer user name: ${user.name}`,
      });
      continue;
    }
    if (user.hasOwnProperty('billingAddresses') && user.billingAddresses) {
      const userDataBillingAddress = { billingAddresses: user.billingAddresses };
      const hasStealerBillingAddress = await isStealerUser({ user: userDataBillingAddress });

      if (hasStealerBillingAddress) {
        stealerUsersByBillingAddress.push({
          id: user.id,
          name: user.name,
          email: await User.decryptEmailWithRedis(user.em),
          detectedByRule: `Stealer user billingAddress: ${hasStealerBillingAddress.data}, rule: ${hasStealerBillingAddress.rule}`,
        });
      }
    }
  }

  return {
    error: 0,
    stealerUsersByBillingAddress,
  };
};
