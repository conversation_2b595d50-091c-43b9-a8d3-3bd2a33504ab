const User = require('../../model/audb/User');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');

module.exports = async () => {
  const userFingerprintsPayment = await UserFingerprintPayment.aggregate([
    {
      $match: { isStealer: true },
    },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $group: {
        _id: '$fingerprint',
        fingerprint: { $first: '$fingerprint' },
        stealerDescription: { $first: '$stealerDescription' },
        updated: { $first: '$updated' },
        userAgent: { $first: '$userAgent' },
        users: { $push: { uid: '$uid', em: '$User.em' } },
      },
    },
    {
      $project: { _id: 0, fingerprint: 1, stealerDescription: 1, updated: 1, users: 1, userAgent: 1 },
    },
    {
      $sort: { updated: -1 },
    },
  ])
    .then(fingerprints => Promise.all(fingerprints.map(async (fingerprint) => {
    // if some dependencies not found users will include empty objects
      fingerprint.users = fingerprint.users.filter(user => user.hasOwnProperty('uid'));
      fingerprint.users = await Promise.all(fingerprint.users.map(async (user) => {
        user.email = user.em ? await User.decryptEmailWithRedis(user.em) : '';
        user.uidEmail = user.uid + (user.email ? `(${user.email})` : '');
        delete user.em;

        return user.uidEmail;
      }));
      fingerprint.users = fingerprint.users.join(', ');

      return fingerprint;
    })));

  return {
    error: 0,
    userFingerprintsPayment,
  };
};
