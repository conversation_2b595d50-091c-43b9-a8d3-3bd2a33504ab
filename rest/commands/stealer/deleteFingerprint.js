const { ApiError } = require('@s1/api-errors');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ fingerprint }) => {
  if (!fingerprint) throw new ApiError(903, 'fingerprint is required');

  const blacklistedFingerprints = await UserFingerprintPayment.find({ fingerprint, isBlacklisted: true }, { uid: 1 })
    .lean();

  if (blacklistedFingerprints.length) {
    const removeResult = await UserFingerprintPayment.updateMany(
      { fingerprint, isBlacklisted: true },
      { $set: { isBlacklisted: false }, $unset: { blacklistDescription: 1 } },
      { upsert: false },
    ).exec();

    if (removeResult) {
      // on success remove all cache for blacklisted fingerprints
      const cacheKeyForRemove = [];
      blacklistedFingerprints.forEach(fingerprint => cacheKeyForRemove.push(`userFingerprintPayment_id_${fingerprint.uid}`));
      cacheKeyForRemove.push(`userFingerprintPayment_blacklist_fingerprint_${fingerprint}`);
      await removeRedisCacheByKeys(cacheKeyForRemove);
    }

    return {
      error: 0,
      success: !!removeResult,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
