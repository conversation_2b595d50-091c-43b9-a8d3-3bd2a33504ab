const { ApiError, WrongParamsError } = require('@s1/api-errors');
const mongoose = require('mongoose');
const StealerGeneralConfig = require('../../model/audb/StealerGeneralConfig');
const User = require('../../model/audb/User');

/**
 * @param {Object} user
 * @param {string} permissionGroupId
 * */
module.exports = async (user, permissionGroupId = null) => {
  if (!user) throw new WrongParamsError();

  try {
    if (!permissionGroupId) {
      const stealerGeneralConfig = await StealerGeneralConfig.findOne({}).exec();

      if (!stealerGeneralConfig) throw new ApiError('Stealer General Config is not configured');
      if (!stealerGeneralConfig.permissionGroup) throw new ApiError('Stealer permission group is not configured');

      permissionGroupId = stealerGeneralConfig.permissionGroup.toString();
    }
    if (!(user instanceof mongoose.Document)) {
      user = await User.findOne({ id: user.id }).exec();
    }

    user.permissionGroups = [permissionGroupId];
    await user.save();
  } catch (e) {
    console.log('Cannot merge users to the group, error', e);
  }
};
