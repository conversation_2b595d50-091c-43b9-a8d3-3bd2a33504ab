const { ApiError } = require('@s1/api-errors');
const config = require('../../../config');
const getRelatedUsersToCurrentStealerUser = require('./getRelatedUsersToCurrentStealerUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserCard = require('../../model/audb/UserCard');
const User = require('../../model/audb/User');
const Stealer = require('../../model/audb/Stealer');
const PaymentWhitelistFingerprint = require('../../model/audb/PaymentWhitelistFingerprint');
const StealerWhitelistCard = require('../../model/audb/StealerWhitelistCard');
const PaymentIgnoreFingerprintUseragent = require('../../model/audb/PaymentIgnoreFingerprintUseragent');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAndAddAllUserIpsToStealerList = require('../../commands/stealer/getAndAddAllUserIpsToStealerList');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const { creationMethods } = require('../../constants/stealer');
const { ignoredCardNumbersEncryptedList } = require('../../constants/ignoredCardNumbers');
const moveUserToStealerPermissionGroup = require('./moveUserToStealerPermissionGroup');
const StealerGeneralConfig = require('../../model/audb/StealerGeneralConfig');

/**
 * Add user to the stealer list
 * Clean cache keys for stealer_id_${userId}
 * */
const addUserToStealerList = async (userId, description = '') => {
  try {
    let stealer = await Stealer.findOne({ uid: userId })
      .lean().cache(config.stealerList.cache, `stealer_id_${userId}`);

    if (stealer) return;

    stealer = new Stealer();
    stealer.creationMethod = creationMethods.automatic;
    stealer.description = description;
    stealer.uid = userId;
    await stealer.save();
    await removeRedisCacheByKeys([`stealer_id_${userId}`]);
  } catch (e) {
    console.error(`Cannot add user# ${userId} to the payment stealer list`, e);
  }
};

const getWhitelistedFingerprints = async () => {
  const whitelistedFingerprintModels = await PaymentWhitelistFingerprint.find().lean();
  const fingerprints = whitelistedFingerprintModels.map(model => model.fingerprint);

  return fingerprints;
};

const getWhitelistedCards = async () => {
  const whitelistedCardModels = await StealerWhitelistCard.find().lean();
  const cards = whitelistedCardModels.map(model => model.number);

  return cards;
};

const getIgnoredUseragents = async () => {
  const userAgentsModels = await PaymentIgnoreFingerprintUseragent.find().lean();
  const userAgents = userAgentsModels.map(model => model.userAgent);

  return userAgents;
};

/**
 * Add all user fingerprints from the stealer list.
 * Clean cache keys for userFingerprintPayment_id and userFingerprintPayment_stealer_fingerprint.
 * */
const addUserFingerprintsToStealerList = async (userId, description = '') => {
  if (!config.stealerList.fingerprints.enabled) return;

  const userFingerprintsCacheKeys = [];
  const userFingerprintsUpdate = await UserFingerprintPayment.find(
    { uid: userId, $or: [{ isStealer: { $exists: false } }, { isStealer: false }] },
    { fingerprint: 1, userAgent: 1 },
  ).lean();

  // filter ignored and whitelisted fingerprints
  const whitelistedFingerprints = await getWhitelistedFingerprints();
  const ignoredUseragents = await getIgnoredUseragents();
  const filteredUserFingerprintsUpdate = userFingerprintsUpdate
    .filter(model => !whitelistedFingerprints.includes(model.fingerprint))
    .filter(model => !ignoredUseragents.includes(model.userAgent));

  const userFingerprints = [];
  filteredUserFingerprintsUpdate.forEach((model) => {
    userFingerprintsCacheKeys.push(`userFingerprintPayment_stealer_fingerprint_${model.fingerprint}`);
    userFingerprints.push(model.fingerprint);
  });

  if (userFingerprints.length) {
    const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
    // update fingerprints for all users
    await UserFingerprintPayment.updateMany(
      { fingerprint: { $in: userFingerprints }, uid: { $nin: adminsAndSupportsIDs } },
      { $set: { isStealer: true, stealerDescription: description } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userFingerprintPayment_id_${userId}`, ...userFingerprintsCacheKeys]);
  }
};

/**
 * Add all user cards to the stealer list.
 * Clean cache keys for userCard_uid and userCard_stealer_cardNumber.
 * */
const addUserCardsToStealerList = async (userId, description) => {
  const userCardsCacheKeys = [];
  // eslint-disable-next-line max-len
  const userCardsForUpdate = await UserCard.find({ uid: userId, $or: [{ isStealer: { $exists: false } }, { isStealer: false }] }, { number: 1 }).lean();
  let filteredCards = process.env.NODE_ENV === 'production' ? userCardsForUpdate.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCardsForUpdate;
  const whitelistedCards = await getWhitelistedCards();
  filteredCards = filteredCards.filter(card => !whitelistedCards.includes(card.number));

  const userCards = [];
  filteredCards.forEach((model) => {
    userCardsCacheKeys.push(`userCard_stealer_cardNumber_${model.number}`);
    userCards.push(model.number);
  });

  if (userCards.length) {
    await UserCard.updateMany(
      { uid: userId, number: { $in: userCards } },
      { $set: { isStealer: true, stealerDescription: description } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Add admin comment to the user on add him to the stealer list
 * */
const addUserAdminCommentOnAddToStealerList = async (userId, comment) => {
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async (currentUserId, adminUser, description = '') => {
  const currentUser = await User.findOne({ id: currentUserId }).lean();

  // for admin or support do not need to check is stealer
  if (!currentUser || isAdminOrSupportUser(currentUser)) return;

  const currentUserEmail = await User.decryptEmailWithRedis(currentUser.em);
  const reason = description ? ` by reason: ${description}` : '';
  const mainUserDescription = `User ${currentUser.id} ${currentUserEmail} manually added to the stealer list by ${adminUser.name} (${adminUser.id})${reason}`;
  const users = await getRelatedUsersToCurrentStealerUser(currentUser, mainUserDescription);
  const stealerGeneralConfig = await StealerGeneralConfig.findOne({}).exec();

  if (!stealerGeneralConfig) throw new ApiError('Stealer General Config is not configured');
  if (!stealerGeneralConfig.permissionGroup) throw new ApiError('Stealer permission group is not configured');

  const permissionGroupId = stealerGeneralConfig.permissionGroup.toString();

  for (let i = 0; i < users.length; ++i) {
    const user = users[i].user;
    const userDescription = users[i].description;

    if (!user || isAdminOrSupportUser(user)) continue;
    if (currentUserId !== user.id) {
      await addUserToStealerList(user.id, userDescription);
      await moveUserToStealerPermissionGroup(user, permissionGroupId)
      await addUserFingerprintsToStealerList(user.id, userDescription);
      await addUserCardsToStealerList(user.id, userDescription);
      await addUserAdminCommentOnAddToStealerList(user.id, userDescription);
      await getAndAddAllUserIpsToStealerList(user, userDescription);
    }
  }

  await addUserFingerprintsToStealerList(currentUserId, mainUserDescription);
  await addUserCardsToStealerList(currentUserId, mainUserDescription);
  await addUserAdminCommentOnAddToStealerList(currentUserId, mainUserDescription);
  await getAndAddAllUserIpsToStealerList(currentUser, mainUserDescription);
};
