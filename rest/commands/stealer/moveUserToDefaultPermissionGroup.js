const { WrongParamsError } = require('@s1/api-errors');
const mongoose = require('mongoose');
const User = require('../../model/audb/User');
const getAllUserPermissionGroups = require('../permission/getAllUserPermissionGroups');

/**
 * @param {Object} user
 * */
module.exports = async (user) => {
  if (!user) throw new WrongParamsError();

  const permissionGroupsResult = await getAllUserPermissionGroups();
  const permissionGroups = permissionGroupsResult.list || [];
  const defaultPermissionGroup = permissionGroups.find(group => group.isUserDefaultGroup) || permissionGroups[0] || null;

  if (defaultPermissionGroup) {
    if (!(user instanceof mongoose.Document)) {
      user = await User.findOne({ id: user.id }).exec();
    }

    user.permissionGroups = [defaultPermissionGroup._id];
    await user.save();
  }
};
