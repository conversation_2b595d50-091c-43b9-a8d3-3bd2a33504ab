const { ApiError } = require('@s1/api-errors');
const StealerWhitelistCard = require('../../model/audb/StealerWhitelistCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (number) => {
  if (!number) throw new ApiError(903, 'number is required');

  const stealerWhitelistCard = await StealerWhitelistCard.findOne({ number }).lean();

  if (stealerWhitelistCard) {
    await StealerWhitelistCard.deleteOne({ number }).exec();
    await removeRedisCacheByKeys(['stealerWhitelistCard']);

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
