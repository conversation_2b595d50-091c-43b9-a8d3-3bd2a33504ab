const { ApiError } = require('@s1/api-errors');
const _ = require('lodash');
const User = require('../../model/audb/User');
const UserIp = require('../../model/audb/UserIp');
const Stealer = require('../../model/audb/Stealer');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');
const { stealerTypesList, stealerTypes } = require('../../constants/stealer');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const getUserWatchingScore = require('../history/getUserWatchingScore');
const { getUserLocation } = require('../../service/maxmind');

const getStealerPs = async () => {
  const aggregation = [
    { $match: { ip: { $exists: true } } },
    {
      $lookup: {
        from: 'userIp',
        localField: 'ip',
        foreignField: 'ip',
        as: 'UserIp',
      },
    },
    { $unwind: { path: '$UserIp', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'UserIp.uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$ip',
        ip: { $first: '$ip' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$modifiedByName' },
        addedByUid: { $first: '$modifiedByUid' },
        users: { $push: { uid: '$User.id', em: '$User.em' } },
      },
    },
    {
      $project: { _id: '$id', ip: 1, creationMethod: 1, description: 1, updated: 1, created: 1, modifiedByName: 1, modifiedByUid: 1, users: 1, type: stealerTypes.ip },
    },
    { $sort: { created: -1 } },
  ];

  const stealerIPs = await Stealer.aggregate(aggregation)
    .then(stealerIPs => Promise.all(stealerIPs.map(async (stealerIP) => {
      // if some dependencies not found users will include empty objects
      stealerIP.users = stealerIP.users.filter(user => user.hasOwnProperty('uid'));
      stealerIP.users = await Promise.all(stealerIP.users.map(async (user) => {
        user.email = user.em ? await User.decryptEmailWithRedis(user.em) : '';
        user.uidEmail = user.uid + (user.email ? `(${user.email})` : '');
        delete user.em;

        return user.uidEmail;
      }));
      stealerIP.users = stealerIP.users.join(', ');

      return stealerIP;
    })));

  return stealerIPs;
};

const getStealerUsers = async () => {
  const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });

  const aggregation = [
    { $match: { uid: { $exists: true } } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$uid',
        uid: { $first: '$uid' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$addedByName' },
        addedByUid: { $first: '$addedByUid' },
        em: { $first: '$User.em' },
        regtime: { $first: '$User.regtime' },
        expires: { $first: '$User.expires' },
        packageId: { $first: '$User.package' },
        permissionGroups: { $first: '$User.permissionGroups' },
      },
    },
    {
      $project: { _id: '$id', uid: 1, creationMethod: 1, description: 1, updated: 1, created: 1, addedByName: 1, addedByUid: 1, em: 1, regtime: 1, expires: 1, packageId: 1, type: stealerTypes.uid, permissionGroups: 1 },
    },
    { $sort: { created: -1 } },
  ];

  const permissionGroups = await UserPermissionGroup.find({}, { _id: 1, name: 1 }).lean();
  const permissionGroupsObject = {};
  let defaultPermissionGroup;
  permissionGroups.forEach((group) => {
    permissionGroupsObject[group._id] = group;

    if (group.isUserDefaultGroup) defaultPermissionGroup = group;
  });

  const stealerUsers = await Stealer.aggregate(aggregation)
    .then(stealerUsers => Promise.all(stealerUsers.map(async (stealerUser) => {
      stealerUser.email = stealerUser.em ? await User.decryptEmailWithRedis(stealerUser.em) : '';
      stealerUser.paidTypes = await getUserPaidTypes(stealerUser.uid);
      stealerUser.watchingScore = await getUserWatchingScore(stealerUser.uid);
      stealerUser.package = packagesObject.hasOwnProperty(stealerUser.packageId) ? packagesObject[stealerUser.packageId] : null;
      stealerUser.lastPayments = await PaymentLog.find({
        uid: stealerUser.uid,
        amount: { $gt: 0 },
        pptype: { $ne: 'manual' },
      }, { pptype: 1, package: 1 }).sort({ _id: -1 }).limit(3).lean()
        .cache(300)
        .then(logs => (_.map(logs, (log) => {
          if (packagesObject.hasOwnProperty(log.package)) {
            const { id, ...pack } = packagesObject[log.package];
            log = Object.assign(log, pack);
          }

          delete log._id;

          return log;
        })));
      stealerUser.UserPermissionGroups = stealerUser.permissionGroups && stealerUser.permissionGroups.length
        ? stealerUser.permissionGroups.map(groupId => permissionGroupsObject[groupId])
        : [defaultPermissionGroup];
      const userIPModel = await UserIp.findOne({ uid: stealerUser.uid }).lean();

      if (userIPModel) {
        const { countryName, countryCode, stateCode, ISP } = getUserLocation(userIPModel.ip);
        stealerUser.ipInfo = { ip: userIPModel.ip, countryName, countryCode, stateCode, ISP };
      } else {
        stealerUser.ipInfo = null;
      }

      return stealerUser;
    })));

  return stealerUsers;
};

const getStealerBillingDetails = async (virtualType) => {
  const stealerBillingDetails = await Stealer.find({ ip: { $exists: false }, uid: { $exists: false } }).lean(virtualType);

  return stealerBillingDetails;
};

module.exports = async (type) => {
  let stealersList = [];
  const virtualType = { virtuals: ['type'] };

  if (!type) {
    stealersList = await Stealer.find().sort({ _id: -1 }).populate('User').lean(virtualType)
      .then(items => Promise.all(items.map(async (item) => {
        item.email = item.User ? await User.decryptEmailWithRedis(item.User.em) : '';
        delete item.User;

        return item;
      })));
  } else if (!stealerTypesList.includes(type)) {
    return new ApiError(903, 'Invalid stealer type');
  } else {
    switch (type) {
      case stealerTypes.ip:
        stealersList = await getStealerPs();
        break;
      case stealerTypes.uid:
        stealersList = await getStealerUsers();
        break;
      case stealerTypes.billingDetails:
        stealersList = await getStealerBillingDetails(virtualType);
        break;
      default:
        break;
    }
  }

  return {
    error: 0,
    stealersList,
  };
};
