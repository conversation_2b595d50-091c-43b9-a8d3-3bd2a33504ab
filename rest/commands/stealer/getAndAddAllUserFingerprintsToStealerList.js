const config = require('../../../config');
const addAllUserPaymentFingerprintsToStealerList = require('../../commands/stealer/addAllUserPaymentFingerprintsToStealerList');
const getUserFingerprintsPayment = require('../../commands/user/getUserFingerprintsPayment');

module.exports = async (user, description = '') => {
  if (!config.stealerList.fingerprints.enabled) return;

  const userFingerprintModels = await getUserFingerprintsPayment(user.id);
  const userFingerprints = userFingerprintModels.map(model => model.fingerprint);

  await addAllUserPaymentFingerprintsToStealerList(userFingerprints, description);
};
