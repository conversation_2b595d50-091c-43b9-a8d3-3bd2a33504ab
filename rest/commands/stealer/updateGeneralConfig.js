const { WrongParamsError } = require('@s1/api-errors');
const StealerGeneralConfig = require('../../model/audb/StealerGeneralConfig');

module.exports = async (data, user) => {
  if (!data || !data.streamingServers) throw new WrongParamsError();

  let stealerGeneralConfig = await StealerGeneralConfig.findOne({}).exec();

  if (!stealerGeneralConfig) stealerGeneralConfig = new StealerGeneralConfig();
  if (user) {
    stealerGeneralConfig.modifiedByUid = user.id;
    stealerGeneralConfig.modifiedByName = user.name;
  }

  stealerGeneralConfig.streamingServers = data.streamingServers;
  stealerGeneralConfig.permissionGroup = data.permissionGroup;
  await stealerGeneralConfig.save();

  return {
    error: 0,
    generalConfig: stealerGeneralConfig,
  };
};
