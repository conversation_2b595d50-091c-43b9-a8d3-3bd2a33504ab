const getPreviousUserIPs = require('../../middleware/helpers/getPreviousUserIPs');
const addAllUserIpsToStealerList = require('../../commands/stealer/addAllUserIpsToStealerList');

module.exports = async (user, description = '') => {
  const userIPModels = await getPreviousUserIPs(user.id);
  const userIPs = userIPModels.map(model => model.ip);

  await addAllUserIpsToStealerList(user, userIPs, description);
};
