const isLocal = require('is-local-ip');
const moment = require('moment');
const log = require('@s1/log').create(__filename);
const StealerIpReview = require('../../model/audb/StealerIpReview');
const { OUR_SERVERS_IPS, PAYMENT_WHITELISTED_IPS } = require('../../constants/ip');
const isCellularIp = require('../../helpers/proxy/isCellularIp');
const isAppleRelay = require('../../helpers/proxy/isAppleRelay');
const isProxy = require('../../helpers/proxy/isProxy');
const getUserIpsInStealerList = require('../../middleware/helpers/stealer/getUserIpsInStealerList');
const getUserIpsInStealerReview = require('../../middleware/helpers/stealer/getUserIpsInStealerReview');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

module.exports = async (user, userIPs, description = '') => {
  if (!user || isAdminOrSupportUser(user)) return;

  // limit only last 15 IPs
  const lastUserIPs = userIPs.length > 15 ? userIPs.slice(0, 15) : userIPs;
  const userStealerIpModels = await getUserIpsInStealerList(user, lastUserIPs);
  const userStealerIps = userStealerIpModels.map(ipModel => ipModel.ip);
  const userStealerReviewIps = await getUserIpsInStealerReview(user, lastUserIPs);
  // filter IPs already added to the stealer list or to review
  const notExistStealerIps = lastUserIPs.filter(userIp => !userStealerIps.includes(userIp)
    && !userStealerReviewIps.includes(userIp));
  const ipsForInsert = [];
  const cacheKeysForRemove = [];
  const now = moment().unix();
  const dateNow = new Date();

  // add only new not cellular IPs to the stealer list
  for (let i = 0; i < notExistStealerIps.length; ++i) {
    const userIp = notExistStealerIps[i];

    if (!isLocal(userIp) && !isCellularIp(userIp) && !isAppleRelay(userIp) && !await isProxy(userIp)
      && !OUR_SERVERS_IPS.has(userIp) && !PAYMENT_WHITELISTED_IPS.has(userIp)) {
      // disabled to save to Stealer list
      // ipsForInsert.push({ ip: userIp, creationMethod: creationMethods.automatic, description });

      // save user IPs to the temporary table for review before add to the stealer list
      ipsForInsert.push({ ip: userIp, description, created: now, updated: now, createdForTtlIndex: dateNow });
      cacheKeysForRemove.push(`stealer_ip_${userIp}`);
    }
  }

  if (ipsForInsert.length) {
    try {
      await StealerIpReview.insertMany(ipsForInsert, { ordered: false });
    } catch (e) {
      // some IPs might be already added and we can get an error
      log.info(`Cannot add new stealer IPs for review, error: ${e.stack || e.message}`);
    }
    await removeRedisCacheByKeys(cacheKeysForRemove);
  }
};
