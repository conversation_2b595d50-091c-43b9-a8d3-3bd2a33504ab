const { ApiError } = require('@s1/api-errors');
const _ = require('lodash');
const User = require('../../model/audb/User');
const UserIp = require('../../model/audb/UserIp');
const Stealer = require('../../model/audb/Stealer');
const PaymentLog = require('../../model/audb/PaymentLog');
const { Package } = require('../../model/audb/Package');
const getRelatedUsersIDsToCurrentStealerUser = require('./getRelatedUsersIDsToCurrentStealerUser');
const { stealerTypes } = require('../../constants/stealer');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const { getUserLocation } = require('../../service/maxmind');
const getUserWatchingScore = require('../history/getUserWatchingScore');

module.exports = async (userId) => {
  if (!userId) return new ApiError(903, 'userId is required');

  const usersIds = await getRelatedUsersIDsToCurrentStealerUser(userId);
  const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
  const packagesObject = {};
  allPackages.forEach((pack) => {
    packagesObject[pack.id] = pack;
  });
  const users = await Stealer.aggregate([
    { $match: { uid: { $in: usersIds } } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'uid',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $project: { UserIp: 0 },
    },
    {
      $group: {
        _id: '$uid',
        uid: { $first: '$uid' },
        id: { $first: '$_id' },
        description: { $first: '$description' },
        creationMethod: { $first: '$creationMethod' },
        updated: { $first: '$updated' },
        created: { $first: '$created' },
        addedByName: { $first: '$addedByName' },
        addedByUid: { $first: '$addedByUid' },
        em: { $first: '$User.em' },
        regtime: { $first: '$User.regtime' },
        expires: { $first: '$User.expires' },
        packageId: { $first: '$User.package' },
      },
    },
    {
      $project: { _id: '$id', uid: 1, creationMethod: 1, description: 1, updated: 1, created: 1, addedByName: 1, addedByUid: 1, em: 1, regtime: 1, expires: 1, packageId: 1, type: stealerTypes.uid },
    },
    { $sort: { created: -1 } },
  ])
    .then(stealerUsers => Promise.all(stealerUsers.map(async (stealerUser) => {
      stealerUser.email = stealerUser.em ? await User.decryptEmailWithRedis(stealerUser.em) : '';
      stealerUser.paidTypes = await getUserPaidTypes(stealerUser.uid);
      stealerUser.watchingScore = await getUserWatchingScore(stealerUser.uid);
      stealerUser.package = packagesObject.hasOwnProperty(stealerUser.packageId) ? packagesObject[stealerUser.packageId] : null;
      stealerUser.lastPayments = await PaymentLog.find({
        uid: stealerUser.uid,
        amount: { $gt: 0 },
        pptype: { $ne: 'manual' },
      }, { pptype: 1, package: 1 }).sort({ _id: -1 }).limit(3).lean()
        .cache(300)
        .then(logs => (_.map(logs, (log) => {
          if (packagesObject.hasOwnProperty(log.package)) {
            const { id, ...pack } = packagesObject[log.package];
            log = Object.assign(log, pack);
          }

          delete log._id;

          return log;
        })));

      const userIPModel = await UserIp.findOne({ uid: stealerUser.uid }).lean();

      if (userIPModel) {
        const { countryName, countryCode, stateCode, ISP } = getUserLocation(userIPModel.ip);
        stealerUser.ipInfo = { ip: userIPModel.ip, countryName, countryCode, stateCode, ISP };
      } else {
        stealerUser.ipInfo = null;
      }

      return stealerUser;
    })));

  return {
    error: 0,
    users,
  };
};
