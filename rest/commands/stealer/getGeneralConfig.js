const config = require('../../../config');
const StealerGeneralConfig = require('../../model/audb/StealerGeneralConfig');

module.exports = async () => {
  const generalConfig = await StealerGeneralConfig.findOne().lean().cache(config.stealerList.cache, 'stealerGeneralConfig');

  if (generalConfig) return {
    error: 0,
    generalConfig,
  };

  return {
    error: 0,
    generalConfig: new StealerGeneralConfig(),
  };
};
