const Stealer = require('../../model/audb/Stealer');
const User = require('../../model/audb/User');
const config = require('../../../config');
const { creationMethods } = require('../../constants/stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const moveUserToStealerPermissionGroup = require('./moveUserToStealerPermissionGroup');

const addUserToStealerlist = async (user, description = '') => {
  if (!user || isAdminOrSupportUser(user)) return;

  let stealerUser = await Stealer.findOne({ uid: user.id })
    .lean().cache(config.stealerList.cache, `stealer_id_${user.id}`);

  if (!stealerUser) {
    stealerUser = new Stealer({
      uid: user.id,
      creationMethod: creationMethods.automatic,
      description,
    });
    await removeRedisCacheByKeys([`stealer_id_${user.id}`]);
    await stealerUser.save();
    await moveUserToStealerPermissionGroup(user);

    try {
      const users = await User.getRelatedUsersUsedTheSameDevice(user);
      const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
      const filteredUsers = users ? users.filter(user => !adminsAndSupportsIDs.includes(user.id)) : [];

      if (filteredUsers && filteredUsers.length) {
        const userEmail = await User.decryptEmailWithRedis(user.em);
        const relatedUserDescription = `Detected by registered keys data for the user ${user.id} ${userEmail} <-- ${description}`;
        filteredUsers.forEach(relatedUser => addUserToStealerlist(relatedUser, relatedUserDescription));
      }
    } catch (e) {
      console.error(`Cannot get related users used the same device for the user ${user.id}`, e);
    }
  }
};

module.exports = addUserToStealerlist;
