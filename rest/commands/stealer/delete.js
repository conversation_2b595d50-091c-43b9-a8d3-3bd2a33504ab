const { ApiError } = require('@s1/api-errors');
const Stealer = require('../../model/audb/Stealer');
const StealerIpReview = require('../../model/audb/StealerIpReview');
const { ipReviewStatuses } = require('../../constants/stealer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const removeStealerUserAndRelatedUsers = require('./removeStealerUserAndRelatedUsers');

module.exports = async ({ _id, adminUser }) => {
  if (!_id) throw new ApiError(903, 'ID is required');

  const removeResult = await Stealer.findOneAndDelete({ _id }).exec();

  let stealerIpReview;

  if (removeResult) {
    if (removeResult.ip) {
      stealerIpReview = await StealerIpReview.findOne({ ip: removeResult.ip }).exec();
      await removeRedisCacheByKeys([`stealer_ip_${removeResult.ip}`]);

      if (stealerIpReview) {
        stealerIpReview.status = ipReviewStatuses.unblock;
        stealerIpReview.createdForTtlIndex = new Date();
        await stealerIpReview.save();
      }
    } else if (removeResult.uid) {
      await removeStealerUserAndRelatedUsers(removeResult.uid, adminUser);
    } else {
      await removeRedisCacheByKeys(['stealer_billingDetails']);
    }
  }

  return {
    error: 0,
    success: !!removeResult,
    stealer: removeResult,
    stealerIpReview: stealerIpReview ? { ...stealerIpReview.toObject() } : null,
  };
};
