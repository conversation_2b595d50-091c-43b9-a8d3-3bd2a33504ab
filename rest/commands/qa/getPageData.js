/* eslint camelcase: 0 */

const QAPage = require('../../model/audb/QAPage');
const QACategory = require('../../model/audb/QACategory');
const QACategoryContent = require('../../model/audb/QACategoryContent');

module.exports = async (type) => {
  const page = await QAPage.findOne({ title: type }).exec();

  if (!page) return;

  let categories = await QACategory.find({ pageId: page._id }).exec();
  categories = await Promise.all(categories.map(async ({ _id, title_he, title_en, order }) => {
    const questions = await QACategoryContent.find({ categoryId: _id }).exec();

    return {
      title_he,
      title_en,
      order,
      questions: questions.sort((a, b) => a.order > b.order).map(question => ({
        question_en: question.question_en,
        question_he: question.question_he,
        answer_en: question.answer_en,
        answer_he: question.answer_he,
        order: question.order,
      })),
    };
  }));

  return { categories: categories.sort((a, b) => a.order > b.order) };
};
