const QACategory = require('../../../model/audb/QACategory');
const QACategoryContent = require('../../../model/audb/QACategoryContent');

module.exports = async (pageId) => {
  const categories = await QACategory.find({ pageId }).sort({ order: 'asc' }).exec();

  return Promise.all(categories.map(async (category) => {
    const questionsCount = await QACategoryContent.countDocuments({ categoryId: category._id }).exec();

    return { ...category.toJSON(), questionsCount };
  }));
};
