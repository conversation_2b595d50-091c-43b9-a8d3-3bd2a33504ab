const QACategory = require('../../../model/audb/QACategory');
const QACategoryContent = require('../../../model/audb/QACategoryContent');

module.exports = async (id) => {
  const category = await QACategory.findOne({ _id: id }).exec();
  const result = { ...category.toJSON() };

  if (category) result.questionsCount = await QACategoryContent
    .countDocuments({ categoryId: category._id })
    .exec();

  return result;
};
