const QAPage = require('../../../model/audb/QAPage');
const QACategory = require('../../../model/audb/QACategory');
const removeCategory = require('../category/remove');

module.exports = async (id) => {
  const relatedCategories = await QACategory.find({ pageId: id }).exec();
  const promises = relatedCategories.map(category => removeCategory(category._id));
  promises.push(QAPage.findByIdAndRemove(id));
  await Promise.all(promises);
};
