const moment = require('moment');
const Notice = require('../../model/audb/Notice');
const getNextModelId = require('../../helpers/getNextModelId');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

const getTime = (day, hour) => {
  let result;

  if (day && hour) result = moment(Date.parse(`${day} ${hour}`)).unix();
  else if (day) result = moment(Date.parse(day)).unix();

  return result;
};

module.exports = async (data) => {
  try {
    if (getTime(data.startdate, data.starthis)) data.starttime = getTime(data.startdate, data.starthis);
    if (getTime(data.enddate, data.endhis)) data.endtime = getTime(data.enddate, data.endhis);

    data.id = data.id || await getNextModelId(Notice);
    delete data._id;
    const result = await Notice.findOneAndUpdate(
      { id: data.id },
      { $set: data },
      { upsert: true, new: true },
    ).lean().exec();

    return formatResponseIDs(result);
  } catch (error) {
    throw new Error(error);
  }
};
