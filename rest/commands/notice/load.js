const moment = require('moment');
const Notice = require('../../model/audb/Notice');

module.exports = async () => {
  try {
    const notices = await Notice.find({}).lean().exec();
    notices.forEach((notice) => {
      const { starttime, starthis, endhis, endtime } = notice;

      notice.startdate = moment.unix(starttime || new Date()).format('YY-MM-DD');
      notice.enddate = moment.unix(endtime || new Date()).format('YY-MM-DD');
      notice.starthis = moment(starthis || new Date(), 'HHmmss').format('HH:mm:ss');
      notice.endhis = moment(endhis || new Date(), 'HHmmss').format('HH:mm:ss');
    });

    return notices;
  } catch (error) {
    throw new Error(error);
  }
};
