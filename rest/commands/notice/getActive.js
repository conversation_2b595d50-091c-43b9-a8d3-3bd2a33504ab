const moment = require('moment');
const Notice = require('../../model/audb/Notice');

/**
 * Returns all active notices that should end after the current time expires.
 *
 * @param locale - Current locale
 * @returns {Promise<[{}]>} - All notices
 */
module.exports = async (locale) => {
  const now = moment().unix();

  const notices = await Notice.find({
    starttime: { $lte: now },
    endtime: { $gte: now },
  }).cache(300).exec()
    .then(notices => Promise.all(notices.map(notice => notice.format({ locale }))));
  const filteredNotices = notices.filter(notice => !notice.tagname || notice.tagname && !notice.tagname.toLowerCase().includes('suggest_app_'))

  return filteredNotices;
};
