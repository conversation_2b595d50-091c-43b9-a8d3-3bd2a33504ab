const splitTimeIntoDays = require('../../../helpers/splitTimeIntoDays');
const DaytimeWorkActivityLog = require('../../../model/audb/DaytimeWorkActivityLog');
const { DAYTIME_WORK_ACTIVITY_TYPES } = require('../../../constants/employee');

/**
 * @param {number} userId - userID from activity
 * @param {object} activity - current or new activity
 * @param {object} data - body from post or current activity data for approve
 * @param {boolean} isUpdate - we updating(approving) existing record or creating new one
 * */
module.exports = async (userId, activity, data, isUpdate) => {
  const range = splitTimeIntoDays(data.newStartTime, data.newEndTime);
  // set original type for the all days
  const type = activity.type;

  for (let i = 0; i < range.length; ++i) {
    // update exist record from first range
    if (!isUpdate || i > 0) activity = new DaytimeWorkActivityLog({ userId });

    const timesByDay = range[i];

    activity.type = type;
    activity.date = new Date(timesByDay.startTime * 1000);
    activity.startTime = timesByDay.startTime;
    activity.endTime = timesByDay.endTime;
    activity.comment = data.comment;
    activity.type = DAYTIME_WORK_ACTIVITY_TYPES.manual;

    if (activity.newDate) activity.newDate = undefined;
    if (activity.newStartTime) activity.newStartTime = undefined;
    if (activity.newEndTime) activity.newEndTime = undefined;
    if (activity.editReasonComment) activity.editReasonComment = undefined;

    await activity.save();
  }

  return { success: true };
};
