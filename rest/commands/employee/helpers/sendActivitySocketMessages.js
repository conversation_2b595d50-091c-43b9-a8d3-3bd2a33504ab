const { SocketClientNames } = require('../../../constants/socket');
const checkAwaitingApprovalActivities = require('./../checkAwaitingApprovalActivities');

/**
 * @param {number} userId - userID from activity
 * @param {object} currentUser - user from request
 * @param {boolean} checkApprovalActivities - default false
 * */
module.exports = (userId, currentUser, checkApprovalActivities = false) => {
  // send to the current user
  const socketDataUser = { data: { userId }, groupName: currentUser.id, eventName: 'trackerUpdated' };
  global.io.emit('group', socketDataUser);

  if (userId !== currentUser.id) {
    // send to the updated user
    const socketDataUser = { data: { userId }, groupName: userId, eventName: 'trackerUpdated' };
    global.io.emit('group', socketDataUser);
  }

  const socketDataAdminPanelSingleUser = { data: { userId }, groupName: SocketClientNames.adminPanel, eventName: 'employeeActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelSingleUser);

  const socketDataAdminPanelMultiUsers = { data: null, groupName: SocketClientNames.adminPanel, eventName: 'employeesActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelMultiUsers);

  if (checkApprovalActivities) checkAwaitingApprovalActivities();
};
