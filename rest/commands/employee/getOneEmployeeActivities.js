const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');

const getDetailedAggregation = (filter) => {
  const aggregation = [
    { $match: filter },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          year: { $year: '$date' },
          month: { $month: '$date' },
          week: { $isoWeek: '$date' },
          day: { $dayOfMonth: '$date' },
        },
        logs: { $push: '$$ROOT' },
        totalTime: { $sum: { $subtract: ['$endTime', '$startTime'] } },
      },
    },
    {
      $sort: {
        '_id.year': -1,
        '_id.month': -1,
        '_id.week': -1,
        '_id.day': -1,
      },
    },
    {
      $project: {
        _id: 0,
        date: {
          $dateFromParts: {
            year: '$_id.year',
            month: '$_id.month',
            day: '$_id.day',
          },
        },
        logs: 1,
        totalTime: 1,
      },
    },
    { $sort: { date: -1 } },
  ];

  return aggregation;
};

const getDailyAggregation = (filter) => {
  const aggregation = [
    { $match: filter },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          year: { $year: '$date' },
          month: { $month: '$date' },
          day: { $dayOfMonth: '$date' },
        },
        totalTime: { $sum: { $subtract: ['$endTime', '$startTime'] } },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $sort: {
        '_id.year': -1,
        '_id.month': -1,
        '_id.day': -1,
      },
    },
    {
      $project: {
        _id: 0,
        date: {
          $dateFromParts: {
            year: '$_id.year',
            month: '$_id.month',
            day: '$_id.day',
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
    { $sort: { date: -1 } },
  ];

  return aggregation;
};

const getWeeklyAggregation = (filter) => {
  const aggregation = [
    { $match: filter },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          year: { $isoWeekYear: '$date' },
          week: { $isoWeek: '$date' },
        },
        totalTime: { $sum: { $subtract: ['$endTime', '$startTime'] } },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $sort: {
        '_id.year': -1,
        '_id.week': -1,
      },
    },
    {
      $project: {
        _id: 0,
        weekStartDate: {
          $dateFromParts: {
            isoWeekYear: '$_id.year',
            isoWeek: '$_id.week',
            isoDayOfWeek: 1, // Assuming Monday is the start of the week
          },
        },
        weekEndDate: {
          $dateFromParts: {
            isoWeekYear: '$_id.year',
            isoWeek: '$_id.week',
            isoDayOfWeek: 7, // Assuming Sunday is the end of the week
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
    { $sort: { weekStartDate: -1 } },
  ];

  return aggregation;
};

const getMonthlyAggregation = (filter) => {
  const aggregation = [
    { $match: filter },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          year: { $year: '$date' },
          month: { $month: '$date' },
        },
        totalTime: { $sum: { $subtract: ['$endTime', '$startTime'] } },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $sort: {
        '_id.year': -1,
        '_id.month': -1,
      },
    },
    {
      $project: {
        _id: 0,
        monthStartDate: {
          $dateFromParts: {
            year: '$_id.year',
            month: '$_id.month',
            day: 1,
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
    { $sort: { monthStartDate: -1 } },
  ];

  return aggregation;
};

/**
 * Get activities for all or current/selected user by period
 * @param {object} data - filter
 * @param {number} userId - filter
 * */
module.exports = async (data, userId = null) => {
  const filter = {
    $or: [
      {
        $and: [
          { userId },
          { startTime: { $gte: data.startTime } },
          { startTime: { $lt: data.endTime } },
        ],
      },
      {
        $and: [
          { userId },
          { newStartTime: { $gte: data.startTime } },
          { newEndTime: { $lt: data.endTime } },
        ],
      },
    ],
  };

  let aggregation = [];

  switch (data.groupBy) {
    case 'daily':
      aggregation = getDailyAggregation(filter);
      break;
    case 'weekly':
      aggregation = getWeeklyAggregation(filter);
      break;
    case 'monthly':
      aggregation = getMonthlyAggregation(filter);
      break;
    default:
      aggregation = getDetailedAggregation(filter);
      break;
  }

  const activities = await DaytimeWorkActivityLog.aggregate(aggregation);

  return {
    error: 0,
    activities,
  };
};
