const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');

/**
 * Get employee config by user ID
 * @param {object} data
 * */
module.exports = async (data) => {
  const filter = {
    $and: [
      { userId: data.userId },
      { startTime: { $exists: true } },
      { endTime: { $exists: true } },
      { startTime: { $gte: data.startTime } },
      { endTime: { $lte: data.endTime } },
    ],
  };

  const aggregation = [
    {
      $match: filter,
    },
    {
      $group: {
        _id: {
          userId: '$userId',
        },
        totalTime: { $sum: { $subtract: ['$endTime', '$startTime'] } },
      },
    },
  ];
  const activities = await DaytimeWorkActivityLog.aggregate(aggregation).exec();

  const totalTime = activities.length ? activities[0].totalTime : 0;
  const totalHours = Math.round(totalTime * 100 / 3600) / 100;

  return {
    error: 0,
    result: {
      totalHours,
    },
  };
};
