const moment = require('moment-timezone');
const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const { SocketClientNames } = require('../../constants/socket');
const DaytimeWorkActivityAutoStop = require('../../model/audb/DaytimeWorkActivityAutoStop');
const EmployeeConfig = require('../../model/audb/EmployeeConfig');

const getEmployeeWorkedTimes = async (userId) => {
  const startOfDay = moment().tz('UTC').startOf('day').unix();
  const endOfDay = moment().tz('UTC').startOf('day').add(1, 'day')
    .subtract(1, 'second')
    .unix();

  // add one more day, because of week started from the Sunday
  const startOfWeek = moment().tz('UTC').startOf('week').add(1, 'day')
    .unix();
  const endOfWeek = moment().tz('UTC').startOf('week').add(1, 'week')
    .add(1, 'day')
    .subtract(1, 'second')
    .unix();

  const aggregation = [
    {
      $match: {
        $and: [
          { userId },
          { endTime: { $gt: 0 } },
          { newEndTime: { $exists: false } },
          { newEndTime: null },
        ],
      },
    },
    {
      $project: { _id: 0, userId: 1 },
    },
    {
      $lookup: {
        from: 'daytimeWorkActivityLog',
        let: { userId: '$userId', startTime: '$startTime' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$userId', '$$userId'] },
                  { $gte: ['$startTime', startOfDay] },
                  { $lte: ['$startTime', endOfDay] },
                ],
              },
            },
          },
        ],
        as: 'dailyLogs',
      },
    },
    {
      $lookup: {
        from: 'daytimeWorkActivityLog',
        let: { userId: '$userId', startTime: '$startTime' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$userId', '$$userId'] },
                  { $gte: ['$startTime', startOfWeek] },
                  { $lte: ['$startTime', endOfWeek] },
                ],
              },
            },
          },
        ],
        as: 'weeklyLogs',
      },
    },
    {
      $addFields: {
        dailyTotalTime: {
          $sum: {
            $map: {
              input: '$dailyLogs',
              as: 'log',
              in: { $subtract: ['$$log.endTime', '$$log.startTime'] },
            },
          },
        },
        weeklyTotalTime: {
          $sum: {
            $map: {
              input: '$weeklyLogs',
              as: 'log',
              in: { $subtract: ['$$log.endTime', '$$log.startTime'] },
            },
          },
        },
      },
    },
    {
      $project: { userId: 1, dailyTotalTime: 1, weeklyTotalTime: 1 },
    },
    {
      $lookup: {
        from: 'employeeConfig',
        localField: 'userId',
        foreignField: 'userId',
        as: 'EmployeeConfig',
      },
    },
    { $unwind: { path: '$EmployeeConfig', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'daytimeWorkActivityAutoStop',
        localField: 'userId',
        foreignField: 'userId',
        as: 'DaytimeWorkActivityAutoStop',
      },
    },
    { $unwind: { path: '$DaytimeWorkActivityAutoStop', preserveNullAndEmptyArrays: true } },
  ];

  const currentlyWorkingEmployees = await DaytimeWorkActivityLog.aggregate(aggregation).exec();

  return currentlyWorkingEmployees.length ? currentlyWorkingEmployees[0] : null;
};

module.exports = async (user, comment = null) => {
  if (!user) throw new Error('Please login');

  const filter = { $and: [
    { userId: user.id },
    { startTime: { $exists: true } },
    { startTime: { $ne: null } },
    { startTime: { $ne: undefined } },
    { $or: [
      { endTime: { $exists: false } },
      { endTime: null },
    ] },
  ] };
  const startedActivity = await DaytimeWorkActivityLog.findOne(filter).lean();

  if (startedActivity) {
    throw new Error('Tracker already started');
  }

  const workedTimes = await getEmployeeWorkedTimes(user.id);

  if (workedTimes) {
    const employeeConfig = await EmployeeConfig.findOne({ userId: user.id }).lean();
    let limitsReached = false;
    let notifyMessage;

    if (employeeConfig.workingWeekTimeLimitStrict && employeeConfig.workingWeekTimeLimit
      && workedTimes.weeklyTotalTime >= employeeConfig.workingWeekTimeLimit) {
      limitsReached = true;

      notifyMessage = `Yor reached weekly limit ${employeeConfig.workingWeekTimeLimit / 3600} hours`;
    } else if (employeeConfig.workingDayTimeLimitStrict && employeeConfig.workingDayTimeLimit
      && workedTimes.dailyTotalTime >= employeeConfig.workingDayTimeLimit) {
      limitsReached = true;

      notifyMessage = `Yor reached daily limit ${employeeConfig.workingDayTimeLimit / 3600} hours`;
    }
    if (limitsReached) {
      const data = {
        notifyMessage,
      };
      const socketDataUser = { data, groupName: user.id, eventName: 'trackerAutomaticallyStopped' };
      global.io.emit('group', socketDataUser);

      throw new Error(`Yor reached weekly limit ${employeeConfig.workingWeekTimeLimit / 3600} hours`);
    }
  }

  const activity = new DaytimeWorkActivityLog({
    userId: user.id,
    date: new Date(),
    startTime: parseInt(Date.now() / 1000),
    comment,
  });
  await activity.save();

  // after user reached limits and did not confirm and started again,
  // no need to check limits and lets mark he interacted
  await DaytimeWorkActivityAutoStop.findOneAndUpdate(
    { userId: user.id },
    { $set: {
      interacted: true,
    } },
    { upsert: false, new: false },
  ).lean();

  const socketDataUser = { data: null, groupName: user.id, eventName: 'trackerStarted' };
  global.io.emit('group', socketDataUser);

  const socketDataAdminPanelSingleUser = { data: { userId: user.id }, groupName: SocketClientNames.adminPanel, eventName: 'employeeActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelSingleUser);

  const socketDataAdminPanelMultiUsers = { data: null, groupName: SocketClientNames.adminPanel, eventName: 'employeesActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelMultiUsers);

  return {
    error: 0,
    success: true,
  };
};
