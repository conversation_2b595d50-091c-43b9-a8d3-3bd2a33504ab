const EmployeeConfig = require('../../model/audb/EmployeeConfig');

/**
 * Get employee config by user ID
 * @param {number} userId
 * */
module.exports = async (userId) => {
  let employeeConfig = await EmployeeConfig.findOne({ userId }).lean();

  if (!employeeConfig) {
    employeeConfig = new EmployeeConfig({ userId });
    await employeeConfig.save();
  }

  return {
    error: 0,
    employeeConfig,
  };
};
