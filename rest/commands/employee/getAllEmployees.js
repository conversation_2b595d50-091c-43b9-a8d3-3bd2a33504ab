const User = require('../../model/audb/User');
const EmployeeConfig = require('../../model/audb/EmployeeConfig');
const { IGNORED_USERS_IDS } = require('../../constants/employee');

module.exports = async () => {
  const aggregation = [
    {
      $match: {
        $and: [
          { id: { $nin: IGNORED_USERS_IDS } },
          { $or: [{ isdeveloper: 1 }, { issupport: 1 }, { isadmin: 1 }] },
        ],
      },
    },
    {
      $project: { _id: 0, id: 1, em: 1, name: 1, isdeveloper: 1, issupport: 1, isadmin: 1 },
    },
    {
      $lookup: {
        from: 'employeeConfig',
        localField: 'id',
        foreignField: 'userId',
        as: 'EmployeeConfig',
      },
    },
    { $unwind: { path: '$EmployeeConfig', preserveNullAndEmptyArrays: true } },
  ];
  const users = await User.aggregate(aggregation).exec();

  for (let i = 0; i < users.length; ++i) {
    const user = users[i];
    user.email = User.decryptEmail(user.em);
    delete user.em;

    if (!user.EmployeeConfig) {
      user.EmployeeConfig = new EmployeeConfig({ userId: user.id });
      await user.EmployeeConfig.save();
    }
  }

  return {
    error: 0,
    employees: users,
  };
};
