const EmployeePaymentHistory = require('../../model/audb/EmployeePaymentHistory');
const User = require('../../model/audb/User');
const { IGNORED_USERS_IDS } = require('../../constants/employee');

const getPaymentHistory = async (data) => {
  const filter = { $and: [] };

  if (data.userId) filter.$and.push({ userId: data.userId });
  else filter.$and.push({ userId: { $nin: IGNORED_USERS_IDS } });

  filter.$and.push({ created: { $gte: data.startTime } });
  filter.$and.push({ created: { $lte: data.endTime } });

  const aggregation = [
    {
      $match: filter,
    },
    {
      $sort: { _id: -1 },
    },
    {
      $lookup: {
        from: 'tuser',
        localField: 'userId',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'tuser',
        localField: 'addedByUid',
        foreignField: 'id',
        as: 'AddedByUser',
      },
    },
    { $unwind: { path: '$AddedByUser', preserveNullAndEmptyArrays: true } },
    {
      $project: {
        userId: 1,
        startTime: 1,
        endTime: 1,
        hours: 1,
        amount: 1,
        comment: 1,
        addedByUid: 1,
        created: 1,
        walletAddress: 1,
        walletAddressInfo: 1,
        'User.em': '$User.em',
        'User.name': '$User.name',
        'User.isdeveloper': '$User.isdeveloper',
        'User.issupport': '$User.issupport',
        'User.isadmin': '$User.isadmin',
        addedByName: '$AddedByUser.name',
        addedByEm: '$AddedByUser.em',
      },
    },
  ];

  const paymentHistories = await EmployeePaymentHistory.aggregate(aggregation).exec();

  return { paymentHistories };
};

/**
 * Get activities awaiting for approval
 * */
module.exports = async (data) => {
  const result = await getPaymentHistory(data);

  const formattedPaymentHistories = result.paymentHistories
    .map((log) => {
      log.User.email = User.decryptEmail(log.User.em);
      delete log.User.em;
      log.addedByEmail = User.decryptEmail(log.addedByEm);
      delete log.addedByEm;

      return log;
    });

  return {
    error: 0,
    paymentHistories: formattedPaymentHistories,
  };
};
