const moment = require('moment-timezone');
const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const User = require('../../model/audb/User');
const { IGNORED_USERS_IDS } = require('../../constants/employee');

moment.locale('en');

const getStartOfDay = (unixTime) => {
  const startOfDay = moment.unix(unixTime).tz('UTC').startOf('day');

  return startOfDay.unix() / 1000;
};

const getEndOfDay = (unixTime) => {
  const startOfNewDay = moment.unix(unixTime).tz('UTC').startOf('day');
  const time = startOfNewDay.unix() / 1000 + 24 * 3600 - 1;

  return time;
};

const getDailyActivities = async (usersIds, data) => {
  const filter = {
    $and: [
      { userId: { $in: usersIds } },
      { startTime: { $gte: data.startTime } },
      { startTime: { $lte: data.endTime } },
    ],
  };

  const allDays = [];
  const allDaysHeaders = [];
  const currentDate = moment(data.startTime * 1000).tz('UTC');
  const endDate = moment(data.endTime * 1000).tz('UTC');

  while (currentDate < endDate) {
    allDays.push({
      date: currentDate.format('DD-MM-YYYY'),
      startTime: getStartOfDay(currentDate),
      endTime: getEndOfDay(currentDate),
    });

    const day = currentDate.date();
    const monthName = currentDate.format('MMM');
    const year = currentDate.format('YYYY');

    allDaysHeaders.push(`${day} ${monthName} ${year}`);

    currentDate.add(1, 'day');
  }

  const aggregation = [
    {
      $match: filter,
    },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          userId: '$userId',
          startOfDay: { $dateToString: { format: '%d-%m-%Y', date: '$date' } },
          startOfDayNumber: {
            $toLong: {
              $dateFromParts: {
                year: { $year: '$date' },
                month: { $month: '$date' },
                day: { $dayOfMonth: '$date' }
              }
            }
          },
        },
        totalTime: {
          $sum: {
            $cond: [{ $gt: ['$endTime', 0] }, { $subtract: ['$endTime', '$startTime'] }, 0],
          },
        },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id.userId',
        startOfDay: '$_id.startOfDay',
        startOfDayNumber: '$_id.startOfDayNumber',
        totalTime: { $ifNull: ['$totalTime', 0] },
        lastLog: 1,
      },
    },
    {
      $sort: {
        startOfDayNumber: -1,
      },
    },
    {
      $lookup: {
        from: 'employeePaymentHistory',
        let: { userId: '$userId', startOfDay: '$startOfDay' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$userId', '$$userId'] },
                  {
                    $or: [
                      { $and: [
                        { $gte: [{ $toDate: '$$startOfDay' }, { $toDate: { $multiply: ['$startTime', 1000] } }] },
                        { $lte: [{ $toDate: '$$startOfDay' }, { $toDate: { $multiply: ['$endTime', 1000] } }] },
                      ] },
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: 'paymentHistory',
      },
    },
    {
      $group: {
        _id: '$userId',
        days: {
          $push: {
            startOfDay: '$startOfDay',
            totalTime: '$totalTime',
            lastLog: '$lastLog',
            paymentHistory: '$paymentHistory',
          },
        },
        totalTime: { $sum: '$totalTime' },
        lastLog: { $first: '$lastLog' },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id',
        days: {
          $map: {
            input: allDays,
            as: 'day',
            in: {
              startOfDay: '$$day',
              totalTime: {
                $sum: {
                  $map: {
                    input: '$days',
                    as: 'd',
                    in: {
                      $cond: [{ $eq: ['$$d.startOfDay', '$$day.date'] }, '$$d.totalTime', 0],
                    },
                  },
                },
              },
              lastLog: {
                $arrayElemAt: [
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$days',
                          as: 'd',
                          cond: { $eq: ['$$d.startOfDay', '$$day.date'] },
                        },
                      },
                      as: 'd',
                      in: '$$d.lastLog',
                    },
                  },
                  0,
                ],
              },
              paymentHistory: {
                $arrayElemAt: [
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$days',
                          as: 'd',
                          cond: { $eq: ['$$d.startOfDay', '$$day.date'] },
                        },
                      },
                      as: 'd',
                      in: '$$d.paymentHistory',
                    },
                  },
                  0,
                ],
              },
            },
          },
        },
        totalTime: 1,
        lastLog: 1,
        status: {
          $cond: {
            if: {
              $eq: [
                { $size: { $filter: { input: '$days', as: 'd', cond: { $gt: ['$$d.totalTime', 0] } } } },
                { $size: { $filter: { input: '$days',
                  as: 'd',
                  cond: {
                    $and: [{ $gt: ['$$d.totalTime', 0] }, { $ne: ['$$d.paymentHistory', null] }, { $gt: [{ $size: '$$d.paymentHistory' }, 0] }],
                  },
                },
                },
                },
              ],
            },
            then: 'paid',
            else: {
              $cond: {
                if: {
                  $gt: [
                    { $size: { $filter: { input: '$days', as: 'd', cond: { $and: [{ $gt: ['$$d.totalTime', 0] }, { $ne: ['$$d.paymentHistory', null] }, { $gt: [{ $size: '$$d.paymentHistory' }, 0] }] } } } },
                    0,
                  ],
                },
                then: 'partial_paid',
                else: 'not_paid',
              },
            },
          },
        },
      },
    },
  ];

  const activities = await DaytimeWorkActivityLog.aggregate(aggregation);

  const missingUserIds = usersIds.filter(userId => !activities.find(item => item.userId === userId));

  const missingUserData = missingUserIds.map(userId => ({
    userId,
    days: allDays.map(day => ({
      startOfDay: day,
      totalTime: 0,
      lastLog: null,
    })),
    totalTime: 0,
    lastLog: null,
  }));

  const finalResult = activities.concat(missingUserData);

  return { activities: finalResult, headers: allDaysHeaders };
};

const getWeeklyActivities = async (usersIds, data) => {
  const filter = {
    $and: [
      { userId: { $in: usersIds } },
      { startTime: { $gte: data.startTime } },
      { startTime: { $lte: data.endTime } },
    ],
  };

  const allDays = [];
  const currentDate = moment(data.startTime * 1000).tz('UTC').startOf('day');
  const endDate = moment(data.endTime * 1000).tz('UTC');

  while (currentDate < endDate) {
    allDays.push({ startOfDay: currentDate.format('DD-MM-YYYY'), startOfDayNumber: currentDate.unix(), });
    currentDate.add(1, 'day');
  }

  const allWeeks = [];
  const allWeeksHeaders = [];
  const currentWeekStart = moment(data.startTime * 1000).tz('UTC').startOf('week');
  const endWeek = moment(data.endTime * 1000).tz('UTC').endOf('week');

  while (currentWeekStart <= endWeek) {
    const currentWeekEnd = currentWeekStart.clone().add(6, 'days');
    allWeeks.push({
      weekStart: currentWeekStart.format('DD-MM-YYYY'),
      weekEnd: currentWeekEnd.format('DD-MM-YYYY'),
      weekStartNumber: currentWeekStart.unix(),
      weekEndNumber: currentWeekEnd.unix(),
    });
    const dayStart = currentWeekStart.date();
    const dayEnd = currentWeekEnd.date();
    const monthNameStart = currentWeekStart.format('MMM');
    const monthNameEnd = currentWeekEnd.format('MMM');
    const yearStart = currentWeekStart.format('YYYY');
    const yearEnd = currentWeekEnd.format('YYYY');
    allWeeksHeaders.push({
      weekStart: `${dayStart} ${monthNameStart} ${yearStart}`,
      weekEnd: `${dayEnd} ${monthNameEnd} ${yearEnd}`,
    });

    currentWeekStart.add(1, 'week');
  }

  const aggregation = [
    {
      $match: filter,
    },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          userId: '$userId',
          startOfDay: { $dateToString: { format: '%d-%m-%Y', date: '$date' } },
          startOfDayNumber: {
            $toLong: {
              $dateFromParts: {
                year: { $year: '$date' },
                month: { $month: '$date' },
                day: { $dayOfMonth: '$date' }
              }
            }
          },
        },
        totalTime: {
          $sum: {
            $cond: [{ $gt: ['$endTime', 0] }, { $subtract: ['$endTime', '$startTime'] }, 0],
          },
        },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id.userId',
        startOfDay: '$_id.startOfDay',
        startOfDayNumber: '$_id.startOfDayNumber',
        totalTime: { $ifNull: ['$totalTime', 0] },
        lastLog: 1,
      },
    },
    {
      $sort: {
        startOfDayNumber: -1,
      },
    },
    {
      $group: {
        _id: '$userId',
        days: {
          $push: {
            startOfDay: '$startOfDay',
            startOfDayNumber: '$startOfDayNumber',
            totalTime: '$totalTime',
            lastLog: '$lastLog',
          },
        },
        totalTime: { $sum: '$totalTime' },
        lastLog: { $first: '$lastLog' },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id',
        days: {
          $map: {
            input: allDays,
            as: 'day',
            in: {
              startOfDay: '$$day.startOfDay',
              startOfDayNumber: '$$day.startOfDayNumber',
              totalTime: {
                $sum: {
                  $map: {
                    input: '$days',
                    as: 'd',
                    in: {
                      $cond: [{ $eq: ['$$d.startOfDay', '$$day.startOfDay'] }, '$$d.totalTime', 0],
                    },
                  },
                },
              },
              lastLog: {
                $arrayElemAt: [
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$days',
                          as: 'd',
                          cond: { $eq: ['$$d.startOfDay', '$$day.startOfDay'] },
                        },
                      },
                      as: 'd',
                      in: '$$d.lastLog',
                    },
                  },
                  0,
                ],
              },
            },
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
    {
      $project: {
        _id: 0,
        userId: 1,
        weeks: {
          $map: {
            input: allWeeks,
            as: 'week',
            in: {
              weekStart: '$$week.weekStart',
              weekEnd: '$$week.weekEnd',
              weekStartNumber: '$$week.weekStartNumber',
              weekEndNumber: '$$week.weekEndNumber',
              totalTime: {
                $sum: {
                  $map: {
                    input: '$days',
                    as: 'day',
                    in: {
                      $cond: [
                        {
                          $and: [
                            { $gte: ['$$day.startOfDayNumber', '$$week.weekStartNumber'] },
                            { $lte: ['$$day.startOfDayNumber', '$$week.weekEndNumber'] },
                          ],
                        },
                        '$$day.totalTime',
                        0,
                      ],
                    },
                  },
                },
              },
              lastLog: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: {
                        $map: {
                          input: {
                            $filter: {
                              input: '$days',
                              as: 'd',
                              cond: {
                                $and: [
                                  { $gte: ['$$d.startOfDayNumber', '$$week.weekStartNumber'] },
                                  { $lte: ['$$d.startOfDayNumber', '$$week.weekEndNumber'] },
                                ],
                              },
                            },
                          },
                          as: 'd',
                          in: '$$d.lastLog',
                        },
                      },
                      as: 'log',
                      cond: { $ne: ['$$log', null] },
                    },
                  },
                  -1,
                ],
              },
            },
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
  ];

  const activities = await DaytimeWorkActivityLog.aggregate(aggregation);

  const missingUserIds = usersIds.filter(userId => !activities.find(item => item.userId === userId));

  const missingUserData = missingUserIds.map(userId => ({
    userId,
    weeks: allWeeks.map(week => ({
      weekStart: week.weekStart,
      weekEnd: week.weekEnd,
      totalTime: 0,
      lastLog: null,
    })),
    totalTime: 0,
    lastLog: null,
  }));

  const finalResult = activities.concat(missingUserData);

  return { activities: finalResult, headers: allWeeksHeaders };
};

const getMonthlyActivities = async (usersIds, data) => {
  const filter = {
    $and: [
      { userId: { $in: usersIds } },
      { startTime: { $gte: data.startTime } },
      { startTime: { $lte: data.endTime } },
    ],
  };

  const allMonths = [];
  const allMonthsHeaders = [];
  const currentDate = moment(data.startTime * 1000).tz('UTC').startOf('month');
  const endDate = moment(data.endTime * 1000).tz('UTC');

  while (currentDate < endDate) {
    const month = currentDate.format('MM');
    const monthName = currentDate.format('MMM');
    const year = currentDate.format('YYYY');

    allMonths.push({ startOfMonth: `${month}-${year}`, startOfMonthNumber: currentDate.unix() });
    allMonthsHeaders.push(`${monthName} ${year}`);

    currentDate.add(1, 'month');
  }

  const aggregation = [
    {
      $match: filter,
    },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $group: {
        _id: {
          userId: '$userId',
          startOfMonth: { $dateToString: { format: '%m-%Y', date: '$date' } },
          startOfMonthNumber: {
            $toLong: {
              $dateFromParts: {
                year: { $year: '$date' },
                month: { $month: '$date' },
              }
            }
          },
        },
        totalTime: {
          $sum: {
            $cond: [{ $gt: ['$endTime', 0] }, { $subtract: ['$endTime', '$startTime'] }, 0],
          },
        },
        lastLog: {
          $first: {
            startTime: '$startTime',
            endTime: '$endTime',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id.userId',
        startOfMonth: '$_id.startOfMonth',
        startOfMonthNumber: '$_id.startOfMonthNumber',
        totalTime: { $ifNull: ['$totalTime', 0] },
        lastLog: 1,
      },
    },
    {
      $sort: {
        startOfMonthNumber: -1,
      },
    },
    {
      $group: {
        _id: '$userId',
        months: {
          $push: {
            startOfMonth: '$startOfMonth',
            totalTime: '$totalTime',
            lastLog: '$lastLog',
          },
        },
        totalTime: { $sum: '$totalTime' },
        lastLog: { $first: '$lastLog' },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id',
        months: {
          $map: {
            input: allMonths,
            as: 'month',
            in: {
              month: '$$month',
              totalTime: {
                $sum: {
                  $map: {
                    input: '$months',
                    as: 'm',
                    in: {
                      $cond: [
                        {
                          $and: [
                            { $eq: ['$$m.startOfMonth', '$$month.startOfMonth'] },
                          ],
                        },
                        '$$m.totalTime',
                        0
                      ],
                    },
                  },
                },
              },
              lastLog: {
                $arrayElemAt: [
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: '$months',
                          as: 'm',
                          cond: { $eq: ['$$m.startOfMonth', '$$month.startOfMonth'] },
                        },
                      },
                      as: 'm',
                      in: '$$m.lastLog',
                    },
                  },
                  0,
                ],
              },
            },
          },
        },
        totalTime: 1,
        lastLog: 1,
      },
    },
  ];

  const activities = await DaytimeWorkActivityLog.aggregate(aggregation);

  const missingUserIds = usersIds.filter(userId => !activities.find(item => item.userId === userId));

  const missingUserData = missingUserIds.map(userId => ({
    userId,
    months: allMonths.map(month => ({
      month,
      totalTime: 0,
      lastLog: null,
    })),
    totalTime: 0,
    lastLog: null,
  }));

  const finalResult = activities.concat(missingUserData);

  return { activities: finalResult, headers: allMonthsHeaders };
};

const getCurrentlyWorkingUsersIds = async () => {
  const filter = { $and: [
    { startTime: { $exists: true } },
    { startTime: { $ne: null } },
    { startTime: { $ne: undefined } },
    { $or: [
      { endTime: { $exists: false } },
      { endTime: null },
    ] },
  ] };

  const logs = await DaytimeWorkActivityLog.find(filter, { userId: 1 }).lean();
  const usersIds = logs.map(log => log.userId);

  return usersIds;
};

/**
 * Get activities for all or current/selected user by period
 * @param {object} data - filter
 * */
module.exports = async (data) => {
  const users = await User.aggregate([
    {
      $match: { $or: [{ isdeveloper: 1 }, { issupport: 1 }, { isadmin: 1 }] },
    },
    {
      $project: { _id: 0, id: 1, em: 1, name: 1, isdeveloper: 1, issupport: 1, isadmin: 1 },
    },
    {
      $lookup: {
        from: 'employeeConfig',
        localField: 'id',
        foreignField: 'userId',
        as: 'EmployeeConfig',
      },
    },
    { $unwind: { path: '$EmployeeConfig', preserveNullAndEmptyArrays: true } },
  ]).exec();

  const usersIds = users.map(user => user.id).filter(userId => !IGNORED_USERS_IDS.includes(userId));
  const usersObj = {};
  users.forEach((user) => {
    usersObj[user.id] = user;
  });

  let result;
  switch (data.groupBy) {
    case 'weekly':
      result = await getWeeklyActivities(usersIds, data);
      break;
    case 'monthly':
      result = await getMonthlyActivities(usersIds, data);
      break;
    default:
      result = await getDailyActivities(usersIds, data);
      break;
  }

  const currentlyWorkingUsersIds = await getCurrentlyWorkingUsersIds();
  const sortCompare = (a, b) => {
    if (a.isWorking && !b.isWorking) {
      return -1;
    }
    if (!a.isWorking && b.isWorking) {
      return 1;
    }
    if (a.totalTime > b.totalTime) {
      return -1;
    }
    if (a.totalTime < b.totalTime) {
      return 1;
    }

    return a.userId - b.userId;
  };

  result.activities = result.activities
    .map((activity) => {
      const user = usersObj[activity.userId];
      activity.User = Object.assign({}, user);
      activity.User.email = User.decryptEmail(user.em);
      delete activity.User.em;
      activity.isWorking = !!currentlyWorkingUsersIds.includes(activity.userId);

      return activity;
    })
    .sort(sortCompare);

  return {
    error: 0,
    result,
  };
};
