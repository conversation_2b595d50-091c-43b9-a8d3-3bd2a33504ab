const EmployeeGeneralConfig = require('../../model/audb/EmployeeGeneralConfig');

/**
 * Update employee general config
 * */
module.exports = async (data) => {
  const config = await EmployeeGeneralConfig.findOne({}).exec() || new EmployeeGeneralConfig();

  Object.entries(data).forEach(([key, value]) => {
    config[key] = value;
  });

  await config.save();

  return {
    error: 0,
    config,
  };
};
