const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const { SocketClientNames } = require('../../constants/socket');

module.exports = async (user, userId) => {
  if (!user) throw new Error('Please login');

  await DaytimeWorkActivityLog.findOneAndUpdate(
    {
      $and: [
        { userId },
        { startTime: { $exists: true } },
        { startTime: { $ne: null } },
        { startTime: { $ne: undefined } },
        { $or: [{ endTime: { $exists: false } }, { endTime: null }] },
      ],
    },
    {
      $set: {
        endTime: parseInt(Date.now() / 1000),
      },
    },
    { upsert: false, new: false },
  ).lean();

  const socketDataUser = { data: null, groupName: userId, eventName: 'trackerStopped' };
  global.io.emit('group', socketDataUser);

  const socketDataAdminPanelSingleUser = {
    data: { userId },
    groupName: SocketClientNames.adminPanel,
    eventName: 'employeeActivitiesUpdated',
  };
  global.io.emit('group', socketDataAdminPanelSingleUser);

  const socketDataAdminPanelMultiUsers = { data: null, groupName: SocketClientNames.adminPanel, eventName: 'employeesActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelMultiUsers);

  return {
    error: 0,
    success: true,
  };
};
