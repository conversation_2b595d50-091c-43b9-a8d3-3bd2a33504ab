const EmployeePaymentHistory = require('../../model/audb/EmployeePaymentHistory');
const { SocketClientNames } = require('../../constants/socket');

/**
 * Check existing records into selected range, exclude current by _id
 * */
const isExistsRecordsInRange = async (filter) => {
  const rangeActivities = await EmployeePaymentHistory.find(filter).exec();

  return !!rangeActivities.length;
};

module.exports = async (body, user) => {
  if (!body) throw new Error('Wrong body params');
  if (!body.userId) throw new Error('User ID is require');
  if (!body.startTime) throw new Error('startTime is require');
  if (!body.endTime) throw new Error('endTime is require');
  if (!body.hours) throw new Error('hours is require');
  if (!body.amount) throw new Error('amount is require');

  const isUpdate = !!body._id;
  let paymentHistory;

  if (isUpdate) {
    paymentHistory = await EmployeePaymentHistory.findOne({ _id: body._id }).exec();

    if (!paymentHistory) throw new Error('PaymentHistory not found');
  } else {
    paymentHistory = new EmployeePaymentHistory({ userId: body.userId });
  }

  const filter = {
    $and: [
      { userId: body.userId },
      {
        $or: [
          {
            startTime: {
              $lte: body.startTime,
            },
            endTime: {
              $gte: body.endTime,
            },
          },
          {
            startTime: {
              $gte: body.startTime,
            },
            endTime: {
              $lte: body.endTime,
            },
          },
          {
            $and: [
              {
                startTime: {
                  $gte: body.startTime,
                },
              },
              {
                startTime: {
                  $lte: body.endTime,
                },
              },
            ],
          },
          {
            $and: [
              {
                endTime: {
                  $gte: body.startTime,
                },
              },
              {
                endTime: {
                  $lte: body.endTime,
                },
              },
            ],
          },
        ],
      },
    ],
  };

  if (isUpdate) {
    filter.$and.push({ _id: { $ne: body._id } });
  }

  const rangeExists = await isExistsRecordsInRange(filter);

  if (rangeExists) throw new Error('Time range covered another time, please change the time range');

  paymentHistory.startTime = body.startTime;
  paymentHistory.endTime = body.endTime;
  paymentHistory.hours = body.hours;
  paymentHistory.amount = body.amount;
  paymentHistory.comment = body.comment;
  paymentHistory.walletAddress = body.walletAddress || '';
  paymentHistory.walletAddressInfo = body.walletAddressInfo || '';
  paymentHistory.addedByUid = user.id;
  await paymentHistory.save();

  const socketDataAdminPanelMultiUsers = { data: null, groupName: SocketClientNames.adminPanel, eventName: 'employeesPaymentHistoryUpdated' };
  global.io.emit('group', socketDataAdminPanelMultiUsers);

  const socketDataAdminPanelSingleUserActivities = { data: { userId: body.userId }, groupName: SocketClientNames.adminPanel, eventName: 'employeeActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelSingleUserActivities);

  const socketDataAdminPanelMultiUsersActivities = { data: null, groupName: SocketClientNames.adminPanel, eventName: 'employeesActivitiesUpdated' };
  global.io.emit('group', socketDataAdminPanelMultiUsersActivities);

  return {
    error: 0,
    success: true,
  };
};
