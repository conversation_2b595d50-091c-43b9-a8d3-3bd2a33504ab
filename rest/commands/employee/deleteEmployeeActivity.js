const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const sendActivitySocketMessages = require('./helpers/sendActivitySocketMessages');

module.exports = async (_id, user) => {
  if (!_id) throw new Error('ID is require');

  const activity = await DaytimeWorkActivityLog.findOne({ _id }).exec();

  if (!activity) throw new Error('Work activity already removed');

  await activity.remove();

  sendActivitySocketMessages(activity.userId, user, true);

  return {
    error: 0,
    success: true,
  };
};
