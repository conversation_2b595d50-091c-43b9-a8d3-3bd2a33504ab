const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');

const formatComment = (comment, textForRemove) => {
  const startIndex = comment.indexOf(textForRemove);

  if (startIndex !== -1) {
    try {
      comment = comment.substring(0, startIndex).trim();
    } catch (e) {}
  }

  return comment;
};

/**
 * Get last employee activity
 * @param {object} user - current employee
 * */
module.exports = async (user) => {
  const aggregation = [
    { $match: { $and: [
      { userId: user.id },
      { comment: { $exists: true } },
      { comment: { $ne: null } },
      { comment: { $ne: '' } },
    ] } },
    {
      $sort: {
        startTime: -1,
      },
    },
    {
      $limit: 100,
    },
    {
      $group: {
        _id: '$comment',
        latestCommentId: { $first: '$_id' },
      },
    },
    {
      $sort: {
        latestCommentId: -1,
      },
    },
    {
      $limit: 20,
    },
  ];
  const commentsResult = await DaytimeWorkActivityLog.aggregate(aggregation);

  const regexps = ['Tracker automatically stopped, you reached daily limit', 'Stopped, because of inactive for a'];
  const comments = commentsResult.map((result) => {
    let comment = result._id;
    regexps.forEach((regex) => {
      comment = formatComment(comment, regex);
    });

    return comment;
  });

  // remove duplicates after replacing text
  const uniqueCommentsWithIndex = comments.reduce((acc, comment, index) => {
    if (!comment.trim() || acc.some(item => item.comment === comment)) {
      return acc;
    }

    acc.push({ comment, index });

    return acc;
  }, []);
  const sortedUniqueComments = uniqueCommentsWithIndex.sort((a, b) => a.index - b.index);
  const lastUniqueComments = sortedUniqueComments.map(item => item.comment).slice(0, 10);

  return {
    error: 0,
    comments: lastUniqueComments,
  };
};
