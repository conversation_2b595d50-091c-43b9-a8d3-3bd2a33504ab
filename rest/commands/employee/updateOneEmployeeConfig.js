const EmployeeConfig = require('../../model/audb/EmployeeConfig');

/**
 * Update employee config by user ID
 * @param {object} data
 * */
module.exports = async (data) => {
  let employeeConfig = await EmployeeConfig.findOne({ userId: data.userId }).exec();

  if (!employeeConfig) {
    employeeConfig = new EmployeeConfig({ userId: data.userId });
  }

  Object.entries(data).forEach(([key, value]) => {
    if (key !== '_id' && key !== 'userId') employeeConfig[key] = value;
  });

  await employeeConfig.save();

  return {
    error: 0,
    employeeConfig,
  };
};
