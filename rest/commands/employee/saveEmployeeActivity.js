const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const { DAYTIME_WORK_ACTIVITY_TYPES } = require('../../constants/employee');
const sendActivitySocketMessages = require('./helpers/sendActivitySocketMessages');
const saveApprovedActivity = require('./helpers/saveApprovedActivity');

/**
 * Check existing records into selected range, exclude current by _id
 * */
const isExistsRecordsInRange = async (filter) => {
  const rangeActivities = await DaytimeWorkActivityLog.find(filter).exec();

  return !!rangeActivities.length;
};

const hasDateChanged = (time1, time2) => {
  const previousDate = new Date(time1 * 1000);
  const currentDate = new Date(time2 * 1000);

  const previousUTCDate = previousDate.getUTCDate();
  const previousUTCMonth = previousDate.getUTCMonth();
  const previousUTCYear = previousDate.getUTCFullYear();

  const currentUTCDate = currentDate.getUTCDate();
  const currentUTCMonth = currentDate.getUTCMonth();
  const currentUTCYear = currentDate.getUTCFullYear();

  return (
    previousUTCDate !== currentUTCDate
    || previousUTCMonth !== currentUTCMonth
    || previousUTCYear !== currentUTCYear
  );
};

module.exports = async (body, userId, user) => {
  if (!userId) throw new Error('User ID is require');
  if (!body) throw new Error('Wrong body params');
  if (!body.newStartTime) throw new Error('newStartTime is require');
  if (!body.newEndTime) throw new Error('newEndTime is require');

  const isUpdate = !!body._id;
  let activity;

  if (isUpdate) {
    activity = await DaytimeWorkActivityLog.findOne({ _id: body._id }).exec();

    if (!activity) throw new Error('Work activity not found');
    // if range not changed update comment only
    if (activity.startTime === body.newStartTime && activity.endTime === body.newEndTime) {
      if (activity.comment === body.comment) {
        return {
          error: 0,
          success: true,
        };
      }

      activity.comment = body.comment;
      await activity.save();

      return {
        error: 0,
        success: true,
      };
    }
  } else {
    activity = new DaytimeWorkActivityLog({ userId, type: DAYTIME_WORK_ACTIVITY_TYPES.manual });
  }
  if (!(user.isadmin || user.issuperadmin) && !body.editReasonComment) {
    throw new Error(`Reason for ${isUpdate ? 'edit' : 'update'} work activity time is require`);
  }

  const filter = {
    $and: [
      { userId },
      {
        $or: [
          {
            startTime: {
              $lte: body.newStartTime,
            },
            endTime: {
              $gte: body.newEndTime,
            },
          },
          {
            startTime: {
              $gte: body.newStartTime,
            },
            endTime: {
              $lte: body.newEndTime,
            },
          },
          {
            $and: [
              {
                startTime: {
                  $gte: body.newStartTime,
                },
              },
              {
                startTime: {
                  $lte: body.newEndTime,
                },
              },
            ],
          },
          {
            $and: [
              {
                endTime: {
                  $gte: body.newStartTime,
                },
              },
              {
                endTime: {
                  $lte: body.newEndTime,
                },
              },
            ],
          },
        ],
      },
    ],
  };

  if (isUpdate) {
    filter.$and.push({ _id: { $ne: body._id } });
  }

  const rangeExists = await isExistsRecordsInRange(filter);

  if (rangeExists) throw new Error('Time range covered another time, please change the time range');
  if (user.isadmin || user.issuperadmin) {
    await saveApprovedActivity(userId, activity, body, isUpdate);
  } else {
    // if changed to less time (but the time is in the previous range) and date is the same, no need to approve
    if (!isUpdate
      || (body.newEndTime - body.newStartTime > activity.endTime - activity.startTime)
      || body.newStartTime < activity.startTime || body.newEndTime > activity.endTime
      || hasDateChanged(activity.startTime, body.newStartTime)) {
      if (!isUpdate) {
        activity.date = new Date(body.newStartTime * 1000);
        // activity.startTime = body.newStartTime;
        // activity.endTime = body.newEndTime;
      }

      activity.newDate = new Date(body.newStartTime * 1000);
      activity.newStartTime = body.newStartTime;
      activity.newEndTime = body.newEndTime;
      activity.comment = body.comment;
      activity.editReasonComment = body.editReasonComment;
    } else {
      activity.date = new Date(body.newStartTime * 1000);
      activity.startTime = body.newStartTime;
      activity.endTime = body.newEndTime;
      activity.comment = body.comment;

      if (activity.newDate) activity.newDate = undefined;
      if (activity.newStartTime) activity.newStartTime = undefined;
      if (activity.newEndTime) activity.newEndTime = undefined;
      if (activity.editReasonComment) activity.editReasonComment = undefined;
    }

    await activity.save();
  }

  sendActivitySocketMessages(userId, user, true);

  return {
    error: 0,
    success: true,
  };
};
