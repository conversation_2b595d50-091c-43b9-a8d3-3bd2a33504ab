const moment = require('moment-timezone');
const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const User = require('../../model/audb/User');
const { IGNORED_USERS_IDS } = require('../../constants/employee');

const getPaymentHistory = async (usersIds, data) => {
  const allDays = [];
  const allDaysHeaders = [];
  const currentDayStart = moment(data.startTime * 1000).tz('UTC');
  const endMonth = moment(data.endTime * 1000).tz('UTC');

  while (currentDayStart < endMonth) {
    allDays.push(currentDayStart.format('DD-MM-YYYY'));

    const day = currentDayStart.date();
    const monthName = currentDayStart.format('MMM');
    const year = currentDayStart.format('YYYY');

    allDaysHeaders.push(`${day} ${monthName} ${year}`);

    currentDayStart.add(1, 'day');
  }

  const aggregation = [
    {
      $match: {
        $and: [
          { userId: { $in: usersIds } },
          { startTime: { $gte: data.startTime } },
          { endTime: { $lte: data.endTime } },
        ],
      },
    },
    {
      $lookup: {
        from: 'employeePaymentHistory',
        let: { userId: '$userId', startTime: '$startTime', endTime: '$endTime' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$userId', '$$userId'] },
                  { $or: [
                    { $and: [
                      { $gte: ['$$startTime', '$startTime'] },
                      { $lte: ['$$endTime', '$endTime'] },
                    ] },
                  ] },
                ],
              },
            },
          },
        ],
        as: 'paymentHistory',
      },
    },
    {
      $group: {
        _id: {
          userId: '$userId',
          startOfDay: { $dateToString: { format: '%d-%m-%Y', date: '$date' } },
        },
        paymentHistory: {
          $first: '$paymentHistory',
        },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id.userId',
        startOfDay: '$_id.startOfDay',
        isPaid: { $gt: [{ $size: '$paymentHistory' }, 0] },
      },
    },
    {
      $sort: {
        startOfDay: 1,
      },
    },
    {
      $group: {
        _id: '$userId',
        days: {
          $push: {
            startOfDay: '$startOfDay',
            isPaid: '$isPaid',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        userId: '$_id',
        days: {
          $map: {
            input: allDays,
            as: 'day',
            in: {
              startOfDay: '$$day',
              isPaid: {
                $cond: [
                  { $in: ['$$day', '$days.startOfDay'] },
                  {
                    $let: {
                      vars: {
                        matchedDay: {
                          $arrayElemAt: [
                            {
                              $filter: {
                                input: '$days',
                                as: 'd',
                                cond: { $eq: ['$$d.startOfDay', '$$day'] },
                              },
                            },
                            0,
                          ],
                        },
                      },
                      in: '$$matchedDay.isPaid',
                    },
                  },
                  null,
                ],
              },
            },
          },
        },
      },
    },
    {
      $sort: {
        userId: 1,
      },
    },
  ];

  const activities = await DaytimeWorkActivityLog.aggregate(aggregation);

  const missingUserIds = usersIds.filter(userId => !activities.find(item => item.userId === userId));

  const missingUserData = missingUserIds.map(userId => ({
    userId,
    days: allDays.map(day => ({
      startOfDay: day,
      isPaid: null,
    })),
  }));

  const finalResult = activities.concat(missingUserData);

  return { paymentHistories: finalResult, headers: allDaysHeaders };
};

/**
 * Get activities awaiting for approval
 * */
module.exports = async (data) => {
  const users = await User.find(
    { $or: [{ isdeveloper: 1 }, { issupport: 1 }, { isadmin: 1 }] },
    { _id: 0, id: 1, em: 1, name: 1, isdeveloper: 1, issupport: 1, isadmin: 1 },
  ).lean();
  const usersIds = data.userId ? [data.userId] : users.map(user => user.id).filter(userId => !IGNORED_USERS_IDS.includes(userId));
  const usersObj = {};
  users.forEach((user) => {
    usersObj[user.id] = user;
  });

  const result = await getPaymentHistory(usersIds, data);

  const formattedPaymentHistories = result.paymentHistories
    .map((log) => {
      const user = usersObj[log.userId];
      log.User = Object.assign({}, user);
      log.User.email = User.decryptEmail(user.em);
      delete log.User.em;

      return log;
    });

  return {
    error: 0,
    paymentHistories: formattedPaymentHistories,
    headers: result.headers,
  };
};
