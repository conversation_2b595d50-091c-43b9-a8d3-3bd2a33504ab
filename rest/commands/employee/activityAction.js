const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const sendActivitySocketMessages = require('./helpers/sendActivitySocketMessages');
const saveApprovedActivity = require('./helpers/saveApprovedActivity');

const approveActivity = async (activity) => {
  await saveApprovedActivity(activity.userId, activity, activity, true);

  return { success: true };
};

const declineActivity = async (activity) => {
  await activity.delete();

  return { success: true };
};

/**
 * @param {object} data
 * @param {object} adminUser
 * */
module.exports = async (data, adminUser) => {
  if (!data.action) throw new Error('Action is require');
  if (!data._id) throw new Error('ID is require');

  const activity = await DaytimeWorkActivityLog.findOne({ _id: data._id }).exec();

  if (!activity) throw new Error('Work activity not found');

  let result = { success: false };

  switch (data.action) {
    case 'approve':
      result = await approveActivity(activity);
      break;
    case 'decline':
      result = await declineActivity(activity);
      break;
    default:
      throw new Error('Wrong action');
  }

  if (result && result.success) {
    sendActivitySocketMessages(activity.userId, adminUser, true);
  }

  return {
    error: 0,
    result,
  };
};
