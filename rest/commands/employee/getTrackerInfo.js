const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');

/**
 * Get last employee activity
 * @param {object} user - current employee
 * */
module.exports = async (user) => {
  const filter = { $and: [
    { userId: user.id },
    { startTime: { $exists: true } },
    { startTime: { $ne: null } },
    { startTime: { $ne: undefined } },
    { $or: [
      { endTime: { $exists: false } },
      { endTime: null },
    ] },
  ] };
  const activity = await DaytimeWorkActivityLog.findOne(filter).lean();

  return {
    error: 0,
    activity,
  };
};
