const DaytimeWorkActivityLog = require('../../model/audb/DaytimeWorkActivityLog');
const User = require('../../model/audb/User');

const getAwaitingApprovalActivities = async () => {
  const activities = await DaytimeWorkActivityLog.find({ $or: [{ newStartTime: { $exists: true } }, { newEndTime: { $exists: true } }] })
    .lean();

  return activities;
};

/**
 * Get activities awaiting for approval
 * */
module.exports = async () => {
  const users = await User.find(
    { $or: [{ isdeveloper: 1 }, { issupport: 1 }, { isadmin: 1 }] },
    { _id: 0, id: 1, em: 1, name: 1, isdeveloper: 1, issupport: 1, isadmin: 1 },
  ).sort({ _id: -1 }).lean();
  const usersObj = {};
  users.forEach((user) => {
    usersObj[user.id] = user;
  });

  const foundActivities = await getAwaitingApprovalActivities();

  const activities = foundActivities.map((activity) => {
    const user = usersObj[activity.userId];
    activity.User = Object.assign({}, user);
    activity.User.email = User.decryptEmail(user.em);
    delete activity.User.em;

    return activity;
  });

  return {
    error: 0,
    activities,
  };
};
