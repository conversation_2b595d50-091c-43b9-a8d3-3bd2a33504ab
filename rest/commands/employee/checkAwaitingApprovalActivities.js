const hasAwaitingApprovalActivities = require('./hasAwaitingApprovalActivities');
const { SocketClientNames } = require('../../constants/socket');
const { ADMIN_PANEL_NOTIFICATONS } = require('../../constants/notification');

module.exports = async () => {
  const existsActivitiesForApproval = await hasAwaitingApprovalActivities();
  const data = ADMIN_PANEL_NOTIFICATONS.approveEmployeeWorkActivity;

  if (existsActivitiesForApproval) {
    data.action = 'show';
  } else {
    data.action = 'hide';
  }

  const socketDataAdminPanelMultiUsers = { data, groupName: SocketClientNames.adminPanel, eventName: 'employeesActivitiesAwaitingApproval' };
  global.io.emit('group', socketDataAdminPanelMultiUsers);
};
