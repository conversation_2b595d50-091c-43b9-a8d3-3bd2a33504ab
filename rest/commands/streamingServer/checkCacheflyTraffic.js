const axios = require('axios');
const moment = require('moment');

// https://api.cachefly.com/api/2.5/reports/hod?from=2024-12-11&to=2024-12-13&groupBy=uid
const API_URL = 'https://api.cachefly.com/api/2.5';
const HEADERS = {
  accept: 'application/json',
};

// 2023-04-01T00:00:00Z
const getCurrentMonth = () => {
  const startOfMonth = `${moment().locale('en').startOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;
  const endOfMonth = `${moment().locale('en').endOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;

  return { startOfMonth, endOfMonth };
};

/**
 * Get Cachefly traffic
 *
 * @param {string} apiKey
 * @param {string} startOfMonth
 * @param {string} endOfMonth
 * */
const getTraffic = async (apiKey, startOfMonth, endOfMonth) => {
  try {
    const headers = HEADERS;
    headers.Authorization = `Bearer ${apiKey}`;
    const url = `${API_URL}/reports/hod?from=${startOfMonth}&to=${endOfMonth}&groupBy=uid`;
    const response = await axios.get(url, { headers });

    if (response && response.status === 200 && response.data && response.data) {
      return response.data;
    }
  } catch (e) {
    console.error('Check Cachefly streaming server traffic error:', e);
  }

  return false;
};

/**
 * Check server used traffic from the Cachefly source
 *
 * @param {string} apiKey
 * 
 * @returns {number|false} total bytes during the last month or false
 * */
module.exports = async (apiKey) => {
  const { startOfMonth, endOfMonth } = getCurrentMonth();
  const trafficResult = await getTraffic(apiKey, startOfMonth, endOfMonth);

  if (trafficResult && trafficResult.data && trafficResult.data[0] && trafficResult.data[0].gigsTotal) {
    const totalTraffic = trafficResult.data[0].gigsTotal * 1000 * 1000 * 1000;

    return totalTraffic;
  }

  return false;
};

