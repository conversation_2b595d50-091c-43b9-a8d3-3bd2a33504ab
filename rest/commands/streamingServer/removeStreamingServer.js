const UserConfig = require('../../model/audb/UserConfig');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (_id) => {
  // import there to prevent circular dependencies
  const StreamingServerISP = require('../../model/audb/StreamingServerISP');
  const StreamingServerCountry = require('../../model/audb/StreamingServerCountry');
  const StreamingServerMap = require('../../model/audb/StreamingServerMap');
  const StreamingServerMapAuto = require('../../model/audb/StreamingServerMapAuto');
  const StreamingServerState = require('../../model/audb/StreamingServerState');
  const StreamingServerLoadBalancer = require('../../model/audb/StreamingServerLoadBalancer');
  const StreamingServerGroup = require('../../model/audb/StreamingServerGroup');

  await UserConfig.updateMany(
    { sss: { $in: [_id] } },
    { $pull: { sss: _id } },
    { upsert: false },
  );
  await UserConfig.updateMany(
    { speed: _id },
    { $unset: { speed: 1 } },
    { upsert: false },
  );
  await StreamingServerMap.deleteMany({ $or: [{ mapfrom: _id }, { mapto: _id }] }).exec();
  await StreamingServerMapAuto.updateMany(
    { urgent: { $in: [_id] } },
    { $pull: { urgent: _id } },
    { upsert: false },
  );
  await StreamingServerMapAuto.deleteOne({ _id }).exec();
  await StreamingServerState.updateMany(
    { sss: { $in: [_id] } },
    { $pull: { sss: _id } },
    { upsert: false },
  );
  await StreamingServerState.deleteMany({ sss: [] }).exec();
  await StreamingServerLoadBalancer.updateMany(
    { 'streamingServers._id': { $in: [_id] } },
    { $pull: { streamingServers: { _id } } },
    { upsert: false },
  );
  await StreamingServerLoadBalancer.deleteMany({ $or: [{ streamingServers: [] }, { streamingServers: { $size: 1 } }] }).exec();
  await StreamingServerCountry.updateMany(
    { sss: { $in: [_id] } },
    { $pull: { sss: _id } },
    { upsert: false },
  );
  await StreamingServerCountry.deleteMany({ sss: [] }).exec();
  await StreamingServerISP.updateMany(
    { sss: { $in: [_id] } },
    { $pull: { sss: _id } },
    { upsert: false },
  );
  await StreamingServerISP.deleteMany({ sss: [] }).exec();
  await StreamingServerGroup.findOneAndUpdate(
    { 'sss._id': _id },
    { $pull: { sss: { _id } } },
    { upsert: false },
  );
  await removeRedisCacheByKeys(['']);

  return false;
};

