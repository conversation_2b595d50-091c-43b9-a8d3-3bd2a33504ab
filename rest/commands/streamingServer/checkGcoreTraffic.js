const axios = require('axios');
const moment = require('moment');

// https://api.gcore.com/cdn/statistics/aggregate/stats?flat=true&from=2024-12-13&to=2024-12-14&group_by=client&metrics=total_bytes&service=CDN
const API_URL = 'https://api.gcore.com';
const HEADERS = {
  accept: 'application/json',
};

// 2023-04-01T00:00:00Z
const getCurrentMonth = () => {
  const startOfMonth = `${moment().locale('en').startOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;
  const endOfMonth = `${moment().locale('en').endOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;

  return { startOfMonth, endOfMonth };
};

/**
 * Get Gcore traffic
 *
 * @param {string} apiKey
 * @param {string} startOfMonth
 * @param {string} endOfMonth
 * */
const getTraffic = async (apiKey, startOfMonth, endOfMonth) => {
  try {
    const headers = HEADERS;
    headers.Authorization = `APIKey ${apiKey}`;
    const url = `${API_URL}/cdn/statistics/aggregate/stats?from=${startOfMonth}&to=${endOfMonth}&flat=true&group_by=client&metrics=total_bytes&service=CDN`;
    const response = await axios.get(url, { headers });

    if (response && response.status === 200 && response.data && response.data) {
      return response.data;
    }
  } catch (e) {
    console.error('Check Gcore streaming server traffic error:', e);
  }

  return false;
};

/**
 * Check server used traffic from the Gcore source
 *
 * @param {string} apiKey
 * 
 * @returns {number|false} total bytes during the last month or false
 * */
module.exports = async (apiKey) => {
  const { startOfMonth, endOfMonth } = getCurrentMonth();
  const trafficResult = await getTraffic(apiKey, startOfMonth, endOfMonth);

  if (trafficResult && trafficResult[0] && trafficResult[0].metrics) {
    const totalTraffic = trafficResult[0].metrics.total_bytes;

    return totalTraffic;
  }

  return false;
};

