const axios = require('axios');
const qs = require('query-string');
const moment = require('moment');

const API_URL = 'https://striketracker3.highwinds.com';
const HEADERS = {
  accept: 'application/json',
  'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.139 Safari/537.36',
};

// 2023-04-01T00:00:00Z
const getCurrentMonth = () => {
  const startOfDay = `${moment().locale('en').startOf('month').format('YYYY-MM-DDTHH:mm:ss')}Z`;
  const endOfDay = `${moment().locale('en').endOf('month').format('YYYY-MM-DDTHH:mm:ss')}Z`;

  return { startOfDay, endOfDay };
};

const login = async (username, password) => {
  try {
    const data = {
      grant_type: 'password',
      username,
      password,
    };

    const response = await axios.request({
      method: 'post',
      headers: HEADERS,
      data: qs.stringify(data),
      url: `${API_URL}/auth/token`,
    });

    if (response && response.status === 200 && response.data) return response.data;
  } catch (e) {
    console.error('Check Highwinds streaming server traffic Scheduler::Cannot login', e);
  }

  return false;
};

const parseMetrics = (names, values) => {
  const metrics = {};

  for (let i = 0; i < names.length; ++i) {
    metrics[names[i]] = values[i];
  }

  return metrics;
};

const getAccountStatistics = async (token, accountHash, startDate, endDate) => {
  try {
    const headers = HEADERS;
    headers.Authorization = token;
    // 3 - "HTTP Caching" platform
    // 34 - "HTTPS Caching (SDS)" platform
    const query = `startDate=${startDate}&endDate=${endDate}&platforms=3,34&granularity=P1M`;
    const url = `${API_URL}/api/accounts/${accountHash}/analytics/transfer?${query}`;

    const response = await axios.get(url, { headers });

    if (response && response.status === 200 && response.data && response.data.series && response.data.series.length) {
      return response.data;
    }
  } catch (e) {
    console.error('Check Highwinds streaming server traffic::Cannot getCustomerStatistics', e);
  }

  return false;
};

/**
 * Check server used traffic from the Highwinds source
 *
 * @param {string} user
 * @param {string} password
 * @param {string} accountHash - like account ID from the Highwinds admin panel
 * */
module.exports = async (user, password, accountHash) => {
  if (!user || !password || !accountHash) return false;

  const authResponse = await login(user, password);

  if (authResponse) {
    const tokenType = 'Bearer';
    const accessToken = authResponse.access_token;
    const token = `${tokenType} ${accessToken}`;

    const { startOfDay, endOfDay } = getCurrentMonth();
    const statisticsResponse = await getAccountStatistics(token, accountHash, startOfDay, endOfDay);

    if (statisticsResponse && statisticsResponse.series && statisticsResponse.series.length) {
      let totalTraffic = 0;

      const metrics = parseMetrics(statisticsResponse.series[0].metrics, statisticsResponse.series[0].data[0]);

      if (metrics.hasOwnProperty('xferUsedTotalMB')) {
        totalTraffic = Math.round(metrics.xferUsedTotalMB * 1000 * 1000);
      }

      return totalTraffic;
    }
  }

  return false;
};

