const checkSynedgeTraffic = require('./checkSynedgeTraffic');
const checkHighwindsTraffic = require('./checkHighwindsTraffic');
const checkEdgecastTraffic = require('./checkEdgecastTraffic');
const checkGcoreTraffic = require('./checkGcoreTraffic');
const checkCacheflyTraffic = require('./checkCacheflyTraffic');

/**
 * Get server used traffic
 * @param {object} server
 * 
 * @returns {number|false} total bytes during the last month or false
 * */
module.exports = async (server) => {
  if (server) {
    switch (server.trafficProvider) {
      case 'synedge':
        if (!server.trafficLogin || !server.trafficPassword) return false;

        return checkSynedgeTraffic(server.trafficLogin, server.trafficPassword);
      case 'highwinds':
        if (!server.trafficLogin || !server.trafficPassword || !server.trafficAccountHash) return false;

        return checkHighwindsTraffic(server.trafficLogin, server.trafficPassword, server.trafficAccountHash);
      case 'edgecast':
        if (!server.trafficApiKey || !server.trafficAccountNumber) return false;

        return checkEdgecastTraffic(server.trafficApiKey, server.trafficAccountNumber);
      case 'gcore':
        if (!server.trafficApiKey) return false;

        return checkGcoreTraffic(server.trafficApiKey);
      case 'cachefly':
        if (!server.trafficApiKey) return false;

        return checkCacheflyTraffic(server.trafficApiKey);
      default:
        break;
    }
  }

  return false;
};

