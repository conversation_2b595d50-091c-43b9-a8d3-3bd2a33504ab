const axios = require('axios');
const moment = require('moment');

const API_URL = 'https://api.edgecast.com/v2';
const HEADERS = {
  accept: 'application/json',
  host: 'api.edgecast.com',
  'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.139 Safari/537.36',
};

// 2023-04-01T00:00:00Z
const getCurrentMonth = () => {
  const startOfMonth = `${moment().locale('en').startOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;
  const endOfMonth = `${moment().locale('en').endOf('month').format('YYYY-MM-DDTHH:mm:ss')}`;

  return { startOfMonth, endOfMonth };
};

/**
 * Get Edgecast traffic
 *
 * @param {string} apiKey
 * @param {string} accountNumber
 * @param {string} startOfMonth
 * @param {string} endOfMonth
 * */
const getTraffic = async (apiKey, accountNumber, startOfMonth, endOfMonth) => {
  try {
    const headers = HEADERS;
    headers.Authorization = `TOK:${apiKey}`;
    const url = `${API_URL}/reporting/customers/${accountNumber}/bytestransferred?begindate=${startOfMonth}&enddate=${endOfMonth}`;
    const response = await axios.get(url, { headers });

    if (response && response.status === 200 && response.data && response.data) {
      return response.data;
    }
  } catch (e) {
    console.error('Check Edgecast streaming server traffic error:', e);
  }

  return false;
};

/**
 * Check server used traffic from the Edgecast source
 *
 * @param {string} apiKey
 * @param {string} accountNumber - like account ID from the Edgecast admin panel
 * */
module.exports = async (apiKey, accountNumber) => {
  const { startOfMonth, endOfMonth } = getCurrentMonth();
  const trafficResult = await getTraffic(apiKey, accountNumber, startOfMonth, endOfMonth);

  if (trafficResult && trafficResult.Bytes) {
    const totalTraffic = trafficResult.Bytes;

    return totalTraffic;
  }

  return false;
};

