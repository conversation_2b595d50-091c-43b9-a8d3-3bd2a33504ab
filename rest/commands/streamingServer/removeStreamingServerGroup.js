const removeStreamingServer = require('./removeStreamingServer');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (_id) => {
  // import there because it replaced from the removeStreamingServer with object instead of model
  const StreamingServerGroup = require('../../model/audb/StreamingServerGroup');

  const streamingServerGroup = await StreamingServerGroup.findOne({ _id }).lean();

  if (!streamingServerGroup) return false;
  if (streamingServerGroup.hasOwnProperty('sss') && streamingServerGroup.sss && streamingServerGroup.sss.length) {
    const promises = streamingServerGroup.sss.map(async (server) => {
      await removeStreamingServer(server._id);
    });
    await Promise.all(promises);
  }

  await removeStreamingServer(_id);
  await StreamingServerGroup.deleteOne({ _id });
  await removeRedisCacheByKeys(['']);

  return true;
};

