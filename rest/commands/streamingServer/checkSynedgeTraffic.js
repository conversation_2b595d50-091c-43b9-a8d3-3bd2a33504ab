const axios = require('axios');
const qs = require('query-string');
const moment = require('moment');

const API_URL = 'https://api.synedge.com';
const HEADERS = {
  accept: 'application/json',
  'Content-Type': 'application/x-www-form-urlencoded',
};

function getCurrentMonth() {
  const startOfDay = moment().locale('en').startOf('month').format('YYYY-MM-DDTHH:mm:ss');
  const endOfDay = moment().locale('en').endOf('month').format('YYYY-MM-DDTHH:mm:ss');

  return { startOfDay, endOfDay };
}

async function login(username, password) {
  try {
    const data = {
      username,
      password,
    };

    const response = await axios.request({
      method: 'post',
      headers: HEADERS,
      data: qs.stringify(data),
      url: `${API_URL}/login`,
    });

    if (response && response.status === 200 && response.data) return response.data;
  } catch (e) {
    console.error('Check Synedge streaming server traffic Scheduler::Cannot login', e);
  }

  return false;
}

async function getCustomers(token) {
  try {
    const headers = HEADERS;
    headers.authorization = token;

    const response = await axios.request({
      method: 'get',
      headers,
      url: `${API_URL}/customers`,
    });

    if (response && response.status === 200 && response.data && response.data.response) return response.data.response;
  } catch (e) {
    console.error('Check Synedge streaming server traffic Scheduler::Cannot getCustomers', e);
  }

  return false;
}

async function getCustomerStatistics(token, customerId, start, end, metric) {
  try {
    const headers = HEADERS;
    headers.authorization = token;
    const data = { start, end, metric, granularity: '1d' };
    const query = qs.stringify(data);

    const response = await axios.request({
      method: 'get',
      headers,
      url: `${API_URL}/${customerId}/statistics/customer/totals?${query}`,
    });

    if (response && response.status === 200 && response.data && response.data.response) return response.data.response;
  } catch (e) {
    console.error('Check Synedge streaming server traffic error', e);
  }

  return false;
}

/**
 * Check server used traffic from the Synedge source
 *
 * @param {string} user
 * @param {string} password
 * */
module.exports = async (user, password) => {
  if (!user || !password) return false;

  const authResponse = await login(user, password);

  if (authResponse) {
    const tokenType = authResponse.token_type;
    const accessToken = authResponse.access_token;
    const token = `${tokenType} ${accessToken}`;

    const customers = await getCustomers(token);

    if (customers && customers.length) {
      const customer = customers[0];
      const { startOfDay, endOfDay } = getCurrentMonth();
      const statisticsResponse = await getCustomerStatistics(token, customer.id, startOfDay, endOfDay, 'datatransfer');

      if (statisticsResponse && statisticsResponse.series && statisticsResponse.series.length
        && statisticsResponse.series[0].all && statisticsResponse.series[0].all.length) {
        let totalTraffic = 0;
        statisticsResponse.series[0].all.forEach(serie => Object.entries(serie).forEach(([, traffic]) => { totalTraffic += traffic; }));

        return totalTraffic;
      }
    }
  }

  return false;
};

