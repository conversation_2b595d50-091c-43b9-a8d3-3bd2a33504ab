const {
  InvalidInputFieldError,
  RegisterEmailExistsError,
  RegisterEmailBlockedError,
  RegisterClosedError,
} = require('@s1/api-errors');
const RegisterValidator = require('../../helpers/RegisterValidator');
const User = require('../../model/audb/User');
const UserConfig = require('../../model/audb/UserConfig');
const BlockedEmail = require('../../model/audb/BlockedEmail');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');
const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');
const saveUserIp = require('../user/saveUserIp');
const getPaymentBlacklistGeneralConfig = require('../paymentBlacklist/getGeneralConfig');
const noReplyEmail = require('../../../config').email.noReply;

module.exports = async ({ ip, referral, email, locale, name, password, repassword, fingerprint, fingerprintPayment,
  macaddress, cookiekey, storagekey, flashkey, phone, phonearea, userAgent = null }) => {
  const validator = new RegisterValidator();
  const validationResults = await validator.validate({ name, email, password, repassword, phone, phonearea, referral });

  if (validationResults.error.length) throw new InvalidInputFieldError({ result: validationResults }, locale);

  const emailLower = email.toLowerCase();
  const existingUser = await User.findOne()
    .byEmail(emailLower)
    .exec();

  if (existingUser && existingUser.isNgateUser) throw new RegisterClosedError(locale);
  if (existingUser) throw new RegisterEmailExistsError(locale);
  if (await BlockedEmail.isBlocked(emailLower)) throw new RegisterEmailBlockedError(locale);
  if (!await User.canRegister() && !referral) throw new RegisterClosedError(locale);

  const defaultPermissionGroup = await UserPermissionGroup.findOne({ isUserDefaultGroup: true }).lean().cache(3600, 'userPermissionGroup_default');
  const userPermissionGroupIds = [];

  if (defaultPermissionGroup) userPermissionGroupIds.push(defaultPermissionGroup._id.toString());

  const createdUser = await User.register({
    email: emailLower,
    name,
    password,
    fingerprint,
    macaddress,
    cookiekey,
    storagekey,
    flashkey,
    phone,
    phonearea,
    referral,
    ip,
    permissionGroups: userPermissionGroupIds,
  });

  const paymentBlacklistedGeneralConfig = await getPaymentBlacklistGeneralConfig();
  const blacklistedStreamingServersIds = paymentBlacklistedGeneralConfig.generalConfig.streamingServers || [];

  const userConfig = new UserConfig({ uid: createdUser.id });

  if (blacklistedStreamingServersIds && blacklistedStreamingServersIds.length) {
    userConfig.speed = blacklistedStreamingServersIds[0];
  }

  await userConfig.save();

  if (fingerprintPayment) {
    const fingerprintPaymentModel = new UserFingerprintPayment({
      fingerprint: fingerprintPayment,
      uid: createdUser.id,
      ip,
      userAgent,
      createdForTtlIndex: new Date(),
    });
    await fingerprintPaymentModel.save();
  }
  if (ip) {
    await saveUserIp(createdUser, ip);
  }

  try {
    const sendingResult = await createdUser.mail('registertemplate', noReplyEmail, {}, {}, locale);

    if (sendingResult.error) throw new Error('rethrow');
  } catch (e) {
    console.error(e);
    await User.deleteOne({ id: createdUser.id });

    throw new RegisterEmailBlockedError(locale);
  }

  return {
    error: 0,
    id: createdUser.id,
    package: createdUser.package,
    expires: createdUser.expires,
  };
};
