const moment = require('moment');
const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const Rememberme = require('../../model/audb/Rememberme');
const UserLogLogin = require('../../model/audb/UserLogLogin');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');
const UserFreeze = require('../../model/audb/UserFreeze');
const UserConfig = require('../../model/audb/UserConfig');
const UserGlobal = require('../../model/audb/UserGlobal');
const clearSessionRemembermeDevice = require('../../service/audb/clearSessionRemembermeDevice');
const isSet = require('../../helpers/isSet');
const md5 = require('../../helpers/md5');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');
const getAppAccessByPackageAndVersionNames = require('../../commands/apps/getAppAccessByPackageAndVersionNames');
const { USER_GROUPS } = require('../../constants/testers');
const { IOS_TYPES_LIST, ANDROID_TYPES_LIST } = require('../../constants/app');

/**
 * Activates the user by the 'activekey' field which was generated for confirming email.
 *
 * If the user is active, the 'activekey' field will be cleared, the previous password will be incorrect and
 * will clear Session, Rememberme collections by user id.
 *
 * If the user is inactive, it will only receive a trial package without resetting password.
 *
 * @param {string} userActiveKey - It will be the new password, if you are an active user. Also this field
 * needs for confirming newest users (secret code from email) and it must be equal to activekey in the user's schema.
 * @param {user as User} user - User info by session ID
 * @param {string} userIP - User IP
 * @param {string} userActiveKey - User activation key
 * @returns {Promise<Object as User>} - Updated user.
 */
const activateUserByActiveKey = async (user, userIP, userActiveKey) => {
  if (user.activekey === userActiveKey) {
    if (user.isactive === 0) {
      await user.addTrialPeriod(userIP);
      user.activekey = '';
      await user.save();
    } else {
      const encryptedPassword = md5(`${userActiveKey}${user.salt}`);
      user.password = encryptedPassword;
      user.activekey = '';
      await user.save();

      await clearSessionRemembermeDevice(user.id);
    }

    const leanUser = user.toObject();
    leanUser.activenow = true;
    leanUser.loginbyactivekey = true;

    return leanUser;
  }

  return {};
};

module.exports = async ({ request, userIP, rememberme, remembermekey, session, sessionID, user, pass, os, appName = null, locale }) => {
  if (rememberme) {
    const _rememberMe = new Rememberme({
      user_id: user.id,
      created: Date.now(),
    });
    _rememberMe.save();
    session.remembermekey = _rememberMe._id.toString();
  }
  if (remembermekey) session.remembermekey = remembermekey;

  session.user_id = user.id;
  let activenow = false;
  let loginbyactivekey = false;

  if (user && pass) {
    const activateResult = await activateUserByActiveKey(user, userIP, pass);
    activenow = activateResult.activenow;
    loginbyactivekey = activateResult.loginbyactivekey;
  }

  const _userModel = await User.findOne({ id: user.id }).basicPopulation().exec();

  // // TODO remove/disable after 01 Mar 2025
  // const addExtraTimeResult = await addOldUserExtraTimeOnLogin(_userModel);

  // if (addExtraTimeResult) _userModel = await User.findOne({ id: user.id }).basicPopulation().exec();

  await UserLogLogin.write({ ...request });
  const [featuresList, freeze, userConfig, playerRule] = await Promise.all([
    PackageToFeatures.findOne({ id: _userModel.Package.pgid }).cache(60).exec(),
    UserFreeze.findOne().byUidAndCurrentTime(_userModel.id).cache(60).exec(),
    UserConfig.findOne({ uid: _userModel.id }).cache(60).exec(),
    _userModel.getPlayerRule('', 0),
  ]);
  const player = await playerRule.getPlayer();
  const formatted = _userModel.format({
    featuresList,
    freeze,
    userConfig,
    playerRule,
    player,
    userPackage: _userModel.Package,
    sid: sessionID,
    billingAddresses: _userModel.billingAddresses,
    locale,
    remembermekey: session.remembermekey,
  });

  if (!isSet(_userModel.exominbuffer) || !isSet(_userModel.exoplaybackbuffer)) {
    const { exominbuffer, exoplaybackbuffer } = await UserGlobal.getExoPlayerBuffer();

    formatted.exominbuffer = exominbuffer;
    formatted.exoplaybackbuffer = exoplaybackbuffer;
  }
  if (!formatted.isactive) formatted.error = 0;

  delete formatted._id;

  formatted.ptime = moment().unix();
  formatted.activenow = activenow;
  formatted.loginbyactivekey = loginbyactivekey;

  if (os && ANDROID_TYPES_LIST.includes(os)) {
    // for Apple need to check APP permissions by packageName and versionName
    const { 'user-agent': userAgent } = request.headers;
    const packageRegex = /package:([^,]+)/;
    const versionRegex = /ver-([^,]+)/;
    const packageMatch = userAgent ? userAgent.match(packageRegex) : null;
    const versionMatch = userAgent ? userAgent.match(versionRegex) : null;

    const packageName = packageMatch && packageMatch.length && packageMatch.length > 1 ? packageMatch[1] : null;
    const versionName = versionMatch && versionMatch.length && versionMatch.length > 1 ? versionMatch[1] : null;

    if (packageName && versionName) {
      const appAccess = await getAppAccessByPackageAndVersionNames(packageName, versionName);

      if (appAccess) {
        formatted.webcug = await getUserGroupForWebcamApp(user, appName, appAccess);
      } else {
        formatted.webcug = await getUserGroupForWebcamApp(user);
      }
    } else {
      formatted.webcug = await getUserGroupForWebcamApp(user);
    }
    if (formatted.webcug === USER_GROUPS.regular) {
      throw new ApiError(501, 'On authorize device error occurred');
    }
  } else {
    formatted.webcug = await getUserGroupForWebcamApp(user, appName);

    if (os && IOS_TYPES_LIST.includes(os) && formatted.webcug === USER_GROUPS.regular) {
      throw new ApiError(501, 'On authorize device error occurred');
    }
  }

  return formatted;
};
