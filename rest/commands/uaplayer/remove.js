const { MissingUserIDForRemoveUserPlayerError } = require('@s1/api-errors');
const PlayerDefaultRule = require('../../model/audb/PlayerDefaultRule');
const PlayerGlobalRule = require('../../model/audb/PlayerGlobalRule');
const PlayerUserRule = require('../../model/audb/PlayerUserRule');
const { forWhoConstants } = require('./index');

module.exports = async (
  { uid = 0, forWho, flash = 0, mdkey = '' },
) => {
  const deleteOptions = {
    mdkey, uid, flash,
  };

  let response = {};

  switch (forWho) {
    case forWhoConstants.USER: {
      if (!uid) throw new MissingUserIDForRemoveUserPlayerError();

      response = await PlayerUserRule.deleteMany(deleteOptions).lean().exec();
      break;
    }
    case forWhoConstants.GLOBAL: {
      response = await PlayerGlobalRule.deleteMany({ ...deleteOptions, uid: 0 }).lean().exec();
      break;
    }
    case forWhoConstants.DEFAULT: {
      response = await PlayerDefaultRule.deleteMany({ ...deleteOptions, uid: 0 }).lean().exec();
      break;
    }
    default:
      break;
  }

  const result = response.ok ? [true] : null;

  return result;
};
