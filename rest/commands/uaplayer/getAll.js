const User = require('../../model/audb/User');
const PlayerDefaultRule = require('../../model/audb/PlayerDefaultRule');
const PlayerGlobalRule = require('../../model/audb/PlayerGlobalRule');
const PlayerUserRule = require('../../model/audb/PlayerUserRule');
const { forWhoConstants } = require('./index');

/**
 * Returns all rules for UA player via user ID or mdkey.
 *
 * @param {object} object
 * @param {number} object.uid - User ID
 * @param {string} object.forWho - Audb model name
 * @param {string} object.mdkey - Key from player setting
 * @returns {Promise<Array | Object>} Information about player rule
 */
module.exports = async (
  { uid = 0, forWho, mdkey = '' },
) => {
  switch (forWho) {
    case forWhoConstants.USER: {
      const response = await PlayerUserRule.find({ uid }).lean().exec();

      return response;
    }
    case forWhoConstants.GLOBAL: {
      const response = await PlayerGlobalRule.find().lean().exec();

      return response;
    }
    case forWhoConstants.DEFAULT: {
      const response = await PlayerDefaultRule.findOne().lean().exec();

      return response;
    }
    case forWhoConstants.GLOBAL_AND_DEFAULT: {
      const result = {};
      result.dd = await PlayerDefaultRule.findOne().lean().exec();
      result.gg = await PlayerGlobalRule.find().lean().exec();

      return result;
    }
    case forWhoConstants.UA: {
      const result = {};
      result.gg = await PlayerGlobalRule.find({ mdkey }).lean().exec();
      result.user = await PlayerUserRule.find({ mdkey }).lean().exec();

      return result;
    }
    case forWhoConstants.USER_UA: {
      const result = {};
      result.uuas = await PlayerUserRule.find({ uid }).lean().exec();
      result.lastsUA = await User.getLastLog(uid);

      return result;
    }
    default:
      break;
  }

  return null;
};
