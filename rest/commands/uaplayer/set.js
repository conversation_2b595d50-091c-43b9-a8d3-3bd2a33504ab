const { MissingPlayerIDSetError, MissingUserIDForSetUserPlayerError } = require('@s1/api-errors');
const PlayerDefaultRule = require('../../model/audb/PlayerDefaultRule');
const PlayerGlobalRule = require('../../model/audb/PlayerGlobalRule');
const PlayerUserRule = require('../../model/audb/PlayerUserRule');
const PlayerTemporaryRule = require('../../model/audb/PlayerTemporaryRule');
const { forWhoConstants } = require('./index');
const md5 = require('../../helpers/md5');

module.exports = async (
  { uid = 0, ua, forWho, name = '', playerid = 0, live = 0, record = 0, flash = 0, player = 0, memo = '', mdkey = '' },
) => {
  if (!playerid) throw new MissingPlayerIDSetError();

  const findOptions = {
    mdkey, uid, flash,
  };
  const updateOptions = {
    ...findOptions, name, playerid, live, record, player, ua, memo, created: parseInt(Date.now() / 1000),
  };

  switch (forWho) {
    case forWhoConstants.USER: {
      if (!uid) throw new MissingUserIDForSetUserPlayerError();

      const response = await PlayerUserRule.findOneAndUpdate(
        findOptions, updateOptions, { upsert: true, new: true },
      ).lean().exec();

      return response;
    }
    case forWhoConstants.GLOBAL: {
      findOptions.uid = 0;
      updateOptions.uid = 0;
      const response = await PlayerGlobalRule.findOneAndUpdate(
        findOptions, updateOptions, { upsert: true, new: true },
      ).lean().exec();

      return response;
    }
    case forWhoConstants.DEFAULT: {
      findOptions.ua = 'default';
      updateOptions.ua = 'default';
      findOptions.uid = 0;
      updateOptions.uid = 0;
      const response = await PlayerDefaultRule.findOneAndUpdate(
        findOptions, updateOptions, { upsert: true, new: true },
      ).lean().exec();

      return response;
    }
    case forWhoConstants.TEMPORARY_USER: {
      if (!uid) throw new MissingUserIDForSetUserPlayerError();

      findOptions.ua = 'temp';
      updateOptions.ua = 'temp';
      findOptions.mdkey = md5('temp');
      updateOptions.mdkey = md5('temp');
      updateOptions.memo = memo || null;
      updateOptions.name = name || null;
      const response = await PlayerTemporaryRule.findOneAndUpdate(
        findOptions, updateOptions, { upsert: true, new: true },
      ).lean().exec();

      return response;
    }
    default:
      break;
  }

  return null;
};
