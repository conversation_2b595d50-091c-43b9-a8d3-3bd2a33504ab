const PlayerDefaultRule = require('../../model/audb/PlayerDefaultRule');
const PlayerGlobalRule = require('../../model/audb/PlayerGlobalRule');
const PlayerUserRule = require('../../model/audb/PlayerUserRule');
const { forWhoConstants } = require('./index');

/**
 * Returns all rules for UA player via user ID or mdkey.
 *
 * @param {object} object
 * @param {number} object.uid - User ID
 * @param {string} object.forWho - Audb model name
 * @param {string} object.mdkey - Key from player setting
 * @returns {Promise<Array | Object>} Information about player rule
 */
module.exports = async (
  { uid = 0, forWho, mdkey = '' },
) => {
  const findOptions = {
    mdkey, uid,
  };

  let response = [];

  switch (forWho) {
    case forWhoConstants.USER: {
      response = await PlayerUserRule.find(findOptions).lean().exec();
      break;
    }
    case forWhoConstants.GLOBAL: {
      response = await PlayerGlobalRule.find({ ...findOptions, uid: 0 }).lean().exec();
      break;
    }
    case forWhoConstants.DEFAULT: {
      response = await PlayerDefaultRule.find({ ...findOptions, uid: 0 }).lean().exec();
      break;
    }
    default:
      break;
  }

  return response.length ? response : null;
};
