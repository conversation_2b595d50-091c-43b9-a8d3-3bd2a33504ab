const UserLogLogin = require('../../../model/audb/UserLogLogin');
const UserLogLive = require('../../../model/audb/UserLogLive');
const UserLogRecord = require('../../../model/audb/UserLogRecord');
const UserLogVod = require('../../../model/audb/UserLogVod');
const SpeedTest = require('../../../model/audb/SpeedTest');

module.exports = async ({ userId, logId, type }) => {
  let Model;

  switch (type) {
    case 'login':
      Model = UserLogLogin;
      break;
    case 'live':
      Model = UserLogLive;
      break;
    case 'record':
      Model = UserLogRecord;
      break;
    case 'vod':
      Model = UserLogVod;
      break;
    case 'speedtest':
      Model = SpeedTest;
      break;
    default:
      throw new Error(`Wrong type ${type}`);
  }

  try {
    const deletedItem = await Model.findOneAndDelete({ _id: logId, uid: userId });

    return { errorcode: 0, result: !!deletedItem };
  } catch (e) {
    throw new Error(e);
  }
};
