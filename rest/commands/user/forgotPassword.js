const {
  ForgotPasswordError,
  EmailSendingError,
  CheckEmailError,
} = require('@s1/api-errors');
const log = require('@s1/log').create(__filename);
const moment = require('moment');
const User = require('../../model/audb/User');
const ForgotPassword = require('../../model/audb/ForgotPassword');
const Validator = require('../../helpers/ForgotPasswordValidator');
const config = require('../../../config');
const i18n = require('../../helpers/geti18n');

module.exports = async ({
  email,
  locale,
}) => {
  const emailLower = email.toLowerCase();

  const validator = new Validator(locale);
  await validator.validateEmail(emailLower);

  const existingUser = await User.findOne()
    .byEmail(emailLower).exec();

  if (!existingUser) throw new CheckEmailError(locale);
  if (existingUser.isNgateUser) throw new CheckEmailError(locale);

  // Check forgot password tries
  const forgotTries = await ForgotPassword.find({
    uid: existingUser.id,
    created: {
      $gte: moment().subtract(1, 'd').unix(),
    },
  }).exec();

  if (forgotTries.length >= 10) throw new ForgotPasswordError(locale);

  const oldActiveKey = existingUser.activekey;
  const oldActivekeygentime = existingUser.activekeygentime;

  // set new pass and increase ForgotPassword attempts
  await existingUser.forgotPassword();
  const emailData = {
    newpassword: existingUser.activekey,
    email: existingUser.email,
  };
  try {
    await existingUser.mail('forgetpassword', config.email.noReply, null, emailData, locale);
  } catch (e) {
    log.error('ERROR ON EMAIL SENDING');
    log.error(e);

    // rollback user if we can't send email
    existingUser.activekey = oldActiveKey;
    existingUser.activekeygentime = oldActivekeygentime;
    existingUser.save();

    // rollback attempts if we can't send email
    const lastForgotPasswordAttempt = await ForgotPassword.findOne({
      uid: existingUser.id,
    }).sort('-_id').limit(1).exec();

    lastForgotPasswordAttempt.remove();

    throw new EmailSendingError(locale);
  }

  i18n.setLocale(locale);

  return { errorcode: 0, results: i18n.__('A temporary password has been successfully sent by email'), email: emailLower };
};
