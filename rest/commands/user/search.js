const User = require('../../model/audb/User');
const { SEARCH_REQUEST_DATA } = require('../../constants/usercenter');
const getUserSearchQuery = require('../../helpers/query/userSearch');
const getUserWatchingScore = require('../history/getUserWatchingScore');
const { Package } = require('../../model/audb/Package');
const AdminFilter = require('../../model/audb/AdminFilter');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');

/**
 * Get all permission group users
 * @param {object} data - filter
 * @param {string} data.userPermissionGroupId - permission group ID
 * @param {number} data.page - current page
 * @param {number} data.limit - limit per page
 * */
module.exports = async ({ data, user }) => {
  const { page = 0, limit = 100, sortField = 'id', sortOrder = 1 } = data;
  const skip = page * limit;

  const userMatch = {};
  const userAnd = [];
  const viewName = 'usersList';

  await AdminFilter.findOneAndUpdate(
    { userId: user.id, viewName },
    { $set: { userId: user.id, viewName, filter: data } },
    { upsert: true, new: true },
  ).exec();

  for (const key of Object.keys(data)) {
    switch (key) {
      case SEARCH_REQUEST_DATA.USER_PERMISSION_GROUP_ID:
      case SEARCH_REQUEST_DATA.ID:
      case SEARCH_REQUEST_DATA.EMAIL:
      case SEARCH_REQUEST_DATA.NAME:
      case SEARCH_REQUEST_DATA.TKEY:
      case SEARCH_REQUEST_DATA.CARD:
      case SEARCH_REQUEST_DATA.TRANSACTION_ID:
      case SEARCH_REQUEST_DATA.WALLET_ADDRESSS:
      case SEARCH_REQUEST_DATA.PAID_TYPES:
      case SEARCH_REQUEST_DATA.IS_BLACKLISTED:
      case SEARCH_REQUEST_DATA.IS_STEALER:
      case SEARCH_REQUEST_DATA.IS_ACTIVE:
      case SEARCH_REQUEST_DATA.IS_TRIAL:
      case SEARCH_REQUEST_DATA.IS_EXPIRED:
      case SEARCH_REQUEST_DATA.IS_ADMIN:
      case SEARCH_REQUEST_DATA.IS_SUPPORT:
      case SEARCH_REQUEST_DATA.IS_DEVELOPER:
      case SEARCH_REQUEST_DATA.IS_VODADMIN:
      {
        const { operator, query: queryValue } = await getUserSearchQuery(key, data[key]);

        if (operator && queryValue) userAnd.push({ [operator]: queryValue });

        break;
      }
      default:
        break;
    }
  }

  if (userAnd.length) userMatch.$and = userAnd;

  const filteredUsers = await User.find(userMatch, { _id: 0, id: 1 }).lean();
  const usersIds = filteredUsers.map(user => user.id);

  const baseMatch = { $match: { id: { $in: usersIds } } };
  const usersAggregation = [
    baseMatch,
    {
      $sort: {
        [sortField]: sortOrder,
      },
    },
    { $skip: skip },
    { $limit: limit },
    {
      $project: {
        _id: 0, id: 1, em: 1, name: 1, permissionGroups: 1, package: 1, regtime: 1, expires: 1, billingAddresses: 1,
      },
    },
    {
      $lookup: {
        from: 'userPermissionGroup',
        localField: 'permissionGroups',
        foreignField: '_id',
        as: 'UserPermissionGroups',
      },
    },
    {
      $lookup: {
        from: 'pp',
        localField: 'id',
        foreignField: 'uid',
        as: 'PaymentLogs',
      },
    },
    {
      $project: {
        id: 1,
        em: 1,
        name: 1,
        PaymentLogs: 1,
        UserPermissionGroups: 1,
        package: 1,
        regtime: 1,
        expires: 1,
        billingAddresses: 1,
      },
    },
  ];

  const usersResult = await User.aggregate(usersAggregation).exec();

  const packagesObject = {};
  let defaultPackage;

  if (usersResult && usersResult.length) {
    const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
    allPackages.forEach((pack) => {
      packagesObject[pack.id] = pack;

      if (pack.price === 0) defaultPackage = pack;
    });
  }

  const users = await Promise.all(usersResult.map(async (user) => {
    user.email = User.decryptEmail(user.em);
    delete user.em;
    user.watchingScore = await getUserWatchingScore(user.uid);
    user.isBlacklisted = await isBlacklistedUser({ user });
    user.package = packagesObject.hasOwnProperty(user.package) ? packagesObject[user.package] : defaultPackage;
    const userPaymentsMap = {};

    user.PaymentLogs.filter(log => log.amount > 0).forEach((log) => {
      if (!userPaymentsMap[log.pptype]) {
        userPaymentsMap[log.pptype] = 0;
      }

      userPaymentsMap[log.pptype]++;
    });

    user.paidTypes = Object.entries(userPaymentsMap).map(([type, totalCount]) => ({ type, totalCount }));

    return user;
  }));

  const total = usersIds.length;
  const pages = Math.ceil(total / limit);

  return {
    error: 0,
    users,
    total,
    pages,
    page,
  };
};
