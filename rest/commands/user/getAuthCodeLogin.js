const QRCode = require('qrcode');
const moment = require('moment');
const { ApiError } = require('@s1/api-errors');
const AuthCode = require('../../model/audb/AuthCode');
const config = require('../../../config');
const i18n = require('../../helpers/geti18n');
const generateUniqueAuthCode = require('../../helpers/generateUniqueAuthCode');
const getAppAccessByName = require('../../commands/apps/getAppAccessByName');

module.exports = async ({ qrCodeWidth = 255, dName, os, appName, headers, locale = config.i18n.defaultLocale }) => {
  const type = 'login';

  i18n.setLocale(locale);

  if (!dName) throw new ApiError(400, i18n.__('Device name is required'));
  if (!os) throw new ApiError(400, i18n.__('OS name is required'));
  if (appName) appName = decodeURIComponent(appName);

  const { 'user-agent': userAgent } = headers;
  const packageRegex = /package:([^,]+)/;
  const versionRegex = /ver-([^,]+)/;
  const packageMatch = userAgent ? userAgent.match(packageRegex) : null;
  const versionMatch = userAgent ? userAgent.match(versionRegex) : null;
  const packageName = packageMatch && packageMatch.length && packageMatch.length > 1 ? packageMatch[1] : null;
  const versionName = versionMatch && versionMatch.length && versionMatch.length > 1 ? versionMatch[1] : null;

  const authPackagesSitesForTvIndex = Math.floor(Math.random() * config.authPackagesSitesForTv.length);
  const authCode = new AuthCode({
    authPackagesSitesForTvIndex,
    type,
    dName,
    os,
    appName,
    packageName,
    versionName,
    expires: moment().unix() + config.authCode.expireHours * 3600,
    createdForTtlIndex: new Date(),
  });

  authCode.code = await generateUniqueAuthCode();
  await authCode.save();

  const authPackagesSitesForTv = config.authPackagesSitesForTv[authCode.authPackagesSitesForTvIndex]
    || config.authPackagesSitesForTv[0];

  // get login link by app name if provided
  let appLoginUrl;

  if (appName) {
    const appAccess = await getAppAccessByName(appName);

    if (appAccess) appLoginUrl = appAccess.loginUrl;
  }

  const loginLink = `${appLoginUrl || authPackagesSitesForTv}`;
  const authLink = `${appLoginUrl || authPackagesSitesForTv}?code=${authCode.code.toUpperCase()}`;
  const qrCode = await QRCode.toDataURL(
    authLink,
    { type: 'png',
      width: qrCodeWidth,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    },
  );

  return {
    code: authCode.code.toUpperCase(),
    type,
    expires: authCode.expires,
    loginLink,
    authLink,
    qrCode,
  };
};
