const User = require('../../model/audb/User');

module.exports = async (data) => {
  if (!data.userId) throw new Error('userId required');
  if (!data.email) throw new Error('email required');

  const user = await User.findOne({ id: data.userId }).exec();

  if (user) {
    user.lastMercuryoEm = User.encryptEmail(data.email);
    await user.save();

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
