const moment = require('moment');
const QRCode = require('qrcode');
const AuthCode = require('../../model/audb/AuthCode');
const config = require('../../../config');
const generateUniqueAuthCode = require('../../helpers/generateUniqueAuthCode');
const getAppAccessByName = require('../../commands/apps/getAppAccessByName');

module.exports = async (user, type, appName, qrCodeWidth = 255, locale = config.i18n.defaultLocale) => {
  const now = moment().unix();

  let authCode = await AuthCode.findOne({ uid: user.id, type }).exec();
  const authPackagesSitesForTvIndex = Math.floor(Math.random() * config.authPackagesSitesForTv.length);

  if (!authCode || authCode.expires < now) {
    authCode = new AuthCode({
      uid: user.id,
      authPackagesSitesForTvIndex,
      type,
      expires: now + config.authCode.expireHours * 3600,
      createdForTtlIndex: new Date(),
    });
    authCode.code = await generateUniqueAuthCode();
    await authCode.save();
  }

  // get login link by app name if provided
  let appLoginUrl;

  if (appName) {
    appName = decodeURIComponent(appName);
    const appAccess = await getAppAccessByName(appName);

    if (appAccess) appLoginUrl = appAccess.loginUrl;
  }

  const authPackagesSitesForTv = config.authPackagesSitesForTv[authCode.authPackagesSitesForTvIndex]
    || config.authPackagesSitesForTv[0];
  const loginLink = `${appLoginUrl || authPackagesSitesForTv}`;
  const authLink = `${appLoginUrl || authPackagesSitesForTv}?code=${authCode.code.toUpperCase()}`;
  const qrCode = await QRCode.toDataURL(
    authLink,
    { type: 'png',
      width: qrCodeWidth,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    },
  );

  return {
    code: authCode.code.toUpperCase(),
    type,
    expires: authCode.expires,
    created: authCode.created,
    loginLink,
    authLink,
    qrCode,
  };
};
