const { WrongParamsError } = require('@s1/api-errors');
const PaymentLog = require('../../model/audb/PaymentLog');

module.exports = async (user, fromTime, locale) => {
  if (!fromTime) throw new WrongParamsError(locale);

  // find last XX min user success payment
  const lastPayment = await PaymentLog.findOne(
    { uid: user.id, upback: true, isRefunded: false, created: { $gte: fromTime } },
  ).sort({ created: -1 }).exec();

  return {
    errorcode: 0,
    paid: !!lastPayment,
  };
};
