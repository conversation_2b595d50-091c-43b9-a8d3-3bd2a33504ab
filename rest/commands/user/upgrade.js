const moment = require('moment');
const { UserExpiredSoonUpgradePackageError, UpgradeExtraPackageError } = require('@s1/api-errors');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');

module.exports = async ({ user = {}, sessionID, locale }) => {
  const packageFeatures = await PackageToFeatures.findOne({ id: user.Package.pgid }).exec() || {};

  if (packageFeatures.extra) {
    throw new UpgradeExtraPackageError(locale);
  }
  if (moment(user.expires * 1000) < moment().add(10, 'days')) {
    throw new UserExpiredSoonUpgradePackageError(locale);
  }

  return user.getUpgradeInvoice(sessionID, locale);
};
