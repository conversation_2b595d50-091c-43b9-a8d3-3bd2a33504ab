/* eslint-disable no-case-declarations */
const moment = require('moment');
const { favorites: { TYPES }, vod: { STREAM_TYPE_VOD },  } = require('@s1/api-constants');
const log = require('@s1/log').create(__filename);
const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const categoriesCache = require('../../../service/cache/categories');
const User = require('../../../model/audb/User');
const UserFavoriteLive = require('../../../model/audb/UserFavoriteLive');
const UserFavoriteVod = require('../../../model/audb/UserFavoriteVod');
const UserFavoriteTvShow = require('../../../model/audb/UserFavoriteTvShow');
const UserVodPosition = require('../../../model/audb/UserVodPosition');
const Schedule = require('../../../model/audb/Schedule');
const settings = require('../../../service/settings');
const AUserPosition = require('../../../model/audb/abstract/AUserPosition');

const sortOption = { created: -1, order: -1 };

const createVodPositionGetter = (vodPositions) => {
  const positionMap = new Map();
  vodPositions.forEach(position => {
    positionMap.set(position.vod, position.position);
  });

  return (vodId) => positionMap.get(vodId) || 0;
};

async function prepareVods(vodFavorites, user = {}, thumbnailDomain, locale) {
  const favoriteVodsIds = vodFavorites.map(favorite => favorite.chvod);
  const [userVodPositions, { alreadySeen: ALREADY_SEEN_OPTIONS }, vods] = await Promise.all([
    UserVodPosition.find({ uid: user.id, vod: { $in: favoriteVodsIds } }).lean().exec(),
    settings.get(),
    Vod.find({ id: { $in: favoriteVodsIds } }).exec(),
  ]);
  const vodMap = new Map(vods.map(v => [v.id, v]));

  const uniqueCateIds = [...new Set(vods.map(v => v.cateid).filter(Boolean))];
  const categoryMap = await getMultipleCategoryAndUpper(uniqueCateIds, thumbnailDomain, locale);
  const vodPicturePromises = vods.map(vod =>
    vod.resolvePicture({ domain: thumbnailDomain }).catch(() => null)
  );
  const vodPictures = await Promise.all(vodPicturePromises);
  const vodPictureMap = new Map();
  vods.forEach((vod, index) => {
    vodPictureMap.set(vod.id, vodPictures[index]);
  });
  const getVodPosition = createVodPositionGetter(userVodPositions);

  const results = [];

  for (const favorite of vodFavorites) {
    try {
      const favoriteObject = favorite.toObject ? favorite.toObject() : { ...favorite };
      const vod = vodMap.get(favorite.chvod);

      if (!vod) {
        log.error(`Vod #${favorite.chvod}, mentioned at favorites of user #${user && user.id} does not exist`);
        continue;
      }

      const categoryData = categoryMap.get(vod.cateid);

      if (!categoryData) continue;

      const vodPosition = getVodPosition(vod.id);
      const formattedVod = vod.format({ locale });

      formattedVod.position = vodPosition;
      formattedVod.isAlreadySeen = AUserPosition.isAlreadySeen(vod, vodPosition, ALREADY_SEEN_OPTIONS);
      formattedVod.showpic = vodPictureMap.get(vod.id);
      formattedVod.picture = vodPictureMap.get(vod.id);
      favoriteObject.show = formattedVod;
      favoriteObject.category = categoryData.category;
      favoriteObject.upper = categoryData.upper;

      results.push(favoriteObject);
    } catch (e) {
      log.error(`Error while processing favorite vod #${favorite.chvod} for user #${user.id}: ${e.message || e.toString()}`);
    }
  }

  return results;
}

async function prepareTvShows(shows, thumbnailDomain, locale) {
  const uniqueCateIds = [...new Set(shows.map(item => item.chvod).filter(Boolean))];
  const categoryMap = await getMultipleCategoryAndUpper(uniqueCateIds, thumbnailDomain, locale);
  const result = [];

  for (const item of shows) {
    const itemObject = item.toObject ? item.toObject() : { ...item };
    const categoryData = categoryMap.get(item.chvod);

    if (!categoryData) continue;

    itemObject.category = categoryData.category;
    itemObject.upper = categoryData.upper;
    result.push(itemObject);
  }

  return result;
}

async function getMultipleCategoryAndUpper(cateIds, thumbnailDomain, locale) {
  const resultMap = new Map();
  const categories = await Category.find({ id: { $in: cateIds } }, { _id: 0 }).exec();
  const categoryByIdMap = new Map(categories.map(cat => [cat.id, cat]));
  const categoryPathPromises = categories.map(async category => {
    try {
      const upperList = await category.getFullPath();

      return { categoryId: category.id, upperList };
    } catch (error) {
      console.warn(`Failed to get full path for category ${category.id}`, error);

      return { categoryId: category.id, upperList: [] };
    }
  });

  const categoryPaths = await Promise.all(categoryPathPromises);
  const categoryPathMap = new Map();
  categoryPaths.forEach(({ categoryId, upperList }) => {
    categoryPathMap.set(categoryId, upperList);
  });

  for (const cateId of cateIds) {
    const category = categoryByIdMap.get(cateId);

    if (!category) continue;

    try {
      const result = {};
      const upperList = categoryPathMap.get(cateId) || [];

      result.category = await prepareCategory(category, thumbnailDomain, locale);
      result.upper = await Promise.all(
        upperList.map(cat => prepareCategory(cat, thumbnailDomain, locale))
      );

      resultMap.set(cateId, result);
    } catch (error) {
      console.warn(`Failed to build category chain for cateId=${cateId}`, error);
    }
  }

  return resultMap;
}

async function prepareCategory(category, vodThumbnailDomain, locale) {
  category.showpic = await category.resolvePicture(categoriesCache, { domain: vodThumbnailDomain });
  const categoryObject = category.format({ locale });
  delete categoryObject._id;

  return categoryObject;
}

async function getLiveAndRadio(userId, locale, { ISP, countryCode, stateCode, userConfig = {} }) {
  const result = { live: [], radio: [] };

  const data = await UserFavoriteLive.find({ uid: userId })
    .populate({
      path: 'channel',
      match: { ifshow: 0 },
      populate: { path: 'channelGroup' },
    })
    .limit(100)
    .sort(sortOption)
    .exec();

  if (data.length === 0) return result;

  const streamingServers = await User.getStreamingServers(ISP, countryCode, stateCode, 2, userConfig, { id: userId });
  const thumbnailOptions = {
    domain: streamingServers.mainServer.sip,
  };

  const currentTime = moment().startOf('minute').unix();
  const fromTime = moment().subtract(6, 'hours').startOf('hour').unix();
  const channelIdsForSchedule = data.map(item => item.chvod).filter(Boolean);

  const schedules = await Schedule.aggregate([
    {
      $match: {
        channel: { $in: channelIdsForSchedule },
        rdatetime: { $gte: fromTime, $lte: currentTime },
      },
    },
    {
      $sort: { rdatetime: -1 },
    },
    {
      $group: {
        _id: "$channel",
        schedule: { $first: "$$ROOT" },
      },
    },
    {
      $replaceWith: "$schedule",
    },
  ]).exec();

  const schedulesMap = new Map();
  schedules.forEach(schedule => {
    if (!schedulesMap.has(schedule.channel)) {
      schedulesMap.set(schedule.channel, schedule);
    }
  });

  for (const item of data) {
    if (!item.channel || !item.channel.id) continue;

    const dataObject = item.toObject();
    dataObject.channel = item.channel.format({ locale });
    delete dataObject.channel._id;
    dataObject.channel.isradio = item.channel.channelGroup.isradio;

    const show = schedulesMap.get(item.chvod);

    if (show) show.Channel = item.channel;

    dataObject.channel.show = show
      ? Schedule.format(show, { scheduleChannel: item.channel, locale, thumbnailOptions })
      : Schedule.getSimplifiedDefaultSchedule(item.chvod, locale);

    if (dataObject.channel.isradio) result.radio.push(dataObject);
    else result.live.push(dataObject);
  }

  return result;
}

async function getFavoritesFromModel(type, model, userId) {
  let favs;
  try {
    favs = model.find({ uid: userId })
      .sort(sortOption)
      .lean()
      .exec();
  } catch (error) {
    console.error(error);
    favs = [];
  }

  return favs;
}

module.exports = async ({ type, user, types = TYPES, ISP, countryCode, stateCode, locale }) => {
  const userId = user.id;
  const result = {};

  switch (type) {
    case types.LIVE_ID_ONLY:
      result.live = await UserFavoriteLive.find({ uid: userId })
        .select({ id: 1 })
        .limit(100)
        .sort(sortOption)
        .lean()
        .exec();
      break;
    case types.LIVE:
    case types.RADIO:
      const { live, radio } = await getLiveAndRadio(userId, locale, { ISP, countryCode, stateCode });
      result.live = [...live, ...radio];
      break;
    case types.RECORD:
      result.record = [];
      break;
    case types.VOD:
      const [vod, tvshow] = await Promise.all([
        getFavoritesFromModel('vod', UserFavoriteVod, userId),
        getFavoritesFromModel('tvshow', UserFavoriteTvShow, userId),
      ]);

      result.vod = vod;
      result.tvshow = tvshow;
      break;
    case types.TVSHOW:
      result.tvshow = await getFavoritesFromModel('tvshow', UserFavoriteTvShow, userId);
      break;
    case types.ALL: {
      const [vod, tvshow, liveRadioData] = await Promise.all([
        getFavoritesFromModel('vod', UserFavoriteVod, userId),
        getFavoritesFromModel('tvshow', UserFavoriteTvShow, userId),
        getLiveAndRadio(userId, locale, { ISP, countryCode, stateCode })
      ]);

      result.record = [];
      result.vod = vod;
      result.tvshow = tvshow;
      result.live = [...liveRadioData.live, ...liveRadioData.radio];
      break;
    }
    default: break;
  }

  let thumbnailDomain;

  if (result.vod && result.vod.length || result.tvshow && result.tvshow.length) {
    const streamingServers = await user.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD);
    thumbnailDomain = streamingServers.mainServer.sip;
  }
  if (result.vod && result.vod.length) result.vod = await prepareVods(result.vod, user, thumbnailDomain, locale);
  if (result.tvshow && result.tvshow.length) result.tvshow = await prepareTvShows(result.tvshow, thumbnailDomain, locale);

  result.record = [];

  return result;
};
