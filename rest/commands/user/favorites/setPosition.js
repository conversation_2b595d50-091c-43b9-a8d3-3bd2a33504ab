const getUserFavoriteModelByType = require('../../../helpers/getUserFavoriteModelByType');

module.exports = async ({ user, id, favoritesModel, type, to }) => {
  const model = favoritesModel || getUserFavoriteModelByType(type);
  const [topItem] = await model.find({ uid: user.id })
    .sort({ order: -1 })
    .exec();
  const itemToMove = await model.findOne({ _id: id });

  if (!itemToMove || !topItem || Number.isNaN(to) || to <= 0) return;

  const order = topItem.order - to + 1;
  const { order: currentOrder } = itemToMove;
  const moveUp = order > currentOrder;
  const fromOrder = moveUp ? { $gt: currentOrder } : { $gte: order };
  const toOrder = moveUp ? { $lte: order } : { $lt: currentOrder };
  const updateRange = model.updateMany(
    {
      $and: [
        { uid: user.id },
        { _id: { $ne: id } },
        { order: fromOrder },
        { order: toOrder },
      ],
    },
    {
      $inc: { order: moveUp ? -1 : 1 },
    },
  );
  const move = model.updateOne({ _id: id }, { order });
  await Promise.all([updateRange, move]);
};
