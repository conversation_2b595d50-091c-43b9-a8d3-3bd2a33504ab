const UserLogLive = require('../../model/audb/UserLogLive');
const User = require('../../model/audb/User');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserLogVod = require('../../model/audb/UserLogVod');

const ADDITIONAL_USERS_TO_SKIP = [59962]; // monitor

const getTargetModel = (type) => {
  switch (type) {
    case 'live':
      return UserLogLive;
    case 'record':
      return UserLogRecord;
    case 'vod':
      return UserLogVod;
    default:
      return null;
  }
};

module.exports = async (from, to, type) => {
  const usersToSkip = [...ADDITIONAL_USERS_TO_SKIP];
  const filter = { $and: [{ uid: { $nin: usersToSkip }, }] };

  if (from) filter.$and.push({ playtime: { $gte: parseInt(from) } });
  if (to) filter.$and.push({ playtime: { $lte: parseInt(to) } });

  const Model = getTargetModel(type);

  if (!Model) {
    return {
      errorcode: 1,
      result: [],
    };
  }

  const result = await Model.aggregate([
    { $match: filter },
    {
      $group: {
        _id: '$uid',
        activities: { $sum: 1 },
      },
    },
    {
      $sort: { activities: -1 },
    },
    {
      $limit: 50,
    },
    {
      $lookup: {
        from: 'tuser',
        localField: '_id',
        foreignField: 'id',
        as: 'User',
      },
    },
    { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'userDevToolAction',
        localField: '_id',
        foreignField: 'userId',
        as: 'devToolActions'
      }
    },
    {
      $addFields: {
        devToolActionsCount: { $size: { $ifNull: ['$devToolActions', []] } }
      }
    },
    {
      $project: { user_id: '$User.id', em: '$User.em', name: '$User.name', regtime: '$User.regtime', activities: 1, _id: 0, devToolActionsCount: 1 },
    },
  ]).then((items) =>
    Promise.all(
      items.map(async (item) => {
        item.email = item.em ? await User.decryptEmailWithRedis(item.em) : '';
        delete item.em;

        return item;
      }),
    ),
  );

  return {
    errorcode: 0,
    result,
  };
};
