const AdminFilter = require('../../model/audb/AdminFilter');

/**
 * Get admin/support user filter for selected view
 *
 * @param {object} options
 * @param {number} options.viewName - view name filter for
 * @param {number} options.user - logged user
 * */
module.exports = async ({ viewName, user }) => {
  const adminFilter = await AdminFilter.findOne({ userId: user.id, viewName }).lean();
  const { filter } = adminFilter || {};

  return {
    error: 0,
    filter,
  };
};
