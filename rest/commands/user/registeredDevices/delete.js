const { DeviceNotExistError } = require('@s1/api-errors');
const RegisteredDevice = require('../../../model/audb/RegisteredDevice');
const Session = require('../../../model/audb/Session');
const removeUserPlayRoutesCache = require('../../../commands/cache/removeUserPlayRoutesCache');

/**
 * Delete registered device by id
 *
 * @param {string} id - ObjectID string
 * @returns {*}
 */
module.exports = async (id) => {
  const device = await RegisteredDevice.findOne({ _id: id });

  if (!device) throw new DeviceNotExistError();

  await device.remove();
  await removeUserPlayRoutesCache(device.user_id);
  await Session.deleteMany({ user_id: device.user_id, deviceId: device.deviceId });

  return device;
};
