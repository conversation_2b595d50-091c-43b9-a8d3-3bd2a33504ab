const RegisteredDevice = require('../../../model/audb/RegisteredDevice');
const Rememberme = require('../../../model/audb/Rememberme');
const Session = require('../../../model/audb/Session');
const removeUserPlayRoutesCache = require('../../../commands/cache/removeUserPlayRoutesCache');

/**
 * Delete all registered devices by userId
 *
 * @param userId
 * @returns {*}
 */
module.exports = async (userId) => {
  await Promise.all([
    Rememberme.clear(userId),
    RegisteredDevice.deleteMany({ user_id: userId }),
    removeUserPlayRoutesCache(userId),
    Session.deleteMany({ user_id: userId }),
  ]);
};
