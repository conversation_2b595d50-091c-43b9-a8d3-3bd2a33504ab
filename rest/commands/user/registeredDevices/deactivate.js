const RegisteredDevice = require('../../../model/audb/RegisteredDevice');
const removeUserPlayRoutesCache = require('../../../commands/cache/removeUserPlayRoutesCache');

/**
 * Deactivate registered device by deviceId, used for logout
 * @param {object} data
 * @returns {*}
 */
module.exports = async (data) => {
  const { user, id, deviceId } = data;

  if (id) await RegisteredDevice.updateOne({ _id: id }, { is_active: false }, { upsert: false });
  else if (user && deviceId) await RegisteredDevice.updateOne({ user_id: user.id, deviceId }, { is_active: false }, { upsert: false });

  await removeUserPlayRoutesCache(user.id);
};
