const sendMailCommand = require('../mail/send');
const User = require('../../model/audb/User');
const Suspended = require('../../model/audb/Suspended');
const config = require('../../../config');
const { creationMethods } = require('../../constants/suspended');
const suspendRelatedUsers = require('../../commands/suspend/suspendRelatedUsers');

module.exports = async (uid, reason, comment, paymentLog = {}, locale) => {
// block device
  await User.blockDevices({ uid, reason });

  // just suspend
  await Suspended.updateOne({ uid }, { creationMethod: creationMethods.automatic, description: comment }, { upsert: true });
  await suspendRelatedUsers(parseInt(uid), null, comment);

  const user = await User.findOne({ id: uid }).exec();
  await user.addAdminComment(comment, uid);
  await sendMailCommand({
    tag: 'dispute_started',
    from: config.email.noReply,
    to: user.email,
    data: paymentLog,
    locale,
  });

  return 'user blocked';
};
