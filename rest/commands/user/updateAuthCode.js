const { ApiError, WrongParamsError } = require('@s1/api-errors');
const config = require('../../../config');
const getCode = require('./getAndValidateAuthCode');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');
const { USER_GROUPS } = require('../../constants/testers');
const { IOS_TYPES_LIST, ANDROID_TYPES_LIST } = require('../../constants/app');
const getAppAccessByPackageAndVersionNames = require('../../commands/apps/getAppAccessByPackageAndVersionNames');

module.exports = async (body, user, headers, locale = config.i18n.defaultLocale) => {
  const { sid, code } = body;

  if (!sid) throw new WrongParamsError(locale);

  const authCode = await getCode(code.toLowerCase(), locale);

  if (authCode.type === 'login') {
    if (ANDROID_TYPES_LIST.includes(authCode.os)) {
      // for Apple need to check APP permissions by packageName and versionName
      const { packageName = null, versionName = null } = authCode;
      let userAndroidGroup;

      if (packageName && versionName) {
        const appAccess = await getAppAccessByPackageAndVersionNames(packageName, versionName);

        if (appAccess) {
          userAndroidGroup = await getUserGroupForWebcamApp(user, '', appAccess);
        } else {
          userAndroidGroup = await getUserGroupForWebcamApp(user);
        }
      } else {
        userAndroidGroup = await getUserGroupForWebcamApp(user);
      }
      if (userAndroidGroup === USER_GROUPS.regular) {
        throw new ApiError(501, 'On authorize device error occurred');
      }
    } else if (IOS_TYPES_LIST.includes(authCode.os)) {
      const userIosGroup = await getUserGroupForWebcamApp(user, authCode.appName);

      if (userIosGroup === USER_GROUPS.regular) {
        throw new ApiError(501, 'On authorize device error occurred');
      }
    }
  }

  authCode.sid = sid;
  try {
    await authCode.save();

    return {
      errorcode: 0,
      success: true,
    };
  } catch (e) {
    throw new ApiError(501, 'On authorize device error occurred');
  }
};
