const { WrongRequestArgumentError } = require('@s1/api-errors');
const checkIsUserBlacklisted = require('./checkIsUserBlacklisted');

module.exports = async ({ req, res, user, action, body }) => {
  switch (action) {
    // admin panel, checking is fraud user
    case 'blacklisted':
      return checkIsUserBlacklisted({ req, res, user, body });
    default:
      throw new WrongRequestArgumentError('Wrong parameter action');
  }
};
