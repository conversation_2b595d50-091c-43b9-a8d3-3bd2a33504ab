const config = require('../../../config');
const getCode = require('./getAndValidateAuthCode');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');
const User = require('../../model/audb/User');
const Session = require('../../model/audb/Session');

module.exports = async (code, locale = config.i18n.defaultLocale) => {
  const authCode = await getCode(code, locale);

  const response = {
    errorcode: 0,
    code: authCode.code.toUpperCase(),
    type: authCode.type,
  };

  if (authCode.dName) response.dName = authCode.dName;
  if (authCode.os) response.os = authCode.os;
  if (authCode.sid) {
    response.sid = authCode.sid;

    const userSession = await Session.findOne({ __sid: authCode.sid }).lean().cache(3600);

    if (userSession) {
      const user = await User.findOne({ id: userSession.user_id }).lean().cache(3600);

      if (user) response.webcug = await getUserGroupForWebcamApp(user, authCode.appName);
    }
  }

  return response;
};
