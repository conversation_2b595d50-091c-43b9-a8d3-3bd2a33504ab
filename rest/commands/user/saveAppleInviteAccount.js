const AppleInviteAccount = require('../../model/audb/AppleInviteAccount');

module.exports = async (data) => {
  if (!data.name) return new Error('name required');
  if (!data.algorithm) return new Error('algorithm required');
  if (!data.keyid) return new Error('keyid required');
  if (!data.aud) return new Error('aud required');
  if (!data.iss) return new Error('iss required');
  if (!data.cert) return new Error('cert required');
  if (!data.type) return new Error('type required');
  if (!data.appId || !data.appName) return new Error('app required');
  if (!data.groupId || !data.groupName) return new Error('group required');

  const { name, description = '', enabled = false, isDefault = false, algorithm, keyid, aud, iss, _id, cert, type, appId, appName, groupId, groupName } = data;
  const dataForSave = { name, description, enabled, isDefault, algorithm, keyid, aud, iss, cert, type, appId, appName, groupId, groupName };

  if (isDefault) {
    await AppleInviteAccount.findOneAndUpdate(
      { isDefault: true },
      { $set: { isDefault: false } },
      { upsert: false, new: false },
    ).lean();
  }
  if (_id) {
    await AppleInviteAccount.findOneAndUpdate(
      { _id },
      { $set: dataForSave },
      { upsert: true, new: true },
    ).lean();
  } else {
    const appleInviteAccount = new AppleInviteAccount(dataForSave);
    await appleInviteAccount.save();
  }

  return {
    error: 0,
    success: true,
  };
};
