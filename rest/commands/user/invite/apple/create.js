const AppleInvite = require('../../../../model/audb/AppleInvite');
const { LastIdModel } = require('../../../../model/audb/LastId');

module.exports = async ({ uid, type, email }) => {
  const id = await LastIdModel.getNextId(AppleInvite, 'inviteid');
  const condition = { uid, type, email };
  const updates = { inviteid: id, uid, type, email };

  return AppleInvite.updateOne(condition, updates, { upsert: true });
};
