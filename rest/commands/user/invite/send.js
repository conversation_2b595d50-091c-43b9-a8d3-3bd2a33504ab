const {
  IncorrectEmailError,
  ReferralCodeUserRegisteredError,
  ReferralCodeUserAlreadyInvitedError,
  ReferralCodeNotFoundError,
  ReferralCodeAlreadyUsedError,
  ReferralCodeAlreadySentError,
} = require('@s1/api-errors');
const config = require('../../../../config');
const validateEmail = require('../../../helpers/validators/emailValidator');
const validateReferralCode = require('../../../helpers/validators/referralCodeValidator');
const User = require('../../../model/audb/User');
const PaymentLog = require('../../../model/audb/PaymentLog');
const ReferralCode = require('../../../model/audb/ReferralCode');

module.exports = async ({ user, emailto, name, uniquekey, fromname, locale }) => {
  try {
  	if (!validateEmail(emailto)) throw new IncorrectEmailError();
  	if (!validateReferralCode(uniquekey)) throw new Error('Referral code not found');

    const existingUser = await User.findOne().byEmail(emailto).exec();

    if (existingUser) {
      const payment = await PaymentLog.findOne({ uid: existingUser.id, upback: true }).lean().exec();

      throw new ReferralCodeUserRegisteredError({ uid: existingUser.id, paid: !!payment }, locale);
    }

    const [referralCode, referralByEmail] = await Promise.all([
      ReferralCode.findOne({ fromid: user.id, uniquekey }).exec(),
      ReferralCode.findOne({ fromid: user.id, em: User.encryptEmail(emailto) }).lean().exec(),
    ]);

    if (referralByEmail) throw new ReferralCodeUserAlreadyInvitedError(locale);
    if (!referralCode) throw new ReferralCodeNotFoundError(locale);
    if (referralCode.status > 0 || referralCode.newid) throw new ReferralCodeAlreadyUsedError(locale);
    if (referralCode.em) throw new ReferralCodeAlreadySentError(locale);

    const emailData = {
      inviteurl: config.mainSitePath,
      friendname: name,
      fromname: fromname || user.name,
      uniquekey,
    };
    await User.mail('invitetemplate', config.email.noReply, emailto, {}, emailData);
    referralCode.status = 1;
    referralCode.updated = Math.floor(Date.now() / 1000);
    referralCode.em = User.encryptEmail(emailto);
    referralCode.friendname = name;
    await referralCode.save();
  } catch (error) {
    await ReferralCode.deleteOne({ uniquekey });

    throw error;
  }
};
