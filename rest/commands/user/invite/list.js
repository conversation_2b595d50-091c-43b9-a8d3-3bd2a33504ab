const User = require('../../../model/audb/User');
const Invite = require('../../../model/audb/Invite');

module.exports = async ({ user }) => {
  const referralCodes = await user.getReferralCodes();

  const results = await Promise.all(referralCodes.map(async (code) => {
    const invitedUser = await User.findOne({ id: code.newid }).exec();
    const invite = await Invite.findOne({
      inviteid: code.newid, fromid: code.fromid,
    }).exec();
    const email = await User.decryptEmailWithRedis(code.em) || false;

    return code.format({ nestedId: true, email, user: invitedUser, invite });
  }));

  return results.filter(code => code.emailto);
};
