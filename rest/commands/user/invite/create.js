const { ReferralCodeDailyLimitError, ReferralCodeUnknownError } = require('@s1/api-errors');
const { REFERRAL_CODES_PER_DAY_LIMIT } = require('@s1/api-constants').user;
const ReferralCode = require('../../../model/audb/ReferralCode');

module.exports = async ({ userId, locale }) => {
  const now = Math.floor(Date.now() / 1000);
  const dayAgo = now - (60 * 60 * 24);
  const lastDayUserReferrals = await ReferralCode.countDocuments({ fromid: userId, created: { $gte: dayAgo } });

  if (lastDayUserReferrals >= REFERRAL_CODES_PER_DAY_LIMIT) throw new ReferralCodeDailyLimitError(locale);

  try {
    return ReferralCode.generate(userId);
  } catch (e) {
    throw new ReferralCodeUnknownError(locale);
  }
};
