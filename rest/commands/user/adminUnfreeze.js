const moment = require('moment');
const UserFreeze = require('../../model/audb/UserFreeze');

module.exports = async ({ user, starttime, endtime, working }) => {
  const nowDate = moment().unix();

  if (!starttime && !endtime) {
    const result = await user.unfreeze();

    return { errorcode: 0, result: !!result };
  }
  if (endtime > nowDate && working) {
    const result = await user.unfreeze();

    return { errorcode: 30, result };
  }

  const delay = await UserFreeze.findOne({ uid: user.id, starttime, endtime }).exec();

  if (delay) {
    return { errorcode: 0, result: !!await delay.remove() };
  }

  return { errorcode: 0, result: false };
};
