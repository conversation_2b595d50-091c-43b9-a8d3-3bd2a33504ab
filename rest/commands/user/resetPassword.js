const { WrongResetPasswordError } = require('@s1/api-errors');
const removeManyDevicesCommand = require('../../commands/user/registeredDevices/deleteMany');

module.exports = async ({ user, oldPassword, newPassword, locale }) => {
  if (!oldPassword || !newPassword) throw new WrongResetPasswordError(locale);

  const isSuccess = await user.resetPassword(oldPassword, newPassword);

  if (!isSuccess) throw new WrongResetPasswordError(locale);

  await removeManyDevicesCommand(user.id);
};
