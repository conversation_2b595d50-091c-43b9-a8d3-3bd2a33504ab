const User = require('../../../model/audb/User');
const { SEARCH_REQUEST_DATA } = require('../../../constants/usercenter');
const getUserSearchQuery = require('../../../helpers/query/userSearch');

module.exports = async (request) => {
  const { body } = request;
  const { filters, query } = body;

  const userMatch = query;
  const userAnd = [];

  for (const key of Object.keys(filters)) {
    switch (key) {
      case SEARCH_REQUEST_DATA.USER_PERMISSION_GROUP_ID:
      case SEARCH_REQUEST_DATA.ID:
      case SEARCH_REQUEST_DATA.EMAIL:
      case SEARCH_REQUEST_DATA.NAME:
      case SEARCH_REQUEST_DATA.TKEY:
      case SEARCH_REQUEST_DATA.CARD:
      case SEARCH_REQUEST_DATA.TRANSACTION_ID:
      case SEARCH_REQUEST_DATA.WALLET_ADDRESSS:
      case SEARCH_REQUEST_DATA.PAID_TYPES:
      case SEARCH_REQUEST_DATA.IS_BLACKLISTED:
      case SEARCH_REQUEST_DATA.IS_STEALER:
      case SEARCH_REQUEST_DATA.IS_ACTIVE:
      case SEARCH_REQUEST_DATA.IS_TRIAL:
      case SEARCH_REQUEST_DATA.IS_EXPIRED:
      case SEARCH_REQUEST_DATA.IS_ADMIN:
      case SEARCH_REQUEST_DATA.IS_SUPPORT:
      case SEARCH_REQUEST_DATA.IS_DEVELOPER:
      case SEARCH_REQUEST_DATA.IS_VODADMIN:
      case SEARCH_REQUEST_DATA.USER_AGENT:
      case SEARCH_REQUEST_DATA.EMAILS:
      case SEARCH_REQUEST_DATA.PAID:
      case SEARCH_REQUEST_DATA.USERS_FROM_COUNTRIES:
      case SEARCH_REQUEST_DATA.WATCH_ACTIVITIES_LAST_DAYS:
      {
        const { operator, query: queryValue } = await getUserSearchQuery(key, filters[key]);

        if (operator && queryValue) userAnd.push({ [operator]: queryValue });

        break;
      }
      default:
        break;
    }
  }

  if (userAnd.length) userMatch.$and = userAnd;

  const filteredUsers = await User.find(userMatch, { _id: 0, id: 1 }).lean();
  const usersIds = filteredUsers.map(user => user.id);

  return usersIds;
};
