const { WrongEmailTemplateError } = require('@s1/api-errors');
const getUsersIDs = require('./getUsersIDs');
const sendOneUserEmail = require('./send');
const User = require('../../../model/audb/User');

module.exports = async (request) => {
  const { user, body } = request;
  const { locale } = request.query;
  const { template, type } = body;

  if (!template) throw new WrongEmailTemplateError();

  const usersIds = await getUsersIDs(request);
  const responseData = {
    currentProgress: 0,
    successSend: 0,
    failedSend: 0,
    totalSelectedUsers: usersIds.length,
  };

  for (let i = 0; i < usersIds.length; ++i) {
    const currentUserId = usersIds[i];
    const currentUser = await User.findOne({ id: currentUserId }).lean();
    const email = User.decryptEmail(currentUser.em);
    const requestForSend = {
      query: { locale },
      body: { template, email, id: currentUserId, type },
    };

    let success = false;

    try {
      const sendResponse = await sendOneUserEmail(requestForSend);

      if (sendResponse && sendResponse.success) success = true;
    } catch (e) {} // error already saved in the sendOneUserEmail()

    if (success) {
      responseData.successSend++;
    } else {
      responseData.failedSend++;
    }

    responseData.currentProgress++;

    const socketDataUser = { data: { sentResults: responseData, userId: user.id }, groupName: user.id, eventName: 'adminPanelSendEmailsAllUsers' };
    global.io.emit('group', socketDataUser);
  }

  return { success: true, message: 'Sent response via socket' };
};
