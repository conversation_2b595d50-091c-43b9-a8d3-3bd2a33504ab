const logger = require('@s1/log');
const { WrongEmailTemplateError } = require('@s1/api-errors');
const moment = require('moment');
const User = require('../../../model/audb/User');
const config = require('../../../../config');
const { MailTemplatePostmark } = require('../../../model/audb/MailTemplate');

const log = logger.create(__filename);

module.exports = async (request) => {
  const { locale } = request.query;
  const { template, email, id, type } = request.body;
  const emailFrom = config.email.noReply;
  const emailTemplate = await MailTemplatePostmark.findOne({ id: template }).lean().exec();

  if (!emailTemplate) throw new WrongEmailTemplateError();

  const emailTemplateLastThreeCharacters = emailTemplate.tagname.slice(-3);
  const allLocales = config.i18n.locales;
  const emailTemplateLocales = new Set();
  allLocales.forEach(_locale => emailTemplateLocales.add(`_${_locale}`));

  if (emailTemplateLocales.has(emailTemplateLastThreeCharacters)) {
    emailTemplate.tagname = emailTemplate.tagname.slice(0, -3);
  }

  try {
    const result = await User.mail(emailTemplate.tagname, emailFrom, email, {}, {}, locale);

    if (result) {
      let updatedUser;

      if (type === 'email') {
        updatedUser = await User.findOneAndUpdate(
          { id },
          { $set: { lastemailtemplate: emailTemplate.tagname, lastsendemaildate: moment().unix() } },
          { upsert: false, new: true },
        ).exec();
      } else {
        updatedUser = await User.findOneAndUpdate(
          { id },
          { $inc: { activeagain: 1 } },
          { upsert: false, new: true },
        ).lean();
      }

      return {
        errorcode: 0,
        success: true,
        result: `email ${email} success`,
        saveresult: updatedUser,
      };
    }
  } catch (e) {
    console.log('Send emails::cannot send email, error:', e);
    const errorMessage = e.stack || (e.message && e.message.toString());
    log.error('Send emails::cannot send email, error:', errorMessage);

    return {
      success: false,
      error: errorMessage,
    };
  }

  return {
    success: false,
    error: 'Cannot send email, unknown error',
  };
};
