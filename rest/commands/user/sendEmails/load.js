// const AdminFilter = require('../../../model/audb/AdminFilter');
const User = require('../../../model/audb/User');
const getUsersIDs = require('./getUsersIDs');

module.exports = async (request) => {
  const { body, user } = request;
  const { limit = 10, offset: skip = 0, sort = { regtime: -1 } } = body;

  // no need to save for now, because is NOT implemented on UI side
  // const viewName = 'sendEmails';
  //
  // await AdminFilter.findOneAndUpdate(
  //   { userId: user.id, viewName },
  //   { $set: { userId: user.id, viewName, filter: JSON.stringify(body) } },
  //   { upsert: true, new: true },
  // ).exec();

  const usersIds = await getUsersIDs(request);

  const baseMatch = { $match: { id: { $in: usersIds } } };
  const usersAggregation = [
    baseMatch,
    { $skip: skip },
    { $limit: limit },
    { $sort: sort },
    {
      $project: {
        _id: 0, id: 1, em: 1, name: 1, lastsendemaildate: 1, lastemailtemplate: 1, isverifiedemail: 1, expires: 1, regtime: 1,
      },
    },
  ];

  const usersResult = await User.aggregate(usersAggregation).exec();
  const users = usersResult.map((user) => {
    user.email = User.decryptEmail(user.em);
    delete user.em;

    return user;
  });
  const total = usersIds.length;

  const socketDataUser = { data: { lists: users, total, userId: user.id }, groupName: user.id, eventName: 'adminPanelSendEmailsLoadUsers' };
  global.io.emit('group', socketDataUser);

  return { success: true, message: 'Sent response via socket' };
};
