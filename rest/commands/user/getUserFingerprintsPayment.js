const config = require('../../../config');
const UserFingerprintPayment = require('../../model/audb/UserFingerprintPayment');

module.exports = async (id) => {
  if (!config.paymentBlacklist.fingerprints.enabled) return [];

  const userFingerprints = await UserFingerprintPayment.find({ uid: id })
    .lean().cache(config.paymentBlacklist.cache, `userFingerprintPayment_id_${id}`);

  return userFingerprints;
};
