const AppleInvite = require('../../model/audb/AppleInvite');
const AppleInviteAccount = require('../../model/audb/AppleInviteAccount');
const User = require('../../model/audb/User');
const { LastIdModel } = require('../../model/audb/LastId');
const TestFlight = require('../../classes/TestFlight');

module.exports = async (data) => {
  const { userId, accountId, accountName, email, firstName, lastName } = data;

  if (!email) throw new Error('email required');

  const appleInviteAccount = await AppleInviteAccount.findOne({ _id: accountId }).lean();

  if (!appleInviteAccount) throw new Error('Account not found');

  const { aud, iss, keyid, algorithm, cert, appId, appName, groupId, groupName, type } = appleInviteAccount;
  const service = new TestFlight({ aud, iss, keyid, cert, algorithm });
  const result = await service.addTester({ appId, groupId, email, firstName, lastName });

  let userIdByEmail;

  if (!userId) {
    const em = User.encryptEmail(email);
    const user = await User.findOne({ em }).lean();

    if (!user) return result;

    userIdByEmail = user.id;
  }

  const inviteid = await LastIdModel.getNextId(AppleInvite, 'inviteid');
  const appleInvite = new AppleInvite({
    inviteid,
    type,
    email,
    uid: userId || userIdByEmail,
    success: result.success,
    message: result.hasOwnProperty('message') ? result.message : null,
    response: result.hasOwnProperty('response') ? result.response : null,
    accountId,
    accountName,
    appId,
    appName,
    groupId,
    groupName,
  });
  await appleInvite.save();

  return result;
};
