const log = require('@s1/log').create(__filename);
const {
  AlreadyReason,
  ErrorWhileRefunding,
} = require('../../errors');
const blockUserCommand = require('./block');

module.exports = async ({ paymentLog, locale }) => {
  const { uid } = paymentLog;

  if (paymentLog.hasOwnProperty('isRefunded') && paymentLog.isRefunded
    || paymentLog._doc.hasOwnProperty('isRefunded') && paymentLog._doc.isRefunded) throw new AlreadyReason('Already refunded');

  try {
    await paymentLog.refund();
  } catch (e) {
    log.error(e.stack || (e.message && e.message.toString()));

    throw new ErrorWhileRefunding('Error while refunding');
  }

  const reason = 'Fraud payment';
  const comment = `tkey : ${paymentLog.tkey} - bank notified us its a fraud and we made a refund and blocked his account`;
  const blockUserResult = await blockUserCommand(uid, reason, comment, paymentLog, locale);

  return blockUserResult;
};
