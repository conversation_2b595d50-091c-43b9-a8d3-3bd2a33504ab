const {
  UserFreezeError,
  UserSuspendedError,
} = require('@s1/api-errors');
const PackageToFeatures = require('../../model/audb/PackageToFeatures');
const UserFreeze = require('../../model/audb/UserFreeze');
const UserGlobal = require('../../model/audb/UserGlobal');
const favoritesListCommand = require('../../commands/user/favorites/list');
const getUserGroupForWebcamApp = require('../../helpers/getUserGroupForWebcamApp');

module.exports = async ({
  user, authenticationError, fav, useragent, flash, sessionID, IPInfo, checkperm, appName = null, locale,
}) => {
  const [featuresList, freeze, userGlobal, playerRule] = await Promise.all([
    PackageToFeatures.findOne({ id: user.Package.pgid }).cache(3600).exec(),
    UserFreeze.findOne().byUidAndCurrentTime(user.id).cache(120).exec(),
    UserGlobal.findOne({ data: 'exoplayer' }).cache(3600).exec(),
    user.getPlayerRule(useragent || '', flash || 0),
  ]);
  const player = await playerRule.getPlayer();
  const result = user.format({
    featuresList: featuresList.format({ locale }),
    freeze,
    userConfig: user.config,
    playerRule,
    player,
    userPackage: user.Package,
    billingAddresses: user.billingAddresses,
    sid: sessionID,
    locale,
  });

  if (authenticationError) {
    if (authenticationError instanceof UserFreezeError) result.status = 10;
    else if (authenticationError instanceof UserSuspendedError) result.status = 100;

    result.error = authenticationError.code;
    result.errorcode = authenticationError.code;
    result.errormsg = authenticationError.message;
  } else result.error = 0;

  result.exominbuffer = userGlobal.minbuffer;
  result.exoplaybackbuffer = userGlobal.playblackbuffer;
  result.ipinfo = IPInfo;
  result.vodcate = null;
  result.ptime = null;
  result.channels = null;

  const conditionalPromises = [];

  if (fav && fav === '1') {
    conditionalPromises.push(
      favoritesListCommand({ type: 99, user, locale }).then(myfav => ({ myfav }))
    );
  }
  if (checkperm && checkperm === '1') {
    conditionalPromises.push(
      getUserGroupForWebcamApp(user, appName).then(webcug => ({ webcug }))
    );
  }
  if (conditionalPromises.length > 0) {
    const conditionalResults = await Promise.all(conditionalPromises);
    conditionalResults.forEach(resultObj => {
      Object.assign(result, resultObj);
    });
  }

  delete result.em;
  delete result.salt;
  delete result.password;
  delete result.config;
  delete result._id;
  delete result.registerip;
  delete result.byid;
  delete result.package.days;
  delete result.userconf._id;
  delete result.Package;
  delete result.Freeze;
  delete result.proxyCounts;
  delete result.proxyNetworks;

  return result;
};
