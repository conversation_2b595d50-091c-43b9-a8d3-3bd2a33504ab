const log = require('@s1/log').create(__filename);
const { user: { REFUND_REASONS } } = require('@s1/api-constants');
const {
  RefundReasonNotSet,
  UnknownReason,
  AlreadyReason,
  ErrorWhileRefunding,
} = require('../../errors');

module.exports = async ({ reason, paymentLog }) => {
  if (!reason) throw new RefundReasonNotSet('Reason not set');
  if (!REFUND_REASONS[reason]) throw new UnknownReason('Unknown reason');
  if (paymentLog.hasOwnProperty('isRefunded') && paymentLog.isRefunded
    || paymentLog._doc.hasOwnProperty('isRefunded') && paymentLog._doc.isRefunded) throw new AlreadyReason('Already refunded');

  try {
    await paymentLog.refund(REFUND_REASONS[reason]);
  } catch (e) {
    log.error(e.stack || (e.message && e.message.toString()));

    throw new ErrorWhileRefunding('Error while refunding');
  }
};
