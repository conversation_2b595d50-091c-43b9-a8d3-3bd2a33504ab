const {
  LoginFailedError,
} = require('@s1/api-errors');
const i18n = require('../../helpers/geti18n');

module.exports = async ({ user, password, locale }) => {
  const isCheckPassword = user.checkPassword(password);

  i18n.setLocale(locale);

  if (!isCheckPassword) {
    throw new LoginFailedError(i18n.__('password wrong!'));
  }

  const isStatusUnfreeze = await user.unfreeze();
  const results = isStatusUnfreeze ? i18n.__('delay removed successfully') : isStatusUnfreeze;

  return { error: 0, results };
};
