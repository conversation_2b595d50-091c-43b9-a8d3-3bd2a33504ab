const moment = require('moment');
const { ApiError, WrongParamsError } = require('@s1/api-errors');
const AuthCode = require('../../model/audb/AuthCode');
const config = require('../../../config');
const i18n = require('../../helpers/geti18n');

module.exports = async (code, locale = config.i18n.defaultLocale) => {
  i18n.setLocale(locale);

  if (!code) throw new WrongParamsError(locale);

  const authCode = await AuthCode.findOne({ code }).exec();
  const now = moment().unix();

  if (!authCode) throw new ApiError(501, i18n.__('Wrong code'));
  if (authCode.expires < now) throw new ApiError(501, i18n.__('Code %s expired', code.toUpperCase()));

  return authCode;
};
