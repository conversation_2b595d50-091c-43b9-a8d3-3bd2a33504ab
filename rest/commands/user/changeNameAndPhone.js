const { InvalidInputFieldError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const RegisterValidator = require('../../helpers/RegisterValidator');

module.exports = async ({ id, update, locale }) => {
  const validator = new RegisterValidator();
  validator.validateName(update.name);
  validator.validatePhoneArea(update.phonearea);
  validator.validatePhone(update.phone);

  if (validator.results.error.length) throw new InvalidInputFieldError({ result: validator.results }, locale);
  else return User.findOneAndUpdate({ id }, update, { new: true });
};
