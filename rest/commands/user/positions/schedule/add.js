const UserSchedulePosition = require('../../../../model/audb/UserSchedulePosition');
const Schedule = require('../../../../model/audb/Schedule');
const cache = require('../../../../service/cache');
const settings = require('../../../../service/settings');

module.exports = async ({ uid, channel, rdatetime, position = 0 }) => {
  const [schedule, { alreadySeen }] = await Promise.all([
    Schedule.findOne({ channel, rdatetime }).exec(),
    settings.get(),
  ]);

  if (position < alreadySeen.limit) return;

  await UserSchedulePosition.updateOne(
    { channel, rdatetime, uid },
    {
      $set: {
        channel,
        rdatetime,
        uid,
        position: await UserSchedulePosition.calculatePositionWithBounds(schedule, position),
        createdForTtlIndex: new Date(),
      },
    },
    { upsert: true },
  );
  cache.withNamespace('schedules').flush('schedules', () => {
  });

  return true;
};
