const { HttpError } = require('@s1/api-errors');
const Vod = require('@s1/vod-models/Vod');
const UserVodPosition = require('../../../../model/audb/UserVodPosition');
const settings = require('../../../../service/settings');

module.exports = async ({ uid, vod, position }) => {
  const [video, { alreadySeen }] = await Promise.all([
    Vod.findOne({ id: vod }).exec(),
    settings.get(),
  ]);

  if (position < alreadySeen.limit) return;
  if (video === null) throw new HttpError('Vod not found', 404);

  const duration = video.length || 0;
  let newPosition = 0;

  // eslint-disable-next-line max-len
  if (position > 3 * 60 && position <= duration - 30) newPosition = await UserVodPosition.calculatePositionWithBounds(video, position);

  return UserVodPosition.updateOne(
    { uid, vod },
    {
      $set: {
        uid,
        vod,
        position: newPosition,
      },
    },
    { upsert: true },
  );
};
