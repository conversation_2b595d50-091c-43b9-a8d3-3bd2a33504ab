const { WrongParamsError } = require('@s1/api-errors');
const moment = require('moment');

const EXTRA_TIME_HOURS = 5;
const ATTEMPT_PERIOD_DAYS = 7;

module.exports = async (user) => {
  if (!user) throw new WrongParamsError();

  const now = moment().unix();

  // check if user already tried to pay once per period
  if (!user.lastPaymentAttemptDate || user.lastPaymentAttemptDate + ATTEMPT_PERIOD_DAYS * 24 * 3600 < now) {
    // add extra hours for the user while funds are in processing
    // for expired or will expire in next few hours
    if (user.expires < now + EXTRA_TIME_HOURS * 3600) {
      user.expires = now + EXTRA_TIME_HOURS * 3600;
    }

    user.lastPaymentAttemptDate = now;
    await user.save();

    return {
      errorcode: 0,
      success: true,
      message: `Added extra time ${EXTRA_TIME_HOURS} hours`,
    };
  }

  return {
    errorcode: 0,
    success: false,
    message: 'Already added extra time',
  };
};
