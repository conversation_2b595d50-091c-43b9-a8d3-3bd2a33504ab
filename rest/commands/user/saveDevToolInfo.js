const isLocal = require('is-local-ip');
const moment = require('moment');
const { OUR_SERVERS_IPS } = require('../../constants/ip');
const UserDevToolAction = require('../../model/audb/UserDevToolAction');

const prepareIps = (value) => {
  const ips = [];
  const existIPs = new Set();

  if (value && value.length) {
    const now = moment().unix();

    for (let i = 0; i < value.length; ++i) {
      const userIp = value[i].trim();

      if (userIp && !existIPs.has(userIp) && !isLocal(userIp) && !OUR_SERVERS_IPS.has(userIp)) {
        existIPs.add(userIp);
        ips.push({ ip: userIp, timestamp: now });
      }
    }
  }

  return ips;
};

/**
 * Merge only new IPs do not exist in the last timestamp
 * */
const mergeNewIps = (existingIps = [], newIps = []) => {
  if (!newIps.length) return existingIps;

  const updatedIps = [...existingIps];
  const latestTimestamp = Math.max(...existingIps.map(e => e.timestamp || 0));
  const latestIpsSet = new Set(
    existingIps.filter(e => e.timestamp === latestTimestamp).map(e => e.ip)
  );

  const shouldAdd = newIps.some(({ ip }) => !latestIpsSet.has(ip));

  if (shouldAdd) {
    const now = moment().unix();

    for (const { ip } of newIps) {
      updatedIps.push({ ip, timestamp: now });
    }
  }

  return updatedIps;
};

module.exports = async (user, data) => {
  const { isActive, uuid, ips, deviceInfo, actionType, sourceType, scriptVersion, devtoolDetections } = data;
  const now = moment().unix();
  const preparedIps = prepareIps(ips);

  try {
    if (isActive) {
      const LAST_ACTION_SEC = 10;
      const lastAction = await UserDevToolAction
        .findOne({ userId: user.id, uuid, endTime: { $ne: null, $gt: now - LAST_ACTION_SEC }, isActive: false })
        .sort({ endTime: -1 }).exec();

      // no need to create new log if we have previous one in last XX seconds
      if (lastAction) {
        lastAction.isActive = true;
        lastAction.endTime = null;
        lastAction.ips = mergeNewIps(lastAction.ips,  preparedIps);
        lastAction.actionType = actionType;
        lastAction.createdForTtlIndex = new Date();
        await lastAction.save();
      } else if (deviceInfo && deviceInfo.browser) {
        const newAction = new UserDevToolAction({
          userId: user.id,
          uuid,
          isActive: true,
          deviceInfo,
          startTime: now,
          ips: preparedIps,
          actionType,
          sourceType,
          createdForTtlIndex: new Date(),
          scriptVersion,
          devtoolDetections,
        });
        await newAction.save();
      }
    } else {
      const currentAction = await UserDevToolAction.findOne({ userId: user.id, uuid, isActive: true })
        .sort({ startTime: -1 });

      if (currentAction) {
        currentAction.isActive = false;
        currentAction.endTime = now;
        currentAction.ips = mergeNewIps(currentAction.ips,  preparedIps);
        currentAction.actionType = actionType;
        currentAction.createdForTtlIndex = new Date();
        await currentAction.save();
      }
    }
  } catch (e) {
    console.error('On save Dev Tool info error:', e);
  }

  return {
    error: 0,
    success: true,
  };
};
