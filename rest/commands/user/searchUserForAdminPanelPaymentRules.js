const User = require('../../model/audb/User');
const UserCard = require('../../model/audb/UserCard');
const UserConfig = require('../../model/audb/UserConfig');
const getPreviousUserIPs = require('../../middleware/helpers/getPreviousUserIPs');
const { getUserLocation } = require('../../service/maxmind');
const getAllUserPermissionGroups = require('../permission/getAllUserPermissionGroups');

const getUserPermissionPayments = async (user) => {
  const permissionPayments = {
    groups: [],
    types: [],
    accounts: [],
  };

  const permissionGroupsResult = await getAllUserPermissionGroups();
  const permissionGroups = permissionGroupsResult.list || [];
  let userPermissionGroupsIDs = [];

  if (user.hasOwnProperty('permissionGroups') && user.permissionGroups) {
    userPermissionGroupsIDs = user.permissionGroups.map(groupId => groupId.toString());
  } else {
    const defaultPermissionGroup = permissionGroups.find(group => group.isUserDefaultGroup);

    if (defaultPermissionGroup) {
      userPermissionGroupsIDs = [defaultPermissionGroup._id];
    } else {
      userPermissionGroupsIDs = [permissionGroups[0]._id];
    }
  }

  const userPermissionGroups = permissionGroups.filter(group => userPermissionGroupsIDs.includes(group._id.toString()));
  const permissionPaymentGroupsIDs = [];
  const permissionPaymentTypesIDs = [];
  const permissionPaymentAccountsIDs = [];

  userPermissionGroups.forEach((permissionGroup) => {
    if (permissionGroup.hasOwnProperty('payments') && permissionGroup.payments) {
      if (permissionGroup.payments.hasOwnProperty('groups') && permissionGroup.payments.groups) permissionPaymentGroupsIDs.push(...permissionGroup.payments.groups);
      if (permissionGroup.payments.hasOwnProperty('types') && permissionGroup.payments.types) permissionPaymentTypesIDs.push(...permissionGroup.payments.types);
      if (permissionGroup.payments.hasOwnProperty('accounts') && permissionGroup.payments.accounts) permissionPaymentAccountsIDs.push(...permissionGroup.payments.accounts);
    }
  });

  permissionPayments.groups = Array.from(new Set(permissionPaymentGroupsIDs));
  permissionPayments.types = Array.from(new Set(permissionPaymentTypesIDs));
  permissionPayments.accounts = Array.from(new Set(permissionPaymentAccountsIDs));

  return permissionPayments;
};

/**
 * Search user for the admin panel payment rules
 * */
module.exports = async (data) => {
  if (!data) throw new Error('wrong request');
  if (!data.id && !data.email) throw new Error('id or email required');

  const query = {};

  if (data.id) query.id = data.id;
  if (data.email) query.em = User.encryptEmail(data.email);

  const user = await User.findOne(query, { id: 1, em: 1, name: 1, billingAddresses: 1, permissionGroups: 1 }).exec();

  if (!user) return {
    error: 0,
    user: null,
  };

  // format fields
  const formattedUser = user.toObject();
  formattedUser.email = User.decryptEmail(user.em);
  delete formattedUser.em;
  formattedUser.lastBillingAddress = await user.getLastBillingAddress();
  formattedUser.billingAddresses = formattedUser.billingAddresses ? formattedUser.billingAddresses : {};
  formattedUser.billingAddresses = Object.values(formattedUser.billingAddresses).reverse();
  formattedUser.billingAddresses = formattedUser.billingAddresses.filter((address) => {
    // all fields are required, do not show no fully filled address
    if (!address.firstname || !address.firstname.trim() || !address.lastname || !address.lastname.trim()
      || !address.country || !address.country.trim() || !address.city || !address.city.trim()
      || !address.address || !address.address.trim() || !address.zip || !address.zip.trim()
      // eslint-disable-next-line no-mixed-operators
      || address.country && address.country === 'US' && (!address.state || !address.state.trim() || address.state.trim().length !== 2)) {
      return false;
    }

    return true;
  });

  const userCardNumbers = await UserCard.aggregate([
    {
      $match: { uid: user.id },
    },
    {
      $project: { _id: 0, first6: 1, last4: 1, isBlacklisted: 1, cardType: '$type', cardBrand: '$brand', cardCountry: '$country', cardBankName: '$bank' },
    },
  ]).exec();
  formattedUser.cards = userCardNumbers.reverse();

  const userIPModels = await getPreviousUserIPs(user.id);
  const userIPs = userIPModels.map(model => model.ip);
  const lastUserIPs = userIPs.length > 15 ? userIPs.slice(0, 15) : userIPs;

  formattedUser.ips = lastUserIPs.map((ip) => {
    const userLocation = getUserLocation(ip);

    return {
      ip,
      ipCountry: userLocation.countryCode,
      ipState: userLocation.countryCode === 'US' ? userLocation.stateCode : '',
    };
  });
  formattedUser.config = await UserConfig.findOne({ uid: user.id }).lean() || {};
  formattedUser.permissionPayments = await getUserPermissionPayments(formattedUser);

  return {
    error: 0,
    user: formattedUser,
  };
};
