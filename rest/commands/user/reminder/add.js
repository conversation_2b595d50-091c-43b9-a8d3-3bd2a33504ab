const moment = require('moment');
const UserReminder = require('../../../model/audb/UserReminder');
const Schedule = require('../../../model/audb/Schedule');
const Channel = require('../../../model/audb/Channel');

module.exports = async ({ uid, channel, rdatetime, locale }) => {
  const reminder = await UserReminder.create({ uid, channel, rdatetime });
  const [show, channelInfo] = await Promise.all([
    Schedule.findOne({ channel, rdatetime }).exec(),
    Channel.findOne({ id: channel }).lean({ virtuals: true }).exec(),
  ]);
  const scheduleDate = moment.unix(rdatetime);
  const defaultShow = Schedule.getDefaultSchedule({
    channel: channelInfo,
    scheduleDate,
    locale,
  });
  reminder.show = show || defaultShow;
  reminder.Channel = channelInfo;

  return reminder;
};
