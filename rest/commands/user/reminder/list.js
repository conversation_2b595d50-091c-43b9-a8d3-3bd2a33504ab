const moment = require('moment');
const { user: { statuses } } = require('@s1/api-constants');
const UserReminder = require('../../../model/audb/UserReminder');
const Schedule = require('../../../model/audb/Schedule');
const Channel = require('../../../model/audb/Channel');
const { fillSubscriptions } = require('../../../service/subscribeForData');
const sUserFavoriteChannel = require('../../../model/subscription/sUserFavoriteChannel');

module.exports = async ({ user, withChannel, ISP, countryCode, stateCode }) => {
  const now = Math.floor(Date.now() / 1000);
  const [result, streamingServers] = await Promise.all([
    UserReminder.find({ uid: user.id, rdatetime: { $gt: now } }).lean().exec(),
    user.getStreamingServers(ISP, countryCode, stateCode, 2),
  ]);

  if (result.length === 0) return result;

  const channelList = {};
  const reminders = [];
  const channelIds = new Set();
  const showsQueries = result.map((reminder) => {
    const { channel, rdatetime } = reminder;
    channelIds.add(channel);
    channelList[channel] = channelList[channel] || {};

    return { channel, rdatetime };
  });
  const promises = [Schedule.find({ $or: showsQueries }).lean().exec()];

  if (withChannel) promises.push(Channel.find({ id: { $in: [...channelIds] } }).lean({ virtuals: true }).exec());

  const [shows, channels = []] = await Promise.all(promises);
  const baseCondition = { uid: user.id };
  const subscription = sUserFavoriteChannel({ baseCondition });

  if (withChannel) channels.forEach((channel) => {
    const { id } = channel;
    subscription.add(channel);
    channelList[id] = { Channel: channel };
  });

  await fillSubscriptions();
  shows.forEach((show) => {
    const { channel, rdatetime } = show;
    channelList[channel][rdatetime] = { show };
  });
  result.forEach((reminder) => {
    const { channel, rdatetime } = reminder;
    const scheduleDate = moment.unix(reminder.rdatetime);
    const info = {
      ...reminder,
      isActive: (reminder.rdatetime - (Date.now() / 1000)) < statuses.REMIND_DELAY,
      Channel: channelList[channel] ? channelList[channel].Channel : {},
    };

    if (channelList[channel][rdatetime]) info.show = channelList[channel][rdatetime].show;

    const currentChannel = channels.find(channel => channel.id === channel) || channel;
    info.show = info.show || Schedule.getDefaultSchedule({
      channel: currentChannel,
      scheduleDate,
      thumbnailDomain: streamingServers.mainServer.sip,
    });
    reminders.push(info);
  });

  return reminders;
};
