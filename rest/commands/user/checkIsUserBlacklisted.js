const { WrongRequestArgumentError } = require('@s1/api-errors');
const logger = require('@s1/log');
const isLocal = require('is-local-ip');
const _isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const addUserToPaymentBlacklist = require('../../commands/paymentBlacklist/addUserToPaymentBlacklist');
const addAllUserIpsToPaymentBlacklist = require('../../commands/paymentBlacklist/addAllUserIpsToPaymentBlacklist');
const addAllUserPaymentFingerprintsToPaymentBlacklist = require('../../commands/paymentBlacklist/addAllUserPaymentFingerprintsToPaymentBlacklist');
const addCookieToResponse = require('../../middleware/helpers/paymentBlacklist/addCookieToResponse');
const getPreviousUserIPs = require('../../middleware/helpers/getPreviousUserIPs');
const getUserFingerprintsPayment = require('../../commands/user/getUserFingerprintsPayment');
const { OUR_SERVERS_IPS } = require('../../constants/ip');
const UserIp = require('../../model/audb/UserIp');
const config = require('../../../config');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const PaymentBlacklist = require('../../model/audb/PaymentBlacklist');

const log = logger.create(__filename);

module.exports = async ({ req, res, user, body = null }) => {
  if (!user) throw new WrongRequestArgumentError('User sid is required');

  // save user billing address
  try {
    const billingAddress = {
      country: body.country,
      state: body.state,
      address: body.address,
      city: body.city,
      zip: body.zip,
      firstname: body.firstname,
      lastname: body.lastname,
    };

    await user.addBillingAddress(billingAddress);
  } catch (e) {
    const errorMessage = e.stack || (e.message && e.message.toString());
    log.error(`checkIsUserBlacklisted::cannot save billing address, body: '${body}', error: ${errorMessage}`);
  }

  const { ip: userIp } = body;

  // check and save new detected user IP
  if (userIp && !isLocal(userIp) && !OUR_SERVERS_IPS.has(userIp)) {
    let userIPModel = await UserIp.findOne({ uid: user.id, ip: userIp })
      .lean().cache(config.paymentBlacklist.cache, `userIp_id_${user.id}_ip_${userIp}`);

    // save detected new user IP address
    if (!userIPModel) {
      userIPModel = new UserIp({ uid: user.id, ip: userIp });
      await userIPModel.save();
      await removeRedisCacheByKeys([`userIp_id_${user.id}`]);
    }
  }
  // skip checking is blacklisted user with skipBlacklistPayment property
  if (user.skipBlacklistPayment) return {
    error: 0,
    isBlackListed: false,
  };

  const isBlacklistedUser = await _isBlacklistedUser({ user });
  const isBlacklistedUserData = body ? await _isBlacklistedUser({ user: body }) : false;

  let paymentBlacklistedUserIp;

  if (userIp) {
    paymentBlacklistedUserIp = await PaymentBlacklist.findOne({ ip: userIp })
      .lean().cache(config.paymentBlacklist.cache, `paymentBlacklist_ip_${userIp}`);
  }
  if (isBlacklistedUser || isBlacklistedUserData || paymentBlacklistedUserIp) {
    const userIPModels = await getPreviousUserIPs(user.id);
    const userIPs = userIPModels.map(model => model.ip);
    const userFingerprintModels = await getUserFingerprintsPayment(user.id);
    const userFingerprints = userFingerprintModels.map(model => model.fingerprint);

    const blacklistedRule = isBlacklistedUser.rule || isBlacklistedUserData.rule;
    const blacklistedAddress = isBlacklistedUser.data || isBlacklistedUserData.data;
    const userDescription = paymentBlacklistedUserIp ? `Blocked by blacklisted userIP ${userIp} for the user ${user.id} ${user.email}` : `Blocked by blacklisted billingAddress, rule: ${blacklistedRule}, address: ${blacklistedAddress}`;
    const ipDescription = paymentBlacklistedUserIp ? `Blocked by blacklisted userIP ${userIp} for the user ${user.id} ${user.email}` : `Blocked by blacklisted billingAddress for the user ${user.id} ${user.email}, rule: ${blacklistedRule}, address: ${blacklistedAddress}`;
    await addUserToPaymentBlacklist(user, userDescription);
    // no need to wait for add to the blacklist ips, etc
    addAllUserIpsToPaymentBlacklist(user, userIPs, ipDescription);
    addAllUserPaymentFingerprintsToPaymentBlacklist(userFingerprints, userDescription);
    await addCookieToResponse(user, req, res);

    return {
      error: 0,
      isBlackListed: true,
    };
  }

  return {
    error: 0,
    isBlackListed: false,
  };
};
