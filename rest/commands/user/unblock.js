const sendMailCommand = require('../mail/send');
const User = require('../../model/audb/User');
const Suspended = require('../../model/audb/Suspended');
const config = require('../../../config');
const removeSuspendedUserAndRelatedUsers = require('../../commands/suspend/removeSuspendedUserAndRelatedUsers');

module.exports = async (uid, reason, comment, paymentLog = {}, locale) => {
// unblock device
  await User.unblockDevice({ uid });

  // just unsuspend
  await removeSuspendedUserAndRelatedUsers(uid, null, comment);

  const user = await User.findOne({ id: uid }).exec();
  await user.addAdminComment(comment, uid);
  await sendMailCommand({
    tag: 'dispute_won',
    from: config.email.noReply,
    to: user.email,
    data: paymentLog,
    locale,
  });

  return 'user unlocked';
};
