const isLocal = require('is-local-ip');
const UserDevToolAction = require('../../model/audb/UserDevToolAction');
const { getUserLocation } = require('../../service/maxmind');
const proxydetect = require('../../service/proxydetect');
const getProxyRule = require('../proxy/getProxyRule');
const AdminFilter = require('../../model/audb/AdminFilter');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const isStealerUser = require('../payment/helpers/rules/isStealerUser');
const User = require('../../model/audb/User');
const getUsersIDs = require('../user/sendEmails/getUsersIDs');
const { SEARCH_REQUEST_DATA } = require('../../constants/usercenter');

module.exports = async (user, data) => {
  const { page, pageSize, filter, type = null } = data;
  const { userId, isActiveDevTool, sourceType, devtoolDetections } = filter;

  const viewName = 'usersDevToolActionsList';

  await AdminFilter.findOneAndUpdate(
    { userId: user.id, viewName },
    { $set: { userId: user.id, viewName, filter } },
    { upsert: true, new: true },
  ).exec();

  const queryFilter = {};

  if (userId) queryFilter.userId = userId;
  else {
    const numberFilterKeys = [
      SEARCH_REQUEST_DATA.IS_ACTIVE,
      SEARCH_REQUEST_DATA.IS_TRIAL,
      SEARCH_REQUEST_DATA.IS_EXPIRED,
      SEARCH_REQUEST_DATA.IS_ADMIN,
      SEARCH_REQUEST_DATA.IS_SUPPORT,
      SEARCH_REQUEST_DATA.IS_DEVELOPER,
      SEARCH_REQUEST_DATA.IS_VODADMIN,
      SEARCH_REQUEST_DATA.IS_BLACKLISTED,
      SEARCH_REQUEST_DATA.IS_STEALER,
    ];
    const stringFilterKeys = [
      SEARCH_REQUEST_DATA.USER_PERMISSION_GROUP_ID,
      SEARCH_REQUEST_DATA.EMAIL,
      SEARCH_REQUEST_DATA.NAME,
    ];
    const hasActiveNumberFilters = numberFilterKeys.some(key => filter[key] === 0 || filter[key] === 1);
    const hasActiveStringFilters = stringFilterKeys.some(key => filter[key] && filter[key] !== '');

    if (hasActiveNumberFilters || hasActiveStringFilters) {
      const dataForUserIds = {
        body: {
          filters: filter,
          query: {},
        }
      }
      const usersIds = await getUsersIDs(dataForUserIds);
      queryFilter.userId = { $in: usersIds };
    }
  }
  if (isActiveDevTool === 1) {
    queryFilter.isActive = true;
  } else if (isActiveDevTool === 0) {
    queryFilter.isActive = false;
  }
  if (sourceType && sourceType !== 'All') {
    queryFilter.sourceType = sourceType;
  }
  if (devtoolDetections && devtoolDetections.length) {
    if (!queryFilter.hasOwnProperty('$and')) queryFilter.$and = [{ devtoolDetections: { $exists: true } }];

    devtoolDetections.forEach((detection) => {
      if (detection === 'debugDelay') {
        queryFilter.$and.push({ 'devtoolDetections.debugDelay': { $gt: 0 } });
      } else {
        queryFilter.$and.push({ [`devtoolDetections.${detection}`]: true });
      }
    });
  }

  let query = UserDevToolAction.find(queryFilter)
    .sort({ startTime: -1 })
    .skip(page * pageSize)
    .limit(pageSize);

  if (type === 'list') {
    query = query.populate('User');
  }

  const actions = await query.lean();

  const total = await UserDevToolAction.countDocuments(queryFilter).exec();
  const formattedActions = await Promise.all(
    actions.map(async (action) => {
      const ips = [];

      for (let i = 0; i < action.ips.length; ++i) {
        const userIP = action.ips[i].ip;
        const userLocation = await getUserLocation(userIP);
        const ipInfo = { ...action.ips[i], userLocation };

        const proxy = {
          vpn: false,
          rule: 'none',
        };

        if (!isLocal(userIP)) {
          const proxyResult = await proxydetect.check(userIP);
          proxy.vpn = proxyResult.result;
          proxy.rule = await getProxyRule({ userIP, userISP: userLocation.ISP, userLocation });
        }

        ipInfo.proxy = proxy;
        ips.push(ipInfo);
      }

      action.ips = ips;

      if (action.User) {
        const actionUser = {
          id: action.User.id,
          name: action.User.name,
          expires: action.User.expires,
        };
        actionUser.email = User.decryptEmail(action.User.em);
        actionUser.isBlacklisted = await isBlacklistedUser({ user: action.User });
        actionUser.isStealerUser = await isStealerUser({ user: action.User });

        action.User = { ...actionUser };
      }

      return action;
    })
  );

  const pages = Math.ceil(total / pageSize);

  return {
    error: 0,
    result: {
      actions: formattedActions,
      total,
      pages,
    },
  };
};
