const AppleInviteAccount = require('../../model/audb/AppleInviteAccount');

module.exports = async (_id) => {
  if (!_id) throw new Error('ID is required');

  const pppleInviteAccount = await AppleInviteAccount.findOne({ _id }).exec();

  if (!pppleInviteAccount) throw new Error('Apple invite account has been deleted');

  await pppleInviteAccount.remove();

  return {
    error: 0,
    success: true,
  };
};
