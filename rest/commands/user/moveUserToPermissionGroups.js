const User = require('../../model/audb/User');
const UserPermissionGroup = require('../../model/audb/UserPermissionGroup');

/**
 * Move user to users permission group
 *
 * @param {array} option.userIds
 * @param {array} option.permissionGroups
 * */
module.exports = async (data) => {
  if (!data) throw new Error('wrong request');
  if (!data.userIds || !data.userIds.length) throw new Error('Users IDs is required');
  if (!data.permissionGroups || !data.permissionGroups.length) throw new Error('Permission groups are required');

  const permissionGroupsModels = await UserPermissionGroup.find({ isUserGroup: true }).lean();
  const permissionGroupsIDs = permissionGroupsModels.map(group => group._id.toString());

  for (let i = 0; i < data.userIds.length; ++i) {
    const userId = data.userIds[i];
    const user = await User.findOne({ id: userId }).exec();

    if (!user) throw new Error('User not found');

    const nonUsersPermissions = (user.permissionGroups || []).map(group => group.toString())
      .filter(group => !permissionGroupsIDs.includes(group.toString()));
    const newPermissions = nonUsersPermissions.concat(data.permissionGroups);

    user.permissionGroups = newPermissions;

    await user.save();
  }

  return { error: 0, success: true };
};
