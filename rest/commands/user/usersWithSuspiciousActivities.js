const User = require('../../model/audb/User');
const SuspectedWatchActivity = require('../../model/audb/SuspectedWatchActivity');

const getUsersWithSuspiciousActivities = async () => {
  const users = await SuspectedWatchActivity.aggregate([
    {
      $group: {
        _id: {
          uid: "$uid",
          type: "$type",
          date: { $dateToString: { format: "%Y-%m-%d", date: "$createdForTtlIndex" } }
        },
        logs: {
          $push: {
            count: { $size: "$$ROOT.logs" },
            startTime: "$$ROOT.startTime",
            vodNames: {
              $map: {
                input: {
                  $filter: {
                    input: "$$ROOT.logs",
                    as: "log",
                    cond: {
                      $and: [
                        {
                          $eq: [{ $type: "$$log.vodname" }, "string"]
                        },
                        { $ne: ["$$log.vodname", null] },
                        { $ne: ["$$log.vodname", ""] }
                      ]
                    }
                  }
                },
                as: "filteredLog",
                in: "$$filteredLog.vodname"
              }
            },
            channelNames: {
              $map: {
                input: {
                  $filter: {
                    input: "$$ROOT.logs",
                    as: "log",
                    cond: {
                      $and: [
                        {
                          $eq: [{ $type: "$$log.chname" }, "string"]
                        },
                        { $ne: ["$$log.chname", null] },
                        { $ne: ["$$log.chname", ""] }
                      ]
                    }
                  }
                },
                as: "filteredLog",
                in: "$$filteredLog.chname"
              }
            },
          }
        },
        maxEndTime: { $max: "$$ROOT.endTime" }
      }
    },
    {
      $lookup: {
        from: 'tuser',
        localField: '_id.uid',
        foreignField: 'id',
        as: 'User'
      }
    },
    {
      $unwind: {
        path: "$User",
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'userDevToolAction',
        localField: '_id.uid',
        foreignField: 'userId',
        as: 'devToolActions'
      }
    },
    {
      $addFields: {
        devToolActionsCount: { $size: { $ifNull: ['$devToolActions', []] } }
      }
    },
    {
      $project: {
        _id: 0,
        id: "$_id.uid",
        type: "$_id.type",
        date: "$_id.date",
        logs: 1,
        name: "$User.name",
        em: "$User.em",
        regtime: "$User.regtime",
        devToolActionsCount: 1,
        maxEndTime: 1,
      }
    },
    {
      $sort: {
        maxEndTime: -1,
      }
    }
  ])
    .then((items) =>
      Promise.all(
        items.map(async (user) => {
          user.email = await User.decryptEmailWithRedis(user.em);
          delete user.em;

          if (user.logs) {
            user.logs.sort((a, b) => {
              if (a.startTime < b.startTime) return 1;
              if (a.startTime > b.startTime) return -1;

              return 0;
            });
          }

          return user;
        }),
      ),
    );

  return users;
};

module.exports = {
  load: async () => {
    const result = await getUsersWithSuspiciousActivities();

    return {
      error: 0,
      result,
    };
  },
  remove: async (userId) => {
    if (!userId) {
      return {
        error: 1,
        message: 'User ID is required',
      };
    }

    await SuspectedWatchActivity.deleteMany({ uid: userId }).exec();

    return {
      error: 0,
      result: true,
    };
  },
};
