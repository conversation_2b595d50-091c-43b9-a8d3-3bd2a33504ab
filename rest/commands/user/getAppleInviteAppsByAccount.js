const TestFlight = require('../../classes/TestFlight');

module.exports = async (data) => {
  if (!data.algorithm) return new Error('algorithm required');
  if (!data.keyid) return new Error('keyid required');
  if (!data.aud) return new Error('aud required');
  if (!data.iss) return new Error('iss required');
  if (!data.cert) return new Error('cert required');

  const { aud, iss, keyid, algorithm, cert } = data;
  const service = new TestFlight({ aud, iss, keyid, cert, algorithm });
  const result = await service.getAllAppsWithGroups();

  return {
    error: 0,
    apps: result,
  };
};
