const UserConfig = require('../../../model/audb/UserConfig');
const StreamingServerGroup = require('../../../model/audb/StreamingServerGroup');

module.exports = () => UserConfig.aggregate([
  { $match: { speed: { $ne: null } } },
  { $group: { _id: '$speed', total: { $sum: 1 } } },
  { $sort: { total: -1 } },
]).then((counts) => {
  const promises = counts.map(async (count) => {
    const streamingServer = (await StreamingServerGroup.getServer(count._id)) || { sname: 'UNEXISTENT', sip: '' };
    const { sname, sip } = streamingServer;
    Object.assign(count, { name: sname, host: sip });

    return count;
  });

  return Promise.all(promises);
});
