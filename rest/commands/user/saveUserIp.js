const isLocal = require('is-local-ip');
const { OUR_SERVERS_IPS } = require('../../constants/ip');
const UserIp = require('../../model/audb/UserIp');
const config = require('../../../config');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (user, userIp) => {
  if (!userIp) return {
    error: 0,
    success: false,
  };
  if (userIp && !isLocal(userIp) && !OUR_SERVERS_IPS.has(userIp)) {
    const userIPModel = await UserIp.findOne({ uid: user.id, ip: userIp })
      .lean().cache(config.paymentBlacklist.cache, `userIp_id_${user.id}_ip_${userIp}`);

    // save detected new user IP address
    if (!userIPModel) {
      try {
        const ipData = { uid: user.id, ip: userIp };
        await UserIp.updateOne(ipData, { $set: ipData }, { upsert: true }).exec();
      } catch (e) {
        console.log(`Cannot save user IP, user: ${user.id}, userIP: ${userIp}`);
      }
      await removeRedisCacheByKeys([`userIp_id_${user.id}`]);
    }
  }

  return {
    error: 0,
    success: true,
  };
};
