const { InvoiceNotFound } = require('../../../errors');
const Invoice = require('../../../model/audb/Invoice');
const i18n = require('../../../helpers/geti18n');

module.exports = async ({ sessionID, tkey, locale }) => {
  const invoice = await Invoice.findOne({ tkey });

  i18n.setLocale(locale);

  if (!invoice) throw new InvoiceNotFound(i18n.__('No such invoice #%s', tkey));

  return await invoice.getUrl(sessionID);
};
