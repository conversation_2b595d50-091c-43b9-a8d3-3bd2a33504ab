const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const Suspended = require('../../model/audb/Suspended');
const { suspendedTypes, suspendedTypesList } = require('../../constants/suspended');

module.exports = async (type) => {
  let list = [];

  if (!type || !suspendedTypesList.includes(type)) return new ApiError(903, 'Invalid suspended type');

  switch (type) {
    case suspendedTypes.uid:
      list = await Suspended.aggregate([
        { $match: { uid: { $exists: true } } },
        {
          $lookup: {
            from: 'tuser',
            localField: 'uid',
            foreignField: 'id',
            as: 'User',
          },
        },
        { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$uid',
            uid: { $first: '$uid' },
            id: { $first: '$_id' },
            description: { $first: '$description' },
            creationMethod: { $first: '$creationMethod' },
            updated: { $first: '$updated' },
            created: { $first: '$created' },
            addedByName: { $first: '$addedByName' },
            addedByUid: { $first: '$addedByUid' },
            em: { $first: '$User.em' },
          },
        },
        {
          $project: { _id: '$id', uid: 1, creationMethod: 1, description: 1, updated: 1, created: 1, addedByName: 1, addedByUid: 1, em: 1, type: suspendedTypes.uid },
        },
        { $sort: { created: -1 } },
      ])
        .then(results => Promise.all(results.map(async (result) => {
          result.email = result.em ? await User.decryptEmailWithRedis(result.em) : '';

          return result;
        })));
      break;
    default:
      break;
  }

  return {
    error: 0,
    result: list,
  };
};
