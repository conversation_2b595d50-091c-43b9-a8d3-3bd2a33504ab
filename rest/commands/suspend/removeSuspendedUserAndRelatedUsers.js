const getRelatedUsersIDsToCurrentSuspendedUser = require('./getRelatedUsersIDsToCurrentSuspendedUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserCard = require('../../model/audb/UserCard');
const Suspended = require('../../model/audb/Suspended');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const { ignoredCardNumbersEncryptedList } = require('../../constants/suspended');

/**
 * Remove all user cards from the suspended list.
 * Clean cache keys for userCard_uid and userCard_suspended_cardNumber.
 * */
const removeUserSuspendedCards = async (userId) => {
  // remove all user card numbers from the suspended list
  const userCardsCacheKeys = [];
  const userCardsForUpdate = await UserCard.find({ uid: userId, isSuspended: true }, { number: 1 }).lean();
  const filteredCardNumbers = process.env.NODE_ENV === 'production' ? userCardsForUpdate.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCardsForUpdate;

  const userSuspendedCards = [];
  filteredCardNumbers.forEach((model) => {
    userCardsCacheKeys.push(`userCard_suspended_cardNumber_${model.number}`);
    userSuspendedCards.push(model.number);
  });

  if (userSuspendedCards.length) {
    // update cards for all users
    await UserCard.updateMany(
      { number: { $in: userSuspendedCards } },
      { $set: { isSuspended: false, suspendDescription: '' } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Add admin comment to the user on remove him from the suspended list
 * */
const addUserAdminCommentOnRemoveFromSuspendList = async (userId, adminUser, comment = null) => {
  if (adminUser || comment) {
    const message = adminUser ? `User removed from the suspended list by ${adminUser.name} (${adminUser.id})` : comment;
    await UserAdminComment.createNew(userId, message);
  }
};

module.exports = async (currentUserId, adminUser, comment = null) => {
  const usersIds = await getRelatedUsersIDsToCurrentSuspendedUser(currentUserId);
  const redisKeys = [`suspended_id_${currentUserId}`];

  for (let i = 0; i < usersIds.length; ++i) {
    const userId = usersIds[i];
    await removeUserSuspendedCards(userId);
    await addUserAdminCommentOnRemoveFromSuspendList(userId, adminUser, comment);
    redisKeys.push(`suspended_id_${userId}`);
  }

  await removeRedisCacheByKeys(redisKeys);
  await Suspended.deleteMany({ uid: { $in: usersIds } }).exec();
};
