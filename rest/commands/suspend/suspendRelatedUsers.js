const getRelatedUsersToCurrentSuspendedUser = require('./getRelatedUsersToCurrentSuspendedUser');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const UserCard = require('../../model/audb/UserCard');
const Suspended = require('../../model/audb/Suspended');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const { creationMethods, ignoredCardNumbersEncryptedList } = require('../../constants/suspended');

/**
 * Add user to the suspended list
 * Clean cache keys for suspended_id_${userId}
 * */
const addUserToSuspendedList = async (userId, description = '') => {
  try {
    const suspended = new Suspended();
    suspended.creationMethod = creationMethods.automatic;
    suspended.description = description;
    suspended.uid = userId;
    await removeRedisCacheByKeys([`suspended_id_${userId}`]);
    await suspended.save();
  } catch (e) {
    console.error(`Cannot add user# ${userId} to the suspended list`, e);
  }
};

/**
 * Add all user cards to the suspended list.
 * Clean cache keys for userCard_uid and userCard_suspended_cardNumber.
 * */
const addUserCardsToSuspendedList = async (userId, description) => {
  const userCardsCacheKeys = [];
  const userCardsForUpdate = await UserCard.find(
    { $and: [
      { uid: userId },
      { $or: [
        { isSuspended: { $exists: false } },
        { isSuspended: false },
      ] },
    ] },
    { number: 1 },
  ).lean();
  const userCards = [];
  // do not filter userCardNumbers for local and dev tests
  const filteredCards = process.env.NODE_ENV === 'production' ? userCardsForUpdate.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCardsForUpdate;
  filteredCards.forEach((model) => {
    userCardsCacheKeys.push(`userCard_suspended_cardNumber_${model.number}`);
    userCards.push(model.number);
  });

  if (userCards.length) {
    await UserCard.updateMany(
      { uid: userId, number: { $in: userCards } },
      { $set: { isSuspended: true, suspendDescription: description } },
      { upsert: false },
    ).exec();
    await removeRedisCacheByKeys([`userCard_uid_${userId}`, ...userCardsCacheKeys]);
  }
};

/**
 * Add admin comment to the user on add him to the suspended list
 * */
const addUserAdminCommentOnAddToSuspendedList = async (userId, comment) => {
  await UserAdminComment.createNew(userId, comment);
};

module.exports = async (currentUserId, adminUser, description = '') => {
  // import there, because of circular dependencies
  const User = require('../../model/audb/User');

  const currentUser = await User.findOne({ id: currentUserId }).lean();

  // for admin or support do not need to check is suspended or block him
  if (!currentUser || isAdminOrSupportUser(currentUser)) return;

  const currentUserEmail = await User.decryptEmailWithRedis(currentUser.em);
  const reason = description ? ` by reason: ${description}` : '';
  const mainUserDescription = adminUser ? `User ${currentUser.id} ${currentUserEmail} manually added to the suspended list by ${adminUser.name} (${adminUser.id})${reason}` : `User ${currentUser.id} ${currentUserEmail} automatically added to the suspended list${reason}`;
  const users = await getRelatedUsersToCurrentSuspendedUser(currentUser, mainUserDescription);

  for (let i = 0; i < users.length; ++i) {
    const user = users[i].user;
    const userDescription = users[i].description;

    if (!user || isAdminOrSupportUser(user)) continue;
    if (currentUserId !== user.id) {
      await addUserToSuspendedList(user.id, userDescription);
      await addUserCardsToSuspendedList(user.id, userDescription);
      await addUserAdminCommentOnAddToSuspendedList(user.id, userDescription);
    }
  }

  await removeRedisCacheByKeys([`suspended_id_${currentUserId}`]);
  await addUserAdminCommentOnAddToSuspendedList(currentUserId, mainUserDescription);

  if (!currentUser.skipSuspendRelatedUsers) await addUserCardsToSuspendedList(currentUserId, mainUserDescription);
};
