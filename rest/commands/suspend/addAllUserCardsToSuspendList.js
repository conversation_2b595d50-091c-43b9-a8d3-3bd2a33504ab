const moment = require('moment');
const log = require('@s1/log').create(__filename);
const config = require('../../../config');
const User = require('../../model/audb/User');
const UserCard = require('../../model/audb/UserCard');
const Suspended = require('../../model/audb/Suspended');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const { ignoredCardNumbersEncryptedList, creationMethods } = require('../../constants/suspended');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

module.exports = async (user, userCardNumbers, description = '') => {
  if (!user || isAdminOrSupportUser(user) || !userCardNumbers || !userCardNumbers.length || user.skipSuspendRelatedUsers) return;

  const adminsAndSupportsIDs = await getAdminsAndSupportsIDs();
  // do not filter userCardNumbers for local and dev tests
  let filteredCardNumbers = process.env.NODE_ENV === 'production' ? userCardNumbers.filter(cardNumber => !ignoredCardNumbersEncryptedList.includes(cardNumber)) : userCardNumbers;
  const whitelistedCards = await getWhitelistCardNumbers();
  filteredCardNumbers = filteredCardNumbers.filter(cardNumber => !whitelistedCards.includes(cardNumber));
  // search card numbers not yet suspended for all users, exclude admins adn supports
  const notSuspendedUserCardNumberModels = await UserCard.find(
    { uid: { $nin: adminsAndSupportsIDs }, number: { $in: filteredCardNumbers }, isSuspended: false },
    { uid: 1, number: 1, first6: 1, last4: 1 },
  ).lean();
  // search for card numbers will be added to the suspend list to block their users too
  const notSuspendedUserCardNumbersUIDs = notSuspendedUserCardNumberModels.map(model => model.uid);
  const usersUIDs = Array.from(new Set(notSuspendedUserCardNumbersUIDs));
  const now = moment().unix();
  // update card numbers not yet suspended for all users, exclude admins adn supports
  await UserCard.updateMany(
    { number: { $in: filteredCardNumbers }, isSuspended: false, uid: { $nin: adminsAndSupportsIDs } },
    { $set: { isSuspended: true, suspendDescription: description, updated: now } },
    { upsert: false },
  ).exec();
  // need to clean cache keys for blocked users only
  const userCardNumberKeys = [];
  // cache keys by each user
  usersUIDs.forEach(uid => userCardNumberKeys.push(`userCard_uid_${uid}`));
  await removeRedisCacheByKeys(userCardNumberKeys);

  // add all users IDs for suspended card numbers to the suspend list
  if (notSuspendedUserCardNumberModels.length) {
    const suspendedUids = [];

    for (let i = 0; i < usersUIDs.length; ++i) {
      const suspendedUidModel = await Suspended.findOne({ uid: usersUIDs[i] })
        .lean().cache(config.suspend.cache, `suspended_id_${usersUIDs[i]}`);

      if (suspendedUidModel) suspendedUids.push(suspendedUidModel.uid);
    }

    const uidsForInsert = [];
    const cacheKeysForRemove = [];
    // insert only not suspended user ids
    for (let i = 0; i < notSuspendedUserCardNumberModels.length; ++i) {
      const model = notSuspendedUserCardNumberModels[i];

      if (!suspendedUids.includes(model.uid)) {
        // create cardNumbersLabel like: iaepBRayYK3CcFtMT8O3 (userId-1 email-1, userId-2 email-2, userId-3 email-3)
        let cardNumbersLabel = `${model.first6.substring(0, 4)}-****-****-${model.last4}`;

        // eslint-disable-next-line max-len
        const userCardModels = await UserCard.aggregate([
          {
            $match: { isSuspended: true, number: model.number },
          },
          {
            $lookup: {
              from: 'tuser',
              localField: 'uid',
              foreignField: 'id',
              as: 'User',
            },
          },
          { $unwind: { path: '$User', preserveNullAndEmptyArrays: true } },
          {
            $project: { uid: 1, em: '$User.em' },
          },
        ]);

        if (userCardModels.length) {
          cardNumbersLabel += ' (';

          for (let f = 0; f < userCardModels.length; ++f) {
            if (f !== 0) cardNumbersLabel += ', ';

            cardNumbersLabel += userCardModels[f].uid;
            const email = userCardModels[f].em ? await User.decryptEmailWithRedis(userCardModels[f].em) : '';
            cardNumbersLabel += email ? ` ${email}` : '';
          }

          cardNumbersLabel += ')';
        }

        uidsForInsert.push({
          creationMethod: creationMethods.automatic,
          uid: model.uid,
          description: `Suspended by card number: ${cardNumbersLabel} ${description ? ` <-- ${description}` : ''}`,
          created: now,
          updated: now,
        });

        cacheKeysForRemove.push(`suspended_id_${model.uid}`);
      }
    }

    if (uidsForInsert.length) {
      try {
        await Suspended.insertMany(uidsForInsert, { ordered: false });
      } catch (e) {
        // some users might be already added and we can get an error
        log.info(`Cannot add new suspended users, error: ${e.stack || e.message}`);
      }
      await removeRedisCacheByKeys(cacheKeysForRemove);
    }
  }
};
