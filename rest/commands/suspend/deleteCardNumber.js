const { ApiError } = require('@s1/api-errors');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async ({ number }) => {
  if (!number) throw new ApiError(903, 'number is required');

  const suspendedCardNumbers = await UserCard.find({ number, isSuspended: true }, { uid: 1 }).lean();

  if (suspendedCardNumbers.length) {
    const removeResult = await UserCard.updateMany(
      { number, isSuspended: true },
      { $set: { isSuspended: false }, $unset: { suspendDescription: 1 } },
      { upsert: false },
    ).exec();

    if (removeResult) {
      // on success remove all cache for suspended card numbers
      const cacheKeyForRemove = [];
      suspendedCardNumbers.forEach(model => cacheKeyForRemove.push(`userCard_uid_${model.uid}`));
      cacheKeyForRemove.push(`userCard_suspended_cardNumber_${number}`);
      await removeRedisCacheByKeys(cacheKeyForRemove);
    }

    return {
      error: 0,
      success: !!removeResult,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
