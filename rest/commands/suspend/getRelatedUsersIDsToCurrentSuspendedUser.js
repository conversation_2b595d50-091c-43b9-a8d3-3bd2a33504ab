const UserCard = require('../../model/audb/UserCard');
const getUserRelatedUsersIds = require('../../commands/payment/getUserRelatedUsersIds');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const { ignoredCardNumbersEncryptedList } = require('../../constants/suspended');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

const getUserCards = async (userId) => {
  const userCards = await UserCard.find({ uid: userId }).lean();
  const cards = userCards.map(model => model.number);

  return cards;
};

const getUsersByCards = async (cards, adminsAndSupportsIDs) => {
  if (!cards || !cards.length) return [];

  const cardModels = await UserCard.find({ number: { $in: cards }, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = cardModels.map(model => model.uid);

  return usersIds;
};

const searchRelatedUsers = async (userId, adminsAndSupportsIDs) => {
  const foundUsersSet = new Set();
  // get usersIds by suspended cards, etc

  const [userSuspendedCards, whitelistedCards] = await Promise.all([
    getUserCards(userId),
    getWhitelistCardNumbers(),
  ]);

  // do not filter userCardNumbers for local and dev tests
  let filteredCards = process.env.NODE_ENV === 'production' ? userSuspendedCards.filter(cardNumber => !ignoredCardNumbersEncryptedList.includes(cardNumber)) : userSuspendedCards;
  filteredCards = filteredCards.filter(cardNumber => !whitelistedCards.includes(cardNumber));
  const suspendedUsersIDsByCards = await getUsersByCards(filteredCards, adminsAndSupportsIDs);
  suspendedUsersIDsByCards.forEach(_userId => foundUsersSet.add(_userId));

  const userRelatedUsersIDs = await getUserRelatedUsersIds(userId, adminsAndSupportsIDs);
  userRelatedUsersIDs.forEach(_userId => foundUsersSet.add(_userId));

  const foundUsersIDs = Array.from(foundUsersSet);

  return foundUsersIDs;
};

const getRelatedUsersToCurrentUser = async (userId, allUsersIDs, adminsAndSupportsIDs) => {
  const User = require('../../model/audb/User');

  const user = await User.findOne({ id: userId }).exec();

  // for admin or support do not need to check is suspended or block him
  if (!user || isAdminOrSupportUser(user) || user.skipSuspendRelatedUsers) return allUsersIDs;
  if (!adminsAndSupportsIDs || !adminsAndSupportsIDs.length) adminsAndSupportsIDs = await getAdminsAndSupportsIDs();

  const foundUsersIDs = await searchRelatedUsers(userId, adminsAndSupportsIDs);

  if (foundUsersIDs.length) {
    const filteredUsers = foundUsersIDs.filter(userId => !adminsAndSupportsIDs.includes(userId));

    const uniqueUsersIDs = [];
    filteredUsers.forEach((_userId) => {
      if (!allUsersIDs.includes(_userId)) {
        uniqueUsersIDs.push(_userId);
        allUsersIDs.push(_userId);
      }
    });

    if (uniqueUsersIDs.length) {
      for (let i = 0; i < uniqueUsersIDs.length; ++i) {
        const foundUsersByUser = await getRelatedUsersToCurrentUser(uniqueUsersIDs[i], allUsersIDs, adminsAndSupportsIDs);

        if (foundUsersByUser.length) {
          allUsersIDs.push(...foundUsersByUser);
          allUsersIDs = Array.from(new Set(allUsersIDs));
        }
      }
    }
  }

  return allUsersIDs;
};

/**
 *  @param {number} userId  - user ID
 * */
module.exports = async (userId) => {
  // initially put current user ID to the all users list
  const allUsersIDs = await getRelatedUsersToCurrentUser(userId, [userId]);

  return allUsersIDs || [userId];
};
