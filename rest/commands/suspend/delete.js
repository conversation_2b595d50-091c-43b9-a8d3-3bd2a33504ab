const { ApiError } = require('@s1/api-errors');
const Suspended = require('../../model/audb/Suspended');
const removeSuspendedUserAndRelatedUsers = require('./removeSuspendedUserAndRelatedUsers');

module.exports = async ({ _id, adminUser }) => {
  if (!_id) throw new ApiError(903, 'ID is required');

  const removeResult = await Suspended.findOneAndDelete({ _id }).exec();

  if (removeResult) {
    if (removeResult.uid) {
      await removeSuspendedUserAndRelatedUsers(removeResult.uid, adminUser);
    }
  }

  return {
    error: 0,
    success: !!removeResult,
    result: removeResult,
  };
};
