const { WrongParamsError, ApiError } = require('@s1/api-errors');
const SuspendWhitelistCard = require('../../model/audb/SuspendWhitelistCard');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const md5 = require('../../helpers/md5');
const { encrypt } = require('../../helpers/security');
const getBinFieldValue = require('../../helpers/getBinFieldValue');
const config = require('../../../config');
const Binlist = require('../../model/audb/Binlist');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.number) throw new ApiError(903, 'number is required');

  const parsedNumber = data.number.replace(/\s/g, '');

  const encryptedCardNumber = md5(encrypt(parsedNumber.toString(), `${parsedNumber}${config.cardDataSecuritySalt}`));

  let whitelistCard = await SuspendWhitelistCard.findOne({ number: data.number }).exec();

  if (!whitelistCard) {
    const first6DigBin = parsedNumber.substring(0, 6);
    const binInfo = await Binlist.findOne({ bin: first6DigBin }).cache(600).exec() || {};

    whitelistCard = new SuspendWhitelistCard();
    whitelistCard.number = encryptedCardNumber;
    whitelistCard.first6 = first6DigBin;
    whitelistCard.last4 = parsedNumber.substring(parsedNumber.length - 4, parsedNumber.length);
    whitelistCard.type =  getBinFieldValue(binInfo.card_type);
    whitelistCard.brand = getBinFieldValue(binInfo.card_brand);
    whitelistCard.country = getBinFieldValue(binInfo.card_issuing_country);
    whitelistCard.bank = getBinFieldValue(binInfo.card_issuing_bank);
    whitelistCard.addedByUid = admin.id;
    whitelistCard.addedByName = admin.name;

    await whitelistCard.save();
    await removeRedisCacheByKeys(['suspendWhitelistCard']);
  }

  await UserCard.updateMany({ number: data.number }, { $set: { isStealer: false } }, { upsert: false }).exec();
  await removeRedisCacheByKeys([`userCard_suspended_cardNumber_${data.number}`]);
  await removeRedisCacheByKeys([`userCard_uid_${data.number}`]);

  return {
    error: 0,
    whitelistCard,
  };
};
