const { WrongParamsError, ApiError } = require('@s1/api-errors');
const Suspended = require('../../model/audb/Suspended');
const User = require('../../model/audb/User');
const { suspendedTypes, suspendedTypesList, creationMethods } = require('../../constants/suspended');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');
const suspendRelatedUsers = require('../../commands/suspend/suspendRelatedUsers');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.type) throw new ApiError(903, 'Type is required');
  if (!suspendedTypesList.includes(data.type)) throw new ApiError(903, 'Invalid suspend user type');

  let suspend;

  if (data._id) {
    suspend = await Suspended.findOne({ _id: data._id }).exec();
    suspend.description = data.description || '';

    if (data.type === suspendedTypes.uid) suspend.creationMethod = data.creationMethod || creationMethods.manual;
  } else {
    suspend = new Suspended();
    suspend.creationMethod = creationMethods.manual;
    suspend.description = data.description;

    if (admin) {
      suspend.addedByUid = admin.id;
      suspend.addedByName = admin.name;
    }
  }

  switch (data.type) {
    case suspendedTypes.uid:
      // eslint-disable-next-line no-case-declarations
      const userId = parseInt(data.uid);
      // eslint-disable-next-line no-case-declarations
      const currentUser = await User.findOne({ id: userId }).lean();

      if (!currentUser) throw new ApiError(903, 'User not found');
      // for admin or support do not need to check is blacklisted or block him
      if (isAdminOrSupportUser(currentUser)) throw new ApiError(903, 'Cannot suspend admin or support user');

      suspend.uid = userId;
      await removeRedisCacheByKeys([`suspended_id_${userId}`]);
      break;
    default:
      break;
  }

  await suspend.save();

  // add all user cards,fingerprints, etc to the suspended list
  if (data.type === suspendedTypes.uid) {
    await suspendRelatedUsers(parseInt(data.uid), admin, data.description || '');
  }

  return {
    error: 0,
    result: { ...suspend.toObject(), type: data.type },
  };
};
