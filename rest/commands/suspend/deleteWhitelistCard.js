const { ApiError } = require('@s1/api-errors');
const SuspendWhitelistCard = require('../../model/audb/SuspendWhitelistCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (number) => {
  if (!number) throw new ApiError(903, 'number is required');

  const whitelistCard = await SuspendWhitelistCard.findOne({ number }).lean();

  if (whitelistCard) {
    await SuspendWhitelistCard.deleteOne({ number }).exec();
    await removeRedisCacheByKeys(['suspendWhitelistCard']);

    return {
      error: 0,
      success: true,
    };
  }

  return {
    error: 0,
    success: false,
  };
};
