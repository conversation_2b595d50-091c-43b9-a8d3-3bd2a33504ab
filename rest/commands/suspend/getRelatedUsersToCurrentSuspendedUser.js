const UserCard = require('../../model/audb/UserCard');
const getAdminsAndSupportsIDs = require('../../helpers/getAdminsAndSupportsIDs');
const { ignoredCardNumbersEncryptedList } = require('../../constants/suspended');
const isAdminOrSupportUser = require('../../helpers/isAdminOrSupportUser');
const getWhitelistCardNumbers = require('./getWhitelistCardNumbers');

const getUserCards = async (userId) => {
  const userCardModels = await UserCard.find({ uid: userId }).lean();

  return userCardModels;
};

const getUsersByCard = async (card, adminsAndSupportsIDs) => {
  const cardModels = await UserCard.find({ number: card, uid: { $nin: adminsAndSupportsIDs } }).lean();
  const usersIds = cardModels.map(model => model.uid);

  return usersIds;
};

const searchRelatedUsers = async (user, description, adminsAndSupportsIDs) => {
  // import there, because of circular dependencies
  const User = require('../../model/audb/User');

  const userId = user.id;
  const userEmail = await User.decryptEmailWithRedis(user.em);
  const [userCards, whitelistedCards] = await Promise.all([
    getUserCards(userId),
    getWhitelistCardNumbers(),
  ]);

  // do not filter userCardNumbers for local and dev tests
  let filteredCards = process.env.NODE_ENV === 'production' ? userCards.filter(card => !ignoredCardNumbersEncryptedList.includes(card.number)) : userCards;
  filteredCards = filteredCards.filter(card => !whitelistedCards.includes(card.number));

  const foundUsers = [];
  // get usersIds by suspended cards

  const userCardsPromises = [];
  filteredCards.forEach((userCard) => {
    userCardsPromises.push((async () => {
      const suspendedUsersIDsByCard = await getUsersByCard(userCard.number, adminsAndSupportsIDs);

      if (suspendedUsersIDsByCard && suspendedUsersIDsByCard.length) {
        // do not search admin and support users
        const cardUsers = await User.find({
          $and: [
            { id: { $in: suspendedUsersIDsByCard } },
            { $or: [{ isdeveloper: { $exists: false } }, { isdeveloper: 0 }] },
            { $or: [{ issupport: { $exists: false } }, { issupport: 0 }] },
            { $or: [{ isadmin: { $exists: false } }, { isadmin: 0 }] },
            { $or: [{ issuperadmin: { $exists: false } }, { issuperadmin: 0 }] },
          ],
        }).lean();
        await Promise.all(cardUsers.map(async (cardUser) => {
          const cardUserEmail = await User.decryptEmailWithRedis(cardUser.em);
          const userBlockedDescription = `User ${cardUser.id} ${cardUserEmail} suspended by card number ${userCard.first6}******${userCard.last4} (${userCard.number}) from ${userId} ${userEmail} <-- ${description}`;
          foundUsers.push({ user: cardUser, description: userBlockedDescription });
        }));
      }
    })());
  });
  await Promise.all(userCardsPromises);

  return foundUsers;
};

const getRelatedUsersToCurrentUser = async (user, allUsersIDs, allUsers, description, adminsAndSupportsIDs = []) => {
  // for admin or support do not need to check is suspended or block him
  if (!user || isAdminOrSupportUser(user) || user.skipSuspendRelatedUsers) return allUsers;
  if (!adminsAndSupportsIDs || !adminsAndSupportsIDs.length) adminsAndSupportsIDs = await getAdminsAndSupportsIDs();

  const foundUsers = await searchRelatedUsers(user, description, adminsAndSupportsIDs);
  const filteredUsers = foundUsers.filter(user => !adminsAndSupportsIDs.includes(user.id));

  if (filteredUsers.length) {
    const uniqueUsers = [];
    filteredUsers.forEach((foundUser) => {
      if (!allUsersIDs.includes(foundUser.user.id)) {
        uniqueUsers.push(foundUser);
        allUsers.push(foundUser);
        allUsersIDs.push(foundUser.user.id);
      }
    });

    if (uniqueUsers.length) {
      for (let i = 0; i < uniqueUsers.length; ++i) {
        const uniqueUser = uniqueUsers[i].user;
        const uniqueUserDescription = uniqueUsers[i].description;
        const foundUsersByUser = await getRelatedUsersToCurrentUser(uniqueUser, allUsersIDs, allUsers, uniqueUserDescription, adminsAndSupportsIDs);
        const filteredFoundUsersByUser = foundUsersByUser.filter(user => !adminsAndSupportsIDs.includes(user.id));

        if (filteredFoundUsersByUser.length) {
          filteredFoundUsersByUser.forEach((foundUser) => {
            if (!allUsersIDs.includes(foundUser.user.id)) {
              allUsers.push(foundUser);
              allUsersIDs.push(foundUser.user.id);
            }
          });
        }
      }
    }
  }

  return allUsers;
};

/**
 *  @param {User} user  - user model
 *  @param {string} description
 * */
module.exports = async (user, description = '') => {
  // initially put current user ID to the all users list
  const allUsersWithDescription = await getRelatedUsersToCurrentUser(user, [user.id], [{ user, description }], description);

  return allUsersWithDescription || [{ user, description }];
};
