const { WrongParamsError, ApiError } = require('@s1/api-errors');
const SuspendWhitelistCard = require('../../model/audb/SuspendWhitelistCard');
const UserCard = require('../../model/audb/UserCard');
const removeRedisCacheByKeys = require('../../service/removeRedisCacheByKeys');

module.exports = async (data, admin) => {
  if (!data) throw new WrongParamsError();
  if (!data.number) throw new ApiError(903, 'number is required');

  let whitelistCard = await SuspendWhitelistCard.findOne({ number: data.number }).exec();

  if (!whitelistCard) {
    whitelistCard = new SuspendWhitelistCard();
    whitelistCard.number = data.number;
    whitelistCard.first6 = data.first6;
    whitelistCard.last4 = data.last4;
    whitelistCard.type = data.type;
    whitelistCard.brand = data.brand;
    whitelistCard.country = data.country;
    whitelistCard.bank = data.bank;
    whitelistCard.addedByUid = admin.id;
    whitelistCard.addedByName = admin.name;

    await whitelistCard.save();
    await removeRedisCacheByKeys(['suspendWhitelistCard']);
  }

  await UserCard.updateMany({ number: data.number }, { $set: { isSuspended: false, suspendDescription: '' } }, { upsert: false }).exec();
  await removeRedisCacheByKeys([`userCard_suspended_cardNumber_${data.number}`]);
  await removeRedisCacheByKeys([`userCard_uid_${data.number}`]);

  return {
    error: 0,
    whitelistCard,
  };
};
