const Vod = require('@s1/vod-models/Vod');
const {
  vod: { searchVod, STREAM_TYPE_VOD },
} = require('@s1/api-constants');
const User = require('../model/audb/User');
const Channel = require('../model/audb/Channel');
const Schedule = require('../model/audb/Schedule');
const UserFavoriteVod = require('../model/audb/UserFavoriteVod');
const UserFavoriteTvShow = require('../model/audb/UserFavoriteTvShow');
const UserVodPosition = require('../model/audb/UserVodPosition');
const categoryCache = require('../service/cache/categories');
const config = require('../../config');

module.exports = async ({ key, type, locale, user, page, ISP, countryCode, stateCode }) => {
  const isRadio = type === 'radio' ? 1 : 0;
  const userConfig = user ? user.config : {};

  const getStreamingServers = () => User.getStreamingServers(ISP, countryCode, stateCode, STREAM_TYPE_VOD, userConfig, user);
  const channelsPromiseGetter = () =>
    Channel.search({
      key,
      isRadio,
      locale,
      user,
      page,
      ISP,
      countryCode,
      stateCode,
    });
  const radioChannelsIds = await Channel.find({ isradio: 1 }, { _id: 0, id: 1 })
    .lean()
    .exec()
    .then((channels) => channels.map((channel) => channel.id));
  const handler = {
    record: () =>
      getStreamingServers().then((streamingServers) =>
        Schedule.search({
          key,
          user,
          thumbnailOptions: { domain: streamingServers.mainServer.sip },
          locale,
          channelsIdsToSkip: radioChannelsIds,
        }),
      ),
    vod: () =>
      (user && user.id ? UserFavoriteVod.find({ uid: user.id }, { _id: 0 }).exec() : Promise.resolve([])).then((favourites) =>
        getStreamingServers().then((streamingServers) =>
          Vod.search({
            key,
            searchVod,
            favourites,
            locale,
            user,
            page,
            UserVodPosition,
            UserFavoriteTvShow,
            UserFavoriteVod,
            categoryCache,
            thumbnailOptions: { domain: streamingServers.mainServer.sip },
            locales: config.i18n.locales,
          }).then((results) => Object.assign(results, { favs: favourites })),
        ),
      ),
    radio: channelsPromiseGetter,
    channel: channelsPromiseGetter,
  }[type];

  return handler && handler();
};
