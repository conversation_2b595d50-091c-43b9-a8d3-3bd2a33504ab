const moment = require('moment');
const log = require('@s1/log').create(__filename);
const User = require('../../model/audb/User');
const UsersStat = require('../../model/audb/UsersStat');

module.exports = async () => {
  try {
    const { active, trial, paid, expired } = await User.getStatistics();
    const total = await User.countDocuments().exec();
    const isadmin = await User.countDocuments({ $or: [{ isadmin: 1 }, { issuperadmin: 1 }] }).exec();
    const time = moment().unix();
    const notexpired = await User.countDocuments({ expires: { $gt: time } }).exec();

    const userStats = new UsersStat({ total, isactive: active, isadmin, paid, trial, expired, notexpired, live: 0, vod: 0, archive: 0, tt: 0 });
    await userStats.save();

    return userStats;
  } catch (e) {
    const errorMessage = e.stack || (e.message && e.message.toString());
    log.error(`Populate users stat error: ${errorMessage}`);

    throw new Error(e);
  }
};
