const addUserBonusDays = require('./addUserBonusDays');
const adminLog = require('./adminLog');
const anotherTrial = require('./anotherTrial');
const changeUserPackage = require('./changeUserPackage');
const changeUserTime = require('./changeUserTime');
const checkTrial = require('./checkTrial');
const comment = require('./comment');
const deleteDevice = require('./deleteDevice');
const deleteUser = require('./deleteUser');
const forgetPassword = require('./forgetPassword');
const getUserCanPayTypes = require('./getUserCanPayTypes');
const load = require('./load');
const updateUser = require('./updateUser');
const updateBufferSize = require('./updateBufferSize');
const stat = require('./stat');
const search = require('./search');
const resetBufferSize = require('./resetBufferSize');
const loadStreamIP = require('./loadStreamIP');
const loadHistory = require('./loadHistory');
const paymentCanPay = require('./paymentCanPay');
const paymentCanPayWithCard = require('./paymentCanPayWithCard');
const adminFavoriteUsers = require('./adminFavoriteUsers');

module.exports = {
  addUserBonusDays,
  adminLog,
  adminFavoriteUsers,
  anotherTrial,
  changeUserPackage,
  changeUserTime,
  checkTrial,
  comment,
  deleteDevice,
  deleteUser,
  forgetPassword,
  getUserCanPayTypes,
  load,
  updateUser,
  updateBufferSize,
  stat,
  search,
  resetBufferSize,
  loadStreamIP,
  loadHistory,
  paymentCanPay,
  paymentCanPayWithCard,
};
