const moment = require('moment');
const PaymentLog = require('../../model/audb/PaymentLog');
const PaymentActionsLog = require('../../model/audb/PaymentActionsLog');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserLogLogin = require('../../model/audb/UserLogLogin');
const Session = require('../../model/audb/Session');
const _checkUserCanPayPaymentTypesOnSubmitPayRules = require('../payment/checkUserCanPayPaymentTypesOnSubmitPayRules');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getUserAllowedPaymentTypes = require('../payment/helpers/getUserAllowedPaymentTypes');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const UserModel = require('../../model/audb/User');
const getAllTypes = require('../payment/getAllTypes');
const getAllAccounts = require('../payment/getAllAccounts');
const { getUserLocation } = require('../../service/maxmind');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');
const getGroupRuleById = require('../payment/getGroupRuleById');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getUserAllowedPaymentAccountsByTypeOnSubmit = require('../payment/helpers/getUserAllowedPaymentAccountsByTypeOnSubmit');
const getAllPaymentOnSubmitPayRules = require('../payment/getAllPaymentOnSubmitPayRules');

function getLastCreatedItem(items, field = 'created') {
  if (items && items.length) {
    items.sort((a, b) => b[field] - a[field]);

    return items[0];
  }

  return null;
}

const getLastPaymentsIp = async (userId) => {
  const startDate = moment().subtract(3, 'months').unix();
  const filter = {
    $and: [{ uid: userId }, { userIp: { $exists: true } }, { userIp: { $ne: null } }, { userIp: { $ne: '' } }, { created: { $gte: startDate } }],
  };

  const lastPaymentIP = await PaymentLog.findOne(filter, { userIp: 1, created: 1 }).sort({ _id: -1 }).lean();
  const lastPaymentActionIP = await PaymentActionsLog.findOne(filter, { userIp: 1, created: 1 }).sort({ _id: -1 }).lean();
  const logModels = [];

  if (lastPaymentActionIP) logModels.push(lastPaymentActionIP);
  if (lastPaymentIP) logModels.push(lastPaymentIP);

  const lastIPModel = getLastCreatedItem(logModels);

  return lastIPModel ? lastIPModel.userIp : null;
};

const getLastWatchLogIp = async (userId) => {
  const startDate = moment().subtract(3, 'months').unix();
  const filter = {
    $and: [{ uid: userId }, { ip: { $exists: true } }, { ip: { $ne: null } }, { ip: { $ne: '' } }, { playtime: { $gte: startDate } }],
  };

  const lastLiveIP = await UserLogLive.findOne(filter, { ip: 1, playtime: 1 }).sort({ _id: -1 }).lean();
  const lastRecordIP = await UserLogRecord.findOne(filter, { ip: 1, playtime: 1 }).sort({ _id: -1 }).lean();
  const lastVodIP = await UserLogVod.findOne(filter, { ip: 1, playtime: 1 }).sort({ _id: -1 }).lean();
  const lastLoginIP = await UserLogLogin.findOne(filter, { ip: 1, playtime: 1 }).sort({ _id: -1 }).lean();

  const logModels = [];

  if (lastLiveIP) logModels.push(lastLiveIP);
  if (lastRecordIP) logModels.push(lastRecordIP);
  if (lastVodIP) logModels.push(lastVodIP);
  if (lastLoginIP) logModels.push(lastLoginIP);

  const lastWatchModel = getLastCreatedItem(logModels, 'playtime');

  return lastWatchModel ? lastWatchModel.ip : null;
};

const getLastUserSid = async (userId) => {
  const session = await Session.findOne({ user_id: userId, __sid: { $ne: 'undefined' } })
    .sort({ _id: -1 })
    .lean();

  if (session) return session.__sid;

  return null;
};

const getAllowedCardPaymentTypes = async (user, paymentInfo, fromPage, tkey, canPayWithBasicRules, allAccounts) => {
  const cardAccounts = allAccounts.filter((account) => account.PaymentGroup.name === 'Card');
  const cardAccountsForCurrentPackage = filterPaymentAccountsByPackage(cardAccounts, paymentInfo.id);
  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);

  const { list: allGroupRules } = await getAllGroupRules();
  const enabledGroupRules = allGroupRules.filter((rule) => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);
  const userAllowedPaymentTypes = await getUserAllowedPaymentTypes(
    userPermissionsGroupRules,
    canPayWithBasicRules,
    null,
    userBypassedPaymentAccountsForCurrentPackage,
    paymentInfo.id,
    cardAccountsForCurrentPackage,
  );
  const allowedPaymentTypes = userAllowedPaymentTypes.filter((type) => type.PaymentGroup.name === 'Card').map((type) => type.name);

  return allowedPaymentTypes;
};

const getAllowedPaymentAccounts = async ({
  user,
  canPayWithBasicRules,
  paymentInfo,
  allAccounts,
  allGroupRules,
  fromPage,
  tkey,
  types,
  userIp,
  countryCode,
  stateCode,
}) => {
  const dataForCheck = { tkey, fromPage, types, userIp, skipSaveLogs: true };

  if (countryCode) dataForCheck.ipCountry = countryCode;
  if (stateCode) dataForCheck.ipState = stateCode;

  const enabledGroupRules = allGroupRules.filter((rule) => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  let userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);

  if (types && types.length) {
    userBypassedPaymentAccountsForCurrentPackage = userBypassedPaymentAccountsForCurrentPackage.filter((account) =>
      types.includes(account.PaymentType.name),
    );
  }

  const filteredAccounts = types && types.length ? allAccounts.filter((account) => types.includes(account.PaymentType.name)) : allAccounts;
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(
    userPermissionsGroupRules,
    canPayWithBasicRules,
    filteredAccounts,
    userBypassedPaymentAccountsForCurrentPackage,
    paymentInfo.id,
  );

  if (!userAllowedPaymentAccounts || !userAllowedPaymentAccounts.length) {
    return {};
  }

  const { list: allOnSubmitRules } = await getAllPaymentOnSubmitPayRules();
  const enabledOnSubmitRules = allOnSubmitRules.filter((rule) => rule.enabled);
  const allowedPaymentAccountsByType = await getUserAllowedPaymentAccountsByTypeOnSubmit(
    user,
    paymentInfo,
    enabledOnSubmitRules,
    dataForCheck,
    userAllowedPaymentAccounts,
    userBypassedPaymentAccountsForCurrentPackage,
  );

  if (!Object.keys(allowedPaymentAccountsByType).length) {
    return {};
  }

  return allowedPaymentAccountsByType;
};

const getUserCanPayWithTypes = async (user, userIp, fromPage = '') => {
  const sid = await getLastUserSid(user.id);

  if (!sid) return [];

  const invoices = await user.getInvoices({ fromPage: 'adminGenerateInvoice', sid, coupon: null, locale: 'en' });

  const allTypes = await getAllTypes();
  const nonCardTypes = (allTypes.list || []).filter((type) => type.PaymentGroup.name !== 'Card').map((type) => type.name);
  const allAccountsResult = await getAllAccounts();
  const allAccounts = allAccountsResult.list || [];
  const { list: allGroupRules } = await getAllGroupRules();
  const canPayTypesObject = {};
  const { countryCode, stateCode } = getUserLocation(userIp);

  for await (const invoice of invoices) {
    if (!invoice) continue;

    const { tokey: tkey = null } = invoice || {};

    const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: invoice, fromPage, tkey });

    if (invoice.canSeeRuleId) {
      const packageCanSeeRule = await getGroupRuleById(invoice.canSeeRuleId.toString());

      if (packageCanSeeRule) invoice.canSeePackage = canPayWithGroupRule(packageCanSeeRule, canPayWithBasicRules);
    } else {
      invoice.canSeePackage = false;
    }
    if (!invoice.canSeePackage) continue;

    // check card types
    const cardsResult = await getAllowedCardPaymentTypes(user, invoice, fromPage, tkey, canPayWithBasicRules, allAccounts);

    if (cardsResult && cardsResult.length) {
      const canPayWithCardAccounts = await getAllowedPaymentAccounts({
        user,
        fromPage,
        canPayWithBasicRules,
        paymentInfo: invoice,
        allAccounts,
        allGroupRules,
        tkey,
        types: cardsResult,
        userIp,
        countryCode,
        stateCode,
      });

      cardsResult.forEach((type) => {
        if (!canPayTypesObject.hasOwnProperty(type) || !canPayTypesObject[type]) {
          canPayTypesObject[type] = [];
        }

        const accounts = (canPayWithCardAccounts[type] || [])
          .filter((acc) => acc.packageId === invoice.id)
          .map((account) => ({ id: account.id, name: account.name, subType: account.subType }));

        canPayTypesObject[type].push({ id: invoice.id, epricestr: invoice.epricestr, price: invoice.price, accounts });
      });
    }

    // check non-card types
    const body = { tkey, fromPage, requestFrom: null, types: nonCardTypes, skipSaveLogs: true, ipCountry: countryCode, ipState: stateCode };
    const result = await _checkUserCanPayPaymentTypesOnSubmitPayRules({ user, body, userIp, saveLogs: false });

    if (result && result.hasOwnProperty('canPayTypes') && result.canPayTypes && result.canPayTypes.length) {
      const canPayWithNonCardAccounts = await getAllowedPaymentAccounts({
        user,
        fromPage,
        canPayWithBasicRules,
        paymentInfo: invoice,
        allAccounts,
        allGroupRules,
        tkey,
        types: result.canPayTypes,
        userIp,
        countryCode,
        stateCode,
      });

      result.canPayTypes.forEach((type) => {
        if (!canPayTypesObject.hasOwnProperty(type) || !canPayTypesObject[type]) {
          canPayTypesObject[type] = [];
        }

        const accounts = (canPayWithNonCardAccounts[type] || [])
          .filter((acc) => acc.packageId === invoice.id || acc.packageIds.includes(invoice.id))
          .map((account) => ({ id: account.id, name: account.name, subType: account.subType }));

        canPayTypesObject[type].push({ id: invoice.id, epricestr: invoice.epricestr, price: invoice.price, accounts });
      });
    }
  }

  const canPayTypes = [];
  Object.entries(canPayTypesObject).forEach(([type, pack]) => {
    canPayTypes.push({
      packages: pack,
      type,
    });
  });

  return canPayTypes;
};

/**
 * Get user can pay types names
 *
 * @param {object} user - user
 * */
module.exports = async (req) => {
  const uid = getParamFromRequest(req, 'uid');

  if (!uid) throw Error('User ID required');

  const user = await UserModel.findOne({ id: uid }).exec();

  if (!user) throw Error('User not found');

  let lastIp = await getLastPaymentsIp(user.id);

  if (!lastIp) {
    lastIp = await getLastWatchLogIp(user.id);
  }

  const canPayTypes = await getUserCanPayWithTypes(user, lastIp);

  return canPayTypes;
};
