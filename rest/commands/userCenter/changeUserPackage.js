const User = require('../../model/audb/User');
const UserLogChanges = require('../../model/audb/UserLogChanges');
const UserAdminComment = require('../../model/audb/UserAdminComment');
const { Package } = require('../../model/audb/Package');

/**
 * Change user package by admin/support
 *
 * @param {object} req - request
 */
module.exports = async (req) => {
  const { body: data, session, user: adminUser } = req;
  const { id: userId, package: newPackage, reason, adminId } = data;

  if (!adminUser) throw new Error('please login');
  if (!userId) throw new Error('userId is required');
  if (!newPackage) throw new Error('package is required');
  if (!reason) throw new Error('reason is required');

  // Set the ID field of the admin who changed the user information
  const byid = session.user_id || adminId;

  const user = await User.findOne({ id: userId }).exec();

  if (user) {
    const oldData = {
      package: user.package,
    };
    user.package = newPackage;
    const updateData = {
      package: user.package,
      byid,
    };

    if (oldData.package === updateData.package) throw new Error('User package was not changed');

    user.markModified('package');
    await user.save();

    const allPackages = await Package.find({}, { id: 1, epricestr: 1, price: 1 }).lean();
    const packagesObject = {};
    allPackages.forEach((pack) => {
      packagesObject[pack.id] = pack;
    });

    const packageFrom = packagesObject.hasOwnProperty(oldData.package) ? packagesObject[oldData.package] : null;
    const packageTo = packagesObject.hasOwnProperty(updateData.package) ? packagesObject[updateData.package] : null;
    const packageFromName = packageFrom ? `${packageFrom.epricestr} $${packageFrom.price}` : 'Unknown package';
    const packageToName = packageTo ? `${packageTo.epricestr} $${packageTo.price}` : 'Unknown package';
    const prefix = `Changed user package from ${packageFromName} to ${packageToName}`;
    const comment = `${prefix}, reason: ${reason}, by ${adminUser.name} (${adminUser.id})`;
    await UserAdminComment.createNew(userId, comment);

    UserLogChanges.add(updateData, oldData);

    return {
      error: 0,
      success: true,
    };
  }

  throw new Error(`User not found, userID: ${userId}`);
};
