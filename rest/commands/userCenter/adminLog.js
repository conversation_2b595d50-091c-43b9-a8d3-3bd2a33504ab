const UserAdminHistory = require('../../model/audb/UserAdminHistory');
const User = require('../../model/audb/User');

module.exports = async (request) => {
  const userID = parseInt(request.query.uid);
  const { admin = {}, support = {} } = request.session;
  const adminID = admin.id || support.id || 0;

  if (userID) {
    await UserAdminHistory.updateOrCreate({ uid: userID, byid: adminID });
  }

  const logs = await UserAdminHistory.getLogsWithUserInfo();
  logs.forEach((log) => {
    log.email = User.decryptEmail(log.em);
  });

  return logs;
};
