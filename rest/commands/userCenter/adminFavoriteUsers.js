const AdminFavoriteUser = require('../../model/audb/AdminFavoriteUser');
const User = require('../../model/audb/User');

module.exports = async (request) => {
  const { user, query } = request;
  const { for: action } = query;

  if (action !== 'load' && request.body) {
    const { body } = request;
    const { userId } = body;

    if (!userId) throw Error('userId is require');

    const data = { adminId: user.id, userId };

    switch (action) {
      case 'add':
        await AdminFavoriteUser.findOneAndUpdate(data, { $set: data }, { upsert: true, new: true }).exec();
        break;
      case 'remove':
        await AdminFavoriteUser.deleteOne(data).exec();
        break;
      default:
        break;
    }
  }

  const logs = await AdminFavoriteUser.find({ adminId: user.id }).populate('User').lean();
  logs.forEach((log) => {
    log.email = User.decryptEmail(log.User.em);
    log.id = log.User.id;
    log.em = log.User.em;
    log.name = log.User.name;
    delete log.User;
  });

  return logs;
};
