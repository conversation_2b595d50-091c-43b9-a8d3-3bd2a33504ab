const { WrongParamsError, ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserLogLogin = require('../../model/audb/UserLogLogin');
const UserLogLive = require('../../model/audb/UserLogLive');
const PaymentLog = require('../../model/audb/PaymentLog');
const PaymentActionsLog = require('../../model/audb/PaymentActionsLog');
const {
  SEARCH_REQUEST_DATA,
  SEARCH_IP_REGEXP,
  SEARCH_DB,
} = require('../../constants/usercenter');
const load = require('../../commands/common/helpers/load');
const { loadAllPackages } = require('../../helpers/getPackagesOldApi');
const maxmind = require('../../service/maxmind');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getUserSearchQuery = require('../../helpers/query/userSearch/index');

const logs = [
  UserLogLive,
  UserLogRecord,
  UserLogVod,
  UserLogLogin,
];

const paymentLogs = [
  PaymentLog,
  PaymentActionsLog,
];

const getByIp = async (ip) => {
  const logResults = await Promise.all(logs.map(log => log.distinct('uid', { ip })));
  const paymentLogResults = await Promise.all(paymentLogs.map(log => log.distinct('uid', { userIp: ip })));
  const uIDs = [].concat(...logResults).concat(...paymentLogResults);
  const queries = {
    model: User,
    find: { id: { $in: uIDs } },
    asktotal: true,
    ip,
    sort: { id: -1 },
  };

  const result = await load(queries, SEARCH_DB);

  return result;
};

const getCountryAndPackage = async (uData) => {
  if (uData.lists) {
    const allPackages = await loadAllPackages();
    for (let i = 0; i < uData.lists.length; ++i) {
      delete uData.lists[i].password;
      const pckg = allPackages.find(item => item.id === uData.lists[i].package);

      if (pckg) {
        uData.lists[i]._doc.packageprice = parseFloat(pckg.price);
        uData.lists[i]._doc.packagename = `${pckg.pgname}${pckg.pricestr}`;
        uData.lists[i]._doc.email = await User.decryptEmailWithRedis(uData.lists[i].em);
        uData.lists[i]._doc.countryfrom = uData.lists[i]._doc.countryfrom || '';
        uData.lists[i]._doc.isp = uData.lists[i]._doc.isp || 'unknown';
      }
      if (uData.lists[i].registerip) {
        const { countryName, ISP } = maxmind.getUserLocation(uData.lists[i].registerip);
        uData.lists[i]._doc.countryfrom = countryName;
        uData.lists[i]._doc.isp = ISP;
      }
    }
  }

  return uData;
};

module.exports = async (req) => {
  try {
    const request = req.body;
    const { user } = req;
    const userMatch = {};
    const userAnd = [];
    const model = User;
    let result;

    for (const key of Object.keys(request)) {
      if (request[key] || request[key] === 0) {
        switch (key) {
          case SEARCH_REQUEST_DATA.EMAIL:
          case SEARCH_REQUEST_DATA.TKEY:
          case SEARCH_REQUEST_DATA.CARD:
          case SEARCH_REQUEST_DATA.IS_EXPIRED:
          case SEARCH_REQUEST_DATA.IS_TRIAL:
          case SEARCH_REQUEST_DATA.IS_ADMIN:
          case SEARCH_REQUEST_DATA.IS_SUPPORT:
          case SEARCH_REQUEST_DATA.IS_DEVELOPER:
          case SEARCH_REQUEST_DATA.IS_VODADMIN:
          case SEARCH_REQUEST_DATA.IS_ACTIVE:
          case SEARCH_REQUEST_DATA.ID:
          case SEARCH_REQUEST_DATA.NAME:
          case SEARCH_REQUEST_DATA.TRANSACTION_ID:
          case SEARCH_REQUEST_DATA.WALLET_ADDRESSS:
          case SEARCH_REQUEST_DATA.IS_BLACKLISTED:
          case SEARCH_REQUEST_DATA.IS_STEALER:
          {
            const { operator, query: queryValue } = await getUserSearchQuery(key, request[key]);

            if (operator && queryValue) userAnd.push({ [operator]: queryValue });

            break;
          }
          case SEARCH_REQUEST_DATA.IP: {
            if (request[key].match(SEARCH_IP_REGEXP)) {
              const userByIp = await getByIp(request[key]);
              result = await getCountryAndPackage(userByIp);
              result.query = userMatch;

              const socketDataUser = { data: { ...result, userId: user.id }, groupName: user.id, eventName: 'adminPanelUsercenterSearch' };
              global.io.emit('group', socketDataUser);

              return formatResponseIDs(result);
            }

            const { operator, query: queryValue } = await getUserSearchQuery('id', request[key]);

            if (operator && queryValue) userMatch[operator] = queryValue;

            break;
          }
          default:
            userMatch[key] = request[key];
        }
      }
    }

    if (userAnd.length) userMatch.$and = userAnd;

    if (Object.keys(userMatch).length > 0) {
      const uData = await load({ model, asktotal: true, find: userMatch, sort: { id: -1 }, limit: 100 }, SEARCH_DB);
      result = await getCountryAndPackage(uData);
      result.query = userMatch;
    } else {
      throw new WrongParamsError('en');
    }

    const socketDataUser = { data: { ...result, userId: user.id }, groupName: user.id, eventName: 'adminPanelUsercenterSearch' };
    global.io.emit('group', socketDataUser);

    return formatResponseIDs(result);
  } catch (error) {
    if (error instanceof ApiError) throw error;

    throw new Error(error);
  }
};
