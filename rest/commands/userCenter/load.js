const moment = require('moment');
const UserAdminHistory = require('../../model/audb/UserAdminHistory');
const User = require('../../model/audb/User');
const UserLog = require('../../model/audb/UserLog');
const UserFreeze = require('../../model/audb/UserFreeze');
const Package = require('../../model/audb/Package').Package;
const Invoice = require('../../model/audb/Invoice');
const Invite = require('../../model/audb/Invite');
const AppleInvite = require('../../model/audb/AppleInvite');
const PaymentLog = require('../../model/audb/PaymentLog');
const Suspended = require('../../model/audb/Suspended');
const UserConfig = require('../../model/audb/UserConfig');
const PlayerTemporaryRule = require('../../model/audb/PlayerTemporaryRule');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const inviteListCommand = require('../../commands/user/invite/list');
const { getUserLocation } = require('../../service/maxmind');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const generateFakeNames = require('../../helpers/generateFakeNames');
const generateFakeAddress = require('../../helpers/generateFakeAddress');
const generateUserNftEmail = require('../../helpers/generateUserNftEmail');
const generateFakeWalletAddress = require('../../helpers/generateFakeWalletAddress');
const { paymentTypes } = require('../../constants/paymentType');
const getUserWatchingScore = require('../history/getUserWatchingScore');
const getUserPaidTypes = require('../payment/helpers/getUserPaidTypes');
const isStealerUser = require('../payment/helpers/rules/isStealerUser');


const _getPaymentType = ({ pptype, stripeId }) => {
  switch (pptype) {
    case 'stripe':
    case paymentTypes.stripe:
      return `merchant${stripeId}`;
    case paymentTypes.braintree:
      return 'merchant0';
    default:
      return pptype;
  }
};

const _getInvoices = async (userID) => {
  const rawInvoices = await Invoice.find({ id: userID }).exec();

  const promises = rawInvoices.map(async ({ tkey, pack }) => {
    const packageInfo = await Package.findOne({ id: pack }).exec();

    if (packageInfo) {
      return { tkey, packageName: packageInfo.epricestr, price: packageInfo.price, packageId: packageInfo.id, days: packageInfo.days };
    }
  });
  const invoices = await Promise.all(promises);

  const filteredInvoices = invoices.filter(Boolean);
  const sortedPackages = filteredInvoices.sort((a, b) => {
    // sort by package days
    if (a.days < b.days) { return -1; }
    if (a.days > b.days) { return 1; }
    // sort by price
    if (a.price < b.price) { return -1; }
    if (a.price > b.price) { return 1; }

    return 0;
  });

  return sortedPackages;
};

const _getPayments = async ({ id, isadmin }) => {
  const payments = await PaymentLog.findOne({ uid: id }, 'pptype stripeId card amount -_id').sort({ created: -1 }).lean().exec();
  const isPaymentType = (payments && payments.pptype) || false;

  if (!isadmin && isPaymentType) {
    payments.pptype = _getPaymentType(payments);
  }
  if (isPaymentType) {
    payments.state = payments.amount > 0;
    delete payments.amount;
    delete payments.stripeId;
  }

  return payments;
};

const _getSuspended = async (userID) => {
  const suspended = await Suspended.findOne({ uid: userID }).lean();

  return !!suspended;
};

const _getUser = async (userID) => {
  const user = await User.findOne({ id: userID }).exec();

  if (!user._doc.hasOwnProperty('mtpelerinEm') && !user.mtpelerinEm) {
    user.mtpelerinEm = User.generateCustomEmail(user.email);
  }
  if (!user._doc.hasOwnProperty('tazapayEm') && !user.tazapayEm) {
    user.tazapayEm = User.generateCustomEmail(user.email);
  }
  if (!user._doc.hasOwnProperty('tazapayName') && !user.tazapayName) {
    user.tazapayName = generateFakeNames();
  }
  if (!user._doc.hasOwnProperty('nftAddress') && !user.nftAddress) {
    user.nftAddress = generateFakeAddress();
  }
  if (!user._doc.hasOwnProperty('nftEm') && !user.nftEm) {
    user.nftEm = generateUserNftEmail(user.email);
  }
  if (!user._doc.hasOwnProperty('nftAddress') && !user.nftAddress) {
    user.nftAddress = generateFakeAddress();
  }
  if (!user._doc.hasOwnProperty('nftWalletAddress') && !user.nftWalletAddress) {
    user.nftWalletAddress = generateFakeWalletAddress();
  }
  if (!user._doc.hasOwnProperty('crossmintWalletAddress') && !user.crossmintWalletAddress) {
    user.crossmintWalletAddress = generateFakeWalletAddress();
  }
  if (!user._doc.hasOwnProperty('crossmintEm') && !user.crossmintEm) {
    user.crossmintEm = generateUserNftEmail(user.email);
  }
  if (!user._doc.hasOwnProperty('expires') || !user._doc.expires) user._doc.expires = 0;
  if (user.isModified()) await user.save();

  user._doc.expires *= 1000;
  user._doc.expiredate = moment(user.expires).format('YYYY-MM-DD');
  user._doc.expirestime = moment(user.expires).format('HH:mm:ss');
  user._doc.email = user.email;
  user.nftEmail = await User.decryptEmailWithRedis(user.nftEm);
  user.mtpelerinEmail = await User.decryptEmailWithRedis(user.mtpelerinEm);
  user.tazapayEmail = await User.decryptEmailWithRedis(user.tazapayEm);

  // IP Info
  if (!user.isadmin && !user.issuperadmin && user.hideip) {
    delete user._doc.registerip;
    delete user._doc.countryfrom;
    delete user._doc.isp;
  } else if (user.registerip) {
    const { countryName, cityName, subdivisionName, ISP } = getUserLocation(user.registerip);

    if (subdivisionName) {
      user._doc.countryfrom = `${countryName}-${subdivisionName}-${cityName}`;
    } else {
      user._doc.countryfrom = `${countryName}-${cityName}`;
    }

    user._doc.isp = ISP;
  }

  return user;
};
const _getInviteList = async (user) => {
  const list = await inviteListCommand({ user });

  return {
    errorcode: 0,
    results: list,
  };
};
const _getUserConfig = async (userID) => {
  const response = await UserConfig.findOne({ uid: userID }).exec() || {};

  if (response.playerid > 0) {
    const ONE_HOUR = 3600;
    const currentDateWithOneHourBehind = Math.floor(Date.now() / 1000 - ONE_HOUR);
    const temporaryRule = await PlayerTemporaryRule.findOne({ uid: userID }).exec();

    if (temporaryRule.created < currentDateWithOneHourBehind) {
      response.playerid = 0;
    }
  } else {
    response.playerid = 0;
  }

  return response;
};
const _getRegistered = async (user) => {
  const users = await User.getRelatedUsersUsedTheSameDevice(user);

  if (users && users.length) {
    return users;
  }
};
const _getDelays = async (userID) => {
  const delays = await UserFreeze.find({ uid: userID }).lean().exec();

  if (delays && delays.length) {
    delays.forEach((delay) => {
      delay.createdstr = moment(delay.created * 1000).format('DD-MM-YYYY HH:mm');
      delay.startstr = moment(delay.starttime * 1000).format('DD-MM-YYYY HH:mm');
      delay.endstr = moment(delay.endtime * 1000).format('DD-MM-YYYY HH:mm');
    });

    return delays;
  }
};
const _deleteExtraUserFields = (user) => {
  if (user.hasOwnProperty('exoplaybackbuffer')) delete user.exoplaybackbuffer;
  if (user.hasOwnProperty('exominbuffer')) delete user.exominbuffer;
  if (user.hasOwnProperty('password')) delete user.password;
};

module.exports = async (req) => {
  const userID = parseInt(getParamFromRequest(req, 'uid', 0));
  const isBook = getParamFromRequest(req, 'book', false);

  if (isBook) {
    const adminID = req.user.id || 0;
    await UserAdminHistory.updateOrCreate({ uid: userID, byid: adminID });
  }

  const user = await _getUser(userID);
  const response = {};

  // 'userrela' table doesn't exist!
  response.userrela = [];
  response.registered = await _getRegistered(user);
  response.payments = await _getPayments(user);
  response.userconf = await _getUserConfig(userID);
  response.delays = await _getDelays(userID);
  response.invoices = await _getInvoices(userID);
  response.tuserlog = await UserLog.find({ uid: userID }).sort({ created: -1 }).exec();
  response.invite = await Invite.find({ fromid: userID }).sort({ created: -1 }).exec();
  response.inviteby = await Invite.findOne({ invitedid: userID }).sort({ created: -1 }).exec();
  response.androidsetting = await user.getExoPlayerBuffer();
  _deleteExtraUserFields(user._doc);
  response.invitelist = await _getInviteList(user);
  response.devices = await user.getRegisteredDevicesList();
  response.appleinvitelist = await AppleInvite.find({ uid: userID }).sort({ _id: -1 }).exec();
  response.paidTypes = await getUserPaidTypes(userID);
  response.watchingScore = await getUserWatchingScore(userID);

  response.user = user._doc;
  response.user.supportId = user.supportId;
  response.user.isBlacklisted = await isBlacklistedUser({ user });
  response.user.suspended = await _getSuspended(userID);
  response.user.isStealer = await isStealerUser({ user });
  // decrypt user emails
  response.user.mtpelerinEmail = await User.decryptEmailWithRedis(response.user.mtpelerinEm);
  response.user.tazapayEmail = await User.decryptEmailWithRedis(response.user.tazapayEm);
  response.user.nftEmail = await User.decryptEmailWithRedis(response.user.nftEm);
  response.user.crossmintEmail = await User.decryptEmailWithRedis(response.user.crossmintEm);
  response.user.lastBillingAddress = await user.getLastBillingAddress();

  return formatResponseIDs(response);
};
