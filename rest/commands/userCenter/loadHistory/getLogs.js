const moment = require('moment');
const load = require('../../common/helpers/load');
const User = require('../../../model/audb/User');

module.exports = async ({ uid, pageSize, page }, { tableName }) => {
  const model = tableName === 'tuser' ? User : null;
  const { lists, total } = await load({
    model,
    table: tableName,
    asktotal: true,
    find: { uid },
    sort: { playtime: -1 },
    limit: pageSize,
    skip: page >= 0 ? pageSize * page : 0,
  });

  const updatedList = lists.map(log => ({
    ...log._doc,
    datetime: moment(log.playtime * 1000).format('DD/MM/YYYY HH:mm:ss'),
  }));

  return { lists: updatedList, total };
};
