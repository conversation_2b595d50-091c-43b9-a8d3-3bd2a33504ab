const User = require('../../../model/audb/User');
const getLogs = require('./getLogs');
const addIpInfoToLogs = require('./addIpInfoToLogs');

module.exports = async (requestParams, user) => {
  if (!user.isadmin && !user.issuperadmin) {
    const user = await User.findOne({ id: requestParams.uid }).exec();

    if (user && user.hideip) {
      return {
        total: 0,
        lists: [],
      };
    }
  }

  const logs = await getLogs(requestParams, { tableName: 'book.loginip' });
  logs.lists = await addIpInfoToLogs(logs.lists);

  return logs;
};
