const isLocal = require('is-local-ip');
const load = require('../../common/helpers/load');
const { getUserLocation } = require('../../../service/maxmind');
const proxydetect = require('../../../service/proxydetect');
const getProxyRule = require('../../../commands/proxy/getProxyRule');

// eslint-disable-next-line no-unused-vars
module.exports = async ({ uid, pageSize, page, filter }, { isadmin, issuperadmin }) => {
  // if (!isadmin && !issuperadmin) {
  //   return {
  //     total: 0,
  //     lists: [],
  //   };
  // }

  const findFilter = { $and: [
    { uid },
  ] };

  if (filter && Object.keys(filter).length) {
    if (filter.hasOwnProperty('generatedUrlsForPaymentType') && filter.generatedUrlsForPaymentType) {
      const orFilter = { $or: [
        { $and: [
          { message: new RegExp('generated payment url', 'i') },
          { message: new RegExp(filter.generatedUrlsForPaymentType, 'i') },
        ] },
        { $and: [
          { message: new RegExp('Generated NFT payment', 'i') },
          { message: new RegExp(filter.generatedUrlsForPaymentType, 'i') },
        ] },
        { $and: [
          { message: new RegExp('requested a wallet address', 'i') },
          { message: new RegExp(filter.generatedUrlsForPaymentType, 'i') },
        ] },
        { $and: [
          // eslint-disable-next-line no-useless-escape
          { message: /createPaymentAddress\(\) walletAddress/i },
          { message: new RegExp(filter.generatedUrlsForPaymentType, 'i') },
        ] },
      ] };

      findFilter.$and.push(orFilter);
    }
    if (filter.hasOwnProperty('paymentTypeTransaction') && filter.paymentTypeTransaction) {
      findFilter.$and.push({ message: new RegExp(filter.paymentTypeTransaction, 'i') });
      findFilter.$and.push({ message: { $not: new RegExp('Get payment info', 'i') } });
      findFilter.$and.push({ message: { $not: new RegExp('Check can pay', 'i') } });
    }
    if (filter.hasOwnProperty('paymentType') && filter.paymentType) {
      findFilter.$and.push({ message: new RegExp(filter.paymentType, 'i') });
    }
    if (filter.hasOwnProperty('walletAddress') && filter.walletAddress) {
      findFilter.$and.push({ 'data.walletAddress': new RegExp(filter.walletAddress, 'i') });
    }
  }

  const { lists, total } = await load({
    table: 'paymentActionsLog',
    asktotal: true,
    find: findFilter,
    sort: { _id: -1 },
    limit: pageSize,
    skip: pageSize * page,
  });

  const updatedList = lists && lists.length ? await Promise.all(lists.map(async (paymentActionsLog) => {
    if (paymentActionsLog.userIp && paymentActionsLog.userIp === 'undefined') {
      const userIP = paymentActionsLog.userIp;
      const userLocation = getUserLocation(userIP);
      const userISP = userLocation.ISP;
      const proxy = {
        vpn: false,
        rule: 'none',
      };

      if (!isLocal(userIP)) {
        const proxyResult = await proxydetect.check(userIP);
        proxy.vpn = proxyResult.result;
        proxy.rule = await getProxyRule({ userIP, userISP, userLocation });
      }

      return { ...paymentActionsLog._doc, userLocation, proxy };
    }

    return { ...paymentActionsLog._doc };
  })) : [];

  return { lists: updatedList, total };
};
