const payment = require('./payment');
const login = require('./login');
const auth = require('./auth');
const watchLive = require('./watchLive');
const watchRecord = require('./watchRecord');
const watchVod = require('./watchVod');
const paymentActionsLog = require('./paymentActionsLog');
const formatResponseIDs = require('../../../helpers/formatResponseIDs');

module.exports = async ({ query, user }) => {
  const uid = parseInt(query.uid || 0);
  const page = parseInt(query.page || 0);
  const pageSize = parseInt(query.pagesize) === 0 ? 10 : parseInt(query.pagesize);
  const forType = query.for;
  const filter = query.hasOwnProperty('filter') && query.filter ? JSON.parse(query.filter) || {} : {};
  const requestParams = { uid, page, pageSize, filter };
  const forTypes = {
    payment,
    login,
    auth,
    watchlive: watchLive,
    watchrecord: watchRecord,
    watchvod: watchVod,
    paymentactionslogs: paymentActionsLog,
  };

  if (forTypes[forType]) {
    const response = await forTypes[forType](requestParams, user);

    return formatResponseIDs(response);
  }
};
