const load = require('../../common/helpers/load');
const formatPaymentLog = require('../../../commands/payment/helpers/formatPaymentLog');

module.exports = async ({ uid, pageSize, page }, { isadmin, issupport, issuperadmin }) => {
  if (!isadmin && !issupport && !issuperadmin) {
    return {
      total: 0,
      lists: [],
    };
  }

  const { lists, total } = await load({
    table: 'pp',
    asktotal: true,
    find: { uid },
    sort: { created: -1 },
    limit: pageSize,
    skip: pageSize * page,
  });

  const updatedList = await Promise.all(lists.map(async payment => formatPaymentLog(payment)));

  return { lists: updatedList, total };
};
