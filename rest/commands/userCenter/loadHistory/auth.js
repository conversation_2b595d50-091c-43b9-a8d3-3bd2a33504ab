const load = require('../../common/helpers/load');
const User = require('../../../model/audb/User');

module.exports = async ({ uid, pageSize, page }, user) => {
  if (!user.isadmin && !user.issuperadmin) {
    const user = await User.findOne({ id: uid }).exec();

    if (user && user.hideip) {
      return {
        total: 0,
        lists: [],
      };
    }
  }

  const { lists, total } = await load({
    table: 'bookauth',
    asktotal: true,
    find: { uid },
    sort: { created: -1 },
    limit: pageSize,
    skip: pageSize * page,
  });

  return { lists, total };
};
