const isLocal = require('is-local-ip');
const proxydetect = require('../../../service/proxydetect');
const { getUserLocation } = require('../../../service/maxmind');
const getProxyRule = require('../../../commands/proxy/getProxyRule');

module.exports = async (logs) => {
  const result = Promise.all(logs.map(async (log) => {
    const userIP = log.ip;
    const userISP = log.isp;
    log.proxy = {
      vpn: false,
      rule: 'none',
    };

    if (!isLocal(userIP)) {
      const userLocation = getUserLocation(userIP);
      log.proxy.rule = await getProxyRule({ userIP, userISP, userLocation });
      log.ipInfo = userLocation;
    }

    const data = await proxydetect.check(userIP);
    log.proxy.vpn = data.result;

    return log;
  }));

  return result;
};
