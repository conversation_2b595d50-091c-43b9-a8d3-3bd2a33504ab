const moment = require('moment');
const User = require('../../model/audb/User');
const UserLogChanges = require('../../model/audb/UserLogChanges');
const UserAdminComment = require('../../model/audb/UserAdminComment');

const _getExpires = ({ expiresdate, expirestime }) => {
  const fullExpireDate = `${expiresdate} ${expirestime}`;
  const expireUnixTime = moment(fullExpireDate).unix();

  return expireUnixTime;
};

/**
 * Change user expires date time by admin/support
 *
 * @param {object} req - request
 */
module.exports = async (req) => {
  const { body: userData, session, user: adminUser } = req;
  const { id: userId, reason, adminId, expiresdate, expirestime } = userData;

  if (!adminUser) throw new Error('please login');
  if (!userId) throw new Error('id is required');
  if (!reason) throw new Error('reason is required');
  if (!expiresdate || !expirestime) throw new Error('expiresdate and expirestime required');

  // Set the ID field of the admin who changed the user information
  const byid = session.user_id || adminId;

  const user = await User.findOne({ id: userId }).exec();

  if (user) {
    const oldData = {
      expires: user.expires,
    };
    user.expires = _getExpires(userData);
    const updateData = {
      expires: user.expires,
      byid,
    };

    if (oldData.expires === updateData.expires) throw new Error('User expire date and time was not changed');

    user.markModified('expires');
    await user.save();
    const timeFrom = moment(oldData.expires * 1000).format('DD/MM/YYYY HH:mm:ss');
    const timeTo = moment(updateData.expires * 1000).format('DD/MM/YYYY HH:mm:ss');
    const prefix = `Changed user time from ${timeFrom} to ${timeTo}`;
    const comment = `${prefix}, reason: ${reason}, by ${adminUser.name} (${adminUser.id})`;
    await UserAdminComment.createNew(userId, comment);

    UserLogChanges.add(updateData, oldData);

    return {
      error: 0,
      success: true,
    };
  }

  throw new Error(`User not found, userId: ${userId}`);
};
