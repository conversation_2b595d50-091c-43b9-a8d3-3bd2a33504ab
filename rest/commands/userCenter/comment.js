const UserAdminComment = require('../../model/audb/UserAdminComment');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

const forConstants = {
  SAVE: 'save',
  LOAD: 'load',
  REMOVE: 'remove',
  LOAD_ALL: 'loadall',
};

/**
 * Always returns all user comments by uid and can delete or create a new one.
 *
 * @param {string | number} uid - User ID.
 * @param {string} for - Action.
 * @param {string} comment - Comment text.
 * @param {string} _id - Unique comment ID.
 * @returns {object} {
 *  total: 123, - Total number of user comments
 *  lists: [{_id: '23sds24dsada2', comment: 'comment', uid: 1234, deltag: false}, ...] - All user comments
 * }
 */
module.exports = async (req) => {
  const uid = parseInt(getParamFromRequest(req, 'uid'));
  const adminUser = req.user;
  const forWhat = getParamFromRequest(req, 'for');
  const data = req.body;
  const commentID = getParamFromRequest(req, '_id');

  switch (forWhat) {
    case forConstants.SAVE:
      if (data._id) {
        await UserAdminComment.update(data);
      } else {
        await UserAdminComment.createNew(uid, data.comment, adminUser.id);
      }

      break;
    case forConstants.REMOVE:
      await UserAdminComment.delete(uid, commentID);
      break;

    default:
      break;
  }

  const allUserComments = await UserAdminComment.loadAll(uid);

  return formatResponseIDs(allUserComments);
};
