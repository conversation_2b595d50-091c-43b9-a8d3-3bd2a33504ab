const User = require('../../../model/audb/User');
const getSIPWithGroupName = require('./getSIPWithGroupName');
const { getUserLocation } = require('../../../service/maxmind');

/**
 * Checks the stream country groups, user config and returns glued string with group name.
 * Also returns the last IP under which the user logged in and ISP.
 *
 * Query
 * @param {string} uid - User ID
 * @returns {Object} {
 *  live: 's1-p9.peer5.net(CDN9-MULTI-LEASE)',
 *  vod: 's1-p9.peer5.net(CDN9-MULTI-LEASE)',
 *  lastip: '************',
 *  isp: 'Maxnet Telecom'
 * }
 */
module.exports = async (req) => {
  const { query } = req;
  const uid = Number(query.uid);
  const userLastIP = await User.getLastIP(uid);
  const user = await User.findOne({ id: uid }).populate('config').exec();

  const { stateCode, countryCode, ISP } = getUserLocation(userLastIP);
  const streamIPOptions = {
    streamingType: 1,
    countryCode,
    stateCode,
    ISP,
  };

  const liveSIP = await getSIPWithGroupName(user, streamIPOptions);

  streamIPOptions.streamingType = 2;
  const vodSIP = await getSIPWithGroupName(user, streamIPOptions);

  return { live: liveSIP, vod: vodSIP, lastip: userLastIP, isp: ISP };
};
