const StreamingServerGroup = require('../../../model/audb/StreamingServerGroup');

module.exports = async (user, streamIPOptions) => {
  const {
    streamingType,
    countryCode,
    stateCode,
    ISP,
  } = streamIPOptions;

  const streamingServers = await user.getStreamingServers(
    ISP,
    stateCode,
    countryCode,
    streamingType,
  );

  let SIP = streamingServers.mainServer.sip;
  const group = await StreamingServerGroup.findOne({ 'sss.sip': SIP }).exec();
  SIP = `${SIP}(${group.sname})`;

  return SIP;
};
