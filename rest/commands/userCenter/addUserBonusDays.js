const moment = require('moment');
const User = require('../../model/audb/User');
const UserLogChanges = require('../../model/audb/UserLogChanges');
const UserAdminComment = require('../../model/audb/UserAdminComment');

const RESET_DAYS = -999999;

/**
 * Add user bonus days by admin/support
 *
 * @param {object} req - request
 */
module.exports = async (req) => {
  const { body: data, session, user: adminUser } = req;
  const { userId, days, reason, adminId } = data;

  if (!adminUser) throw new Error('please login');
  if (!userId) throw new Error('userId is required');
  if (!days) throw new Error('days is required');
  if (!reason) throw new Error('reason is required');
  if (!(days === RESET_DAYS || days)) throw new Error('wrong days');

  // Set the ID field of the admin who changed the user information
  const byid = session.user_id || adminId;

  const user = await User.findOne({ id: userId }).exec();

  if (user) {
    const oldData = {
      expires: user.expires,
    };

    if (days === RESET_DAYS) {
      user.expires = 0;
    } else {
      const now = moment().unix();
      user.expires = (user.expires > now) ? user.expires : now;
      const calculatedTimeToAdd = days * 3600 * 24;
      user.expires += calculatedTimeToAdd;
    }

    const updateData = {
      expires: user.expires,
      byid,
    };
    user.markModified('expires');
    await user.save();
    const prefix = days === RESET_DAYS ? 'User reset' : (days > 0 ? `Added ${days} days` : `Removed ${days} days`);
    const comment = `${prefix}, reason: ${reason}, by ${adminUser.name} (${adminUser.id})`;
    await UserAdminComment.createNew(userId, comment);

    UserLogChanges.add(updateData, oldData);

    return {
      error: 0,
      success: true,
    };
  }

  throw new Error(`User not found, userId: ${userId}`);
};
