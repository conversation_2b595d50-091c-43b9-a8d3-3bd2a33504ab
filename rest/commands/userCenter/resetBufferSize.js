const UserGlobal = require('../../model/audb/UserGlobal');
const User = require('../../model/audb/User');
const update = require('../../commands/common/helpers/update');

module.exports = async ({ body: { user } }) => {
  const modelOptions = {
    update: { exominbuffer: null, exoplaybackbuffer: null },
    find: { id: user.id },
    model: User,
    upsert: true,
  };
  await update(modelOptions);
  const exoPlayerBuffer = await UserGlobal.getExoPlayerBuffer();

  return exoPlayerBuffer;
};
