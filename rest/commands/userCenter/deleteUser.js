const {
  LoginNeededError,
} = require('@s1/api-errors');
const UserLogLogin = require('../../model/audb/UserLogLogin');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserLogVod = require('../../model/audb/UserLogVod');
const UserLogAuth = require('../../model/audb/UserLogAuth');
const UserLogAuthFail = require('../../model/audb/UserLogAuthFail');
const Rememberme = require('../../model/audb/Rememberme');
const User = require('../../model/audb/User');

const logs = [UserLogLogin, UserLogRecord, UserLogLive, UserLogVod, User<PERSON>ogAuth, UserLogAuthFail];

module.exports = async ({ body: { uid }, user: { isadmin, issuperadmin } }) => {
  if (!isadmin && !issuperadmin) return new LoginNeededError();

  const promises = logs.map(log => log.deleteMany({ uid }).exec());
  await Promise.all(promises);
  await Rememberme.deleteMany({ user_id: uid }).exec();
  const response = await User.deleteOne({ id: uid }).exec();

  return response.ok ? [true] : null;
};
