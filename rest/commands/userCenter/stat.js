const UsersStat = require('../../model/audb/UsersStat');
const populateUsersStat = require('./populateUsersStat');
const { clean } = require('../../service/prepare/clean');

module.exports = async () => {
  try {
    let usersStat = await UsersStat.findOne().sort({ _id: -1 }).exec();

    if (!usersStat) {
      usersStat = await populateUsersStat();
    }

    const result = { usersStat: usersStat.toObject() };
    clean(result, {
      usersStat: 'usersStat',
    });

    return result.usersStat;
  } catch (error) {
    throw new Error(error);
  }
};
