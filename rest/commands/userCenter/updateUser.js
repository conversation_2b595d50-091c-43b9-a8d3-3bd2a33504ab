// const moment = require('moment');
const { ApiError } = require('@s1/api-errors');
const { user: { SALT_PREFIX } } = require('@s1/api-constants');
const User = require('../../model/audb/User');
const UserLogChanges = require('../../model/audb/UserLogChanges');
const clearSessionRemembermeDevice = require('../../service/audb/clearSessionRemembermeDevice');
const updateOrCreateModel = require('../../commands/common/helpers/update');
const md5 = require('../../helpers/md5');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const UserConfig = require('../../model/audb/UserConfig');

// const _getExpires = ({ expiresdate, expirestime }) => {
//   const fullExpireDate = `${expiresdate} ${expirestime}`;
//   const expireUnixTime = moment(fullExpireDate).unix();
//
//   return expireUnixTime;
// };

const _deleteUnnecessaryFields = (updateData) => {
  delete updateData.expiredate;
  delete updateData.expiresdate;
  delete updateData.expirestime;
  delete updateData.already;
  delete updateData._id;
};
const _createNewUser = async (updateData) => {
  const isExistUser = await User.findOne({ em: updateData.em }).exec();

  if (isExistUser) {
    throw new ApiError(-3, 'User with this email exists.');
  }

  const newUser = await updateOrCreateModel({ autoinc: 'id', update: updateData, model: User, upsert: { upsert: true } });

  if (!newUser.length) {
    throw new ApiError(-6, 'User didn`t find.');
  }

  const formattedNewUser = formatResponseIDs(newUser[0]);

  return formattedNewUser;
};

/**
 * Checks and compares ID and EM.
 * If the client sends ID and email in body parameters, we can create users with the same field - 'em'.
 * The field 'em' must be unique as '_id'.
 *
 * @param {object as User} oldUser
 * @param {object as User} updateData
 */
const validationUniqueEmail = async (oldUser, updateData) => {
  let updateEm = '';

  if (oldUser.em !== updateData.em) {
    updateEm = updateData.em;
  }
  if (updateEm && updateData.id) {
    const isExist = await User.findOne({ em: updateData.em, id: { $ne: updateData.id } });

    if (isExist) {
      throw new ApiError(-8, 'User with this Email and ID exists.');
    }
  }
};

/**
 * Creates or updates user info.
 *
 * For creating a new user
 * @param {object} body - User data for updating or creating
 * @param {string} body.email - User email - (<EMAIL>)
 * @param {string} body.password - User password - (123123)
 * @returns {Promise<Object>} {
 *    em: 'encryptedEmail',
 *    password: 'encryptedPassword',
 *    salt: 'secretKey' - Secret key for decrypting password
 *    id: 123123 - ID created via LastId model
 *    byid: 123123 - User ID who created this user
 *    *...other fields from User schema*
 * }
 *
 * For updating an user
 * @param {object} body - User data for updating or creating
 * @param {number} body.id - User ID - (123123)
 * @param {string} body.email - !NEW! user email - (<EMAIL>)
 * @param {string} body.password - User password - (123123)
 * @returns {Promise<Object>} {
 *    em: 'encryptedEmail',
 *    password: 'encryptedPassword',
 *    id: 123123 - User ID
 *    byid: 123123 - User ID who updated this user
 *    *...other fields from User schema*
 * }
 */
module.exports = async ({ body: updateData, session }, res, next) => {

  if (updateData.email) {
    const email = updateData.email.toLowerCase();
    updateData.em = User.encryptEmail(email);
    delete updateData.email;
  }
  if (updateData.password) {
    const salt = md5(`${SALT_PREFIX}${Date.now() / 1000}`);
    const encryptedPass = md5(`${updateData.password}${salt}`);
    updateData.password = encryptedPass;
    updateData.salt = salt;
  }

  if (updateData.newuser) {
    try {
      const newUser = await _createNewUser(updateData);
      const userConfig = new UserConfig({ uid: newUser.id });
      await userConfig.save();

      return newUser;
    } catch (error) {
      throw next(error);
    }
  }

  // we don't need to update package and expires with this route for existed users
  delete updateData.expires;
  delete updateData.package;

  // Set the ID field of the admin who changed the user information
  updateData.byid = session.user_id;
  _deleteUnnecessaryFields(updateData);

  const oldUser = await User.findOne({ id: updateData.id }).exec();

  if (oldUser) {
    if (oldUser.isactive === 0 && updateData.isactive === 1) {
      updateData.activeby = updateData.activeby || 'admin';
      updateData.activekey = '';
    } else if (oldUser.isactive === 1 && updateData.isactive === 0) {
      const activeKey = await User.getActiveKey();
      updateData.activekey = activeKey.toString();
    }
  }

  try {
    await validationUniqueEmail(oldUser, updateData);
  } catch (error) {
    throw next(error);
  }

  const updatedUser = await User.findOneAndUpdate(
    { id: updateData.id },
    updateData,
    { new: true },
  );
  UserLogChanges.add(updateData, oldUser);

  if (updateData.password && updatedUser.id) {
    clearSessionRemembermeDevice(updatedUser.id);
  }

  ['issupport', 'isvodadmin'].forEach((key) => {
    if (oldUser[key] && !updateData[key]) {
      clearSessionRemembermeDevice(updatedUser.id);
    }
  });

  return formatResponseIDs(updatedUser);
};
