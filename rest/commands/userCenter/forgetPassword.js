const moment = require('moment');
const User = require('../../model/audb/User');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const config = require('../../../config');

const isActiveKey = (key, createdTime) => {
  if (!key) {
    return false;
  }

  const THREE_DAYS = 3600 * 24 * 3;
  const nowDateWithoutThreeDays = moment().unix() - THREE_DAYS;

  return createdTime > nowDateWithoutThreeDays;
};

/**
 * This method creates an activekey. If the activekey was created 3 days ago, this key will be updated.
 * The active key is the new password by which you can enter your account.
 * Also this method sends a new password to email.
 *
 * Body
 * @param {object} user
 * @param {string} user.em - User email: R/OFF0bhImEVkF7Ns2VyIOjHeKaurEvHuUDUW+N8+oOVJhTOm9I8Mw==;
 * @returns {object} {
 *    pp: {em: 'R/OFF0bhImEVkF7Ns2VyIOjHe'} - Body options
 *    post: false/true - Status of sending the new password to mail
 *    gmail: false - Stub, through Node.js we don't send data to gmail mail
 *    user: {name: '...', id: 123, ...} - Updated user
 * }
 */
module.exports = async (req) => {
  const { em } = req.body.user || {};
  const user = await User.findOne({ em }).exec();

  if (!user || !user.id) {
    return {
      pp: req.body.user || [],
      post: false,
      gmail: false,
      user: {},
    };
  }

  let activeKey;

  if (isActiveKey(user.activekey, user.activekeygentime)) {
    activeKey = user.activekey;
  }
  if (!activeKey) {
    await user.forgotPassword();
    activeKey = user.activekey;
  }

  const isSendingStatus = await user.mail('forgetpassword', config.email.noReply, null, { newpassword: activeKey });

  return formatResponseIDs({
    pp: req.body,
    post: isSendingStatus,
    gmail: false,
    user,
  });
};
