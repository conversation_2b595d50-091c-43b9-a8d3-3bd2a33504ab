const { Package } = require('../../model/audb/Package');
const UserModel = require('../../model/audb/User');
const Invoice = require('../../model/audb/Invoice');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getAllGroupRules = require('../payment/getAllGroupRules');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const getAllAccounts = require('../payment/getAllAccounts');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');

module.exports = async (req) => {
  const uid = getParamFromRequest(req, 'uid');

  if (!uid) throw Error('User ID required');

  const user = await UserModel.findOne({ id: uid }).exec();

  if (!user) throw Error('User not found');

  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allAccounts } = await getAllAccounts();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);
  const cardAccounts = allAccounts.filter(account => account.PaymentGroup.name === 'Card');
  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  const allPackages = await Package.find({ price: { $gt: 0 }, enable: true }).sort({ days: 1 }).cache(600).lean();
  const invoices = await Invoice.find({ id: uid }).exec();

  const canPayWithPackageDays = {};

  // get can pay card types with group rules
  for (let i = 0; i < allPackages.length; ++i) {
    const pack = allPackages[i];
    const invoice = invoices.find(invoice => invoice.packageId === pack.id);
    const { tkey = null } = invoice || {};

    // check only allowed card accounts
    const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: pack, tkey });

    const canSeePackageRule = userPermissionsGroupRules.find(rule => rule._id.toString() === pack.canSeeRuleId.toString());

    if (!canSeePackageRule) {
      if (!canPayWithPackageDays.hasOwnProperty(pack.days)) canPayWithPackageDays[pack.days] = false;
      else canPayWithPackageDays[pack.days] = canPayWithPackageDays[pack.days] || false;
    } else {
      const isUserCanPayWithRule = canPayWithGroupRule(canSeePackageRule, canPayWithBasicRules);

      if (!isUserCanPayWithRule) {
        if (!canPayWithPackageDays.hasOwnProperty(pack.days)) canPayWithPackageDays[pack.days] = false;
        else canPayWithPackageDays[pack.days] = canPayWithPackageDays[pack.days] || false;
      } else {
        const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, pack.id);
        const userBypassedCardPaymentAccountsForCurrentPackage = userBypassedPaymentAccountsForCurrentPackage.filter(account => account.PaymentGroup.name === 'Card');
        const cardAccountsForCurrentPackage = filterPaymentAccountsByPackage(cardAccounts, pack.id);
        // eslint-disable-next-line max-len
        const userAllowedPaymentCardAccounts = await getUserAllowedPaymentAccounts(userPermissionsGroupRules, canPayWithBasicRules, cardAccountsForCurrentPackage, userBypassedCardPaymentAccountsForCurrentPackage, pack.id);

        // eslint-disable-next-line max-len
        if (!canPayWithPackageDays.hasOwnProperty(pack.days)) canPayWithPackageDays[pack.days] = Boolean(userAllowedPaymentCardAccounts && userAllowedPaymentCardAccounts.length);
        // eslint-disable-next-line max-len
        else canPayWithPackageDays[pack.days] = canPayWithPackageDays[pack.days] || Boolean(userAllowedPaymentCardAccounts && userAllowedPaymentCardAccounts.length);
      }
    }
  }

  const result = [];
  Object.entries(canPayWithPackageDays).forEach(([days, canPay]) => {
    result.push({ days, canPay });
  });

  return result;
};
