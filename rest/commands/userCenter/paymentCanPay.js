const { Package } = require('../../model/audb/Package');
const UserModel = require('../../model/audb/User');
const Invoice = require('../../model/audb/Invoice');
const getParamFromRequest = require('../../helpers/getParamFromRequest');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getAllBasicRules = require('../payment/getAllBasicRules');
const getAllAccounts = require('../payment/getAllAccounts');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const hasRuleActiveAccounts = require('../payment/helpers/rules/hasRuleActiveAccounts');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getCanPayStringifyGroupRule = require('../payment/helpers/getCanPayStringifyGroupRule');
const getCanPayStringifyHTMLGroupRule = require('../payment/helpers/getCanPayStringifyHTMLGroupRule');
const getPaymentGeneralConfig = require('../payment/getPaymentGeneralConfig');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');

module.exports = async (req) => {
  const uid = getParamFromRequest(req, 'uid');

  if (!uid) throw Error('User ID required');

  const user = await UserModel.findOne({ id: uid }).exec();

  if (!user) throw Error('User not found');

  const rulesResults = [];

  const { list: allGroupRules } = await getAllGroupRules();
  const { list: allBasicRules } = await getAllBasicRules();
  const { list: allAccounts } = await getAllAccounts();
  const enabledGroupRules = allGroupRules.filter(rule => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(user, enabledGroupRules);
  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(user);
  const allBasicRulesObject = {};
  allBasicRules.forEach((rule) => { allBasicRulesObject[rule._id.toString()] = rule; });
  const allPackages = await Package.find({ price: { $gt: 0 }, enable: true }).sort({ days: 1 }).cache(600).lean();
  let canPayAllBasicRules = {};
  const invoices = await Invoice.find({ id: uid }).exec();

  // collect all basic rules results
  for (let i = 0; i < allPackages.length; ++i) {
    const pack = allPackages[i];
    const invoice = invoices.find(invoice => invoice.packageId === pack.id);
    const { tkey = null } = invoice || {};
    const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: pack, tkey });

    if (i === 0) {
      canPayAllBasicRules = canPayWithBasicRules;
    } else {
      // eslint-disable-next-line no-loop-func
      Object.entries(canPayWithBasicRules).forEach(([key, value]) => { canPayAllBasicRules[key] = value; });
    }
  }

  const canPayBasicRules = { type: 'group', name: 'Basic rules', id: 'basicRule', result: true, childrens: [] };
  allBasicRules.forEach((rule) => {
    canPayBasicRules.childrens.push({
      name: rule.description || rule.name,
      result: canPayAllBasicRules[rule._id.toString()],
    });
  });
  rulesResults.push(canPayBasicRules);
  const canSeeRulesIDs = allPackages.map(pack => pack.canSeeRuleId.toString());

  const paymentGeneralConfigResponse = await getPaymentGeneralConfig();

  if (paymentGeneralConfigResponse && paymentGeneralConfigResponse.generalConfig
    && paymentGeneralConfigResponse.generalConfig.canSeeNftgateLinksInPackageRuleId) {
    canSeeRulesIDs.push(paymentGeneralConfigResponse.generalConfig.canSeeNftgateLinksInPackageRuleId.toString());
  }

  for (let i = 0; i < allPackages.length; ++i) {
    const pack = allPackages[i];
    const invoice = invoices.find(invoice => invoice.packageId === pack.id);
    const { tkey = null } = invoice || {};
    const canPayPackageRules = { type: 'group', name: `Package: ${pack.epricestr}`, id: `packageRule${pack.id}`, result: true, childrens: [] };
    const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: pack, tkey });
    const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, pack.id);
    const packageAccounts = filterPaymentAccountsByPackage(allAccounts, pack.id);

    userPermissionsGroupRules.forEach((rule) => {
      const isUserCanPayWithRule = canPayWithGroupRule(rule, canPayWithBasicRules);
      // skip can see package rules assigned to the package,
      // because these rules has no assigned groups, types and accounts,
      // so we cannot check them
      const hasActiveAccounts = pack.hasOwnProperty('canSeeRuleId') && pack.canSeeRuleId && pack.canSeeRuleId.toString() === rule._id.toString() ? true : hasRuleActiveAccounts(rule, packageAccounts);
      const canBypassRuleByUserConfigs = hasRuleActiveAccounts(rule, userBypassedPaymentAccountsForCurrentPackage);
      let canPayStringRule = getCanPayStringifyGroupRule(rule, allBasicRulesObject, canPayWithBasicRules, true, hasActiveAccounts);
      let canPayStringRuleHTML = getCanPayStringifyHTMLGroupRule(rule, allBasicRulesObject, canPayWithBasicRules, true, hasActiveAccounts);

      if (!canSeeRulesIDs.includes(rule._id.toString())) {
        const hasUserActiveAccountsString = `Has rule active accounts(${hasActiveAccounts})`;
        const canBypassRuleByUserConfigsString = `Rule bypassed by user configs(${canBypassRuleByUserConfigs})`;
        canPayStringRule = `${canBypassRuleByUserConfigsString} OR ${hasUserActiveAccountsString} AND (${canPayStringRule})`;
        canPayStringRuleHTML = `<div class='group_rule-basic_rule'>${canBypassRuleByUserConfigsString}</div> <div class='group_rule-or_operator'>OR</div> <div class='group_rule-basic_rule'>${hasUserActiveAccountsString}</div> <div class='group_rule-and_operator'>AND</div> <div class='group_rule-basic_rule'>(${canPayStringRuleHTML})</div>`;
      }

      const canPayResult = {
        name: rule.name,
        description: rule.description,
        id: `packageRule${pack.id}_${rule._id}`,
        result: canSeeRulesIDs.includes(rule._id.toString())
          ? isUserCanPayWithRule
          : canBypassRuleByUserConfigs || (isUserCanPayWithRule && hasActiveAccounts),
        canPayStringRule,
        canPayStringRuleHTML,
        childrens: [],
      };
      const ruleBasicRuleIds = [];

      // get all basic rules in the current rule
      const getRule = (rules, arr) => {
        rules.forEach((rul) => {
          if (rul.type === 'rule') {
            arr.push(rul.basicRuleId);
          } else if (rul.rules) {
            getRule(rul.rules, arr);
          }
        });
      };

      getRule(rule.rules, ruleBasicRuleIds);
      const ruleRules = [];
      ruleBasicRuleIds.forEach((ruleId) => {
        if (allBasicRulesObject.hasOwnProperty(ruleId) && canPayWithBasicRules.hasOwnProperty(ruleId)) {
          const basicRule = allBasicRulesObject[ruleId];
          ruleRules.push({
            name: basicRule.name,
            description: basicRule.description,
            result: canPayWithBasicRules[ruleId],
          });
        }
      });

      // skip add active accounts and rule bypassed for can see package rules assigned to the package,
      // because these rules has no assigned groups, types and accounts,
      // so we cannot check them
      if (!canSeeRulesIDs.includes(rule._id.toString())) {
        ruleRules.push({
          name: 'Has rule active accounts',
          result: hasActiveAccounts,
        });
        ruleRules.push({
          name: 'Rule bypassed by user configs',
          result: canBypassRuleByUserConfigs,
        });
      }

      canPayResult.childrens = ruleRules;
      canPayPackageRules.childrens.push(canPayResult);
    });

    rulesResults.push(canPayPackageRules);
  }

  return rulesResults;
};
