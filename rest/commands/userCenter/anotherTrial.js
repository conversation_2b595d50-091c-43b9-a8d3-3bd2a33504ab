const moment = require('moment');
const { ApiError } = require('@s1/api-errors');
const User = require('../../model/audb/User');
const Package = require('../../model/audb/Package').Package;
const updateUser = require('./updateUser');
const getParamFromRequest = require('../../helpers/getParamFromRequest');

const extendExpires = async (req, user) => {
  const freePackage = await Package.findOne({ price: 0 }).exec();
  const nowUnixTime = moment().unix();
  const expires = user.expires < nowUnixTime ? nowUnixTime : user.expires;
  const updateOptions = {
    id: user.id,
    package: freePackage.id,
    expires: expires + freePackage.length,
  };
  const updatedUser = await updateUser({ body: updateOptions, session: req.session });

  return updatedUser;
};

/**
 * Checks the user's expiration date and
 * if it is less than the current time it will be renewed vie Free Package (3 days trial).
 *
 * @param {string} uid - User ID
 * @returns {Promise<Object as User>} Updated user with extended expiration and free package
 */
module.exports = async (req, res, next) => {
  const uid = getParamFromRequest(req, 'uid', null);

  if (!uid) {
    throw next(new ApiError(-3, 'User ID (uid) is required'));
  }

  const user = await User.findOne({ id: uid }).lean().exec();
  const nowUnixTime = moment().unix();

  if (!user || (user && user.expires > nowUnixTime)) {
    throw next(new ApiError(-3, 'User still valid, can not add extra trial'));
  }

  const updatedUser = await extendExpires(req, user);

  return updatedUser;
};
