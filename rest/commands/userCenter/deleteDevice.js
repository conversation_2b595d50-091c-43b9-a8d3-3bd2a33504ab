const deleteDeviceCommand = require('../../commands/user/registeredDevices/delete');
const User = require('../../model/audb/User');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

module.exports = async (req) => {
  const id = req.body.id;
  const userId = req.body.uid;

  if (!id) throw new Error('Device id is require');
  if (!userId) throw new Error('uid is require');

  const user = await User.findOne({ id: userId }).exec();

  if (!user) throw new Error('user not found');

  try {
    await deleteDeviceCommand(id);
  } catch (error) {
    console.error(error);
  }

  const devices = await user.getRegisteredDevicesList();
  const result = {
    error: 0,
    devices,
  };
  const formattedResponse = formatResponseIDs(result);

  return formattedResponse;
};
