const { TrailerAssignFailed } = require('../../errors');
const trailersService = require('../../service/trailers');

module.exports = async ({ entity, src, locale }) => {
  try {
    entity.trailer = await trailersService.upload(src);
    await entity.save({ validateBeforeSave: false });

    return entity.format({ locale });
  } catch (error) {
    throw new TrailerAssignFailed(error.message);
  }
};
