const UserPlayErrorLog = require('../../model/audb/UserPlayErrorLog');
const { getUserLocation } = require('../../service/maxmind');

module.exports = async (user, data, userIp, headers) => {
  if (!user) throw new Error('Not authorized');

  const { type, url, nextUrl, fullLinks, error } = data;
  const { 'user-agent': userAgent, 'X-Site': appName } = headers;

  if (!url) throw new Error('url required');
  if (!error) throw new Error('error required');

  const log = new UserPlayErrorLog();
  log.userId = user.id;
  log.userIp = userIp;
  log.type = type;
  log.url = url;
  log.nextUrl = nextUrl;
  log.fullLinks = fullLinks;
  log.error = error;
  log.userAgent = userAgent;
  log.appName = appName;
  log.createdForTtlIndex = new Date();

  if (userIp) {
    const { countryCode, stateCode } = getUserLocation(userIp);
    log.country = countryCode;
    log.state = stateCode;
  }

  log.save();

  return {
    error: 0,
    result: 'Ok',
  };
};
