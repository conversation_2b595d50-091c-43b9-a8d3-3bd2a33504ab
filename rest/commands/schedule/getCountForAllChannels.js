const moment = require('moment');
const Schedule = require('../../model/audb/Schedule');
const Channel = require('../../model/audb/Channel');

const getGroupScheduleChannel = async ({ match, group }) => {
  const response = await Schedule.aggregate([
    { $match: match },
    { $group: group },
  ]);

  return response;
};
const addCountSchedule = ({ channel, groupScheduleChannelWithGenre, groupScheduleChannel }) => {
  const currentChannel = groupScheduleChannel.find(schedule => schedule._id.id === channel.id) || {};
  const currentChannelWithGenre = groupScheduleChannelWithGenre.find(schedule => schedule._id.id === channel.id) || {};
  channel.total = currentChannel.count || 0;
  channel.genre = currentChannelWithGenre.count || 0;

  return channel;
};
const getGroupedScheduleListWithCount = async (date) => {
  const ONE_DAY = 24 * 3600;
  const SIX_HOURS = 6 * 3600;

  const minTime = moment(date, ['D/M/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-M-D']).unix() + SIX_HOURS;
  const maxTime = minTime + ONE_DAY;
  const $andParams = [
    { rdatetime: { $gte: minTime } },
    { rdatetime: { $lt: maxTime } },
  ];
  const match = { $and: $andParams };
  const matchWithGenre = { $and: [
    ...$andParams,
    ...[{ genre: { $ne: '' } }]],
  };
  const group = {
    _id: { id: '$channel' },
    count: { $sum: 1 },
  };

  const groupScheduleChannel = await getGroupScheduleChannel({ match, group });
  const groupScheduleChannelWithGenre = await getGroupScheduleChannel({ match: matchWithGenre, group });

  return {
    groupScheduleChannelWithGenre,
    groupScheduleChannel,
  };
};
const getChannelsListWithCountSchedules = async (date) => {
  const {
    groupScheduleChannelWithGenre,
    groupScheduleChannel,
  } = await getGroupedScheduleListWithCount(date);

  const channels = await Channel.find({}, 'id').lean({ virtuals: true }).exec();

  return channels
    .map(channel => addCountSchedule({ channel, groupScheduleChannelWithGenre, groupScheduleChannel }))
    .sort((a, b) => a.total - b.total);
};

module.exports = {
  getChannelsListWithCountSchedules,
  getGroupedScheduleListWithCount,
};
