const Schedule = require('../../model/audb/Schedule');
const ScheduleOverride = require('../../model/audb/ScheduleOverride');

module.exports = async ({ _id, path, picture }) => {
  const schedule = await Schedule.findOne({ _id }).exec();

  if (!schedule) return false;

  const { channel, rdatetime } = schedule;
  const query = { channel, rdatetime };
  const update = { ...query };
  update.createdForTtlIndex = new Date();

  if (path) update.path = path;
  if (picture) update.picture = picture;

  return ScheduleOverride.findOneAndUpdate(query, update, {
    new: true,
    upsert: true,
  });
};
