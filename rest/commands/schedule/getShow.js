const moment = require('moment');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');

module.exports = async ({ channel, rdatetime, simplify, locale }) => {
  const [schedule, scheduleChannel] = (rdatetime === 'current') ? await Promise.all([
    Schedule.findOne({ rdatetime: { $lte: moment().unix() }, channel }).limit(1).sort('-rdatetime').lean()
      .exec(),
    Channel.findOne({ id: channel }).lean({ virtuals: true }).exec(),
  ]) : await Promise.all([
    Schedule.findOne({ channel, rdatetime }).lean().exec(),
    Channel.findOne({ id: channel }).lean({ virtuals: true }).exec(),
  ]);

  return Schedule.format(schedule, { scheduleChannel, locale, showDescription: true, simplify });
};
