const Schedule = require('../../model/audb/Schedule');

module.exports = async ({ channelID, date }) => {
  const totalNumberGroupedSchedule = await Schedule.find({ channel: +channelID })
    .byDate(date)
    .countDocuments()
    .exec();
  const totalNumberGroupedScheduleWithGenre = await Schedule.find({ channel: +channelID, genre: { $ne: '' } })
    .byDate(date)
    .countDocuments()
    .exec();

  const sumBothScheduleGroups = totalNumberGroupedSchedule + totalNumberGroupedScheduleWithGenre;

  return {
    totalNumberGroupedSchedule,
    totalNumberGroupedScheduleWithGenre,
    sumBothScheduleGroups,
  };
};
