const mongoose = require('mongoose');
const getCurrentModel = require('./helpers/getCurrentModel');

const getCollectionsInfo = async (connection) => {
  const collectionsInfo = await new Promise((resolve, reject) => {
    connection.db.listCollections().toArray((err, collectionsInfo) => {
      if (err) {
        reject(err);
      }

      resolve(collectionsInfo);
    });
  });

  return collectionsInfo;
};
const getCollectionsName = (collections) => {
  const collectionsName = collections.map(collection => collection.name);

  return collectionsName;
};
const getAllCollectionNames = async (dbName) => {
  let connection;

  if (dbName === 'vdf') {
    connection = mongoose.connections[2];
  } else {
    connection = mongoose.connections[1];
  }

  const collectionsInfo = await getCollectionsInfo(connection);
  const collectionsNames = getCollectionsName(collectionsInfo);

  return collectionsNames.sort();
};

module.exports = async ({ func }, dbName) => {
  if (!func) {
    const allCollectionsName = await getAllCollectionNames(dbName);

    return allCollectionsName;
  }

  const [, tableName, method, params] = func.split(/[.()]/);
  // ! TODO You should check this 'params' value. Admin panel sends STRING params instead JSON.
  // Now, It doesn't work!
  const parsedParams = JSON.parse(params);
  const Model = getCurrentModel(tableName, dbName);
  const result = await Model[method](parsedParams);

  return result;
};
