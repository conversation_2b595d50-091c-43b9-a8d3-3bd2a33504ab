const Vod = require('@s1/vod-models/Vod');
const VodCategory = require('@s1/vod-models/Category');
const Vods = require('@s1/vod-models/Vods');
const Cmds = require('@s1/vod-models/Cmds');
const Notice = require('../../../model/audb/Notice');
const Mulset = require('../../../model/audb/Mulset');
const { MailTemplatePostmark } = require('../../../model/audb/MailTemplate');
const User = require('../../../model/audb/User');
const StreamingServerISPCountry = require('../../../model/audb/StreamingServerISPCountry');
const StreamingServerISP = require('../../../model/audb/StreamingServerISP');
const EuropeCountry = require('../../../model/audb/EuropeCountry');
const ChannelGroup = require('../../../model/audb/ChannelGroup');
const SpeedTest = require('../../../model/audb/SpeedTest');
const { TableMapSchema } = require('../../../model/audb/TableMap');
const { LastIdSchema } = require('../../../model/audb/LastId');
const Counters = require('../../../model/audb/Counters');
// Logs
const PaymentLog = require('../../../model/audb/PaymentLog');
const PaymentActionsLog = require('../../../model/audb/PaymentActionsLog');
const UserLogLogin = require('../../../model/audb/UserLogLogin');
const UserLogAuth = require('../../../model/audb/UserLogAuth');
const UserLogLive = require('../../../model/audb/UserLogLive');
const UserLogRecord = require('../../../model/audb/UserLogRecord');
const UserLogVod = require('../../../model/audb/UserLogVod');
const getModelByDBName = require('../../../helpers/getModelByDBName');
const {
  WrongTableName,
} = require('../../../errors');

module.exports = (table, dbName = 'audb') => {
  let Model;

  switch (table) {
    case 'mulset':
      Model = Mulset;
      break;
    case 'notice':
      Model = Notice;
      break;
    case 'newsletter':
      Model = MailTemplatePostmark;
      break;
    case 'tuser':
      Model = User;
      break;
    case 'eurolist':
      Model = EuropeCountry;
      break;
    case 'chgroup':
      Model = ChannelGroup;
      break;
    case 'sss.isp':
      Model = StreamingServerISP;
      break;
    case 'sss.isp.country':
      Model = StreamingServerISPCountry;
      break;
    case 'speedtest':
      Model = SpeedTest;
      break;
    case 'tablemap':
      Model = getModelByDBName({ schema: TableMapSchema, modelName: 'TableMap', dbName });
      break;
    case 'lastid':
      Model = getModelByDBName({ schema: LastIdSchema, modelName: 'LastId', dbName });
      break;
    case 'counters':
      Model = Counters;
      break;
    case 'vodm':
      Model = Vod;
      break;
    case 'vods':
      Model = Vods;
      break;
    case 'vodcate':
      Model = VodCategory;
      break;
    case 'cmds':
      Model = Cmds;
      break;
    // Logs
    case 'pp':
      Model = PaymentLog;
      break;
    case 'paymentActionsLog':
      Model = PaymentActionsLog;
      break;
    case 'book.loginip':
      Model = UserLogLogin;
      break;
    case 'bookauth':
      Model = UserLogAuth;
      break;
    case 'booklive':
      Model = UserLogLive;
      break;
    case 'bookarchive':
      Model = UserLogRecord;
      break;
    case 'bookvod':
      Model = UserLogVod;
      break;

    default:
      throw new WrongTableName(`Table name '${table}' is wrong`);
  }

  return Model;
};
