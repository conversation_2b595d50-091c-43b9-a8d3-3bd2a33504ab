const getCurrentModel = require('./getCurrentModel');

module.exports = async (obj, dbName = 'audb') => {
  const { model, update = {}, table, upsert = { upsert: false }, autoinc, unset } = obj || {};
  const currentUpsert = upsert.upsert || upsert;
  let find = obj.find || {};

  try {
    const MainModel = model || getCurrentModel(table, dbName);

    if (autoinc) {
      if (update._id) delete update._id;
      if (!find[autoinc] && JSON.stringify(find) !== '{}') {
        const theone = await MainModel.findOne(find).exec();

        if (theone) find[autoinc] = theone[autoinc];
      }
      if (!find || !find[autoinc]) {
        const back = {};
        back[autoinc] = 1;
        const sort = {};
        sort[autoinc] = -1;

        const ModelLastId = getCurrentModel('lastid', dbName);
        const nextid = await ModelLastId.getNextId(MainModel);

        if (table === 'cmds') {
          const ModelCounters = getCurrentModel('counters', dbName);
          await ModelCounters.updateMany({ _id: 'cmds' }, { $set: { seq: nextid } }).exec();
        }

        find = {};
        find[autoinc] = nextid;
        update[autoinc] = nextid;
      }
    }

    const options = { $set: update };

    if (unset) options.$unset = unset;

    await MainModel.updateMany(find, options, { upsert: currentUpsert }).exec();
    const response = await MainModel.find(find).exec();

    return response;
  } catch (error) {
    console.error(error);

    return [];
  }
};
