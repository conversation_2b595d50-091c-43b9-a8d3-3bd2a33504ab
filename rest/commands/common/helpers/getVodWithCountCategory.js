const Vod = require('@s1/vod-models/Vod');

module.exports = async (vods = []) => {
  const promises = vods.map(async (vod) => {
    const newItem = JSON.parse(JSON.stringify(vod));

    if (vod.linkto) {
      newItem.vodnumber = 0;

      return vod;
    }

    const vodNumber = await Vod.find({ cateid: vod.id }).countDocuments();
    newItem.vodnumber = vodNumber;

    return newItem;
  });

  const vodWithCountCategory = await Promise.all(promises);

  return vodWithCountCategory;
};
