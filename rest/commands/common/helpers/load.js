const getCurrentModel = require('./getCurrentModel');

const _floorObject = (object) => {
  const newObject = {};
  const keys = Object.keys(object);
  keys.forEach((key) => {
    newObject[key] = Math.floor(object[key]);
  });

  return newObject;
};

module.exports = async ({
  model, table = '', limit = 100, find = {}, sort = {}, offset = 0, skip = 0, fields = {}, asktotal = false,
}, dbName = 'audb') => {
  try {
    const currentSkip = Math.floor(offset || skip);
    const currentLimit = Math.floor(limit);
    const currentSort = _floorObject(sort);
    const Model = model || getCurrentModel(table, dbName);
    const lists = await Model
      .find(find, fields)
      .sort(currentSort)
      .skip(currentSkip)
      .limit(currentLimit)
      .exec();

    if (!asktotal) return lists;

    const total = await Model
      .countDocuments(find)
      .exec();

    return {
      lists,
      total,
    };
  } catch (error) {
    console.error(error);

    return [];
  }
};
