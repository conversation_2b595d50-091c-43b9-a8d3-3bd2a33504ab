const getCurrentModel = require('./getCurrentModel');

module.exports = async (obj, dbName) => {
  const { model, update, table, upsert = false, autoinc, initval } = obj || {};
  let find = obj.find || {};
  const Model = model || getCurrentModel(table, dbName);

  if (autoinc) {
    if (update._id) delete update._id;
    if (find && !find[autoinc]) {
      const ff = {};
      ff[autoinc] = 1;
      const ttone = await Model.findOne(find, ff).exec();

      if (ttone) find[autoinc] = ttone[autoinc];
    }
    if (!find || !find[autoinc]) {
      const back = {};
      back[autoinc] = 1;
      const sort = {};
      sort[autoinc] = -1;
      let nextid = 1;

      if (initval) nextid = initval;

      const lastOne = await Model.find({}, back).sort(sort).limit(1).exec();
      const newNextId = lastOne[0][autoinc] + 1;

      if (lastOne && lastOne.length > 0 && nextid < newNextId) nextid = newNextId;

      find = {};
      find[autoinc] = nextid;
      update[autoinc] = nextid;
    }
  }

  await Model.updateMany(find, { $set: update }, upsert).exec();
  const response = await Model.find(find).exec();

  return response;
};
