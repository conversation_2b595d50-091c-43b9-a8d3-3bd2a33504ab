const { MissingCommonParamsError } = require('@s1/api-errors');
const getCurrentModel = require('./helpers/getCurrentModel');

module.exports = async (bodyRequest, dbName) => {
  const { find, forceall, table } = bodyRequest;

  if (!find && !forceall) throw new MissingCommonParamsError('can not delete all if not set forceall');

  try {
    const Model = getCurrentModel(table, dbName);
    const result = await Model.deleteMany(find).exec();

    if (result.ok === 1) return [true];

    return [false];
  } catch (error) {
    console.error(error);

    return error;
  }
};
