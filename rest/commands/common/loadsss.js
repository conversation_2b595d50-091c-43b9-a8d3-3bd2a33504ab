const load = require('./helpers/load');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getVodWithCountCategory = require('./helpers/getVodWithCountCategory');
const User = require('../../model/audb/User');

module.exports = async (bodyRequest, dbName) => {
  const { table = '' } = bodyRequest;
  const model = table === 'tuser' ? User : null;
  const response = await load({ ...bodyRequest, model }, dbName);
  const formattedResponse = formatResponseIDs(response);

  if (bodyRequest.aftersave === 'vodcatevodnumber') {
    const response = await getVodWithCountCategory(formattedResponse);

    return response;
  }

  return formattedResponse;
};
