const { VodNoInsertError } = require('@s1/api-errors');
const path = require('path');
const update = require('../helpers/update');

const getVodResponse = async ({ vodlistsource, name, description, id }, dbName) => {
  const vodList = vodlistsource.replace('/data/vod/in/vod/', '');
  const optionsVods = {
    table: 'vods',
    find: { vodmid: id },
    update: {
      vodmid: id,
      which: 'other',
      isfroms1: true,
      outfile: vodlistsource,
      vodlist: vodList,
      finish: true,
      success: true,
      downloadonly: false,
      postonly: false,
      title: name,
      subtitle: description,
    },
    autoinc: '_id',
    upsert: {
      upsert: true,
    },
  };
  const [vod] = await update(optionsVods, dbName);

  return vod;
};
const getCmdResponse = async ({ vodlistsource }, dbName, parentId) => {
  const dirName = path.dirname(vodlistsource);
  const baseName = path.basename(vodlistsource);
  const optionsCmds = {
    table: 'cmds',
    find: {
      infile: vodlistsource,
    },
    update: {
      parentid: parentId,
      targetdir: dirName,
      tofile: baseName,
      infile: vodlistsource,
      finish: true,
      dnssupload: 18,
      success: true,
    },
    autoinc: '_id',
    upsert: {
      upsert: true,
    },
  };
  const [cmd] = await update(optionsCmds, dbName);

  return cmd;
};

module.exports = async ({ vod }, dbName) => {
  const { approve, vodlistsource } = vod;

  if (!approve && vodlistsource) {
    const vodResponse = await getVodResponse(vod, dbName);
    const cmdResponse = await getCmdResponse(vod, dbName, vodResponse._id);

    return {
      errorcode: 0,
      vods: vodResponse,
      cmds: cmdResponse,
    };
  }

  throw new VodNoInsertError();
};
