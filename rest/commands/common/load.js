const load = require('./helpers/load');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const User = require('../../model/audb/User');

module.exports = async (bodyRequest, dbName) => {
  const table = bodyRequest.table || 'tuser';
  const model = table === 'tuser' ? User : null;
  const limit = bodyRequest.limit || 10;
  const response = await load({ ...bodyRequest, model, table, limit }, dbName);
  const formattedResponse = formatResponseIDs(response);

  return formattedResponse;
};
