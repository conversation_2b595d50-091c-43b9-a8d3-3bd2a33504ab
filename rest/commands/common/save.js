const update = require('./helpers/update');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const getVodWithCountCategory = require('./helpers/getVodWithCountCategory');

module.exports = async (bodyRequest, dbName) => {
  const response = await update(bodyRequest, dbName);
  const formattedResponse = formatResponseIDs(response);

  if (bodyRequest.aftersave === 'vodcatevodnumber') {
    const response = await getVodWithCountCategory(formattedResponse);

    return response;
  }

  return formattedResponse;
};
