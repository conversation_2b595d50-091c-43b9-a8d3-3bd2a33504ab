const load = require('./helpers/load');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const User = require('../../model/audb/User');

module.exports = async ({
  querybulk = [],
}, dbName) => {
  try {
    const promises = querybulk.map(async (bodyRequest) => {
      const { table = '', name = '' } = bodyRequest;
      const model = table === 'tuser' ? User : null;
      const nickname = name || table;
      const data = await load({ ...bodyRequest, model }, dbName);

      return { nickname, result: data };
    });
    const response = await Promise.all(promises);
    const formattedResponse = formatResponseIDs(response);

    return formattedResponse;
  } catch (error) {
    console.error(error);

    return [];
  }
};
