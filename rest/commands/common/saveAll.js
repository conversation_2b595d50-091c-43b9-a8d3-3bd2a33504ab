const { MissingCommonParamsError } = require('@s1/api-errors');
const updateNew = require('./helpers/updateNew');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

module.exports = async ({ saveall }, dbName) => {
  if (!saveall) throw new MissingCommonParamsError('no saveall to save');

  try {
    const promises = [];
    saveall.forEach((params) => {
      const promise = updateNew(params, dbName);
      promises.push(promise);
    });
    const response = await Promise.all(promises);
    const formattedResponse = formatResponseIDs(response);

    return formattedResponse;
  } catch (error) {
    console.error(error);

    return [];
  }
};
