const ChannelGroup = require('../../model/audb/ChannelGroup');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const { LastIdModel } = require('../../model/audb/LastId');
const isSet = require('../../helpers/isSet');

module.exports = async (group) => {
  const oldid = group.id;

  if (!group.id || (group.id && typeof group.id === 'string' && group.id.indexOf('new') !== -1)) {
    group.id = await LastIdModel.getNextId(ChannelGroup);
  }

  const savedGroup = await ChannelGroup.findOneAndUpdate(
    { id: group.id },
    { $set: group },
    { new: true, upsert: true },
  ).lean().exec();

  savedGroup.oldid = isSet(oldid) ? oldid : null;

  return formatResponseIDs(savedGroup);
};
