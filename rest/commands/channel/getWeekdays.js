const Channel = require('../../model/audb/Channel');

module.exports = async ({ channel, duration }) => {
  if (!channel) return {
    channel: null,
    weekday: Channel.getWeekdays(true, 1, duration),
  };

  const weekdays = await channel.getWeekdays(true, duration);
  const formattedChannel = (channel.length === 0) ? null : channel.toObject();

  return {
    channel: formattedChannel,
    weekday: weekdays,
  };
};
