const Channel = require('../../model/audb/Channel');
const MyFavLive = require('../../model/audb/MyFavLive');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const { ApiError } = require('@s1/api-errors');
const log = require('@s1/log').create(__filename);

/**
 * @param {number} id - channel ID
 * */
module.exports = async (id) => {
  try {
    await Channel.deleteOne({ id });
    await MyFavLive.deleteMany({ chvod: id });

  } catch (error) {
    log.error(`Cannot delete channel #${id}`, error);

    return formatResponseIDs(new ApiError(903, 'Cannot delete channel'));
  }

  return { deleteid: id };
};
