const ChannelGroup = require('../../model/audb/ChannelGroup');
const Channel = require('../../model/audb/Channel');
const formatResponseIDs = require('../../helpers/formatResponseIDs');

module.exports = async (isradio) => {
  const findQuery = {};

  if (isradio !== 'all') findQuery.isradio = isradio && isradio === '1' ? 1 : 0;

  const channelGroups = await ChannelGroup.find(findQuery).lean().exec();
  const groupedChannels = await Promise.all(channelGroups.map(async (group) => {
    group.channels = await Channel.find({ gid: group.id }).sort({ odid: 1 }).lean({ virtuals: true }).exec();

    return group;
  }));

  return { isradio, groups: formatResponseIDs(groupedChannels) };
};
