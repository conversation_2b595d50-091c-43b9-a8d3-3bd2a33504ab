const ChannelGroup = require('../../model/audb/ChannelGroup');
const Channel = require('../../model/audb/Channel');

module.exports = async (newItems, oldItems, gid) => {

  for (const [index, channel] of Object.entries(newItems)) {
    channel.odid = +index + 1
    channel.gid = gid
  }

  const updateChannels = async (items, isNewGroup) => {
    for (const channel of items) {
      let fieldsToSet = { odid: channel.odid };
      if (isNewGroup) fieldsToSet.gid = gid;
      await Channel.updateOne({ id: channel.id }, { $set: fieldsToSet });
    }
  }

  let isNewGroup = false;
  
  if (oldItems.length < newItems.length) isNewGroup = true;
  await updateChannels(newItems, isNewGroup);

};
