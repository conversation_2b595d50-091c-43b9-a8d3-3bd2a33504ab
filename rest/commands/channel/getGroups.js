const moment = require('moment');
const UserFaforitLive = require('../../model/audb/UserFavoriteLive');
const Channel = require('../../model/audb/Channel');
const ChannelGroup = require('../../model/audb/ChannelGroup');
const getAllChannels = require('./getAll');
const processSchedule = require('./processSchedule');
const getLocalizedEntityField = require('../../helpers/getLocalizedEntityField');

module.exports = async ({ allchannel, user, locale, hideShows, isradio, sort, sortGroup, liteQuery = false,
  page, pageSize, groupsOnly, noCache }) => {
  if (allchannel) return getAllChannels({ user, locale, hideShows });
  if (groupsOnly) {
    const groupsWithoutChannels = await ChannelGroup.find().sort({ odid: 1 }).lean().cache(3600, 'channel_group_all').exec();

    return groupsWithoutChannels;
  }

  const isRadio = (isradio || isradio === 0) && parseInt(isradio);
  const hideShow = (hideShows && !!parseInt(hideShows)) || false;
  const sortChannelField = sort || 'odid';
  const sortGroupField = sortGroup || 'odid';
  const groupsCondition = { ...((isRadio || isRadio === 0) ? { isradio: isRadio } : {}) };

  const now = moment().startOf('minute').unix();
  const dayAgo = moment().subtract(1, 'day').unix();

  const aggregation = [
    { $match: groupsCondition },
    {
      $lookup: {
        from: 'channel',
        localField: 'id',
        foreignField: 'gid',
        as: 'channel'
      }
    },
    { $unwind: '$channel' },
    { $sort: { 'channel.id': 1 } },
    { $match: { 'channel.ifshow': 0 } },
  ];

  if (!hideShow) {
    aggregation.push(...[
      {
        $lookup: {
          from: 'schd',
          let: { chId: '$channel.id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$channel', '$$chId'] },
                    { $gte: ['$rdatetime', dayAgo] },
                    { $lte: ['$rdatetime', now] }
                  ]
                }
              }
            },
            { $sort: { rdatetime: -1 } },
            { $limit: 1 }
          ],
          as: 'schedules'
        }
      },
      {
        $addFields: {
          channel: {
            $mergeObjects: [
              '$channel',
              { schedule: { $arrayElemAt: ['$schedules', 0] } }
            ]
          }
        }
      },
    ]);
  }

  aggregation.push(...[
    {
      $group: {
        _id: '$_id',
        id: { $first: '$id' },
        channels: { $push: '$channel' },
        doc: { $first: "$$ROOT" }
      }
    },
    {
      $unset: hideShow ? ['doc.channel'] : ['doc.channel', 'doc.schedules'],
    },
    {
      $replaceRoot: {
        newRoot: {
          $mergeObjects: [
            '$doc',
            { channels: '$channels' }
          ]
        }
      }
    },
    { $sort: { [`${sortGroupField}`]: 1 } },
  ]);

  let cacheTimeSec = 60;

  if (hideShow) {
    cacheTimeSec = 3600;
  }
  if (page && pageSize) {
    aggregation.push(...[
      {
        $limit: Number(pageSize)
      },
      {
        $skip: (page - 1) * pageSize || 0
      }
    ]);
  }

  let groups = noCache ? await ChannelGroup.aggregate(aggregation).exec() :
    await ChannelGroup.aggregate(aggregation).cache(cacheTimeSec).exec();
  const favoritesMap = new Map();

  if (user && !liteQuery) {
    const favorites = await UserFaforitLive.find({ uid: user.id });
    favorites.forEach(favorite => {
      favoritesMap.set(favorite.chvod, favorite);
    });
  }

  groups = groups.map((group) => {
    delete group._id;
    group.gname = getLocalizedEntityField(group, 'gname', locale);
    group.channels.sort((a, b) => parseInt(a[sortChannelField]) - parseInt(b[sortChannelField]));

    group.channels.forEach(channel => {
      delete channel._id;
      channel.allLogos = Channel.getAllLogos(channel);
      channel.image = Channel.getImage(channel);
      channel.txticon = Channel.getTxtIcon(channel);
      channel.name = getLocalizedEntityField(channel, 'name', locale);
      channel.isinfav = favoritesMap.has(channel.id) ? 1 : 0;

      if (!hideShow) {
        const schedule = channel.schedule;
        processSchedule(channel, now, locale)(schedule);
      }

      delete channel.schedule;
    });

    return group;
  });

  return groups;
};
