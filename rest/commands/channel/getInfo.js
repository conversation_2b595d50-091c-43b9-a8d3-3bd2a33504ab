const Channel = require('../../model/audb/Channel');

module.exports = async ({ channelId, user }) => {
  const channel = await Channel.findOne({ id: channelId })
    .populate([...(user ? [{
      path: 'isinfav',
      match: { uid: user.id },
    }] : [])])
    .lean({ virtuals: true })
    .cache(600)
    .exec();

  if (!channel) return;

  channel.isinfav = channel.isinfav ? 1 : 0;

  return channel;
};
