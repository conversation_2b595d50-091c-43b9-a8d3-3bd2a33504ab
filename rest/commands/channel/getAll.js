const moment = require('moment');
const Channel = require('../../model/audb/Channel');
const Schedule = require('../../model/audb/Schedule');
const processSchedule = require('./processSchedule');
const getLocalizedEntityField = require('../../helpers/getLocalizedEntityField');

module.exports = async ({
  locale,
  hideShows,
  user,
  pagination,
  liteQuery,
}) => {
  const now = moment().startOf('minute').unix();
  const dayAgo = moment().subtract(1, 'day').unix();
  const hideShow = (hideShows && !!parseInt(hideShows)) || false;
  let count;
  let channels;

  const virtualFieldsForAddingToQuery = {
    virtuals: ['allLogos', 'image', 'txticon'],
  };

  if (pagination) [channels, count] = await Promise.all([
    Channel.find({ ifshow: 0 })
      .select('-_id')
      .populate([...(user ? [{
        path: 'isinfav',
        match: { uid: user.id },
      }] : [])])
      .skip((pagination.page - 1) * pagination.pageSize)
      .limit(pagination.pageSize)
      .lean(virtualFieldsForAddingToQuery)
      .cache(3600)
      .exec(),
    Channel.find({ ifshow: 0 })
      .select('-_id')
      .countDocuments()
      .cache(600)
      .exec(),
  ]);
  else channels = await Channel.find({ ifshow: 0 })
    .select('-_id')
    .populate([...(user ? [{
      path: 'isinfav',
      match: { uid: user.id },
    }] : [])])
    .lean(virtualFieldsForAddingToQuery)
    .cache(3600)
    .exec();
  if (pagination && !liteQuery) count = await Channel.find({ ifshow: 0 })
    .select('-_id')
    .countDocuments()
    .cache(600);
  if (!hideShow && !liteQuery && channels.length > 0) {
    const channelIds = channels.map(channel => channel.id);

    const schedules = await Schedule.aggregate([
      {
        $match: {
          channel: { $in: channelIds },
          rdatetime: { $gte: dayAgo, $lte: now }
        }
      },
      {
        $sort: { channel: 1, rdatetime: -1 }
      },
      {
        $group: {
          _id: "$channel",
          doc: { $first: "$$ROOT" }
        }
      },
      {
        $replaceRoot: { newRoot: "$doc" }
      },
      {
        $sort: { channel: 1, rdatetime: -1 }
      },
    ]).cache(60).exec();

    const scheduleMap = new Map();
    schedules.forEach(schedule => {
      if (!scheduleMap.has(schedule.channel)) {
        scheduleMap.set(schedule.channel, schedule);
      }
    });

    channels.forEach((channel) => {
      const schedule = scheduleMap.get(channel.id);
      processSchedule(channel, now, locale)(schedule);
    });
  }

  channels.forEach((channel) => {
    channel.isinfav = channel.isinfav ? 1 : 0;
    channel.name = getLocalizedEntityField(channel, 'name', locale);
  });

  return pagination && !liteQuery ? { channels, count } : channels;
};
