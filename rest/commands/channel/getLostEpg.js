const moment = require('moment');
const Channel = require('../../model/audb/Channel');
const ChannelGroup = require('../../model/audb/ChannelGroup');
const Schedule = require('../../model/audb/Schedule');

const countSchedules = async (data) => {
  const { minDate, maxDate } = data;
  const scheduleSumMatch = {
    $and: [
      { rdatetime: { $gte: minDate } },
      { rdatetime: { $lt: maxDate } },
    ],
  };
  const scheduleSum = await Schedule.aggregate([
    { $match: scheduleSumMatch },
    { $group: { _id: { id: '$channel' }, count: { $sum: 1 } } },
  ]).exec();
  const channels = await Channel.find({}, { id: 1 }).lean({ virtuals: true }).exec();
  channels.forEach((channel) => {
    const sum = scheduleSum.find(item => item._id.id === channel.id);
    channel.total = sum ? sum.count : 0;
  });
  channels.sort((a, b) => a.total - b.total);

  return channels;
};

module.exports = async (dateof) => {
  const today = moment().subtract(6, 'hours').format('YYYY-MM-DD');

  if (!dateof) dateof = moment(today, ['D/M/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'YYYY-M-D']).unix();

  const minDate = dateof + 6 * 3600;
  const maxDate = minDate + 24 * 3600;
  const channels = await Channel.find({}).lean({ virtuals: true }).exec();
  const channelGroups = await ChannelGroup.find({}).exec();
  const schedules = await countSchedules({ minDate, maxDate });
  for (let i = 0; i < channels.length; ++i) {
    const foundScheduleById = schedules.find(schedule => channels[i].id === schedule.id);
    channels[i].no = foundScheduleById.total;
    const foundGroupById = channelGroups.find(group => channels[i].gid === group.id);
    channels[i].isradio = foundGroupById.isradio;
  }

  return channels;
};
