const Channel = require('../../model/audb/Channel');
const loadData = require('../channel/loadData');

module.exports = async (groups, isradio) => {
  try {
    if (groups.length) for (let i = 0; i < groups.length; ++i) {
      const group = groups[i];
      for (const channel of group.channels) await Channel.findOneAndUpdate(
        { id: channel.id },
        {
          $set: {
            isradio: group.isradio,
            gid: group.id,
            odid: channel.odid,
          },
        },
      ).exec();
    }

    const result = await loadData(isradio);

    return result;
  } catch (error) {
    throw new Error(error.message);
  }
};
