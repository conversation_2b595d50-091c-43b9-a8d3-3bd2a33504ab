const { WrongRequestArgumentError } = require('@s1/api-errors');
const Channel = require('../../model/audb/Channel');
const MyFavLive = require('../../model/audb/MyFavLive');
const UserLogLive = require('../../model/audb/UserLogLive');
const UserLogRecord = require('../../model/audb/UserLogRecord');
const { LastIdModel } = require('../../model/audb/LastId');
const formatResponseIDs = require('../../helpers/formatResponseIDs');
const isSet = require('../../helpers/isSet');

const actConstants = {
  ONE: 'one',
  DELETE_ONE: 'deleteone',
  ALL: 'all',
};

const isChannelDisabledOrRemoved = updatedChannel => updatedChannel.ifshow !== 0 && updatedChannel && updatedChannel.id;
const removeChannelFromFavoritesAndLogs = (channelId) => {
  Promise.all([
    MyFavLive.deleteMany({ chvod: channelId }).exec(),
    UserLogLive.deleteMany({ channel: channelId }).exec(),
    UserLogRecord.deleteMany({ channel: channelId }).exec(),
  ]);
};
const saveChannel = async (channelData) => {
  const oldid = channelData.id;

  if (!channelData.id || (channelData.id && typeof channelData.id === 'string' && channelData.id.indexOf('new') !== -1)) {
    channelData.id = await LastIdModel.getNextId(Channel);
  }

  delete channelData.editing;
  delete channelData._id;

  const updatedChannel = await Channel.findOneAndUpdate(
    { id: channelData.id },
    { $set: channelData },
    { new: true, upsert: true },
  ).lean({ virtuals: true }).exec();

  if (isChannelDisabledOrRemoved(updatedChannel)) {
    removeChannelFromFavoritesAndLogs(updatedChannel.id);
  }

  return {
    oldid: isSet(oldid) ? oldid : null,
    channel: updatedChannel,
  };
};

module.exports = async (req) => {
  const { act, deleteid } = req.query;
  const data = req.body;
  let result;
  switch (act) {
    case actConstants.ONE: {
      result = await saveChannel(data);
      break;
    }
    case actConstants.DELETE_ONE: {
      await Channel.deleteOne({ id: parseInt(deleteid) }).exec();
      result = { deleteid: parseInt(deleteid) };
      removeChannelFromFavoritesAndLogs(deleteid);
      break;
    }
    case actConstants.ALL: {
      const resultArray = [];

      if (data.length) for (let i = 0; i < data.length; ++i) resultArray.push(await saveChannel(data[i]));

      result = resultArray;
      break;
    }
    default:
      result = new WrongRequestArgumentError(req.__('act_bulksave_error'));
  }

  return formatResponseIDs(result);
};
