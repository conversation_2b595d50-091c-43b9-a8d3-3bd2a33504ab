const Schedule = require('../../model/audb/Schedule');

module.exports = (channel, now, locale) => (schedule) => {
  if (schedule) {
    schedule.name = schedule[`name_${locale}`] || schedule.name;
    schedule.description = schedule[`description_${locale}`] || schedule.description;
    schedule.genre = schedule[`genre_${locale}`] || schedule.genre;
  }

  channel.isinfav = channel.isinfav ? 1 : 0;
  channel.show = (schedule && ((schedule.rdatetime + schedule.lengthtime) > now))
    ? schedule
    : Schedule.getSimplifiedDefaultSchedule(channel, locale);
};
