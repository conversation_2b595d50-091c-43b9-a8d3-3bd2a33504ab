const Vod = require('@s1/vod-models/Vod');
const { clean } = require('../../service/prepare/clean');

module.exports = async ({ days, page, pageSize, locale, Model }) => {
  const res = await Model.getStats({ days, page, pageSize });
  const vodIds = res.map(item => item._id);

  let vods = await Vod.find({ id: { $in: vodIds } });

  vods = vods.map(vod => vod.format({ locale }));
  clean({ vods }, {
    vod: 'vods',
  });

  return vods;
};
