const PopularRecords = require('../../model/audb/PopularRecords');
const Schedule = require('../../model/audb/Schedule');
const Channel = require('../../model/audb/Channel');
const User = require('../../model/audb/User');

module.exports = async ({ days, page = 1, pageSize = 20, ISP, countryCode, stateCode, userConfig = {}, locale }) => {
  const [stats, streamingServers] = await Promise.all([
    PopularRecords.getStats({ days, page, pageSize }),
    User.getStreamingServers(ISP, countryCode, stateCode, 2, userConfig),
  ]);
  const domain = streamingServers.mainServer.sip;
  const channelIds = [];
  const allRdatetimes = [];
  // 1) push unique channels to the results
  // 2) push record date time to separate results
  stats.forEach(({ _id: { channel, rdatetime } }) => {
    if (!channelIds.includes(channel)) channelIds.push(channel);
    if (!allRdatetimes.includes(rdatetime)) allRdatetimes.push(rdatetime);
  });
  // 3) get info about channels
  // 4) get info about channel and specific show record
  const [scheduleChannels] = await Promise.all([
    Channel.find({ id: { $in: channelIds } }).lean({ virtuals: true }).cache(600),
  ]);
  // 5) match stats with channel info
  const fullStats = stats.map(stat => ({
    stat,
    scheduleChannel: scheduleChannels.find(ch => ch.id === stat._id.channel),
  }));
  const result = await Promise.all(fullStats.map(async ({ scheduleChannel, stat }) => {
    const schedule = await Schedule.findOne({
      channel: stat._id.channel,
      rdate: stat._id.rdate,
      name: stat._id.name,
      time: stat._id.time,
    })
      .cache(600);

    return schedule ? schedule.format({
      scheduleChannel,
      thumbnailOptions: { domain },
      locale,
    }) : null;
  }));

  return result.filter(item => !!item);
};
