const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const config = require('@s1/config').get();
const User = require('../../model/audb/User');
const categoriesCache = require('../../service/cache/categories');

const MOVIE = 'movie';
const SEASON = 'season';

function getPosterUrl(posterPath) {
  return `https://${config.baseImagesPath}/assets${posterPath}`;
}

const prepareMovies = async (movies, thumbnailOptions, locale) => {
  const moviesCateid = movies.map(({ cateid }) => cateid);
  const categoriesDoc = await Category.find({ id: { $in: moviesCateid } });

  return Promise.all(movies.map(async (movie) => {
    const category = categoriesDoc.find(({ id }) => id === movie.cateid);
    const formattedMovie = movie.format({ locale });
    const formattedCategory = category.format({ locale });
    formattedCategory.showpic = await category.resolvePicture(categoriesCache, thumbnailOptions);

    return {
      ...formattedMovie,
      category: formattedCategory,
      type: MOVIE,
      poster: getPosterUrl(movie.poster),
    };
  }));
};
const prepareSeason = (thumbnailOptions, locale) => async (season) => {
  const result = season.format({ locale });
  result.showpic = await season.resolvePicture(categoriesCache, thumbnailOptions);
  result.poster = getPosterUrl(result.poster);
  result.type = SEASON;

  return result;
};

module.exports = async ({ page, pageSize = 5, liteQuery, user, ISP, countryCode, stateCode, locale }) => {
  const limit = Math.ceil(pageSize / 2);
  const offset = (page - 1) * pageSize;
  const sorting = { created: -1 };
  const [tvShowCategoriesIds, offlineCategoriesIds] = await categoriesCache
    .wrap('tvShowCategoriesIds:offlineCategoriesIds', '10m', () => Promise.all([
      Category.getIdsByCondition({ isepisode: 8 }),
      Category.getIdsByCondition({ ifonline: 0 }),
    ]));
  const findMovies = Vod.getLatest({
    isTvShow: true,
    tvShowIds: tvShowCategoriesIds,
    offlineIds: offlineCategoriesIds,
    neededCount: limit,
    skip: offset,
    conditions: {
      poster: {
        $exists: true,
      },
    },
  });
  const findSeasons = Category.find({ id: { $in: tvShowCategoriesIds }, poster: { $exists: true } })
    .sort(sorting)
    .skip(offset)
    .limit(limit)
    .exec();
  const getStreamingServers = User.getStreamingServers(ISP, countryCode, stateCode, 2, user ? user.config : {}, user);
  const [latestMovies, seasons, streamingServers] = await Promise.all([findMovies, findSeasons, getStreamingServers]);
  const domain = streamingServers.mainServer.sip;
  const latestMoviesMaxId = latestMovies.map(({ maxId }) => maxId);
  const movies = await Vod.find({ id: { $in: latestMoviesMaxId } });
  const [moviesResults, seasonsResults] = await Promise.all([
    prepareMovies(movies, { domain }, locale),
    Promise.all(seasons.map(prepareSeason({ domain }, locale))),
  ]);
  const combined = [...moviesResults, ...seasonsResults];
  const result = combined.filter(item => !!item);

  if (liteQuery) return result.map(({ id, poster, type }) => ({ id, poster, type }));

  return result;
};
