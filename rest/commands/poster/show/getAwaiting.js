const Vod = require('@s1/vod-models/Vod');
const Category = require('@s1/vod-models/Category');
const categoriesCache = require('../../../service/cache/categories');

module.exports = async ({ page, pageSize }) => {
  const offset = (page - 1) * pageSize;
  const [tvShowCategoriesIds, offlineCategoriesIds] = await categoriesCache
    .wrap('tvShowCategoriesIds:offlineCategoriesIds', '10m', () => Promise.all([
      Category.getIdsByCondition({ isepisode: 8 }),
      Category.getIdsByCondition({ ifonline: 0 }),
    ]));
  const vods = await Vod.getLatest({
    isTvShow: true,
    tvShowIds: tvShowCategoriesIds,
    offlineIds: offlineCategoriesIds,
    neededCount: pageSize,
    skip: offset,
    conditions: {
      poster: {
        $exists: false,
      },
    },
  });

  return Promise.all(vods.map(async ({ maxId: id }) => {
    const vod = await Vod.findOne({ id }).lean().exec();
    const category = await Category.findOne({ id: vod.cateid }).exec();
    vod.category = category.format({});
    vod.category.showpic = await category.resolvePicture(categoriesCache);

    return vod;
  }));
};
