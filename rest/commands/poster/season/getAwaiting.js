const Category = require('@s1/vod-models/Category');
const { TVSHOW_ISEPISODE } = require('@s1/api-constants').category;
const categoriesCache = require('../../../service/cache/categories');

module.exports = async ({ page, pageSize }) => {
  const offset = (page - 1) * pageSize;
  const tvShowIds = await Category.getIdsByCondition({ isepisode: TVSHOW_ISEPISODE });
  const categories = await Category.find({ id: { $in: tvShowIds } })
    .sort({ id: -1 })
    .skip(offset)
    .limit(pageSize)
    .exec();

  return Promise.all(categories.map(async (category) => {
    const result = category.format({});
    result.showpic = await category.resolvePicture(categoriesCache);

    return result;
  }));
};
