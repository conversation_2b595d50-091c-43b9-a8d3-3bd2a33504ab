const User = require('../../model/audb/User');
const { ADMIN_PANEL_NOTIFICATONS, ADMIN_PANEL_NOTIFICATONS_LIST } = require('../../constants/notification');
const checkApproveEmployeeWorkActivities = require('./checkApproveEmployeeWorkActivities');

/**
 * Get all user notifications
 * @param {object} user - current logged user
 * */
module.exports = async (user) => {
  const notificationsResult = [];

  for (let i = 0; i < ADMIN_PANEL_NOTIFICATONS_LIST.length; ++i) {
    const notificationName = ADMIN_PANEL_NOTIFICATONS_LIST[i];
    const notificationConfig = ADMIN_PANEL_NOTIFICATONS[notificationName];

    let result;

    switch (notificationName) {
      case ADMIN_PANEL_NOTIFICATONS.approveEmployeeWorkActivity.type:
        result = await checkApproveEmployeeWorkActivities(user, notificationConfig);
        break;
      default:
        break;
    }

    if (result && result.hasNotifications && result.data) {
      notificationsResult.push(result.data);
    }
  }

  return {
    error: 0,
    result: notificationsResult,
  };
};
