const hasAwaitingApprovalActivities = require('../employee/hasAwaitingApprovalActivities');

/**
 * Check exists awating empployees work activities for approval
 *
 * @param {object} user - current logged user
 * @param {object} config - notification config
 * */
module.exports = async (user, config) => {
  const result = {
    error: 0,
    hasNotifications: false,
    data: null,
  };

  if (config.roles.some(role => user[role])) {
    const existsActivitiesForApproval = await hasAwaitingApprovalActivities();

    if (existsActivitiesForApproval) {
      result.hasNotifications = true;
      result.data = config;
    }
  }

  return result;
};
