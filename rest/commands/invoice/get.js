const { InvoiceNotFoundError, PaymentNoPackageError, PaymentUserNotFoundError, UserSuspendedError } = require('@s1/api-errors');
const Invoice = require('../../model/audb/Invoice');
const User = require('../../model/audb/User');
const isBlacklistedUser = require('../payment/helpers/rules/isBlacklistedUser');
const getUserBypassedPaymentTypeNames = require('../payment/helpers/rules/getUserBypassedPaymentTypeNames');
const generateRandomKey = require('../../helpers/generateRandomKey');
const generateFakeNames = require('../../helpers/generateFakeNames');
const generateFakeAddress = require('../../helpers/generateFakeAddress');
const generateFakeWalletAddress = require('../../helpers/generateFakeWalletAddress');
const generateUserNftEmail = require('../../helpers/generateUserNftEmail');
const getLastCryptoWithCardPaymentType = require('../payment/helpers/getLastCryptoWithCardPaymentType');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const getUserAllowedPaymentAccounts = require('../payment/helpers/getUserAllowedPaymentAccounts');
const getUserAllowedPaymentTypes = require('../payment/helpers/getUserAllowedPaymentTypes');
const getTotalPaidByCryptoWithCardTypes = require('../payment/helpers/getTotalPaidByCryptoWithCardTypes');
const getAllGroupRules = require('../payment/getAllGroupRules');
const getGroupRuleById = require('../payment/getGroupRuleById');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const getUserBypassedPaymentAccounts = require('../payment/helpers/getUserBypassedPaymentAccounts');
const filterPaymentAccountsByPackage = require('../payment/helpers/filterPaymentAccountsByPackage');
const getCanShowNftFirstInTheList = require('../payment/helpers/getCanShowNftFirstInTheList');
const filterGroupRulesByUserPermissionGroups = require('../payment/helpers/filterGroupRulesByUserPermissionGroups');
const addPaymentActionsLog = require('../payment/addActionsLog');
const { encrypt } = require('../../helpers/security');
const config = require('../../../config');
const isSuspendedUser = require('../../commands/payment/helpers/rules/isSuspendedUser');

module.exports = async ({ user, tkey, IPInfo, locale, fromPage = null, saveAllowedPaymentsLog = false }, returnParams) => {
  const invoice = await Invoice.findOne({ tkey }).exec();

  if (!invoice) throw new InvoiceNotFoundError(locale);
  // do not show invoices for another users
  if (!user || invoice.id !== user.id) throw new InvoiceNotFoundError(locale);

  const isUserSuspended = await isSuspendedUser({ user });

  if (isUserSuspended) {
    const onSubmitMessage = 'Get payment info: user suspended';
    addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: {}, userIp: IPInfo && IPInfo.ip ? IPInfo.ip : null });

    throw new UserSuspendedError(locale);
  }

  const paymentInfo = await invoice.getPaymentInfo(false, locale);

  if (!paymentInfo || Object.keys(paymentInfo).length === 0) {
    throw new PaymentNoPackageError(locale);
  }

  const invoiceUser = await User.findOne({ id: invoice.id }).exec();

  if (!invoiceUser) {
    throw new PaymentUserNotFoundError(locale);
  }
  if (!invoiceUser._doc.hasOwnProperty('trezorKey') && !invoiceUser.trezorKey) {
    invoiceUser.trezorKey = generateRandomKey(64);
  }
  if (!invoiceUser._doc.hasOwnProperty('mtpelerinDeviceUniqueId') && !invoiceUser.mtpelerinDeviceUniqueId) {
    invoiceUser.mtpelerinDeviceUniqueId = generateRandomKey(32);
  }
  if (!invoiceUser._doc.hasOwnProperty('mtpelerinEm') && !invoiceUser.mtpelerinEm) {
    invoiceUser.mtpelerinEm = User.generateCustomEmail(invoiceUser.email);
  }
  if (!invoiceUser._doc.hasOwnProperty('tazapayEm') && !invoiceUser.tazapayEm) {
    invoiceUser.tazapayEm = User.generateCustomEmail(invoiceUser.email);
  }
  if (!invoiceUser._doc.hasOwnProperty('tazapayName') && !invoiceUser.tazapayName) {
    invoiceUser.tazapayName = generateFakeNames();
  }
  if (!invoiceUser._doc.hasOwnProperty('nftEm') && !invoiceUser.nftEm) {
    invoiceUser.nftEm = generateUserNftEmail(invoiceUser.email);
  }
  if (!invoiceUser._doc.hasOwnProperty('nftAddress') && !invoiceUser.nftAddress) {
    invoiceUser.nftAddress = generateFakeAddress();
  }
  if (!invoiceUser._doc.hasOwnProperty('nftWalletAddress') && !invoiceUser.nftWalletAddress) {
    invoiceUser.nftWalletAddress = generateFakeWalletAddress();
  }
  if (!invoiceUser._doc.hasOwnProperty('crossmintWalletAddress') && !invoiceUser.crossmintWalletAddress) {
    invoiceUser.crossmintWalletAddress = generateFakeWalletAddress();
  }
  if (!invoiceUser._doc.hasOwnProperty('crossmintEm') && !invoiceUser.crossmintEm) {
    invoiceUser.crossmintEm = generateUserNftEmail(invoiceUser.email);
  }
  if (invoiceUser.isModified()) await invoiceUser.save();

  const userBypassedPaymentAccounts = await getUserBypassedPaymentAccounts(invoiceUser);
  const userBypassedPaymentAccountsForCurrentPackage = filterPaymentAccountsByPackage(userBypassedPaymentAccounts, paymentInfo.id);
  const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user: invoiceUser, paymentInfo, fromPage, tkey });
  const { list: allGroupRules } = await getAllGroupRules();
  const enabledGroupRules = allGroupRules.filter((rule) => rule.enabled);
  const userPermissionsGroupRules = await filterGroupRulesByUserPermissionGroups(invoiceUser, enabledGroupRules);
  // eslint-disable-next-line max-len
  const userAllowedPaymentAccounts = await getUserAllowedPaymentAccounts(
    userPermissionsGroupRules,
    canPayWithBasicRules,
    null,
    userBypassedPaymentAccountsForCurrentPackage,
    invoice.pack,
  );
  // eslint-disable-next-line max-len
  const userAllowedPaymentTypes = await getUserAllowedPaymentTypes(
    userPermissionsGroupRules,
    canPayWithBasicRules,
    userAllowedPaymentAccounts,
    userBypassedPaymentAccountsForCurrentPackage,
    invoice.pack,
  );
  paymentInfo.allowedPaymentTypes = userAllowedPaymentTypes.map((type) => type.name);

  if (saveAllowedPaymentsLog) {
    const onSubmitMessage = `Get payment info: user can pay with types: ${paymentInfo.allowedPaymentTypes.length ? paymentInfo.allowedPaymentTypes.join(',') : 'No allowed payments'}, package: ${paymentInfo.epricestr}`;
    await addPaymentActionsLog({ tkey, user, message: onSubmitMessage, data: {}, userIp: IPInfo && IPInfo.ip ? IPInfo.ip : null });
  }
  if (paymentInfo.canSeeRuleId) {
    const packageCanSeeRule = await getGroupRuleById(paymentInfo.canSeeRuleId.toString());
    paymentInfo.canSeePackage = canPayWithGroupRule(packageCanSeeRule, canPayWithBasicRules);
  } else {
    paymentInfo.canSeePackage = false;
  }

  delete paymentInfo._id;
  paymentInfo.email = invoiceUser.email;
  paymentInfo.username = invoiceUser.name;
  paymentInfo.addresses = invoiceUser.billingAddresses || [];
  paymentInfo.ipinfo = IPInfo;

  // skip checking is blacklisted user with skipBlacklistPayment property
  if (returnParams.isBlacklisted) paymentInfo.isBlacklisted = invoiceUser.skipBlacklistPayment ? false : await isBlacklistedUser({ user: invoiceUser });

  paymentInfo.permissionGroups = invoiceUser.permissionGroups || [];
  paymentInfo.uid = user.id;
  paymentInfo.phone = `${user.phonearea || ''}${user.phone || ''}`;
  paymentInfo.trezorKey = invoiceUser.trezorKey;
  paymentInfo.mtpelerinDeviceUniqueId = invoiceUser.mtpelerinDeviceUniqueId;
  paymentInfo.mtpelerinEmail = await User.decryptEmailWithRedis(invoiceUser.mtpelerinEm);
  paymentInfo.tazapayEmail = await User.decryptEmailWithRedis(invoiceUser.tazapayEm);
  paymentInfo.nftEmail = await User.decryptEmailWithRedis(invoiceUser.nftEm);
  paymentInfo.crossmintEmail = await User.decryptEmailWithRedis(invoiceUser.crossmintEm);
  paymentInfo.nftWalletAddress = invoiceUser.nftWalletAddress;
  paymentInfo.crossmintWalletAddress = invoiceUser.crossmintWalletAddress;
  paymentInfo.lastCryptoWithCardPaymentType = await getLastCryptoWithCardPaymentType(invoiceUser);
  paymentInfo.tazapayName = invoiceUser.tazapayName;
  paymentInfo.lastBillingAddress = await invoiceUser.getLastBillingAddress();

  if (returnParams.bypassedPaymentTypeNames) paymentInfo.bypassedPaymentTypeNames = await getUserBypassedPaymentTypeNames({ user: invoiceUser });

  const typesWithKyc = await getTotalPaidByCryptoWithCardTypes(invoiceUser);
  paymentInfo.hiddenTypesByKyc = typesWithKyc
    .filter((type) => type.totalPaid >= type.maxKycLimit && type.kycAction === 'hide')
    .map((type) => type.name);
  // eslint-disable-next-line max-len
  paymentInfo.setLastTypesByKyc = typesWithKyc
    .filter((type) => type.totalPaid + paymentInfo.price >= type.maxKycLimit && type.kycAction)
    .map((type) => type.name);
  paymentInfo.showNftFirstInTheList = await getCanShowNftFirstInTheList(invoiceUser);

  if (returnParams.relatedUsers) {
    const { userRelationsSalt } = config.cookies;
    // store current user ID only into the cookies
    paymentInfo.relatedUsers = encrypt(parseInt(user.id), userRelationsSalt);
  }

  return paymentInfo;
};
