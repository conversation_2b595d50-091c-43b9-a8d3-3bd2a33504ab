const { payment: { ACTION_LOCK, ACTION_UNLOCK } } = require('@s1/api-constants');
const {
  InvoiceNotFoundError,
  PaymentLockAlreadyToggledError,
  PaymentLockUnknownActionError,
  ChangingLockStateError,
} = require('@s1/api-errors');
const i18n = require('../../helpers/geti18n');
const Invoice = require('../../model/audb/Invoice');

const AVAILABLE_LOCK_ACTIONS = { [ACTION_LOCK]: true, [ACTION_UNLOCK]: true };

/**
 * @param tkey {number} - Id of Invoice
 * @param act {string} - lock/unlock action of invoice
 * @param locale
 * @returns {Promise<{newLockedState: (string), isLocked: boolean}>}
 */
module.exports = async ({ tkey, act, locale }) => {
  const invoice = await Invoice.findOne({ tkey }).exec();
  const isLockAttempt = act === ACTION_LOCK;
  const newLockedState = isLockAttempt ? 'locked' : 'unlocked';

  if (!invoice) throw new InvoiceNotFoundError();
  if (!AVAILABLE_LOCK_ACTIONS[act]) throw new PaymentLockUnknownActionError(locale);

  i18n.setLocale(locale);

  if (invoice.isLocked === isLockAttempt) throw new PaymentLockAlreadyToggledError(i18n.__(`Already ${newLockedState}`));

  invoice.isLocked = isLockAttempt;
  try {
    await invoice.save();

    return { newLockedState, isLocked: isLockAttempt };
  } catch (error) {
    throw new ChangingLockStateError(locale);
  }
};
