const log = require('@s1/log').create(__filename);
const { <PERSON><PERSON><PERSON> } = require('buffer');
const axios = require('axios');
const { getUserLocation } = require('../../service/maxmind');
const config = require('../../../config');
const getPaymentGeneralConfig = require('../payment/getPaymentGeneralConfig');
const getGroupRuleById = require('../payment/getGroupRuleById');
const getAllAccounts = require('../payment/getAllAccounts');
const canPayWithGroupRule = require('../payment/helpers/rules/canPayWithGroupRule');
const getCanPayWithBasicRulesIds = require('../payment/helpers/getCanPayWithBasicRulesIds');
const parseStringToArrayBySeparators = require('../../helpers/parseStringToArrayBySeparators');

/**
 * Get nftgate payment url
 *
 * @param {object} options
 * @param {User} options.user - user model
 * @param {object} options.pack - package info
 * @param {object} options.plinkBase64 - base64 encoded payment link
 * @param {object} options.billingAddress - last user billing address
 * @param {object} options.types - payment types from payment rules general config
 * @param {object} options.userIP - required
 *
 * @return {string|false} - paymentUrl or false
 * */
const getNftgatePaymentUrl = async ({ user, pack, plinkBase64, billingAddress, types, userIP }) => {
  if (!types || !types.length || !userIP) return false;

  const { tokey: tkey, id: packageId, epricestr: packageName } = pack;
  const { email, id: userId } = user;
  const manualBypassedAccounts = types.join(',');
  const requestPage = 'Packages page';
  // eslint-disable-next-line max-len
  const dataForPost = { tkey, email, requestPage, packageId, packageName, userId, userIp: userIP, manualBypassedAccounts, billingAddress, plinkBase64 };
  const url = `${config.crypto.apiUrl}/nft/payment-url?userId=${userId}&tkey=${tkey}&api_key=${config.crypto.apiKey}`;

  try {
    const response = await axios.request({
      method: 'post',
      data: dataForPost,
      url,
    });

    if (response && response.status === 200 && response.hasOwnProperty('data') && response.data
      && response.data.hasOwnProperty('paymentUrl') && response.data.paymentUrl) return response.data.paymentUrl;
  } catch (e) {
    // comes when account is not available or other request errors
    console.log('Cannot get nftgate paymentUrl', e);
    const errorMessage = e.stack || (e.message && e.message.toString());
    log.error(`Add nftgate links::Cannot get nftgate paymentUrl, user: '${user.id}', invoice number: '${tkey}', error: ${errorMessage}`);
  }

  return false;
};

/**
 * Method replaced payment links from billing to the nftgate by rule
 *
 * @param {User} user - user model
 * @param {string} userIP
 * @param {object} data - payment info data included packages with payment links
 *
 * @return {object} data - initial data with replaced packages[].plink
 * */
module.exports = async ({ user, userIP, data }) => {
  if (!userIP || !user) return data;

  const paymentGeneralConfigResponse = await getPaymentGeneralConfig();

  if (!paymentGeneralConfigResponse || !paymentGeneralConfigResponse.generalConfig) return data;

  const paymentGeneralConfig = paymentGeneralConfigResponse.generalConfig;

  if (paymentGeneralConfig && paymentGeneralConfig.allowNftgateLinksInPackagePaymentLink
    && paymentGeneralConfig.nftgateLinksNftPaymentTypes && Object.keys(paymentGeneralConfig.nftgateLinksNftPaymentTypes).length
    && paymentGeneralConfig.canSeeNftgateLinksInPackageRuleId) {
    const paymentTypes = Object.keys(paymentGeneralConfig.nftgateLinksNftPaymentTypes);
    const allAccountsResult = await getAllAccounts();
    const allAccounts = allAccountsResult.list || [];
    // filter disabled accounts and for preset payment types in the config only
    const activeNftPaymentAccounts = allAccounts.filter(
      account => account.enabled && account.PaymentType && account.PaymentType.enabled && paymentTypes.includes(account.PaymentType.name),
    );

    if (activeNftPaymentAccounts.length) {
      const allowCountriesForTheNftgate = paymentGeneralConfig.allowNftgateLinksInPackagePaymentLinkCountries ? parseStringToArrayBySeparators(paymentGeneralConfig.allowNftgateLinksInPackagePaymentLinkCountries, [',', '|']) : [];
      const denyCountriesForTheNftgate = paymentGeneralConfig.denyNftgateLinksInPackagePaymentLinkCountries ? parseStringToArrayBySeparators(paymentGeneralConfig.denyNftgateLinksInPackagePaymentLinkCountries, [',', '|']) : [];

      const { countryCode } = getUserLocation(userIP);

      // allow all countries if empty
      if (!(denyCountriesForTheNftgate && denyCountriesForTheNftgate.length && denyCountriesForTheNftgate.includes(countryCode))
        && (
          !allowCountriesForTheNftgate || !allowCountriesForTheNftgate.length
          || (allowCountriesForTheNftgate && allowCountriesForTheNftgate.length && allowCountriesForTheNftgate.includes(countryCode))
        )
      ) {
        const packageCanSeeRule = await getGroupRuleById(paymentGeneralConfig.canSeeNftgateLinksInPackageRuleId.toString());

        if (!packageCanSeeRule || !packageCanSeeRule.enabled) return data;

        const billingAddress = await user.getLastBillingAddress();
        const promises = [];
        data.packages.forEach((pack) => {
          promises.push((async () => {
            const canPayWithBasicRules = await getCanPayWithBasicRulesIds({ user, paymentInfo: pack, tkey: pack.tokey });
            const canSeeNftgateLinksInPackage = canPayWithGroupRule(packageCanSeeRule, canPayWithBasicRules);

            if (pack.canpay && pack.enable && canSeeNftgateLinksInPackage) {
              // filter accounts by current package
              const filteredAccounts = activeNftPaymentAccounts.filter(account => (account.packageId && account.packageId === pack.id)
                || (account.packageIds && account.packageIds.length && account.packageIds.includes(pack.id)));

              if (filteredAccounts.length) {
                const plinkBase64 = Buffer.from(pack.plink).toString('base64');
                const nftgateUrl = await getNftgatePaymentUrl({
                  user, pack, plinkBase64, billingAddress, types: Object.keys(paymentGeneralConfig.nftgateLinksNftPaymentTypes), userIP,
                });

                if (nftgateUrl) pack.plink = nftgateUrl;
              }
            }

            return pack;
          })());
        });

        const packages = await Promise.all(promises);
        const result = Object.assign(data, { packages });

        return result;
      }
    }
  }

  return data;
};
