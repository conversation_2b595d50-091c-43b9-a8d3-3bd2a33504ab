const PackageFeature = require('../../model/audb/PackageFeature');
const Channel = require('../../model/audb/Channel');
const ChannelGroup = require('../../model/audb/ChannelGroup');
const Suspended = require('../../model/audb/Suspended');
const config = require('../../../config');

module.exports = async ({ fromPage, user, sessionID, coupon, locale }) => {
  const [invoices, features, channels, suspended] = await Promise.all([
    user.getInvoices({ fromPage, sid: sessionID, coupon, locale }),
    PackageFeature.find({}).sort({ sor: 1 }).cache(600).exec(),
    Channel.find({})
      .sort({ odid: 1 })
      .cache(600)
      .exec()
      .then(channels => Promise.all(channels.map(async (channel) => {
        const group = await ChannelGroup.findOne({ id: channel.gid }).exec();
        group.channelGroup = group;
        const channelObj = channel.format({ nestedId: true, locale });
        channelObj.isradio = group.isradio;

        return channelObj;
      }))),
    Suspended.findOne({ uid: user.id }).cache(config.suspend.cache, `suspended_id_${user.id}`).exec(),
  ]);

  const hasPayablePack = invoices.filter(inv => +inv.canpay > 0 && inv.enable).length;
  const canUserBuyPackages = (suspended) => {
    const isUserHasPlayablePackages = hasPayablePack > 0;

    return !suspended && isUserHasPlayablePackages;
  };

  const userCanPay = canUserBuyPackages(suspended);

  const results = {
    features: features.map(feature => feature.format({ locale })),
    currency: 'usd',
    packages: invoices,
    extra: channels.filter(ch => +ch.extra === 1),
    normal: channels.filter(ch => +ch.extra === 0),
  };

  const addresses = user.billingAddresses ? user.billingAddresses : {};

  return { userCanPay, results, addresses };
};
